package cn.chinaunicom.sdsi.cloud.dict.commons.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 地市-区县公司对应关系
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class TSanquanDictDistrictCompaniesQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 父级地区ID
     */
    private String parentId;

    /**
     * 地区显示名称
     */
    private String regionsLabel;

    /**
     * 地区代码值
     */
    private String regionsValue;

    /**
     * 层级（1：省/市,2：区/县）
     */
    private String level;


}
