package cn.chinaunicom.sdsi.cloud.dict.archives.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@TableName("t_sanquan_dict_archives_ai_application")
public class TSanquanDictArchivesAiApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 档案表id
     */
    private String archivesId;

    /**
     * 序号
     */
    private String sort;

    /**
     * AI主要应用场景
     */
    private String applicationScenario;

    /**
     * 建设厂商
     */
    private String constructionManufacturer;

    /**
     * 建设时间
     */
    private String constructionDate;

    /**
     * 应用场景（主要应用场景选择其他时使用）
     */
    private String applicationScenarioOther;

}
