package cn.chinaunicom.sdsi.cloud.dbcp.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * DS等保测评新签约情况统计（区县）
 * </p>
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
public class TSanquanDbcpSignTjQxQueryVo extends BaseQueryVO {


    /**
     * 账期
     */
    private String acctId;

    /**
     * 区县
     */
    private String district;

    /**
     * 签约金额（万元，含税）
     */
    private Double signedTarget;

    /**
     * 核心/重要行业客户签约行业数量（个）
     */
    private Integer coreIndustrySignedNumber;

    /**
     * 地市
     */
    private String city;


}
