package cn.chinaunicom.sdsi.cloud.approval.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 审批记录表
 * </p>
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
public class TSanquanExportApprovalRecordQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 关联审批ID
     */
    private String approvalId;

    /**
     * 关联节点ID
     */
    private String nodeId;

    /**
     * 审批人ID
     */
    private String approverId;

    /**
     * 审批人姓名
     */
    private String approverName;

    /**
     * 审批结果(1:通过,2:拒绝)
     */
    private Integer approveResult;

    /**
     * 审批时间
     */
    private String approveTime;

    /**
     * 审批意见
     */
    private String comment;

    private String createTime;


}
