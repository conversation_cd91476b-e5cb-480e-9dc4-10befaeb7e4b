package cn.chinaunicom.sdsi.cloud.approval.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("t_sanquan_export_log")
public class TSanquanExportLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 关联审批ID
     */
    private String approvalId;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作类型(1:申请,2:审批,3:导出,4:下载)
     */
    private String operationType;

    /**
     * 操作时间
     */
    private String operationTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    private String createTime;


}
