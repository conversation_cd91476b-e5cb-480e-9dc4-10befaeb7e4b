package cn.chinaunicom.sdsi.cloud.gzlbf.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p>
 * 高质量拜访-地市维度清单
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@TableName("t_sanquan_gzlbf_ds_hygzlbf_list")
public class TSanquanGzlbfCityList implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账期
     */
    private String dayId;

    /**
     * 自然客户ID
     */
    private String natureCustId;

    /**
     * 自然客户名称
     */
    private String natureCustName;

    /**
     * 名单制客户编码
     */
    private String rosterCustomerId;

    /**
     * 名单制客户名称
     */
    private String rosterCustomerName;

    /**
     * 名单客户等级
     */
    private String flag;
    /**
     * 客户经理
     */
    private String custManager;

    /**
     * 客户经理工号
     */
    private String custManagerOa;

    /**
     * 地市名称
     */
    private String areaName;

    /**
     * 行业名称
     */
    private String hangye;

    /**
     * 拜访记录ID
     */
    private String recordId;

    /**
     * 拜访时间
     */
    private String visitTime;

    /**
     * 签到地点
     */
    private String signInPlace;

    /**
     * 商机编码
     */
    private String buisnessId;

    /**
     * 访谈内容
     */
    private String ftInfo;

    /**
     * 拜访总结
     */
    private String bfZj;

    /**
     * 商机金额
     */
    private BigDecimal feeNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 拜访质量标签
     */
    private String flagYxbf;

    public String getSignInPlace() {
        if(!StringUtils.isNotEmpty(signInPlace) || "null".equals(signInPlace)){
            return "";
        }
        return signInPlace;
    }

    public String getBuisnessId() {
        if(!StringUtils.isNotEmpty(buisnessId) || "null".equals(buisnessId)){
            return "";
        }
        return buisnessId;
    }

    public String getFtInfo() {
        if(!StringUtils.isNotEmpty(ftInfo) || "null".equals(ftInfo)){
            return "";
        }
        return ftInfo;
    }

    public String getBfZj() {
        if(!StringUtils.isNotEmpty(bfZj) || "null".equals(bfZj)){
            return "";
        }
        return bfZj;
    }

    public String getOppoName() {
        if(!StringUtils.isNotEmpty(oppoName) || "null".equals(oppoName)){
            return "";
        }
        return oppoName;
    }
}
