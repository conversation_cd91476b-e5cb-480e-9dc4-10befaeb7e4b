package cn.chinaunicom.sdsi.cloud.dict.archives.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 业务需求情况
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class TSanquanDictArchivesBusinessDemandQueryVo extends BaseQueryVO {


      private String id;

    /**
     * 档案表id
     */
    private String archivesId;

    /**
     * 问题
     */
    private String problemLabel;

    /**
     * 是否有需求
     */
    private String isDemand;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;


}
