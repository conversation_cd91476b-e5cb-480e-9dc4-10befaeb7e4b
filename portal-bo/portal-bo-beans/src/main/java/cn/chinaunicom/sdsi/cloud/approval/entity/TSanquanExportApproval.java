package cn.chinaunicom.sdsi.cloud.approval.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("t_sanquan_export_approval")
public class TSanquanExportApproval implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId("id")
    private String id;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 模块名称
     */
    private String moduleName;

    /**
     * 导出条件
     */
    private String exportCondition;

    /**
     * 导出字段配置
     */
    private String exportFields;

    /**
     * 申请人工号
     */
    private String applicantId;

    /**
     * 申请人名称
     */
    private String applicantName;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 状态(0:待审批,1:已通过,2:已拒绝,3:已取消,4:审批中)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 导出文件URL
     */
    private String fileUrl;

    /**
     * 导出文件名
     */
    private String fileName;

    /**
     * 文件大小(字节)
     */
    private String fileSize;

    /**
     * 文件过期时间
     */
    private String expireTime;

    /**
     * 审批类型(1:指定人员,2:角色,3:部门负责人)
     */
    private String approveType;

    /**
     * 审批人ID
     */
    private String approverId;

    /**
     * 审批人姓名
     */
    private String approverName;

    /**
     * 审批人电话
     */
    private String approverTel;

    /**
     * 审批时间
     */
    private String approveTime;

    /**
     * 审批意见
     */
    private String comment;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 文件id
     */
    private String fileId;


}
