package cn.chinaunicom.sdsi.cloud.dbcp.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 等保测评一点通人员权限表
 * </p>
 * <AUTHOR>
 * @since 2025-04-01
 */
@Data
public class TSanquanDbcpPersonLevelQueryVo extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员级别(1、省，2、地市，3、区县)
     */
    private String personLevel;

    /**
     * 手机号
     */
    private String personTel;

    /**
     * 地市
     */
    private String personCity;

    /**
     * 区县
     */
    private String personDistrict;

    /**
     * 状态(0、禁用， 1、正常)
     */
    private String personStatus;

    /**
     * 类型
     */
    private String personType;

    /**
     * 职务
     */
    private String jobTitle;

}
