package cn.chinaunicom.sdsi.cloud.dict.archives.vo;

import java.io.Serializable;
import java.util.List;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.*;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <p>
 * 客户DICT数字档案
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class TSanquanDictArchivesVo implements Serializable {


    private String id;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户行业
     */
    private String customerIndustry;

    /**
     * 客户联系人
     */
    private String customerContacts;

    /**
     * 客户联系电话
     */
    private String customerContactTel;

    /**
     * 客户类型
     */
    private String customerType;

    /**
     * 客户经理姓名
     */
    private String customerManager;

    /**
     * 客户经理工号
     */
    private String customerManagerOa;

    /**
     * 客户经理电话
     */
    private String customerManagerTel;

    /**
     * 地市
     */
    private String city;

    /**
     * 区县
     */
    private String county;

    /**
     * 地市区县公司
     */
    private String countyCompany;

    /**
     * 走访标签
     */
    private String visitTags;

    /**
     * 是否陌拜
     */
    private String isMobai;

    /**
     * 营销的产品
     */
    private String marketingProducts;

    /**
     * 网络问题及需求
     */
    private String networkProblemRequirement;

    /**
     * 基础区域问题及需求
     */
    private String baseRegionProblemRequirement;

    /**
     * 中台建设问题及需求
     */
    private String middlePlatformProblemRequirement;

    /**
     * 业务系统建设问题及需求
     */
    private String businessSystemProblemRequirement;

    /**
     * AI应用问题及需求
     */
    private String aiApplicationProblemRequirement;

    /**
     * 大模型使用是否符合预期
     */
    private String aiModelIsExpect;

    /**
     * 希望大模型实现的智能化场景及目标
     */
    private String aiModelSceneTarget;

    /**
     * 希望大模型解决的具体问题是什么
     */
    private String aiModelProblemRequirement;

    /**
     * 技术团队问题及需求
     */
    private String technicalTeamProblemRequirement;

    /**
     * 亟待业务痛点有哪些？
     */
    private String businessPainPoints;

    /**
     * 附件id
     */
    private String attachmentId;

    /**
     * 附件名称
     */
    private String attachmentName;

    /**
     * 创建时间
     */
    private String createDate;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 编辑时间
     */
    private String updateDate;

    /**
     * 编辑人
     */
    private String updateBy;

    private String tenantId;

    /**
     * 是否有信创需求（0、否，1、是）
     */
    private String isXinchuangDemand;

    /**
     * 信创需求内容
     */
    private String xinchuangContent;

    /**
     * 是否有等保需求（0、否，1、是）
     */
    private String isDengbaoDemand;

    /**
     * 等保需求内容
     */
    private String dengbaoContent;

    /**
     * 是否有运维服务需求（0、否，1、是）
     */
    private String isOperationDemand;

    /**
     * 运维服务内容
     */
    private String operationContent;

    /**
     * 是否有无影云电脑需求（0、否，1、是）
     */
    private String isWyydnDemand;

    /**
     * 终端数量
     */
    private String terminalNumWyydn;

    /**
     * 无影云电脑预算金额（元）
     */
    private String budgetWyydn;

    /**
     * 无影云电脑需求描述
     */
    private String demandWyydn;

    /**
     * 是否有云联至简需求
     */
    private String isYlzjDemand;

    /**
     * 企业人数
     */
    private String employeesYlzj;

    /**
     * 云联至简预算金额（元）
     */
    private String budgetYlzj;

    /**
     * 云联至简需求描述
     */
    private String demandYlzj;

    // 网络问题及需求
    private List<TSanquanDictArchivesNetwarkInfo> networkinfoList;

    // 基础区域
    private List<TSanquanDictArchivesBaseRegion> baseRegionList;

    // 中台建设
    private List<TSanquanDictArchivesMiddlePlatform> middlePlatformList;

    // 业务系统建设
    private List<TSanquanDictArchivesBusinessSystem> businessSystemList;

    // AI应用场景
    private List<TSanquanDictArchivesAiApplication> aiApplicationList;

    // 技术团队
    private List<TSanquanDictArchivesTechnicalTeam> technicalTeamList;

    /**
     * 是否转换商机（0、否，1、是）
     */
    private String isTransferOppo;

    /**
     * 客户商机规模（万元）
     */
    private String customerOppoScale;

    /**
     * 客户商机需求
     */
    private String customerOppoDemand;

    /**
     * 创建人组织
     */
    private String createByOrg;

    public TSanquanDictArchives convertVo(TSanquanDictArchivesVo tSanquanDictArchivesVo) {
        TSanquanDictArchives tSanquanDictArchives = new TSanquanDictArchives();
        BeanUtils.copyProperties(tSanquanDictArchivesVo, tSanquanDictArchives);
        return tSanquanDictArchives;
    }
}
