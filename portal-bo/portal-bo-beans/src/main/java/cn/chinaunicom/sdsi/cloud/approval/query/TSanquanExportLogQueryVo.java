package cn.chinaunicom.sdsi.cloud.approval.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 导出日志表
 * </p>
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
public class TSanquanExportLogQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 关联审批ID
     */
    private String approvalId;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 操作类型(1:申请,2:审批,3:导出,4:下载)
     */
    private Integer operationType;

    /**
     * 操作时间
     */
    private String operationTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    private String createTime;


}
