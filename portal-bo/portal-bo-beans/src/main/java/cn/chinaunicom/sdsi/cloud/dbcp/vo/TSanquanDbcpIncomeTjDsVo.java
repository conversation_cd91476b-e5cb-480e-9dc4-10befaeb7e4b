package cn.chinaunicom.sdsi.cloud.dbcp.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * DS等保测评收入完成情况统计（地市）
 * </p>
 * <AUTHOR>
 * @since 2025-03-18
 */
@Data
public class TSanquanDbcpIncomeTjDsVo implements Serializable {


    /**
     * 账期
     */
    private String acctId;

    /**
     * 地市
     */
    private String city;

    /**
     * 收入目标（万元，含税）
     */
    private Double incomeTarget;

    /**
     * 项目收入（万元，含税）
     */
    private Double projectIncome;

    /**
     * 去年同期项目收入（万元，含税）
     */
    private Double projectIncomeLastyear;

    /**
     * 同比增长率
     */
    private Double yearOnYearGrowthRate;

    /**
     * 目标完成率
     */
    private Double targetCompletionRate;

    /**
     * 序时完成率
     */
    private Double sequentialCompletionRate;

    /**
     * 目标完成率排名
     */
    private Integer ratetOrder;


}
