package cn.chinaunicom.sdsi.cloud.dbcp.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 等保测评签约明细(月)
 * </p>
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
public class TSanquanDbcpSignAmountQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 序号
     */
    private String seq;

    /**
     * 账期(月份)
     */
    private String acctId;

    /**
     * 地市
     */
    private String city;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 市公司签署额
     */
    private String cityComSignAmount;

    /**
     * 对方全称
     */
    private String otherside;

    /**
     * 落地区县
     */
    private String district;

    /**
     * 落地行业
     */
    private String industry;

    /**
     * 行业属性细分
     */
    private String subdivisionIndustry;

    /**
     * 备注
     */
    private String remark;

    /**
     * 核心重点行业客户
     */
    private String importIndustryCust;

}
