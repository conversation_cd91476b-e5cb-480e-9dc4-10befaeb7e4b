package cn.chinaunicom.sdsi.cloud.gzlbf.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@TableName("t_sanquan_gzlbf_hygzlbf_hangye")
public class TSanquanGzlbfHygzlbfHangye implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账期
     */
    private String dayId;

    /**
     * 行业
     */
    private String hangye;

    /**
     * 客户经理人数
     */
    private BigDecimal custManagerCn;

    /**
     * 省主责客户数量
     */
    private BigDecimal managerZzCn;

    /**
     * 有效走访次数
     */
    private BigDecimal zfYouxiaoCn;

    /**
     * 客户经理出访率
     */
    private BigDecimal cflManagerRate;

    /**
     * 客户经理出访率排名
     */
    private BigDecimal cflManagerRatePm;

    /**
     * 走访客户数
     */
    private BigDecimal fglZfkhs;

    /**
     * 客户走访覆盖率
     */
    private BigDecimal fglKhzfRate;

    /**
     * 客户走访覆盖率排名
     */
    private BigDecimal fglKhzfRatePm;

    /**
     * 商机客户覆盖率
     */
    private BigDecimal sjKhfgRate;

    /**
     * 商机客户覆盖率排名
     */
    private BigDecimal sjKhfgRatePm;

    /**
     * 综合排名
     */
    private BigDecimal zhPm;

    /**
     * 上周拜访客户数
     */
    private BigDecimal zfKhsLweek;


}
