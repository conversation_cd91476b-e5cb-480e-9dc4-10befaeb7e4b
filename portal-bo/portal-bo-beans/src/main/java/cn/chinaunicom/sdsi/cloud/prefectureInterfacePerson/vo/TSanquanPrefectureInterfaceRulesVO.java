package cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 地市接口人规则表
 *
 * <AUTHOR> 
 * @since  2024-05-30
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TSanquanPrefectureInterfaceRulesVO extends BaseEntity {
	/**
	* id
	*/
	@TableId
	private String id;


	/**
	 * 所在地市
	 */
	private String listCustomerCity;

	/**
	 * 所在区县
	 */
	private String listCustomerDistrict;


	/* 派发类型，1接口人，2，落格人 */
	private String interfaceType;

	/**
	* 地市接口人电话
	*/
	private String tel;

	/**
	 * 地市接口人工号
	 */
	private String jobNumber;
	/**
	 * 地市接口人名字
	 */
	private String name;

	/**
	 * 稽核场景（0、全部，1、调账稽核，2、BO稽核，3、IP地址稽核）
	 */
	private String auditScene;

	/**
	 * 自动派单时间
	 */
	private String autoDispatchDate;

	/**
	 * 是否周末、节假日派单（0、否，1、是）
	 */
	private String isHoliday;

}