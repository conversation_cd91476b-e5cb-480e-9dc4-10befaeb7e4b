package cn.chinaunicom.sdsi.cloud.dict.archives.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@TableName("t_sanquan_dict_archives_technical_team")
public class TSanquanDictArchivesTechnicalTeam implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 档案表id
     */
    private String archivesId;

    /**
     * 序号
     */
    private String sort;

    /**
     * 团队类型
     */
    private String teamType;

    /**
     * 团队规模
     */
    private String teamSize;

    /**
     * 能力评价
     */
    private String capabilityEvaluation;


}
