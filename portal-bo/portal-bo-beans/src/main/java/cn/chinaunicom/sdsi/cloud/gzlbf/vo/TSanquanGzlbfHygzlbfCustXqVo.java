package cn.chinaunicom.sdsi.cloud.gzlbf.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <p>
 * 高质量拜访-清单表
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
public class TSanquanGzlbfHygzlbfCustXqVo implements Serializable {


    /**
     * 账期
     */
    private String dayId;

    /**
     * 行业
     */
    private String hangye;

    /**
     * 客户经理
     */
    private String custManagerName;

    /**
     * 客户经理工号
     */
    private String custManagerCode;

    /**
     * 拜访ID
     */
    private String recordId;

    /**
     * 拜访主题
     */
    private String visitTitle;

    /**
     * 拜访形式
     */
    private String visitType;

    /**
     * 拜访时间
     */
    private String visitTime;

    /**
     * 签到时间
     */
    private String createTime;

    /**
     * 签到地点
     */
    private String signInPlace;

    /**
     * 参访人员角色/参访人员
     */
    private String cfPerson;

    /**
     * 拜访客户名称
     */
    private String customName;

    /**
     * (拜访客户的)自然客户编码
     */
    private String customerId;

    /**
     * 受访人姓名
     */
    private String interviewName;

    /**
     * 受访人电话
     */
    private String interviewTel;

    /**
     * 受访人岗位
     */
    private String interviewJob;

    /**
     * 访谈内容
     */
    private String visitSummary;

    /**
     * 商机编码
     */
    private String oppoNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 是否有效参访
     */
    private BigDecimal flagYouxia;


}
