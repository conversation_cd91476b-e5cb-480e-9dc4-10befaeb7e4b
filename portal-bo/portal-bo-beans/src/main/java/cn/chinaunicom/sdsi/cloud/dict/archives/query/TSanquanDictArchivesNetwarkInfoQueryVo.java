package cn.chinaunicom.sdsi.cloud.dict.archives.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * IT基础设施现状-现有网络情况
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class TSanquanDictArchivesNetwarkInfoQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 档案表id
     */
    private String archivesId;

    /**
     * 运营商
     */
    private String operator;

    /**
     * 宽带
     */
    private String broadband;

    /**
     * 资费（元）
     */
    private String expenses;

    /**
     * 到期时间
     */
    private String expirationDate;


}
