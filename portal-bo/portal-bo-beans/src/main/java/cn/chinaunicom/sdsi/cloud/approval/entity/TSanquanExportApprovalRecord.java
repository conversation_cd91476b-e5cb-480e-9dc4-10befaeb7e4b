package cn.chinaunicom.sdsi.cloud.approval.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-06-10
 */
@Data
@TableName("t_sanquan_export_approval_record")
public class TSanquanExportApprovalRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 关联审批ID
     */
    private String approvalId;

    /**
     * 关联节点ID
     */
    private String nodeId;

    /**
     * 审批人ID
     */
    private String approverId;

    /**
     * 审批人姓名
     */
    private String approverName;

    /**
     * 审批结果(1:通过,2:拒绝)
     */
    private String approveResult;

    /**
     * 审批时间
     */
    private String approveTime;

    /**
     * 审批意见
     */
    private String comment;


    private String createTime;


}
