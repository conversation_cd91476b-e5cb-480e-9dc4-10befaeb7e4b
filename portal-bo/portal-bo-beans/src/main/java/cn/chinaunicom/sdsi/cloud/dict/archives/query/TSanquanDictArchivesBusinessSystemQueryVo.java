package cn.chinaunicom.sdsi.cloud.dict.archives.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 业务系统建设现状
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class TSanquanDictArchivesBusinessSystemQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 档案表id
     */
    private String archivesId;

    /**
     * 序号
     */
    private String sort;

    /**
     * 应用名称
     */
    private String applyName;

    /**
     * 建设厂商
     */
    private String constructionManufacturer;

    /**
     * 建设时间
     */
    private String constructionDate;

    /**
     * 承载的业务数据
     */
    private String bearerServiceData;

    /**
     * 其他应用名称
     */
    private String applyNameOther;

}
