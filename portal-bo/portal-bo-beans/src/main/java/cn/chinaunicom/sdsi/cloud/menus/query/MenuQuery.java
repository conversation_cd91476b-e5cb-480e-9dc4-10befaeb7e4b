package cn.chinaunicom.sdsi.cloud.menus.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "菜单查询")
public class MenuQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    @TableId
    private String id;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "上级id")
    private String parentId;

    @Schema(description = "角色")
    private String role;
}
