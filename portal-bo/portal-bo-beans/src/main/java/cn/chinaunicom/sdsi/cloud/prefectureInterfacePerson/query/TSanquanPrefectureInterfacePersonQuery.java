package cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 地市接口人表
 *
 * <AUTHOR> 
 * @since  2024-05-30
 */

@Data
@Schema(description = "地市接口人表")
public class TSanquanPrefectureInterfacePersonQuery extends BaseQueryVO {
	private static final long serialVersionUID = 1L;

	@Schema(description = "id")
	private String id;

	@Schema(description = "地市接口人工号")
	private String jobNumber;

	@Schema(description = "地市接口人名字")
	private String name;

	@Schema(description = "地市接口人电话")
	private String tel;

	@Schema(description = "所属行业")
	private String industry;

	@Schema(description = "客户省分")
	private String listCustomerProvince;

	@Schema(description = "所在地市")
	private String listCustomerCity;

	@Schema(description = "所在区县")
	private String listCustomerDistrict;

	@Schema(description = "名单制客户id")
	private String rosterCustomerId;

	@Schema(description = "名单制客户名称")
	private String rosterCustomerName;

	@Schema(description = "客户经理id")
	private String customerManagerId;

	@Schema(description = "客户经理名称")
	private String customerManagerName;

	@Schema(description = "省分行业")
	private String provinceIndustry;

	@Schema(description = "客户等级")
	private String listCustomerLevel;

	@Schema(description = "联系地址")
	private String contactAddress;

	@Schema(description = "备用字段1")
	private String attr1;

	@Schema(description = "备用字段2")
	private String attr2;

	@Schema(description = "备用字段3")
	private String attr3;

	@Schema(description = "二级审核（地市审核）")
	private String cityRole;

	@Schema(description = "稽核场景（0、全部，1、调账稽核，2、BO稽核，3、IP地址稽核）")
	private String auditScene;

	@Schema(description = "接口人归属范围（1、地市，2、地市-区县）")
	private String belongScope;

	@Schema(description = "是否默认（0、不是默认，2、默认）")
	private String type;

}