package cn.chinaunicom.sdsi.cloud.gzlbf.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <p>
 *  高质量拜访-地市维度高质量拜访报表
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@TableName("t_sanquan_gzlbf_hygzlbf_dsbf")
public class TSanquanGzlbfCityReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账期
     */
    private String dayId;

    /**
     * 序号
     */
    private String rhIndex;

    /**
     * 地市编码
     */
    private String areaId;

    /**
     * 地市名称
     */
    private String areaName;

    /**
     * 区县编码
     */
    private String cityId;

    /**
     * 区县名称
     */
    private String cityName;

    /**
     * 营服编码
     */
    private String campId;

    /**
     * 营服名称
     */
    private String campName;

    /**
     * 有效走访次数(上周)
     */
    private BigDecimal zfYouxiaoCn;

    /**
     * 上周拜访客户数
     */
    private BigDecimal zfKhsLweek;

    /**
     * 新增商机客户数
     */
    private BigDecimal sjKhsLweek;

    /**
     * 拜访客户数(季度)
     */
    private BigDecimal fglZfkhsJd;

    /**
     * 拜访客户覆盖率(季度)
     */
    private BigDecimal fglKhzfRateJd;

    @TableField(exist = false)
    private String fglKhzfRateJdStr;

    /**
     * 商机金额（万元）(季度)
     */
    private BigDecimal sjFeeJd;

    /**
     * 商机客户覆盖率(年累)
     */
    private BigDecimal sjKhfgRateYear;

    @TableField(exist = false)
    private String sjKhfgRateYearStr;

    public String getFglKhzfRateJdStr() {
        if(StringUtils.isNotEmpty(fglKhzfRateJd)){
           return fglKhzfRateJd.toPlainString()+"%";
        }
        return "";
    }

    public String getSjKhfgRateYearStr() {
        if(StringUtils.isNotEmpty(sjKhfgRateYear)){
            return sjKhfgRateYear.toPlainString()+"%";
        }
        return "";
    }
}
