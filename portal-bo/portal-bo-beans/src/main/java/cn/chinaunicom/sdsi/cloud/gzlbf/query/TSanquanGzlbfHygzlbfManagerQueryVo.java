package cn.chinaunicom.sdsi.cloud.gzlbf.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 高质量拜访-客户经理统计报表
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
public class TSanquanGzlbfHygzlbfManagerQueryVo extends BaseQueryVO {


    /**
     * 账期
     */
    private String dayId;

    /**
     * 行业
     */
    private String hangye;

    /**
     * 客户经理
     */
    private String custManagerName;

    /**
     * 客户经理工号
     */
    private String custManagerCode;

    /**
     * 省主责客户数量
     */
    private BigDecimal managerZzCn;

    /**
     * 有效走访次数
     */
    private BigDecimal zfYouxiaoCn;

    /**
     * 走访客户数
     */
    private BigDecimal fglZfkhs;

    /**
     * 客户走访覆盖率
     */
    private BigDecimal fglKhzfRate;

    /**
     * 客户走访覆盖率排名
     */
    private BigDecimal fglKhzfRatePm;

    /**
     * 商机金额（万元）
     */
    private BigDecimal sjFee;

    /**
     * 商机客户覆盖率
     */
    private BigDecimal sjKhfgRate;

    /**
     * 商机客户覆盖率排名
     */
    private BigDecimal sjKhfgRatePm;

    /**
     * 综合排名
     */
    private BigDecimal zhPm;

    /**
     * 上周拜访客户数
     */
    private BigDecimal zfKhsLweek;


}
