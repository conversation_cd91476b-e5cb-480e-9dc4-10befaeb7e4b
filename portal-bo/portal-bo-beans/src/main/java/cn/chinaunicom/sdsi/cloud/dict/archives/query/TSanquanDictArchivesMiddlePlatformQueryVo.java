package cn.chinaunicom.sdsi.cloud.dict.archives.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 中台建设情况
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
public class TSanquanDictArchivesMiddlePlatformQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 档案表id
     */
    private String archivesId;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 建设厂商
     */
    private String constructionManufacturer;

    /**
     * 建设时间
     */
    private String constructionDate;

    /**
     * 承载的业务数据
     */
    private String businessData;

    /**
     * 其他中台类型（中台类型选择其他时使用）
     */
    private String platformTypeOther;


}
