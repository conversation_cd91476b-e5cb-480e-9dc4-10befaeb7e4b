package cn.chinaunicom.sdsi.cloud.dict.commons.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-04-16
 */
@Data
@TableName("t_sanquan_dict_district_companies")
public class TSanquanDictDistrictCompanies implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 父级地区ID
     */
    private String parentId;

    /**
     * 地区显示名称
     */
    private String regionsLabel;

    /**
     * 地区代码值
     */
    private String regionsValue;

    /**
     * 层级（1：省/市,2：区/县）
     */
    private String level;


}
