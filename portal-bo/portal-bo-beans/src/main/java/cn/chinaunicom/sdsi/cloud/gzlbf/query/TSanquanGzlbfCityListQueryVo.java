package cn.chinaunicom.sdsi.cloud.gzlbf.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 高质量拜访-地市维度清单
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
public class TSanquanGzlbfCityListQueryVo extends BaseQueryVO {



    /**
     * 账期
     */
    private String dayId;

    /**
     * 自然客户ID
     */
    private String natureCustId;

    /**
     * 自然客户名称
     */
    private String natureCustName;

    /**
     * 名单制客户编码
     */
    private String rosterCustomerId;

    /**
     * 名单制客户名称
     */
    private String rosterCustomerName;

    /**
     * 名单客户等级
     */
    private String flag;
    /**
     * 客户经理
     */
    private String custManager;
    private String custManagerName;

    /**
     * 客户经理工号
     */
    private String custManagerOa;
    /**
     * 地市名称
     */
    private String areaName;
    private String areaName2;

    /**
     * 行业名称
     */
    private String hangye;

    /**
     * 拜访记录ID
     */
    private String recordId;

    /**
     * 拜访时间
     */
    private String visitTime;

    /**
     * 签到地点
     */
    private String signInPlace;

    /**
     * 商机编码
     */
    private String buisnessId;

    /**
     * 访谈内容
     */
    private String ftInfo;

    /**
     * 拜访总结
     */
    private String bfZj;

    /**
     * 商机金额
     */
    private BigDecimal feeNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 拜访质量标签
     */
    private String flagYxbf;

    private String startTime;

    private String endTime;


}
