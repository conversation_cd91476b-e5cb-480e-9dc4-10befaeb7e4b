package cn.chinaunicom.sdsi.cloud.gzlbf.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * <p>
 * 高质量拜访-地市维度高质量拜访报表
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
public class TSanquanGzlbfCityReportVo implements Serializable {


    /**
     * 账期
     */
    private String dayId;

    /**
     * 序号
     */
    private String rhIndex;

    /**
     * 地市编码
     */
    private String areaId;

    /**
     * 地市名称
     */
    private String areaName;

    /**
     * 区县编码
     */
    private String cityId;

    /**
     * 区县名称
     */
    private String cityName;

    /**
     * 营服编码
     */
    private String campId;

    /**
     * 营服名称
     */
    private String campName;

    /**
     * 有效走访次数(上周)
     */
    private BigDecimal zfYouxiaoCn;

    /**
     * 上周拜访客户数
     */
    private BigDecimal zfKhsLweek;

    /**
     * 新增商机客户数
     */
    private BigDecimal sjKhsLweek;

    /**
     * 拜访客户数(季度)
     */
    private BigDecimal fglZfkhsJd;

    /**
     * 拜访客户覆盖率(季度)
     */
    private BigDecimal fglKhzfRateJd;

    /**
     * 商机金额（万元）(季度)
     */
    private BigDecimal sjFeeJd;

    /**
     * 商机客户覆盖率(年累)
     */
    private BigDecimal sjKhfgRateYear;


}
