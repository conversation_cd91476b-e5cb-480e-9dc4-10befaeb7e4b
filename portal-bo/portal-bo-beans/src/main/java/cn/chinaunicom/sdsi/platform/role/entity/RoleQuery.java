package cn.chinaunicom.sdsi.platform.role.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class RoleQuery extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    /**
     * 角色名称
     */
    private String roleName;

    private String roleId;

    private String sanquanName;

    /**
     * 角色编码
     */
    private String roleCode;

    private String deleted="0";



}
