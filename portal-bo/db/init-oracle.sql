/*
 Navicat Premium Data Transfer

 Source Server         : localhost oracle cloud
 Source Server Type    : Oracle
 Source Server Version : 180000
 Source Host           : localhost:1521
 Source Schema         : C##CLOUD

 Target Server Type    : Oracle
 Target Server Version : 180000
 File Encoding         : 65001

 Date: 25/06/2021 14:32:06
*/
----创建用户
create user c##cloud identified by cloud default tablespace users;
grant connect,resource,dba  to c##cloud;
-- ----------------------------
-- Table structure for CLOUD_MENU
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_MENU";
CREATE TABLE "C##CLOUD"."CLOUD_MENU" (
  "MENU_ID" NVARCHAR2(20) VISIBLE NOT NULL,
  "MENU_NAME" NVARCHAR2(50) VISIBLE NOT NULL,
  "PARENT_ID" NVARCHAR2(20) VISIBLE,
  "MENU_ORDER" NUMBER(11,0) VISIBLE,
  "MENU_URL" NVARCHAR2(200) VISIBLE,
  "MENU_FRAME" NCHAR(1) VISIBLE,
  "MENU_TYPE" NVARCHAR2(10) VISIBLE,
  "MENU_VISIBLE" NVARCHAR2(4) VISIBLE,
  "MENU_STATUS" NVARCHAR2(10) VISIBLE,
  "MENU_PERMS" NVARCHAR2(100) VISIBLE,
  "MENU_ICON" NVARCHAR2(100) VISIBLE,
  "CREATE_BY" NVARCHAR2(30) VISIBLE,
  "CREATE_DATE" DATE VISIBLE,
  "UPDATE_BY" NVARCHAR2(30) VISIBLE,
  "UPDATE_DATE" DATE VISIBLE,
  "DELETE_FLAG" NVARCHAR2(10) VISIBLE,
  "VERSIONS" NUMBER(11,0) VISIBLE,
  "TENANT_ID" NVARCHAR2(64) VISIBLE,
  "COMPONET" NVARCHAR2(64) VISIBLE,
  "MENU_SCOPE" NVARCHAR2(64) VISIBLE,
  "MENU_OU" NVARCHAR2(255) VISIBLE,
  "MENU_AUTH" NVARCHAR2(255) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_ID" IS '主键';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_NAME" IS '菜单名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."PARENT_ID" IS '父菜单ID';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_ORDER" IS '显示顺序';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_URL" IS '路由地址';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_FRAME" IS '是否为外链（Y-是 N-否）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_TYPE" IS '菜单类型（menu-菜单 folder-目录 operation-操作）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_VISIBLE" IS '显示状态（show-显示 hide-隐藏）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_STATUS" IS '菜单状态（valid-正常 invalid-停用）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_PERMS" IS '权限标识';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."MENU_ICON" IS '菜单图标';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."CREATE_BY" IS '创建者';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."CREATE_DATE" IS '创建时间';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."UPDATE_BY" IS '更新者';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."UPDATE_DATE" IS '更新时间';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."DELETE_FLAG" IS '逻辑删除位';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."VERSIONS" IS '乐观锁标记位';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."TENANT_ID" IS '租户id';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_MENU"."COMPONET" IS '商城定制';
COMMENT ON TABLE "C##CLOUD"."CLOUD_MENU" IS '菜单权限表';

-- ----------------------------
-- Records of CLOUD_MENU
-- ----------------------------
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('2', '测试菜单2', '0', '0', NULL, 'N', NULL, 'show', 'valid', NULL, '#', NULL, TO_DATE('2021-06-17 16:06:21', 'SYYYY-MM-DD HH24:MI:SS'), NULL, TO_DATE('2021-06-17 16:06:21', 'SYYYY-MM-DD HH24:MI:SS'), 'normal', '1', NULL, NULL, NULL, NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('3', '3', '0', '0', NULL, 'N', NULL, 'show', 'valid', NULL, '#', NULL, NULL, NULL, NULL, 'normal', '1', NULL, NULL, NULL, NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu01', 'index', '0', '0', 'index', 'N', NULL, 'show', 'valid', 'sys:index', '#', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'Layout', NULL, NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu0101', '门户首页', 'menu02', '1', 'home', 'N', 'menu', 'show', 'valid', 'sys:index:dashboard', 'icon-home', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'home/index', 'portal', NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu0102', '指挥舱', 'menu02', '2', 'rostrum', 'N', 'menu', 'show', 'valid', 'sys:index:dashboard', 'icon-guanlijiashicang', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'rostrum/index', 'portal', NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu0103', '工作台', 'menu02', '3', 'workbench', 'N', 'menu', 'show', 'valid', 'sys:index:dashboard', 'icon-gongzuotai', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'workbench/index', 'portal', NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu0104', '应用中心', 'menu02', '4', 'app-center', 'N', 'menu', 'show', 'valid', 'sys:index:dashboard', 'icon-gongzuotai1', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'app-center/index', 'portal', NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu0105', '查询中心', 'menu02', '5', 'inquiry-center', 'N', 'menu', 'show', 'valid', 'sys:index:dashboard', 'icon-zaitu', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'inquiry-center/index', 'portal', NULL, 'Y');
INSERT INTO "C##CLOUD"."CLOUD_MENU" VALUES ('menu02', '门户菜单', '0', '0', '/', 'N', 'folder', 'show', 'valid', 'sys:index', '#', NULL, NULL, NULL, NULL, 'normal', '1', NULL, 'Portal', 'portal', NULL, 'Y');

-- ----------------------------
-- Table structure for CLOUD_PORTALORG
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_PORTALORG";
CREATE TABLE "C##CLOUD"."CLOUD_PORTALORG" (
  "OU" NVARCHAR2(32) VISIBLE NOT NULL,
  "NAME" NVARCHAR2(150) VISIBLE,
  "BUSINESSCATEGORY" NVARCHAR2(32) VISIBLE NOT NULL,
  "CUMANAGERNUMBER" NVARCHAR2(64) VISIBLE,
  "DISPLAYNAME" NVARCHAR2(150) VISIBLE,
  "CUSUPERVISORYDEPARTMENT" NVARCHAR2(32) VISIBLE,
  "COSUNIT_COSTID" NVARCHAR2(64) VISIBLE,
  "DN" NVARCHAR2(100) VISIBLE,
  "CUORDER" NUMBER(11,0) VISIBLE,
  "SITE" NVARCHAR2(32) VISIBLE,
  "BRANCHLEADER" NVARCHAR2(360) VISIBLE,
  "ALLLEADER" NVARCHAR2(360) VISIBLE,
  "ZHDB" NVARCHAR2(20) VISIBLE,
  "MANAGER" NVARCHAR2(20) VISIBLE,
  "ISQUXIAN" NUMBER(4,0) VISIBLE,
  "IS_DEL" NUMBER(11,0) VISIBLE,
  "DEFAULTENTERSHOPCODE" NVARCHAR2(8) VISIBLE,
  "ROOMLEADER" NVARCHAR2(360) VISIBLE,
  "ZONGJINGLI" NVARCHAR2(360) VISIBLE,
  "HR_ORG_CODE" NVARCHAR2(30) VISIBLE,
  "CUNC_ORG_SCOPE" NVARCHAR2(50) VISIBLE,
  "OUCLASS_CODE" NVARCHAR2(150) VISIBLE,
  "OUCLASS_NAME" NVARCHAR2(150) VISIBLE,
  "PARENT_TRAIN_CODE" NVARCHAR2(150) VISIBLE,
  "ORG_EMAIL" NVARCHAR2(50) VISIBLE,
  "ORG_CODE" NVARCHAR2(150) VISIBLE,
  "SPECIALITY_CODE" NVARCHAR2(150) VISIBLE,
  "ORG_START_DATE" DATE VISIBLE,
  "ORG_END_DATE" DATE VISIBLE,
  "POSTALCODE" NVARCHAR2(30) VISIBLE,
  "TELEPHONENUMBER" NVARCHAR2(60) VISIBLE,
  "FACSIMILETELEPHONENUMBER" NVARCHAR2(60) VISIBLE,
  "BIG_ORG_CODE" NVARCHAR2(150) VISIBLE,
  "HZ_YES_NO" NVARCHAR2(10) VISIBLE,
  "COUNTRY" NVARCHAR2(150) VISIBLE,
  "CITY" NVARCHAR2(150) VISIBLE,
  "ADDRESS" NVARCHAR2(150) VISIBLE,
  "RESERVED_1" NVARCHAR2(240) VISIBLE,
  "RESERVED_3" NVARCHAR2(240) VISIBLE,
  "RESERVED_4" NVARCHAR2(240) VISIBLE,
  "RESERVED_5" NVARCHAR2(240) VISIBLE,
  "RESERVED_6" NVARCHAR2(240) VISIBLE,
  "RESERVED_7" NVARCHAR2(240) VISIBLE,
  "RESERVED_8" NVARCHAR2(240) VISIBLE,
  "RESERVED_9" NVARCHAR2(240) VISIBLE,
  "RESERVED_10" NVARCHAR2(240) VISIBLE,
  "RESERVED_11" NVARCHAR2(240) VISIBLE,
  "RESERVED_12" NVARCHAR2(240) VISIBLE,
  "RESERVED_13" NVARCHAR2(240) VISIBLE,
  "RESERVED_14" NVARCHAR2(240) VISIBLE,
  "RESERVED_15" NVARCHAR2(240) VISIBLE,
  "FLEX_VALUE" NVARCHAR2(150) VISIBLE,
  "MANAGER_CODE" NVARCHAR2(150) VISIBLE,
  "IS_SINGLEOU" NUMBER(11,0) VISIBLE,
  "IS_COMPANY" NUMBER(11,0) VISIBLE,
  "ZHYLEADER" NVARCHAR2(160) VISIBLE,
  "LAST_UPDATE_DATE" DATE VISIBLE,
  "PROVINCE_OU" NVARCHAR2(30) VISIBLE,
  "COMPANY_OU" NVARCHAR2(30) VISIBLE,
  "PARTYBRANCH" NVARCHAR2(160) VISIBLE,
  "IS_DEPT" NUMBER(11,0) VISIBLE,
  "HO_LEADER" NVARCHAR2(160) VISIBLE,
  "HO_SUBLEADER" NVARCHAR2(160) VISIBLE,
  "LASTUPDATETIME" DATE VISIBLE NOT NULL
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."OU" IS '根据门户现状，组织编码位数不规则，部分组织编码沿用HR的5位数字';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."NAME" IS '中文全称(如：中国联通XX分公司综合部)';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."BUSINESSCATEGORY" IS '组织类型（Org：表示公司;Dept：表示部门）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."CUMANAGERNUMBER" IS '主管领导(主管领导的员工号，有2种特殊情况,如该机构无主管领导，则填0)';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."DISPLAYNAME" IS '中文简称(如综合部)';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."CUSUPERVISORYDEPARTMENT" IS '上级单位的编码(总部和省分公司该字段固定为0000)';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."COSUNIT_COSTID" IS '组织所对应的成本中心id';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."DN" IS '目录条目,比如判断是否为总部用户可以根据DN来判断比较简单';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."CUORDER" IS '数字，只显示当前公司或部门内的排序，排序以升序为准，即数字大者排后';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."SITE" IS '省份简称,如hq，sd';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."BRANCHLEADER" IS '部门分管领导,uid,逗号分割';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ALLLEADER" IS '部门所有领导，uid,逗号分割';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ZHDB" IS '部门综合代表';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."MANAGER" IS '暂不用';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ISQUXIAN" IS '是否区县公司. 1:区县公司  0:非区县公司';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."IS_DEL" IS '否是删除 0:正常 1：删除 2 - 暂时冻结';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."DEFAULTENTERSHOPCODE" IS '该组织的默认进入商店的商店编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ROOMLEADER" IS '处室领导';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ZONGJINGLI" IS '公司总经理';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."HR_ORG_CODE" IS 'HR公司或部门编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."CUNC_ORG_SCOPE" IS '组织范围 ';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."OUCLASS_CODE" IS '组织层级编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."OUCLASS_NAME" IS '组织层级名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."PARENT_TRAIN_CODE" IS '上级业务指导部门编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ORG_EMAIL" IS '电子邮件';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ORG_CODE" IS '公司段';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."SPECIALITY_CODE" IS '专业段代码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ORG_START_DATE" IS '生效日期';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ORG_END_DATE" IS '失效日期';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."POSTALCODE" IS '公司或部门邮政编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."TELEPHONENUMBER" IS '公司或部门办公电话';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."FACSIMILETELEPHONENUMBER" IS '公司或部门传真号码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."BIG_ORG_CODE" IS '所属组织编号';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."HZ_YES_NO" IS '是否在通讯录中显示';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."COUNTRY" IS '国家（地区）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."CITY" IS '地市';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ADDRESS" IS '地址行';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_1" IS '部门/渠道级别';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_3" IS '部门/组织管理者名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_4" IS '归属区县名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_5" IS '归属区域名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_6" IS '所属组织名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_7" IS 'MDM编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_8" IS '预留字段8';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_9" IS '预留字段9';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_10" IS '预留字段10';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_11" IS '预留字段11';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_12" IS '预留字段12';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_13" IS '预留字段13';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_14" IS '预留字段14';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."RESERVED_15" IS '预留字段15';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."FLEX_VALUE" IS '云门户同步_成本中心代码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."MANAGER_CODE" IS '云门户同步_组织管理者';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."IS_SINGLEOU" IS '是否作为独立公司--1：是 0：否';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."IS_COMPANY" IS '是否市公司 0：否  1：是';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."ZHYLEADER" IS '部门秘书，uid,逗号分割';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."LAST_UPDATE_DATE" IS '最后更新时间';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."PROVINCE_OU" IS '省级OU';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."COMPANY_OU" IS '市级OU';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."PARTYBRANCH" IS '部门党总支书记';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."IS_DEPT" IS '暂表示是否为党群工作部部门一级';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."HO_LEADER" IS '国际公司需求部门领导（正职）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALORG"."HO_SUBLEADER" IS '国际公司需求部门领导（副职）';

-- ----------------------------
-- Records of CLOUD_PORTALORG
-- ----------------------------

-- ----------------------------
-- Table structure for CLOUD_PORTALUSER
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_PORTALUSER";
CREATE TABLE "C##CLOUD"."CLOUD_PORTALUSER" (
  "USERID" NVARCHAR2(128) VISIBLE NOT NULL,
  "PASSWORD" NVARCHAR2(64) VISIBLE,
  "PORTAL_UID" NVARCHAR2(32) VISIBLE,
  "EMPLOYEENUMBER" NVARCHAR2(50) VISIBLE,
  "CN" NVARCHAR2(50) VISIBLE,
  "CUMAIL" NVARCHAR2(100) VISIBLE,
  "DISPLAYNAME" NVARCHAR2(50) VISIBLE,
  "OU" NVARCHAR2(50) VISIBLE,
  "ORGFULLNAME" NVARCHAR2(150) VISIBLE,
  "TELEPHONENUMBER" NVARCHAR2(50) VISIBLE,
  "MOBILE" NVARCHAR2(50) VISIBLE,
  "MANAGERNAME" NVARCHAR2(50) VISIBLE,
  "CUMANAGERNUMBER" NVARCHAR2(50) VISIBLE,
  "CUORDER" NVARCHAR2(150) VISIBLE,
  "SITE" NVARCHAR2(32) VISIBLE,
  "TONGJI" NUMBER(11,0) VISIBLE,
  "BIJIA" NUMBER(11,0) VISIBLE,
  "IS_DEL" NUMBER(11,0) VISIBLE,
  "CUCOMPANYNUMBER" NVARCHAR2(50) VISIBLE,
  "CUCOMPANYNUMBERTRAVEL" NVARCHAR2(50) VISIBLE,
  "TRAVEL_LEVEL" NVARCHAR2(8) VISIBLE,
  "LAST_UPDATE_DATE" DATE VISIBLE,
  "REAL_UID" NVARCHAR2(128) VISIBLE,
  "ISMASTER" NUMBER(11,0) VISIBLE,
  "LASTUPDATETIME" DATE VISIBLE NOT NULL,
  "TENANT_ID" NVARCHAR2(36) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."USERID" IS '门户全国目录中的唯一编码，包括9位、7位及统一邮件前缀等类型';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."PASSWORD" IS '使用密码，和portalUser无关';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."PORTAL_UID" IS 'portal的实际uid，不一定为统一邮件前缀';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."EMPLOYEENUMBER" IS 'HR员工编码（非正式用户无该信息）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."CN" IS '中文姓名';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."CUMAIL" IS '统一邮件';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."DISPLAYNAME" IS '般情况下，等于CN字段值，如部门内有重名的情况，可以以特殊名称标明，例如：张明（大），张明（小）等';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."OU" IS '部门编码,根据门户现状，位数不规则，部分沿用HR原编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."ORGFULLNAME" IS '正式用户所属部门名称为长名称，包括公司、部门、处室信息，如中国联通总部管理信息系统部规划应用处，非正式用户则仅包括所属组织信息，如规划应用处';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."TELEPHONENUMBER" IS '用户办公电话';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."MOBILE" IS '手机号码（用于接收短信提醒）等';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."MANAGERNAME" IS '部门领导姓名';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."CUMANAGERNUMBER" IS '部门领导的员工编号';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."CUORDER" IS '三位数字，只定义部门内用户显示排序，部门以外不通过该属性定义。排序以升序为准，即数字大者排后。';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."SITE" IS '省份简称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."TONGJI" IS '统计功能，0：无，1：有';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."BIJIA" IS '价比功能，0：无，1：有';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."CUCOMPANYNUMBER" IS '人员所属分公司编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."CUCOMPANYNUMBERTRAVEL" IS '商旅用二级组织维护';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."TRAVEL_LEVEL" IS '差旅等级';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."LAST_UPDATE_DATE" IS '最后更新时间';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."REAL_UID" IS '主岗uid，兼容兼职添加，当为兼职时此值为主岗uid';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."ISMASTER" IS '是否主岗，默认0 主岗 1兼职';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER"."TENANT_ID" IS '租户id';
COMMENT ON TABLE "C##CLOUD"."CLOUD_PORTALUSER" IS '门户用户表';

-- ----------------------------
-- Records of CLOUD_PORTALUSER
-- ----------------------------
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('10010', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '10010', '000000', '网上营业厅', '网上营业厅', '网上营业厅', '00100066144', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '001000', '001000', 'X', NULL, '10010', '0', TO_DATE('2021-01-15 06:18:55', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('10011', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '10011', '000000', '网上营业厅', '网上营业厅', '网上营业厅', '00100066144', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '001000', '001000', 'X', NULL, '10010', '0', TO_DATE('2021-01-15 06:18:55', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('10012', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '10011', '000000', '网上营业厅', '网上营业厅', '网上营业厅', '00100066144', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '001000', '001000', 'X', NULL, '10010', '0', TO_DATE('2021-01-15 06:18:55', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('2009zc', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '2009zc', '000000', '网上营业厅', '网上营业厅', '网上营业厅', '00100058916', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '1', '001000', NULL, 'X', NULL, '2009zc', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('4006610011', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '4006610011', '90009138', '网上营业厅', '网上营业厅', '网上营业厅', '00100097158', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '001000', NULL, 'X', NULL, '4006610011', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('63999111', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '63999111', '000000', '网上营业厅', '网上营业厅', '网上营业厅', '00100005000', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '001000', NULL, 'X', TO_DATE('2020-09-11 09:44:51', 'SYYYY-MM-DD HH24:MI:SS'), '63999111', '0', TO_DATE('2020-09-11 09:44:51', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('8899', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '8899', '000000', '网上营业厅', '网上营业厅', '网上营业厅', '00100081182', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '001000', NULL, 'X', TO_DATE('2017-03-08 10:59:53', 'SYYYY-MM-DD HH24:MI:SS'), '8899', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('96133dby11', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '42hb00010', '20032919', '网上营业厅', '网上营业厅', '网上营业厅', '00420028572', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '10', 'hb', '0', '0', '0', '00420000021', NULL, 'X', TO_DATE('2019-07-08 15:01:54', 'SYYYY-MM-DD HH24:MI:SS'), '96133dby11', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('aasdf', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', 'XXLI0007', NULL, '网上营业厅', '网上营业厅', '网上营业厅', '00100005000', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '135', 'hq', '0', '0', '0', '001000', NULL, 'X', TO_DATE('2020-09-11 09:44:51', 'SYYYY-MM-DD HH24:MI:SS'), 'aasdf', '0', TO_DATE('2020-09-11 09:44:51', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('abbabdwyt1', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '650100365', '0692030', '网上营业厅', '网上营业厅', '网上营业厅', '00650070441', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '84', 'xj', '0', '0', '1', '0065', NULL, 'X', TO_DATE('2020-01-13 10:13:57', 'SYYYY-MM-DD HH24:MI:SS'), 'abbabdwyt1', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('abbhtgl_jn', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', 'abbhtgl_jn', '90000076', '网上营业厅', '网上营业厅', '网上营业厅', '32058', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'sd', '0', '0', '1', '32057', NULL, 'X', TO_DATE('2018-01-11 10:29:05', 'SYYYY-MM-DD HH24:MI:SS'), 'abbhtgl_jn', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('abblrmtl1', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', '653100185', '0693356', '网上营业厅', '网上营业厅', '网上营业厅', '00653252099', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'xj', '0', '0', '0', '006532', NULL, 'X', TO_DATE('2020-11-18 15:31:32', 'SYYYY-MM-DD HH24:MI:SS'), 'abblrmtl1', '0', TO_DATE('2020-11-18 15:31:32', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('abbysun', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '0886335', '网上营业厅', '网上营业厅', '网上营业厅', '00850014255', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '330', 'ho', '0', '0', '0', '00850066130', NULL, 'X', TO_DATE('2020-09-13 12:00:43', 'SYYYY-MM-DD HH24:MI:SS'), 'abbysun', '0', TO_DATE('2020-09-13 12:00:43', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('abdanjablt', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '0842564', '网上营业厅', '网上营业厅', '网上营业厅', '00653230900', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'xj', '0', '0', '0', '006532', NULL, 'X', TO_DATE('2020-06-23 21:35:41', 'SYYYY-MM-DD HH24:MI:SS'), 'abdanjablt', '0', TO_DATE('2020-06-23 21:35:41', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('abdanjablt_0000', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '0842564', '网上营业厅', '网上营业厅', '网上营业厅', '0000', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'hq', '0', '0', '0', '006532', NULL, 'X', TO_DATE('2020-06-23 21:35:41', 'SYYYY-MM-DD HH24:MI:SS'), 'abdanjablt', '1', TO_DATE('2020-06-23 21:35:41', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('ah-dingls', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', 'ah-dingls', '20233885', '网上营业厅', '网上营业厅', '网上营业厅', '00340507494', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'ah', '0', '0', '1', '003405', NULL, 'X', NULL, 'ah-dingls', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('ah-dingm', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', 'ah-dingm', '20246785', '网上营业厅', '网上营业厅', '网上营业厅', '00340800043', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'ah', '0', '0', '1', '003408', NULL, 'X', NULL, 'ah-dingm', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('ah-dingm3', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '20356770', '网上营业厅', '网上营业厅', '网上营业厅', '00340362968', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'ah', '0', '0', '1', '003403', NULL, 'X', TO_DATE('2020-04-09 10:44:55', 'SYYYY-MM-DD HH24:MI:SS'), 'ah-dingm3', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('ah-dingmc', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '20371957', '网上营业厅', '网上营业厅', '网上营业厅', '00341231455', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'ah', '0', '0', '1', '003412', NULL, 'X', TO_DATE('2020-04-22 02:38:18', 'SYYYY-MM-DD HH24:MI:SS'), 'ah-dingmc', '0', TO_DATE('2020-04-23 18:16:06', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('ah-dingmc3', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '50284638', '网上营业厅', '网上营业厅', '网上营业厅', '00341231455', '网上营业厅', '15633331191', '15633331191', NULL, NULL, '999', 'ah', '0', '0', '0', '003412', NULL, 'X', TO_DATE('2020-05-22 03:03:39', 'SYYYY-MM-DD HH24:MI:SS'), 'ah-dingmc3', '0', TO_DATE('2020-05-22 03:03:39', 'SYYYY-MM-DD HH24:MI:SS'), NULL);
INSERT INTO "C##CLOUD"."CLOUD_PORTALUSER" VALUES ('sysman', '$2a$10$O9RXYfhjrBwhYj.C7Yi.4.Etytfw7Qz7BJEGfWhpg7Lo4w6/o/XIK', NULL, '50359704', '系统管理员', '网上营业厅', '系统管理员', '00340996451', 'CTO办公室', '15633331191', '15633331191', NULL, NULL, '999', 'ah', '0', '0', '0', '003409', NULL, 'X', TO_DATE('2020-11-10 08:42:49', 'SYYYY-MM-DD HH24:MI:SS'), 'ah-dingmm1', '0', TO_DATE('2020-11-10 08:42:49', 'SYYYY-MM-DD HH24:MI:SS'), NULL);

-- ----------------------------
-- Table structure for CLOUD_PORTALUSER_PARTTIME
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME";
CREATE TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" (
  "PORTALID" NVARCHAR2(128) VISIBLE NOT NULL,
  "SITE_TYPE" NVARCHAR2(32) VISIBLE NOT NULL,
  "PASSWORD" NCHAR(32) VISIBLE,
  "PORTAL_UID" NVARCHAR2(128) VISIBLE,
  "EMPLOYEENUMBER" NVARCHAR2(50) VISIBLE,
  "CN" NVARCHAR2(50) VISIBLE,
  "CUMAIL" NVARCHAR2(100) VISIBLE,
  "DISPLAYNAME" NVARCHAR2(50) VISIBLE,
  "OU" NVARCHAR2(50) VISIBLE NOT NULL,
  "ORGFULLNAME" NVARCHAR2(150) VISIBLE,
  "TELEPHONENUMBER" NVARCHAR2(50) VISIBLE,
  "MOBILE" NVARCHAR2(50) VISIBLE,
  "MANAGERNAME" NVARCHAR2(50) VISIBLE,
  "CUMANAGERNUMBER" NVARCHAR2(50) VISIBLE,
  "CUORDER" NVARCHAR2(150) VISIBLE,
  "SITE" NVARCHAR2(32) VISIBLE,
  "TONGJI" NUMBER(11,0) VISIBLE,
  "BIJIA" NUMBER(11,0) VISIBLE,
  "IS_DEL" NUMBER(11,0) VISIBLE,
  "CUCOMPANYNUMBER" NVARCHAR2(50) VISIBLE,
  "CUCOMPANYNUMBERTRAVEL" NVARCHAR2(50) VISIBLE,
  "TRAVEL_LEVEL" NVARCHAR2(8) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."SITE_TYPE" IS '1代表主岗类；2代表借调类；3代表兼职类；';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."PASSWORD" IS '商城所使用密码，和portalUser无关';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."EMPLOYEENUMBER" IS 'HR员工编码（非正式用户无该信息）';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."CN" IS '中文姓名';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."CUMAIL" IS '统一邮件';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."DISPLAYNAME" IS '般情况下，等于CN字段值，如部门内有重名的情况，可以以特殊名称标明，例如：张明（大），张明（小）等';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."OU" IS '部门编码,根据门户现状，位数不规则，部分沿用HR原编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."ORGFULLNAME" IS '正式用户所属部门名称为长名称，包括公司、部门、处室信息，如中国联通总部管理信息系统部规划应用处，非正式用户则仅包括所属组织信息，如规划应用处';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."TELEPHONENUMBER" IS '用户办公电话';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."MOBILE" IS '手机号码（用于接收短信提醒）等';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."MANAGERNAME" IS '部门领导姓名';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."CUMANAGERNUMBER" IS '部门领导的员工编号';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."CUORDER" IS '三位数字，只定义部门内用户显示排序，部门以外不通过该属性定义。排序以升序为准，即数字大者排后。';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."SITE" IS '省份简称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."TONGJI" IS '统计功能，0：无，1：有';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."BIJIA" IS '价比功能，0：无，1：有';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."CUCOMPANYNUMBER" IS '人员所属分公司编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."CUCOMPANYNUMBERTRAVEL" IS '商旅用二级组织维护';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME"."TRAVEL_LEVEL" IS '差旅等级';

-- ----------------------------
-- Records of CLOUD_PORTALUSER_PARTTIME
-- ----------------------------

-- ----------------------------
-- Table structure for CLOUD_ROLE
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_ROLE";
CREATE TABLE "C##CLOUD"."CLOUD_ROLE" (
  "ROLE_ID" NVARCHAR2(20) VISIBLE NOT NULL,
  "TENANT_ID" NVARCHAR2(20) VISIBLE,
  "ROLE_SCOPE" NVARCHAR2(20) VISIBLE,
  "ROLE_PROVINCE" NVARCHAR2(50) VISIBLE,
  "ROLE_CITY" NVARCHAR2(50) VISIBLE,
  "ROLE_OU" NVARCHAR2(50) VISIBLE,
  "ROLE_PUBLIC" NVARCHAR2(10) VISIBLE,
  "ROLE_NAME" NVARCHAR2(50) VISIBLE,
  "ROLE_DESC" NVARCHAR2(200) VISIBLE,
  "ROLE_CODE" NVARCHAR2(20) VISIBLE,
  "ROLE_STATUS" NVARCHAR2(10) VISIBLE,
  "CREATE_BY" NVARCHAR2(30) VISIBLE,
  "CREATE_DATE" DATE VISIBLE,
  "UPDATE_BY" NVARCHAR2(30) VISIBLE,
  "UPDATE_DATE" DATE VISIBLE,
  "DELETE_FLAG" NVARCHAR2(10) VISIBLE,
  "VERSIONS" NUMBER(11,0) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_ID" IS '主键';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."TENANT_ID" IS '租户ID';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_SCOPE" IS '角色范围';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_PROVINCE" IS '所属省分';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_CITY" IS '所属市分';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_OU" IS '角色所属ou';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_PUBLIC" IS '是否公共角色(yes,no)';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_NAME" IS '角色名称';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_DESC" IS '角色描述';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_CODE" IS '角色编码';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."ROLE_STATUS" IS '状态';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."CREATE_DATE" IS '创建时间';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."UPDATE_BY" IS '编辑人';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."UPDATE_DATE" IS '编辑时间';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."DELETE_FLAG" IS '逻辑删除位';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE"."VERSIONS" IS '乐观锁标记位';
COMMENT ON TABLE "C##CLOUD"."CLOUD_ROLE" IS '角色表';

-- ----------------------------
-- Records of CLOUD_ROLE
-- ----------------------------
INSERT INTO "C##CLOUD"."CLOUD_ROLE" VALUES ('1', NULL, NULL, NULL, NULL, NULL, NULL, '门户2', NULL, '002', NULL, NULL, TO_DATE('2021-06-17 15:12:42', 'SYYYY-MM-DD HH24:MI:SS'), NULL, TO_DATE('2021-06-17 15:18:03', 'SYYYY-MM-DD HH24:MI:SS'), 'normal', '1');
INSERT INTO "C##CLOUD"."CLOUD_ROLE" VALUES ('3', NULL, NULL, NULL, NULL, '3', '3', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'normal', '1');
INSERT INTO "C##CLOUD"."CLOUD_ROLE" VALUES ('role01', '1', NULL, NULL, NULL, '001', NULL, '角色', '角色描述', 'roleCode001', NULL, NULL, NULL, NULL, NULL, 'normal', '1');

-- ----------------------------
-- Table structure for CLOUD_ROLE_MENU
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_ROLE_MENU";
CREATE TABLE "C##CLOUD"."CLOUD_ROLE_MENU" (
  "ROLE_MENU_ID" NVARCHAR2(20) VISIBLE NOT NULL,
  "ROLE_ID" NVARCHAR2(20) VISIBLE,
  "MENU_ID" NVARCHAR2(20) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE_MENU"."ROLE_MENU_ID" IS 'ID';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE_MENU"."ROLE_ID" IS '角色ID';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_ROLE_MENU"."MENU_ID" IS '菜单ID';
COMMENT ON TABLE "C##CLOUD"."CLOUD_ROLE_MENU" IS '角色-菜单关联表';

-- ----------------------------
-- Records of CLOUD_ROLE_MENU
-- ----------------------------
INSERT INTO "C##CLOUD"."CLOUD_ROLE_MENU" VALUES ('rmid02', 'role01', 'menu0101');
INSERT INTO "C##CLOUD"."CLOUD_ROLE_MENU" VALUES ('rmid03', 'role01', 'menu02');
INSERT INTO "C##CLOUD"."CLOUD_ROLE_MENU" VALUES ('rmid04', 'role01', 'menu0102');
INSERT INTO "C##CLOUD"."CLOUD_ROLE_MENU" VALUES ('rmid05', 'role01', 'menu0103');
INSERT INTO "C##CLOUD"."CLOUD_ROLE_MENU" VALUES ('rmid06', 'role01', 'menu0104');
INSERT INTO "C##CLOUD"."CLOUD_ROLE_MENU" VALUES ('rmid01', 'role02', 'menu01');

-- ----------------------------
-- Table structure for CLOUD_USER_ROLE
-- ----------------------------
DROP TABLE "C##CLOUD"."CLOUD_USER_ROLE";
CREATE TABLE "C##CLOUD"."CLOUD_USER_ROLE" (
  "USER_ROLE_ID" NVARCHAR2(20) VISIBLE NOT NULL,
  "USER_ID" NVARCHAR2(20) VISIBLE,
  "ROLE_ID" NVARCHAR2(20) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."CLOUD_USER_ROLE"."USER_ROLE_ID" IS '主键';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_USER_ROLE"."USER_ID" IS '人员ID';
COMMENT ON COLUMN "C##CLOUD"."CLOUD_USER_ROLE"."ROLE_ID" IS '角色ID';
COMMENT ON TABLE "C##CLOUD"."CLOUD_USER_ROLE" IS '用户-角色关联表';

-- ----------------------------
-- Records of CLOUD_USER_ROLE
-- ----------------------------
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('001', '10010', 'role0101');
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('002', '10010', 'role01');
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('1405800352934150146', '10010', 'role01');
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('sysman', 'sysman', 'role01');
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('urid01', '10010', 'role01');
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('7', NULL, NULL);
INSERT INTO "C##CLOUD"."CLOUD_USER_ROLE" VALUES ('1407994464613552130', '10010', 'role01');

-- ----------------------------
-- Table structure for OAUTH_CLIENT_INFO
-- ----------------------------
DROP TABLE "C##CLOUD"."OAUTH_CLIENT_INFO";
CREATE TABLE "C##CLOUD"."OAUTH_CLIENT_INFO" (
  "CLIENT_ID" NVARCHAR2(20) VISIBLE NOT NULL,
  "TENANT_ID" NVARCHAR2(20) VISIBLE,
  "CLIENT_APP" NVARCHAR2(30) VISIBLE,
  "CLIENT_KEY" NVARCHAR2(32) VISIBLE,
  "CLIENT_SECRET" NVARCHAR2(255) VISIBLE,
  "CLIENT_PASS" NVARCHAR2(255) VISIBLE,
  "CLIENT_UNIT" NVARCHAR2(50) VISIBLE,
  "CLIENT_CONTACT" NVARCHAR2(20) VISIBLE,
  "CLIENT_TEL" NVARCHAR2(20) VISIBLE,
  "CLIENT_EMAIL" NVARCHAR2(40) VISIBLE,
  "CLIENT_URL" NVARCHAR2(255) VISIBLE,
  "CLIENT_REDIRECT" NVARCHAR2(255) VISIBLE,
  "CLIENT_STATUS" NVARCHAR2(10) VISIBLE,
  "CLIENT_EXPIRATION" DATE VISIBLE,
  "CLIENT_DESCRIPTION" NVARCHAR2(255) VISIBLE,
  "CLIENT_TOKEN_VALIDITY" NUMBER(11,0) VISIBLE,
  "CLIENT_REFRESH_VALIDITY" NUMBER(11,0) VISIBLE,
  "CLIENT_REMARK" NVARCHAR2(255) VISIBLE,
  "CREATE_BY" NVARCHAR2(30) VISIBLE,
  "CREATE_DATE" DATE VISIBLE,
  "UPDATE_BY" NVARCHAR2(30) VISIBLE,
  "UPDATE_DATE" DATE VISIBLE,
  "DELETE_FLAG" NVARCHAR2(10) VISIBLE,
  "VERSIONS" NUMBER(11,0) VISIBLE,
  "ATTRA" NVARCHAR2(255) VISIBLE,
  "ATTRB" NVARCHAR2(255) VISIBLE,
  "ATTRC" NVARCHAR2(255) VISIBLE,
  "ATTRD" NVARCHAR2(255) VISIBLE,
  "ATTRE" NVARCHAR2(255) VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_ID" IS '主键';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."TENANT_ID" IS '租户ID';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_APP" IS '接入系统名';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_KEY" IS '接入系统的Key';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_SECRET" IS '接入系统的密码';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_PASS" IS '密码';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_UNIT" IS '系统负责单位';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_CONTACT" IS '联系人';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_TEL" IS '联系人电话';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_EMAIL" IS '联系人邮箱';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_URL" IS '系统访问地址';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_REDIRECT" IS '系统回调地址';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_STATUS" IS '状态：valid-有效,invalid-无效';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_EXPIRATION" IS '有效期';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_DESCRIPTION" IS '描述';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_TOKEN_VALIDITY" IS '令牌有效期（秒）';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_REFRESH_VALIDITY" IS '刷新令牌有效期（秒）';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CLIENT_REMARK" IS '备注';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."CREATE_DATE" IS '创建时间';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."UPDATE_BY" IS '编辑人';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."UPDATE_DATE" IS '编辑时间';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."DELETE_FLAG" IS '逻辑删除标记';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."VERSIONS" IS '乐观锁标记';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."ATTRA" IS '备用字段a';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."ATTRB" IS '备用字段b';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."ATTRC" IS '备用字段c';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."ATTRD" IS '备用字段d';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CLIENT_INFO"."ATTRE" IS '备用字段e';

-- ----------------------------
-- Records of OAUTH_CLIENT_INFO
-- ----------------------------
INSERT INTO "C##CLOUD"."OAUTH_CLIENT_INFO" VALUES ('1328604431436070913', 'unicom', '统一认证中心', 'bVS46ElU', '58ea04ba02475c8da2321cc99849d2a10f15b749', '$2a$10$j6Mipo7uuJc62Kj54R0O1.3Q1RYZFYwCFjPkVeUwtBb8xx1W1ZOle', 'string', 'string', 'string', 'string', 'http://localhost:8100/purchase', '#', 'valid', NULL, 'string', NULL, NULL, 'string', NULL, TO_DATE('2020-11-17 15:42:35', 'SYYYY-MM-DD HH24:MI:SS'), NULL, TO_DATE('2020-11-17 15:42:35', 'SYYYY-MM-DD HH24:MI:SS'), 'normal', '0', 'string', 'string', 'string', 'string', 'string');

-- ----------------------------
-- Table structure for OAUTH_CODE
-- ----------------------------
DROP TABLE "C##CLOUD"."OAUTH_CODE";
CREATE TABLE "C##CLOUD"."OAUTH_CODE" (
  "CODE" NVARCHAR2(16) VISIBLE,
  "AUTHENTICATION" BLOB VISIBLE
)
TABLESPACE "USERS"
LOGGING
NOCOMPRESS
PCTFREE 10
INITRANS 1
STORAGE (
  BUFFER_POOL DEFAULT
)
PARALLEL 1
NOCACHE
DISABLE ROW MOVEMENT
;
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CODE"."CODE" IS '授权码';
COMMENT ON COLUMN "C##CLOUD"."OAUTH_CODE"."AUTHENTICATION" IS '认证信息';

-- ----------------------------
-- Records of OAUTH_CODE
-- ----------------------------

-- ----------------------------
-- Checks structure for table CLOUD_MENU
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_MENU" ADD CONSTRAINT "SYS_C007301" CHECK (menu_id  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_MENU" ADD CONSTRAINT "SYS_C007302" CHECK (menu_name  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_MENU" ADD CONSTRAINT "SYS_C007346" CHECK ("MENU_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_MENU" ADD CONSTRAINT "SYS_C007347" CHECK ("MENU_NAME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table CLOUD_PORTALORG
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_PORTALORG" ADD CONSTRAINT "SYS_C007303" CHECK (ou  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALORG" ADD CONSTRAINT "SYS_C007304" CHECK (businesscategory  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALORG" ADD CONSTRAINT "SYS_C007305" CHECK (lastUpdateTime  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALORG" ADD CONSTRAINT "SYS_C007348" CHECK ("OU" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALORG" ADD CONSTRAINT "SYS_C007349" CHECK ("BUSINESSCATEGORY" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALORG" ADD CONSTRAINT "SYS_C007350" CHECK ("LASTUPDATETIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table CLOUD_PORTALUSER
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER" ADD CONSTRAINT "SYS_C007376" PRIMARY KEY ("USERID");

-- ----------------------------
-- Checks structure for table CLOUD_PORTALUSER
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER" ADD CONSTRAINT "SYS_C007320" CHECK ("USERID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER" ADD CONSTRAINT "SYS_C007321" CHECK (LASTUPDATETIME  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER" ADD CONSTRAINT "SYS_C007352" CHECK ("LASTUPDATETIME" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table CLOUD_PORTALUSER_PARTTIME
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007314" PRIMARY KEY ("PORTALID", "SITE_TYPE", "OU");

-- ----------------------------
-- Checks structure for table CLOUD_PORTALUSER_PARTTIME
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007306" CHECK (portalid  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007307" CHECK (site_type  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007308" CHECK (ou  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007353" CHECK ("PORTALID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007354" CHECK ("SITE_TYPE" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ADD CONSTRAINT "SYS_C007355" CHECK ("OU" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table CLOUD_PORTALUSER_PARTTIME
-- ----------------------------
CREATE INDEX "C##CLOUD"."departmentnumber"
  ON "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ("ORGFULLNAME" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "C##CLOUD"."index_user_cn"
  ON "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ("CN" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
CREATE INDEX "C##CLOUD"."ou"
  ON "C##CLOUD"."CLOUD_PORTALUSER_PARTTIME" ("OU" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;

-- ----------------------------
-- Checks structure for table CLOUD_ROLE
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_ROLE" ADD CONSTRAINT "SYS_C007311" CHECK (role_id  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_ROLE" ADD CONSTRAINT "SYS_C007356" CHECK ("ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table CLOUD_ROLE_MENU
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_ROLE_MENU" ADD CONSTRAINT "SYS_C007312" CHECK (role_menu_id  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_ROLE_MENU" ADD CONSTRAINT "SYS_C007357" CHECK ("ROLE_MENU_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Checks structure for table CLOUD_USER_ROLE
-- ----------------------------
ALTER TABLE "C##CLOUD"."CLOUD_USER_ROLE" ADD CONSTRAINT "SYS_C007313" CHECK (user_role_id  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."CLOUD_USER_ROLE" ADD CONSTRAINT "SYS_C007358" CHECK ("USER_ROLE_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Primary Key structure for table OAUTH_CLIENT_INFO
-- ----------------------------
ALTER TABLE "C##CLOUD"."OAUTH_CLIENT_INFO" ADD CONSTRAINT "SYS_C007300" PRIMARY KEY ("CLIENT_ID");

-- ----------------------------
-- Checks structure for table OAUTH_CLIENT_INFO
-- ----------------------------
ALTER TABLE "C##CLOUD"."OAUTH_CLIENT_INFO" ADD CONSTRAINT "SYS_C007299" CHECK (client_id  IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;
ALTER TABLE "C##CLOUD"."OAUTH_CLIENT_INFO" ADD CONSTRAINT "SYS_C007359" CHECK ("CLIENT_ID" IS NOT NULL) NOT DEFERRABLE INITIALLY IMMEDIATE NORELY VALIDATE;

-- ----------------------------
-- Indexes structure for table OAUTH_CLIENT_INFO
-- ----------------------------
CREATE UNIQUE INDEX "C##CLOUD"."idx_client_key"
  ON "C##CLOUD"."OAUTH_CLIENT_INFO" ("CLIENT_KEY" ASC)
  LOGGING
  TABLESPACE "USERS"
  VISIBLE
PCTFREE 10
INITRANS 2
STORAGE (
  INITIAL 65536 
  NEXT 1048576 
  MINEXTENTS 1
  MAXEXTENTS 2147483645
  BUFFER_POOL DEFAULT
  FLASH_CACHE DEFAULT
)
   USABLE;
