package cn.chinaunicom.sdsi.checkZhengqi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *  BO稽核结果展示
 * </p>
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@TableName("t_sanquan_adjust_daily")
public class TBoZhengqiCheckAdjustDaily implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;
    private String city;                // 地市
    private String adjustMethod;        // 调账方式
    private String adjustType;          // 调账类型,0掉增，1调减
    private String customerName;        // 客户名称
    private String customerId;          // 客户编码
    private String acctId;              // 调账账号id
    private String userId;              // 调账用户id
    private String deviceNumber;        // 业务号码
    private String networkType;         // 网别
    private String productName;         // 产品名称
    private String productId;           // 产品编码
    private String adjustFee;           // 调账金额（元）
    private String cycleId;             // 调账账期
    private String adjustmentDate;      // 调账日期
    private String requester;           // 调账需求发起人
    private String phone;               // 电话
    private String approvalNo;          // 调账依据（审批流程单号）
    private String revenueOwner;        // 收入归属单位/归属人
    private String auditStatus;         // 稽核(0、否，1、是)
    private String auditId;             // 调整稽核人工号
    private String auditName;           // 调整稽核人工号名称
    private String auditDate;           // 调整稽核人时间
    private String auditManager;        // 指派的稽核负责人
    private String auditManagerId;      // 指派的稽核负责人工号
    private String auditManagerTel;     // 指派的稽核负责人电话
    private String auditResult;         // 本期结合结果反馈  , 1, 已核实，无问题 2,已核实问题，已整改  3 已核实问题，未完成整改  （是否在工单表）
    private String auditResultDate;     // 稽核反馈日期
    private String auditOther;          // 其他
    private String isLarge;             // 是否大额（0、否，1、是）
    private String monthId;             // 月份
    private String dayId;               // 天
    private String status;               // 派单状态，0，未派单，1已派单
    private String sendTime;               // 派单时间

    @TableField(exist = false)
    private String updateType;

}
