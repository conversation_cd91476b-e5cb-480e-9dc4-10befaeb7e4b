package cn.chinaunicom.sdsi.plugins.office.excel.entity;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/8/16 19:55
 */
@Data
@AllArgsConstructor
public class DataParseResult {

    public DataParseResult(String field) {
        this.field = field;
    }

    // title标题
    private String title;

    // 字体查询出来的字段
    private String field;

    //列的排序
    private int cellShort;

    //列的宽度
    private int width;

    // 列的高度
    private int height;

    // 第一行的title
    private DataParseCell firstRowCell;

    // 第二行的title
    private DataParseCell secRowCell;

    // title 合并多少行
    private int rowSpan;

    //过滤sql
    private String sqlFilter;

    //自己children的值
    private boolean children;


}
