package cn.chinaunicom.sdsi.dict.commons.controller;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictIndustryProduct;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictIndustryProductQueryVo;
import cn.chinaunicom.sdsi.dict.commons.service.TSanquanDictIndustryProductService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;
import cn.chinaunicom.sdsi.framework.base.BaseController;
/**
 * <p>
 * 行业与应用名称对应表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@RestController
@RequestMapping("/dict/commons/industryProduct")
public class TSanquanDictIndustryProductController extends BaseController {

    @Autowired
    private TSanquanDictIndustryProductService tSanquanDictIndustryProductService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-04-16
     * @param tSanquanDictIndustryProductVO
     * @return BasePageResponse<TSanquanDictIndustryProduct>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDictIndustryProduct> findPage(TSanquanDictIndustryProductQueryVo tSanquanDictIndustryProductVo){
        return pageOk(tSanquanDictIndustryProductService.findPage(tSanquanDictIndustryProductVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-04-16
     * @param id
     * @return BaseResponse<TSanquanDictIndustryProduct>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanDictIndustryProduct> findOne(String id) {
        return ok(tSanquanDictIndustryProductService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-04-16
     * @return BaseResponse<List<TSanquanDictIndustryProduct>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDictIndustryProduct>> findList(TSanquanDictIndustryProductQueryVo tSanquanDictIndustryProductVo) {
        return ok(tSanquanDictIndustryProductService.findList(tSanquanDictIndustryProductVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-04-16
     * @param tSanquanDictIndustryProduct
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanDictIndustryProduct tSanquanDictIndustryProduct){
        return ok(tSanquanDictIndustryProductService.add(tSanquanDictIndustryProduct));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-04-16
     * @param tSanquanDictIndustryProduct
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanDictIndustryProduct tSanquanDictIndustryProduct) {
        return ok(tSanquanDictIndustryProductService.update(tSanquanDictIndustryProduct));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-04-16
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanDictIndustryProductService.delete(id));
    }

    /**
     * dict行业查询
     * @return
     */
    @GetMapping("/findIndustry")
    public BaseResponse<List< Map<String, String> >> findIndustry() {
        return ok(tSanquanDictIndustryProductService.findIndustry());
    }
}
