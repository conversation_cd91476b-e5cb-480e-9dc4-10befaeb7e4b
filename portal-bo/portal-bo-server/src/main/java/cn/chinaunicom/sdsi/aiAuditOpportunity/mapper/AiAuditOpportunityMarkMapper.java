package cn.chinaunicom.sdsi.aiAuditOpportunity.mapper;

import cn.chinaunicom.sdsi.aiAuditOpportunity.entity.AiAuditOpportunity;
import cn.chinaunicom.sdsi.aiAuditOpportunity.entity.AiAuditOpportunityMark;
import cn.chinaunicom.sdsi.aiAuditOpportunity.vo.AiAuditOpportunityQueryVO;
import cn.chinaunicom.sdsi.aiAuditOpportunity.vo.AiAuditOpportunityVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机池实体类
 */
@Mapper
public interface AiAuditOpportunityMarkMapper extends BaseMapper<AiAuditOpportunity> {
    /**
     * 分页查询商机（手动分页）
     * @return 分页结果
     */
    List<AiAuditOpportunityMark> selectPageListManual(@Param("endDate") String endDate, @Param("size") Integer size, @Param("group") Integer group);


    int updateBatch(List<AiAuditOpportunityMark> list);

    IPage<AiAuditOpportunityVO> findPage(@Param("page") IPage page, @Param("vo") AiAuditOpportunityQueryVO vo);

    void syncOpportunities(@Param("monthId") String monthId, @Param("dayId") String dayId);

    AiAuditOpportunityVO details(@Param("id") String id);
}