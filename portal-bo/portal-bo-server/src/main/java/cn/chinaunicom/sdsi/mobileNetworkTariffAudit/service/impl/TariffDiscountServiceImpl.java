package cn.chinaunicom.sdsi.mobileNetworkTariffAudit.service.impl;

import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity.TariffDiscount;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity.UserAuditDetails;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.mapper.TariffDiscountMapper;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.mapper.UserAuditDetailsMapper;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.service.TariffDiscountService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class TariffDiscountServiceImpl extends ServiceImpl<TariffDiscountMapper, TariffDiscount> implements TariffDiscountService {



    @Override
    public TariffDiscount getProvinceHeadData(String monthId) {
        return lambdaQuery()
                .eq(TariffDiscount::getAreaId, "000")
                .eq(TariffDiscount::getMonthId, monthId)
                .one();


    }

    /**
     * 用于首页展示的图表数据 （各地市）
     * 包括：地市信息、当月ARPU(出账收入/用户数)、当月整体折扣率(出账/标准资费)
     * @param monthId
     * @return
     */
    @Override
    public List<TariffDiscount> findSimpleList(String monthId) {
        return lambdaQuery().eq(TariffDiscount::getMonthId, monthId)
                .ne(TariffDiscount::getAreaId, "000")
                .orderByAsc(TariffDiscount::getRhIndex)
                .select(
                        TariffDiscount::getAreaId,
                        TariffDiscount::getAreaName,
                        TariffDiscount::getRhIndex,
                        TariffDiscount::getArpuAll,
                        TariffDiscount::getZklAll
                ).list();
    }


}
