package cn.chinaunicom.sdsi;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;

/**
 * 用户中心 - 启动类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/11/20
 * @description 1、2021/12/27 删除了 bean 的声明,因为这个 bean 定义在监控组件内;
 */
@EnableDiscoveryClient
@EnableWebSecurity
@EnableCaching
@EnableScheduling
@EnableGlobalMethodSecurity(prePostEnabled = true, jsr250Enabled = true, securedEnabled = true)
@ComponentScan(basePackages = {"cn.chinaunicom"})
@EnableFeignClients(basePackages = "cn.chinaunicom.sdsi")
@SpringBootApplication(scanBasePackages = {"cn.chinaunicom"})
@EnableAspectJAutoProxy(proxyTargetClass=true, exposeProxy=true)
public class SanquanBoApplication {
    public static void main(String[] args) {
        ConfigurableApplicationContext ctx = SpringApplication.run(SanquanBoApplication.class, args);
    }
}
