package cn.chinaunicom.sdsi.dbcp.controller;

import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpSignTjDsQueryVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.dbcp.service.TSanquanDbcpSignTjDsService;
import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignTjDs;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * DS等保测评新签约情况统计（地市） 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@RestController
@RequestMapping("/dbcp/signTjDs")
public class TSanquanDbcpSignTjDsController extends BaseController {

    @Autowired
    private TSanquanDbcpSignTjDsService tSanquanDbcpSignTjDsService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-03-18
     * @param tSanquanDbcpSignTjDsVO
     * @return BasePageResponse<TSanquanDbcpSignTjDs>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDbcpSignTjDs> findPage(TSanquanDbcpSignTjDsQueryVo tSanquanDbcpSignTjDsVo){
        return pageOk(tSanquanDbcpSignTjDsService.findPage(tSanquanDbcpSignTjDsVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-03-18
     * @param id
     * @return BaseResponse<TSanquanDbcpSignTjDs>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanDbcpSignTjDs> findOne(String id) {
        return ok(tSanquanDbcpSignTjDsService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-03-18
     * @return BaseResponse<List<TSanquanDbcpSignTjDs>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDbcpSignTjDs>> findList(TSanquanDbcpSignTjDsQueryVo tSanquanDbcpSignTjDsVo) {
        return ok(tSanquanDbcpSignTjDsService.findList(tSanquanDbcpSignTjDsVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-03-18
     * @param tSanquanDbcpSignTjDs
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanDbcpSignTjDs tSanquanDbcpSignTjDs){
        return ok(tSanquanDbcpSignTjDsService.add(tSanquanDbcpSignTjDs));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-03-18
     * @param tSanquanDbcpSignTjDs
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanDbcpSignTjDs tSanquanDbcpSignTjDs) {
        return ok(tSanquanDbcpSignTjDsService.update(tSanquanDbcpSignTjDs));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-03-18
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanDbcpSignTjDsService.delete(id));
    }

    /**
     * 获取账期列表
     * @return
     */
    @GetMapping("/getAcctIdList")
    public BaseResponse<List<Map<String,String>>> getAcctIdList(){
        return ok(tSanquanDbcpSignTjDsService.getAcctIdList());
    }

    /**
     * 获取最新账期
     * @return
     */
    @GetMapping("/getMaxAcctId")
    public BaseResponse<String> getMaxAcctId(){
        return ok(tSanquanDbcpSignTjDsService.getMaxAcctId());
    }


    /**
     * 导出数据
     */
    @PostMapping("/exportData")
    @Operation(summary = "导出数据", description = "导出数据")
    public void exportData(@RequestBody TSanquanDbcpSignTjDsQueryVo tSanquanDbcpSignTjDsVo) {
        tSanquanDbcpSignTjDsService.exportData(tSanquanDbcpSignTjDsVo);
    }
}
