package cn.chinaunicom.sdsi.tokenValidate;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/02/27 9:25
 */
// 政企运营平台验证
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthTokenVaildate {

    String value() default "";

    Class<?> tokenClass() default Object.class; // 默认值为Object.class，确保必须指定类型
}
