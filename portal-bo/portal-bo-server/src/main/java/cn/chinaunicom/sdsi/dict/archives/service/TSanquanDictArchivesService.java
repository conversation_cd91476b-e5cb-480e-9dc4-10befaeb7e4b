package cn.chinaunicom.sdsi.dict.archives.service;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchives;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesQueryVo;
import cn.chinaunicom.sdsi.cloud.dict.archives.vo.TSanquanDictArchivesVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 客户DICT数字档案 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface TSanquanDictArchivesService extends IService<TSanquanDictArchives> {

    // 分页查询
    IPage<TSanquanDictArchives> findPage(TSanquanDictArchivesQueryVo tSanquanDictArchivesVo);

    // 根据id查询
    TSanquanDictArchivesVo findOne(String id);

    // 查询列表
    List<TSanquanDictArchives> findList(TSanquanDictArchivesQueryVo tSanquanDictArchivesVo);

    // 新增
    boolean addOrUpdate(TSanquanDictArchivesVo tSanquanDictArchivesVo);

    // 修改
    int update(TSanquanDictArchives tSanquanDictArchives);

    // 删除
    int delete(String id);

    // 根据客户id查询
    TSanquanDictArchivesVo getInfoByCustomerId(String customerId);

    // 导入数据
    Map<String, Object> importData(MultipartFile file);
}
