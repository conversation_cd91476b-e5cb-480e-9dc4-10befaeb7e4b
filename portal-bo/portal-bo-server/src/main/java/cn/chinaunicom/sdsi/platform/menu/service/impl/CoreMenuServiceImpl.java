package cn.chinaunicom.sdsi.platform.menu.service.impl;

import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.menu.entity.Menu;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuQuery;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuVo;
import cn.chinaunicom.sdsi.platform.menu.mapper.CoreMenuMapper;
import cn.chinaunicom.sdsi.platform.menu.service.IMenuService;
import cn.chinaunicom.sdsi.platform.role.entity.RoleQuery;
import cn.chinaunicom.sdsi.platform.role.entity.RoleVo;
import cn.chinaunicom.sdsi.platform.role.service.IRoleService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Service
public class CoreMenuServiceImpl extends ServiceImpl<CoreMenuMapper, Menu> implements IMenuService {
    private final String ROOT_PARENT_ID = "0";

    @Autowired
    private IRoleService roleService;

    @Override
    public List<MenuVo> findTreeList(MenuQuery query) {
        List<MenuVo> menuVoList = baseMapper.findList(query);
//        //找到根节点，固定parentId为0
        List<MenuVo> roots = menuVoList.stream().filter(t -> "0".equals(t.getParentId())).collect(Collectors.toList());
        menuVoList.removeAll(roots);
        roots.forEach(t -> t.setChildren(this.makeChildren(menuVoList, t.getId())));
        return roots;
    }

    @Override
    public boolean removeById(String id) {
        if (this.checkHasChildren(id)) {
            return false;
        } else {
            return super.removeById(id);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdate(Menu entity) {
        if (!ROOT_PARENT_ID.equals(entity.getParentId())) {
            entity.setParentIdSet(this.getById(entity.getParentId()).getParentIdSet() + "," + entity.getParentId());
        } else {
            entity.setParentIdSet(ROOT_PARENT_ID);
        }
        return super.saveOrUpdate(entity);
    }

    @Override
    public Boolean checkHasChildren(String parentId) {
        List<MenuVo> list = this.findList(new MenuQuery().setParentIdSet(parentId));
        if (list != null && !list.isEmpty()) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public IPage<MenuVo> findPage(MenuQuery query) {
        IPage _page = QueryVoToPageUtil.toPage(query);
        return baseMapper.findPage(_page, query);
    }

    @Override
    public List<MenuVo> findList(MenuQuery query) {
        return baseMapper.findList(query);
    }

    @Override
    public List<MenuVo> findByRoleId(String roleId) {
        return baseMapper.findByRoleId(roleId);
    }

    @Override
    public List<MenuVo> findByUserId(String userId, String menuType) {
        return baseMapper.findByUserId(userId, menuType);
    }

    @Override
    public List<MenuVo> findUserHasMenu(MenuQuery query, String userId) {
        List<MenuVo> rootList = baseMapper.findUserHasMenu(query, userId);
        List<MenuVo> baseList = rootList.stream().filter(menu -> {
            return menu.getParentId().equals("0");
        }).sorted(Comparator.comparing(MenuVo::getMenuSort)).collect(Collectors.toList());
        for (MenuVo menuVo : baseList) {
            filterMenu(menuVo, rootList);
        }
        // 回显角色名称
        List<RoleVo> listRoles = roleService.findByUserId(userId);
        baseList.forEach(base -> {
            listRoles.forEach(item -> {
               if(StringUtils.isNotEmpty(base.getRoleName())){
                   base.setRoleName(base.getRoleName() + "," + item.getRoleName());
               }else{
                   base.setRoleName(item.getRoleName());
               }
            });
        });
        return baseList;
    }

    private void filterMenu(MenuVo parentVo, List<MenuVo> rootList) {
        List<MenuVo> childrenList = rootList.stream().filter(menu -> {
            return menu.getParentId().equals(parentVo.getId());
        }).sorted(Comparator.comparing(MenuVo::getMenuSort)).collect(Collectors.toList());
        parentVo.setChildren(childrenList);
        for (MenuVo menuVo : childrenList) {
            filterMenu(menuVo, rootList);
        }
    }

    @Override
    public List<MenuVo> userHasPermission(String userId) {
        return baseMapper.userHasPermission(userId);
    }

    /**
     * 根据list和指定父ID，组建子节点集合
     *
     * @param list     List<SysPermissionVO>
     * @param parentId permissionId
     * @return List<SysPermissionVO>
     */
    private List<MenuVo> makeChildren(List<MenuVo> list, String parentId) {
        List<MenuVo> children = list.stream().filter(t -> t.getParentId().equals(parentId)).collect(Collectors.toList());
        list.removeAll(children);
        children.forEach(
                t -> t.setChildren(this.makeChildren(list, t.getId()))
        );
        return children;
    }

    /**
     * 菜单系统配置参数
     * @return
     */
    @Override
    public List<Map<String, String>> findMenusSystem() {
        return baseMapper.findMenusSystem();
    }
}
