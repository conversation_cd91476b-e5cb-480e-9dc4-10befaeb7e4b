package cn.chinaunicom.sdsi.approval.service.impl;

import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApprovalRecord;
import cn.chinaunicom.sdsi.approval.mapper.TSanquanExportApprovalRecordMapper;
import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalRecordService;
import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportApprovalRecordQueryVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * 审批记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class TSanquanExportApprovalRecordServiceImpl extends ServiceImpl<TSanquanExportApprovalRecordMapper, TSanquanExportApprovalRecord> implements TSanquanExportApprovalRecordService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportApprovalRecord
     * @return IPage<TSanquanExportApprovalRecord>
     **/
    @Override
    public IPage<TSanquanExportApprovalRecord> findPage(TSanquanExportApprovalRecordQueryVo tSanquanExportApprovalRecordVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanExportApprovalRecordVo);
        return baseMapper.findPage(page, tSanquanExportApprovalRecordVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param id
     * @return TSanquanExportApprovalRecord
     **/
    @Override
    public TSanquanExportApprovalRecord findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @return List<TSanquanExportApprovalRecord>
     **/
    @Override
    public List<TSanquanExportApprovalRecord> findList(TSanquanExportApprovalRecordQueryVo tSanquanExportApprovalRecordVo) {
        return baseMapper.findList(tSanquanExportApprovalRecordVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportApprovalRecord
     * @return int
     **/
    @Override
    public int add(TSanquanExportApprovalRecord tSanquanExportApprovalRecord) {
        return baseMapper.insert(tSanquanExportApprovalRecord);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportApprovalRecord
     * @return int
     **/
    @Override
    public int update(TSanquanExportApprovalRecord tSanquanExportApprovalRecord) {
        return baseMapper.updateById(tSanquanExportApprovalRecord);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
