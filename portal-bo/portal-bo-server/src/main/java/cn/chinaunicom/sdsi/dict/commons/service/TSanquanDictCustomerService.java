package cn.chinaunicom.sdsi.dict.commons.service;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictCustomer;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictCustomerQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
/**
 * <p>
 * 客户DICT数字档案客户 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
public interface TSanquanDictCustomerService extends IService<TSanquanDictCustomer> {

    // 分页查询
    IPage<TSanquanDictCustomer> findPage(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo);

    // 根据id查询
    TSanquanDictCustomer findOne(String id);

    // 查询列表
    List<TSanquanDictCustomer> findList(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo);

    // 新增
    int add(TSanquanDictCustomer tSanquanDictCustomer);

    // 修改
    int update(TSanquanDictCustomer tSanquanDictCustomer);

    // 删除
    int delete(String id);

}
