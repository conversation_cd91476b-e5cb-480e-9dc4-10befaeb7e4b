package cn.chinaunicom.sdsi.mobileNetworkTariffAudit.mapper;

import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity.TariffDiscount;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity.UserAuditDetails;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.vo.TariffDiscountQueryVo;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.vo.UserAuditDetailsQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *
 */
@Mapper
public interface TariffDiscountMapper extends BaseMapper<TariffDiscount> {


//    IPage<TariffDiscount> findPage(@Param("page") IPage page, @Param("vo") UserAuditDetailsQueryVo vo);

}