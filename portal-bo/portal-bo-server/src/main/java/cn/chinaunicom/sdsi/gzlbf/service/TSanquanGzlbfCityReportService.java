package cn.chinaunicom.sdsi.gzlbf.service;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityReport;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfCityReportQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
/**
 * <p>
 * 高质量拜访-地市维度高质量拜访报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public interface TSanquanGzlbfCityReportService extends IService<TSanquanGzlbfCityReport> {

    String getMaxPatch();

    // 分页查询
    IPage<TSanquanGzlbfCityReport> findPage(TSanquanGzlbfCityReportQueryVo tSanquanGzlbfHygzlbfDsbfVo);

    // 根据id查询
    TSanquanGzlbfCityReport findOne(String id);

    // 查询列表
    List<TSanquanGzlbfCityReport> findList(TSanquanGzlbfCityReportQueryVo tSanquanGzlbfHygzlbfDsbfVo);

    // 新增
    int add(TSanquanGzlbfCityReport tSanquanGzlbfHygzlbfDsbf);

    // 修改
    int update(TSanquanGzlbfCityReport tSanquanGzlbfHygzlbfDsbf);

    // 删除
    int delete(String id);

}
