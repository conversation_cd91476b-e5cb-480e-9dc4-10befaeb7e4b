package cn.chinaunicom.sdsi.gzlbf.service;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfHygzlbfCustXq;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfHygzlbfCustXqQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 高质量拜访-清单表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public interface TSanquanGzlbfHygzlbfCustXqService extends IService<TSanquanGzlbfHygzlbfCustXq> {

    // 分页查询
    IPage<TSanquanGzlbfHygzlbfCustXq> findPage(TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo);

    // 根据id查询
    TSanquanGzlbfHygzlbfCustXq findOne(String id);

    // 查询列表
    List<TSanquanGzlbfHygzlbfCustXq> findList(TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo);

    // 新增
    int add(TSanquanGzlbfHygzlbfCustXq tSanquanGzlbfHygzlbfCustXq);

    // 修改
    int update(TSanquanGzlbfHygzlbfCustXq tSanquanGzlbfHygzlbfCustXq);

    // 删除
    int delete(String id);

    // 获取批次列表
    List<Map<String,String>> getPatchList();

    // 获取最新批次
    String getMaxPatch();

    // 导出数据
    void exportData(TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo);

}
