package cn.chinaunicom.sdsi.daiban.service;

import cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrder;
import cn.chinaunicom.sdsi.cloud.daiban.query.TSanquanDaibanOrderQueryVo;
import cn.chinaunicom.sdsi.cloud.daiban.vo.TSanquanDaibanOrderVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 待办工单任务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface TSanquanDaibanOrderService extends IService<TSanquanDaibanOrder> {

    // 分页查询
    IPage<TSanquanDaibanOrder> findPage(TSanquanDaibanOrderQueryVo tSanquanDaibanOrderVo);

    // 根据id查询
    TSanquanDaibanOrder findOne(String id);

    // 查询列表
    List<TSanquanDaibanOrder> findList(TSanquanDaibanOrderQueryVo tSanquanDaibanOrderVo);

    // 新增
    int add(TSanquanDaibanOrder tSanquanDaibanOrder);

    // 修改
    int update(TSanquanDaibanOrder tSanquanDaibanOrder);

    // 删除
    int delete(String id);

    // 根据工单编号查询
    TSanquanDaibanOrder getByProcessCode(String orderCode);

    // 工单反馈处理
    Map<String, Integer> feedbackHandle(TSanquanDaibanOrderVo tSanquanDaibanOrderVo);

    // 工单转交处理
    Map<String, Integer> transferHandle(TSanquanDaibanOrderVo tSanquanDaibanOrderVo);
}
