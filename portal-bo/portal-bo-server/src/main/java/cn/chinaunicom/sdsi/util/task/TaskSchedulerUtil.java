package cn.chinaunicom.sdsi.util.task;

import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ConcurrentTaskScheduler;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class TaskSchedulerUtil {

    private static final Map<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private static final TaskScheduler taskScheduler = new ConcurrentTaskScheduler();

    /**
     * 注册或更新一个定时任务
 *              Duration.ofDays(1)     // 每天执行一次
     *          Duration.ofHours(1)     // 每小时执行一次
     *          Duration.ofMinutes(30)  // 每30分钟执行一次
     *          Duration.ofSeconds(10)  // 每10秒执行一次
     * @param taskId        任务唯一标识
     * @param timeStr       执行时间格式"HH:mm"
     * @param task          要执行的任务逻辑
     * @return 是否成功注册
     */
    public static boolean scheduleTask(String taskId, String timeStr, Runnable task, Duration duration) {
//        if (!isValidAutoDispatchTime(timeStr)) {
//            return false;
//        }
        // 取消已有任务
        cancelTask(taskId);
        // 解析时间
        LocalTime targetTime = LocalTime.parse(timeStr);
        long initialDelay = calculateInitialDelay(targetTime);
        // 创建并保存新任务
        ScheduledFuture<?> future = taskScheduler.scheduleAtFixedRate(
                task,
                Instant.now().plusSeconds(initialDelay),
                duration
        );
        scheduledTasks.put(taskId, future);
        return true;
    }

    /**
     * 初始化一批任务
     *
     * @param tasks 任务定义
     */
    public static void initScheduledTasks(Iterable<TaskDefinition> tasks) {
        for (TaskDefinition def : tasks) {
            scheduleTask(def.taskId, def.timeStr, def.task, def.duration);
        }
    }

    /**
     * 取消指定任务
     */
    public static void cancelTask(String taskId) {
        ScheduledFuture<?> future = scheduledTasks.get(taskId);
        if (future != null && !future.isCancelled()) {
            future.cancel(false);
        }
        scheduledTasks.remove(taskId);
    }

    /**
     * 清空所有任务
     */
    public static void clearAllTasks() {
        scheduledTasks.forEach((id, future) -> {
            if (!future.isCancelled()) {
                future.cancel(false);
            }
        });
        scheduledTasks.clear();
    }

    // 时间校验逻辑复用
    public static boolean isValidAutoDispatchTime(String timeStr) {
        if (timeStr == null || !timeStr.matches("\\d{2}:\\d{2}")) {
            return false;
        }
        try {
            LocalTime time = LocalTime.parse(timeStr);
            LocalTime minTime = LocalTime.of(8, 30);
            LocalTime maxTime = LocalTime.of(18, 30);
            int minute = time.getMinute();
            if (minute % 30 != 0) {
                return false;
            }
            return !time.isBefore(minTime) && !time.isAfter(maxTime);
        } catch (Exception e) {
            return false;
        }
    }

    // 计算初始延迟秒数
    private static long calculateInitialDelay(LocalTime targetTime) {
        LocalTime now = LocalTime.now();
        long secondsToday = now.toSecondOfDay();
        long secondsTarget = targetTime.toSecondOfDay();
        long delay = secondsTarget - secondsToday;
        if (delay <= 0) {
            delay += TimeUnit.DAYS.toSeconds(1); // 下一天
        }
        return delay;
    }

}