package cn.chinaunicom.sdsi.aiAuditOpportunity.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 商机池实体类
 */
@Getter
@Setter
@TableName("t_sanquan_zqzt_salesup_coll_oppo_pools_mark")
public class AiAuditOpportunityMark implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer id;

    private Integer mainId;

    private String oppoId;

    private String oppoOrderId;

    private String oppoName;

    private String custReq;

    private String oppoCreatetime;

    private String isLow;

    private String reason;

    private String updateTime;

    private String answerContent;

    private String queryTiming;
}