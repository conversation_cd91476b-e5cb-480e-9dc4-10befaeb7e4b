package cn.chinaunicom.sdsi.permissionConfig.service;

import cn.chinaunicom.sdsi.permissionConfig.entity.PermissionConfig;
import cn.chinaunicom.sdsi.permissionConfig.entity.PermissionConfigVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 系统数据权限细化粒度补充表配置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
public interface PermissionConfigService extends IService<PermissionConfig> {

    /**
     * 查询中台同步的人员表信息
     * @param permissionConfig
     * @return
     */
    List<PermissionConfig> getUserInfo(PermissionConfig permissionConfig);
    /**
     * 查询中台同步的人员角色信息，一对多
     * @param permissionConfig
     * @return
     */
    List<PermissionConfigVO> getUserRolesDetail(PermissionConfigVO permissionConfig);

    /**
     * 获取人员工号级别
     * @return
     */
    List<PermissionConfigVO> getUserRolesOaLevel();

}
