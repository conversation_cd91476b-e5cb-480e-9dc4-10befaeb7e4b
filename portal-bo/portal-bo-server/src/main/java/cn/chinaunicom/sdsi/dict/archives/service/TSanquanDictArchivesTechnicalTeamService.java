package cn.chinaunicom.sdsi.dict.archives.service;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesTechnicalTeam;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesTechnicalTeamQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
/**
 * <p>
 * 技术团队现状 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface TSanquanDictArchivesTechnicalTeamService extends IService<TSanquanDictArchivesTechnicalTeam> {

    // 分页查询
    IPage<TSanquanDictArchivesTechnicalTeam> findPage(TSanquanDictArchivesTechnicalTeamQueryVo tSanquanDictArchivesTechnicalTeamVo);

    // 根据id查询
    TSanquanDictArchivesTechnicalTeam findOne(String id);

    // 查询列表
    List<TSanquanDictArchivesTechnicalTeam> findList(TSanquanDictArchivesTechnicalTeamQueryVo tSanquanDictArchivesTechnicalTeamVo);

    // 新增
    int add(TSanquanDictArchivesTechnicalTeam tSanquanDictArchivesTechnicalTeam);

    // 修改
    int update(TSanquanDictArchivesTechnicalTeam tSanquanDictArchivesTechnicalTeam);

    // 删除
    int delete(String id);

    // 批量新增
    int insertBatchSomeColumn(List<TSanquanDictArchivesTechnicalTeam> middlePlatformList);

    // 根据档案id删除
    Boolean deleteByArchivesId(String archivesId);

    // 根据档案id查询
    List<TSanquanDictArchivesTechnicalTeam> findListByArchivesId(String id);
}
