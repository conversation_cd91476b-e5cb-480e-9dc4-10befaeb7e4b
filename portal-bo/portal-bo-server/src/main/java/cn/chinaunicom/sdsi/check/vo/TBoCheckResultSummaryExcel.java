package cn.chinaunicom.sdsi.check.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 稽核统计结果导出
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class TBoCheckResultSummaryExcel {

    @ColumnWidth(20)
    @ExcelProperty(value = "地市")
    String area;

    /* BO状态一致总数*/
    @ColumnWidth(20)
    @ExcelProperty(value = "BO状态一致总数")
    String boMatchTotal;

    /* BO状态不一致总数 */
    @ColumnWidth(20)
    @ExcelProperty(value = "BO状态不一致总数")
    String boDifferenceTotal;

    /* BO状态一致率 */
    @ColumnWidth(20)
    @ExcelProperty(value = "状态一致率")
    String boMatchBilv;

    /* BO状态环比 */
    @ColumnWidth(20)
    @ExcelProperty(value = "环比")
    String boMatchHuanbi;

    /* BO状态一致总数-互联网专线 */
    @ColumnWidth(20)
    @ExcelProperty(value = "BO状态一致总数-互联网专线 ")
    String boNetMatchTotal;

    /* BO状态不一致总数-互联网专线 */
    @ColumnWidth(20)
    @ExcelProperty(value = "BO状态不一致总数-互联网专线 ")
    String boNetDifferenceTotal;

    /* BO状态一致率-互联网专线 */
    @ColumnWidth(20)
    @ExcelProperty(value = "状态一致率-互联网专线")
    String boNetMatchBilv;

    /* BO状态环比-互联网专线 */
    @ColumnWidth(20)
    @ExcelProperty(value = "环比-互联网专线")
    String boNetMatchHuanbi;

    /* BO状态一致总数-数据网元*/
    @ColumnWidth(20)
    @ExcelProperty(value = "BO状态一致总数-数据网元")
    String boDataMatchTotal;

    /* BO状态不一致总数-数据网元*/
    @ColumnWidth(20)
    @ExcelProperty(value = "BO状态不一致总数-数据网元")
    String boDataDifferenceTotal;

    /* BO状态一致率-数据网元 */
    @ColumnWidth(20)
    @ExcelProperty(value = "状态一致率-数据网元")
    String boDataMatchBilv;

    /* BO状态环比-数据网元 */
    @ColumnWidth(20)
    @ExcelProperty(value = "环比-数据网元")
    String boDataMatchHuanbi;

    public String getArea() {
        return area;
    }
}
