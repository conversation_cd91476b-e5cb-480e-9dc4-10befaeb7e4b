package cn.chinaunicom.sdsi.platform.role.controller;


import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.platform.role.entity.*;
import cn.chinaunicom.sdsi.platform.role.service.IRoleMenuService;
import cn.chinaunicom.sdsi.platform.role.service.IRoleService;
import cn.chinaunicom.sdsi.platform.user.entity.UserRole;
import cn.chinaunicom.sdsi.platform.user.service.IUserRoleService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@RestController
@RequestMapping("/role")
public class RoleController extends BaseController {
    @Autowired
    private IRoleService roleService;
    @Autowired
    private IRoleMenuService roleMenuService;
    @Autowired
    private IUserRoleService userRoleService;

    /**
     * 新增
     *
     * @param vo
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "保存")
    public BaseResponse<Boolean> save(@RequestBody @Valid RoleVo vo) {
        Role entity = new Role();
        BeanUtils.copyProperties(vo, entity);
        roleService.saveOrUpdate(entity);
        List<RoleMenu> batchList = new ArrayList<>();
        for (String menuId : vo.getMenuIds()) {
            RoleMenu rm = new RoleMenu();
            rm.setRoleId(entity.getId());
            rm.setMenuId(menuId);
            batchList.add(rm);
        }
        if (!batchList.isEmpty()) {
            roleMenuService.deleteByRoleId(entity.getId());
            roleMenuService.insertBatchSomeColumn(batchList);
        }

        return ok(true);

    }


    /**
     * 逻辑删除
     */
    @GetMapping("/delete")
    @Operation(summary = "删除")
    public BaseResponse<Boolean> delete(String id) {
        Role role = roleService.getById(id);
        role.setDeleted("1");
        return ok(roleService.saveOrUpdate(role));
    }

    @GetMapping("/findOne")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<RoleVo> findProductId(String id) {
        return ok(roleService.findOne(id));
    }

    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<RoleVo>> findList(RoleQuery query) {
        return ok(roleService.findList(query));
    }

    @GetMapping("/findPage")
    @Operation(summary = "查询列表", description = "查询列表")
    public BasePageResponse<RoleVo> findPage(RoleQuery query) {
        return pageOk(roleService.findPage(query));
    }

    @PostMapping("/gant")
    @Operation(summary = "菜单赋权", description = "赋权")
    public BaseResponse<Boolean> gant(@RequestBody List<RoleMenu> roleMenus) {
        try {
            roleMenuService.deleteByRoleId(roleMenus.get(0).getRoleId());
            roleMenuService.insertBatchSomeColumn(roleMenus);
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }

    @Operation(summary = "判断是否存在", description = "分页")
    @GetMapping("/checkExist")
    public BaseResponse<Boolean> checkExist(String roleCode, String id) {
        return new BaseResponse<>(roleService.checkExist(roleCode, id));
    }

    @Operation(summary = "用户拥有的角色", description = "用户拥有的角色")
    @GetMapping("/findByUserId")
    public BaseResponse<List<RoleVo>> findByUserId(String userId) {
        return new BaseResponse<>(roleService.findByUserId(userId));
    }

    @Operation(summary = "用户拥有的角色", description = "用户拥有的角色")
    @GetMapping("/findByUserId2")
    public List<RoleVo> findByUserId2(String userId) {
        return roleService.findByUserId(userId);
    }


    @Operation(summary = "取消用户角色信息", description = "取消用户角色信息")
    @GetMapping("/cancelUserRole")
    public BaseResponse<Boolean> cancelUserRole(String userId, String roleId) {
        try {
            userRoleService.deleteByRoleIdAndUserId(roleId, userId);
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }

    @Operation(summary = "取消用户角色信息", description = "取消用户角色信息")
    @PostMapping("/cancelBatchUserRole")
    public BaseResponse<Boolean> cancelBatchUserRole(@RequestBody RoleBatch roleBatch) {
        try {
            for (String str : roleBatch.getUserIds()) {
                userRoleService.deleteByRoleIdAndUserId(roleBatch.getRoleId(), str);
            }
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }

    @Operation(summary = "取消用户角色信息", description = "取消用户角色信息")
    @PostMapping("/addBatchUserRole")
    public BaseResponse<Boolean> addBatchUserRole(@RequestBody RoleBatch roleBatch) {
        try {
            for (String str : roleBatch.getUserIds()) {
                UserRole ur = new UserRole();
                ur.setRoleId(roleBatch.getRoleId());
                ur.setUserId(str);
                userRoleService.save(ur);
            }
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }

    @Operation(summary = "取消用户角色信息", description = "取消用户角色信息")
    @GetMapping("/addUserRole")
    public BaseResponse<Boolean> addUserRole(String userId, String roleId) {
        try {
            UserRole ur = new UserRole();
            ur.setRoleId(roleId);
            ur.setUserId(userId);
            userRoleService.save(ur);
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }

    @Operation(summary = "查询工号级别", description = "查询工号级别")
    @GetMapping("/getUserOALevel")
    public BaseResponse<String> getUserOALevel() {
        MallUser user = UserUtils.getUser();
        String oaLevel = roleService.getUserOALevel(user.getStaffId());
        return ok(oaLevel);
    }
}
