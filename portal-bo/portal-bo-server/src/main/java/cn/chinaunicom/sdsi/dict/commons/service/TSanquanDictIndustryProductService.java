package cn.chinaunicom.sdsi.dict.commons.service;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictIndustryProduct;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictIndustryProductQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 行业与应用名称对应表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface TSanquanDictIndustryProductService extends IService<TSanquanDictIndustryProduct> {

    // 分页查询
    IPage<TSanquanDictIndustryProduct> findPage(TSanquanDictIndustryProductQueryVo tSanquanDictIndustryProductVo);

    // 根据id查询
    TSanquanDictIndustryProduct findOne(String id);

    // 查询列表
    List<TSanquanDictIndustryProduct> findList(TSanquanDictIndustryProductQueryVo tSanquanDictIndustryProductVo);

    // 新增
    int add(TSanquanDictIndustryProduct tSanquanDictIndustryProduct);

    // 修改
    int update(TSanquanDictIndustryProduct tSanquanDictIndustryProduct);

    // 删除
    int delete(String id);

    // 查询行业
    List<Map<String, String>> findIndustry();
}
