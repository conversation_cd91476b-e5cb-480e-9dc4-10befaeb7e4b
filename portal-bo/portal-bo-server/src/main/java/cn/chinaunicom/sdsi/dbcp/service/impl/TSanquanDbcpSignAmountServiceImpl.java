package cn.chinaunicom.sdsi.dbcp.service.impl;

import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeAmount;
import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignAmount;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpSignAmountQueryVo;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.dbcp.mapper.TSanquanDbcpSignAmountMapper;
import cn.chinaunicom.sdsi.dbcp.service.TSanquanDbcpSignAmountService;
import cn.chinaunicom.sdsi.dbcp.vo.TSanquanDbcpIncomeAmountExcelVo;
import cn.chinaunicom.sdsi.dbcp.vo.TSanquanDbcpSignAmountExcelVo;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 等保测评签约明细(月) 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@Service
public class TSanquanDbcpSignAmountServiceImpl extends ServiceImpl<TSanquanDbcpSignAmountMapper, TSanquanDbcpSignAmount> implements TSanquanDbcpSignAmountService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-17
     * @param tSanquanDbcpSignAmount
     * @return IPage<TSanquanDbcpSignAmount>
     **/
    @Override
    public IPage<TSanquanDbcpSignAmount> findPage(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDbcpSignAmountVo);
        return baseMapper.findPage(page, tSanquanDbcpSignAmountVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-17
     * @param id
     * @return TSanquanDbcpSignAmount
     **/
    @Override
    public TSanquanDbcpSignAmount findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-17
     * @return List<TSanquanDbcpSignAmount>
     **/
    @Override
    public List<TSanquanDbcpSignAmount> findList(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo) {
        return baseMapper.findList(tSanquanDbcpSignAmountVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-17
     * @param tSanquanDbcpSignAmount
     * @return int
     **/
    @Override
    public int add(TSanquanDbcpSignAmount tSanquanDbcpSignAmount) {
        return baseMapper.insert(tSanquanDbcpSignAmount);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-17
     * @param tSanquanDbcpSignAmount
     * @return int
     **/
    @Override
    public int update(TSanquanDbcpSignAmount tSanquanDbcpSignAmount) {
        return baseMapper.updateById(tSanquanDbcpSignAmount);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-17
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 导出模版
     */
    @Override
    public void exportTemplate() {
        try {
            // 3、导出数据
            ExcelUtils.exportExcel(null, TSanquanDbcpSignAmountExcelVo.class, "模板", "等保测评签约明细(月)");
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceErrorException("导出模版错误");
        }
    }

    /**
     * 导入数据
     * @param file
     * @return
     */
    @Override
    public Map<String, Object> uploadSignAmount(MultipartFile file) {
        Map<String,Object> failureDetails = new HashMap<>();
        int successTotal = 0;
        int failureTotal = 0;
        // 第几行
        int rowNum = 1;

        try {
            // 1、解析Excel文件并转换为实体对象列表
            List<TSanquanDbcpSignAmountExcelVo> excelData = ExcelUtils.importExcel(file,TSanquanDbcpSignAmountExcelVo.class);
            // 2、将实体对象列表转换为数据库实体对象列表
            List<TSanquanDbcpSignAmount> dataList = new ArrayList<>();
            for (int i = 0; i < excelData.size(); i++) {
                rowNum++;
                TSanquanDbcpSignAmountExcelVo vo = excelData.get(i);
                // 校验数据是否为空
                if (vo.getAcctId() == null &&
                        vo.getCity() == null &&
                        vo.getContractName() == null &&
                        vo.getCityComSignAmount() == null &&
                        vo.getOtherside() == null &&
                        vo.getDistrict() == null &&
                        vo.getIndustry() == null &&
                        vo.getSubdivisionIndustry() == null) {
                    // 记录失败信息
                    failureDetails.put(String.valueOf(rowNum),"第" + rowNum + "行：所有字段均为空，跳过该行");
                    failureTotal++;
                    continue;
                }
                try {
                    TSanquanDbcpSignAmount entity = new TSanquanDbcpSignAmount();
                    BeanUtils.copyProperties(vo, entity);
//                    entity.setAcctId(DateUtils.format(new Date(), "yyyyMM"));
                    dataList.add(entity);
                    successTotal++;
                } catch (Exception e) {
                    // 处理 BeanUtils.copyProperties 时的异常
                    failureDetails.put(String.valueOf(rowNum),"第" + rowNum + "行：数据转换失败，" + e.getMessage());
                    failureTotal++;
                    e.printStackTrace();
                }
            }
            // 3、批量插入数据
            if (CollUtil.isNotEmpty(dataList)) {
                try {
                    baseMapper.insertBatchSomeColumn(dataList);
                } catch (Exception e) {
                    e.printStackTrace();
                    throw new ServiceErrorException("导入数据错误");
                }
            }
        }catch (IOException e){
            e.printStackTrace();
            throw new ServiceErrorException("导入数据错误");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successTotal", successTotal);
        result.put("failureTotal", failureTotal);
        result.put("failureDetails", failureDetails);

        return result;
    }

    /**
     * 导出明细数据
     * @param tSanquanDbcpSignAmountVo
     */
    @Override
    public void exportData(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo) {
        List<TSanquanDbcpSignAmount> list = this.findList(tSanquanDbcpSignAmountVo);
        List<TSanquanDbcpSignAmountExcelVo> collect = list.stream().map(item -> {
            TSanquanDbcpSignAmountExcelVo tSanquanDbcpSignAmountExcelVo = new TSanquanDbcpSignAmountExcelVo();
            BeanUtils.copyProperties(item, tSanquanDbcpSignAmountExcelVo);
            return tSanquanDbcpSignAmountExcelVo;
        }).collect(Collectors.toList());
        try {
            // 3、导出数据
            ExcelUtils.exportExcel(collect, TSanquanDbcpSignAmountExcelVo.class, "模板", "等保测评签约明细");
        } catch (IOException e) {
            e.printStackTrace();
            throw new ServiceErrorException("导出错误");
        }
    }

}
