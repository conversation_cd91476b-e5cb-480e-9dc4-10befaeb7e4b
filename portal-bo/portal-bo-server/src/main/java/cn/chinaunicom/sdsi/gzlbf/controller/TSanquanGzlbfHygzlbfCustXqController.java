package cn.chinaunicom.sdsi.gzlbf.controller;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfHygzlbfCustXq;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfCityListQueryVo;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfHygzlbfCustXqQueryVo;
import cn.chinaunicom.sdsi.gzlbf.service.TSanquanGzlbfCityListService;
import cn.chinaunicom.sdsi.platform.role.service.IRoleService;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.chinaunicom.sdsi.gzlbf.service.TSanquanGzlbfHygzlbfCustXqService;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;
import cn.chinaunicom.sdsi.framework.base.BaseController;
/**
 * <p>
 * 高质量拜访-清单表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@RestController
@RequestMapping("/gzlbf/custXq")
public class TSanquanGzlbfHygzlbfCustXqController extends BaseController {

    @Autowired
    private TSanquanGzlbfHygzlbfCustXqService tSanquanGzlbfHygzlbfCustXqService;
    @Autowired
    private TSanquanGzlbfCityListService listService;
    @Autowired
    private IRoleService roleService;
    /*
     * <AUTHOR>
     * 区分省级，地市级
     **/
    @GetMapping("/findPage")
    public BasePageResponse findPage(TSanquanGzlbfCityListQueryVo tSanquanGzlbfDsHygzlbfListVo){
        MallUser user = UserUtils.getUser();
        String oaLevel = roleService.getUserOALevel(user.getStaffId());
        Boolean boo = UserUtils.hasPermission("ROLE_CITY_YAOKE","ROLE_SD_CITY");

        TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo = new TSanquanGzlbfHygzlbfCustXqQueryVo();
        if("10".equals(oaLevel)){// 省级
            BeanUtils.copyProperties(tSanquanGzlbfDsHygzlbfListVo,tSanquanGzlbfHygzlbfCustXqVo);
            return pageOk(tSanquanGzlbfHygzlbfCustXqService.findPage(tSanquanGzlbfHygzlbfCustXqVo));
        }else{
            if("全部".equals(tSanquanGzlbfDsHygzlbfListVo.getAreaName())){
                tSanquanGzlbfDsHygzlbfListVo.setAreaName(null);
            }
            if("全部".equals(tSanquanGzlbfDsHygzlbfListVo.getFlag())){
                tSanquanGzlbfDsHygzlbfListVo.setFlag(null);
            }
            if(StringUtils.isNotEmpty(user.getCity())){
                tSanquanGzlbfDsHygzlbfListVo.setAreaName2(user.getCity());
            }
            if(StringUtils.isNotEmpty(tSanquanGzlbfDsHygzlbfListVo.getCustManagerName())){
                tSanquanGzlbfDsHygzlbfListVo.setCustManager(tSanquanGzlbfDsHygzlbfListVo.getCustManagerName());
            }
            if(StringUtils.isNotEmpty(user.getStaffId()) && !boo){
                tSanquanGzlbfDsHygzlbfListVo.setCustManagerOa(user.getStaffId());
            }
            return pageOk(listService.findPage(tSanquanGzlbfDsHygzlbfListVo));
        }
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-03-25
     * @param id
     * @return BaseResponse<TSanquanGzlbfHygzlbfCustXq>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanGzlbfHygzlbfCustXq> findOne(String id) {
        return ok(tSanquanGzlbfHygzlbfCustXqService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-03-25
     * @return BaseResponse<List<TSanquanGzlbfHygzlbfCustXq>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanGzlbfHygzlbfCustXq>> findList(TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo) {
        return ok(tSanquanGzlbfHygzlbfCustXqService.findList(tSanquanGzlbfHygzlbfCustXqVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-03-25
     * @param tSanquanGzlbfHygzlbfCustXq
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanGzlbfHygzlbfCustXq tSanquanGzlbfHygzlbfCustXq){
        return ok(tSanquanGzlbfHygzlbfCustXqService.add(tSanquanGzlbfHygzlbfCustXq));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-03-25
     * @param tSanquanGzlbfHygzlbfCustXq
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanGzlbfHygzlbfCustXq tSanquanGzlbfHygzlbfCustXq) {
        return ok(tSanquanGzlbfHygzlbfCustXqService.update(tSanquanGzlbfHygzlbfCustXq));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-03-25
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanGzlbfHygzlbfCustXqService.delete(id));
    }

    /**
     * 获取批次列表
     * @return
     */
    @GetMapping("/getPatchList")
    public BaseResponse<List<Map<String,String>>> getPatchList( ){
        return ok(tSanquanGzlbfHygzlbfCustXqService.getPatchList());
    }

    /**
     * 获取最新批次
     * @return
     */
    @GetMapping("/getMaxPatch")
    public BaseResponse<String> getMaxPatch(){
        return ok(tSanquanGzlbfHygzlbfCustXqService.getMaxPatch());
    }

    /**
     * 导出数据
     * @param tSanquanGzlbfHygzlbfCustXqVo
     */
    @PostMapping("/exportData")
    @Operation(summary = "导出数据", description = "导出数据")
    private void exportData(@RequestBody TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo) {
        tSanquanGzlbfHygzlbfCustXqService.exportData(tSanquanGzlbfHygzlbfCustXqVo);
    }
}
