package cn.chinaunicom.sdsi.platform.role.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 同步角色，人员信息 历史同步记录
 *
 * 中台下发到t_sanquan_permission_role_user， 同步到，t_sanquan_platform_user_role及相关表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SyncRoleHisVo {

    String taskId;

    String executeSql;

    String executeRes;

    String remark;

}
