package cn.chinaunicom.sdsi.dict.commons.mapper;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictVisitTags;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictVisitTagsQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 走访标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface TSanquanDictVisitTagsMapper extends BaseMapper<TSanquanDictVisitTags> {

    // 分页查询
    IPage<TSanquanDictVisitTags> findPage(@Param("page") IPage page, @Param("query") TSanquanDictVisitTagsQueryVo tSanquanDictVisitTagsVo);

    // 查询列表
    List<TSanquanDictVisitTags> findList(@Param("query") TSanquanDictVisitTagsQueryVo tSanquanDictVisitTagsVo);
}
