package cn.chinaunicom.sdsi.checkZhengqi.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *   政企稽核统计结果展示
 * </p>
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(40) // 标题单元格高度
public class TBoZhengqiCheckAdjustStaticsExcelVo {

    @ColumnWidth(20)
    @ExcelProperty(value = {"地市"})
    private String city;                // 地市

    @ColumnWidth(20)
    @ExcelProperty(value = {"调减客户数"})
    private String adjustCustomerReduceNum;        // 调减客户数

    @ColumnWidth(30)
    @ExcelProperty(value = {"调减金额"})
    private String adjustReduceFee;          // 调减金额

    @ColumnWidth(20)
    @ExcelProperty(value = {"调增客户数"})
    private String adjustCustomerAddNum;        // 调增客户数

    @ColumnWidth(30)
    @ExcelProperty(value = {"调增金额"})
    private String adjustAddFee;          // 调增金额

    @ColumnWidth(20)
    @ExcelProperty(value = {"稽核派单数"})
    private String adjustOrderNum;              // 稽核派单数

    @ColumnWidth(30)
    @ExcelProperty(value = {"稽核金额(元)"})
    private String adjustFee;              // 稽核金额(元)

    @ColumnWidth(20)
    @ExcelProperty(value = {"稽核反馈数"})
    private String adjustFeebackNum;        // 稽核反馈数

    @ColumnWidth(20)
    @ExcelProperty(value = {"稽核反馈占比"})
    private String adjustFeebackRate;         // 稽核反馈占比

}
