package cn.chinaunicom.sdsi.mobileNetworkTariffAudit.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

import java.io.Serializable;

@Data
public class UserAuditDetailsQueryVo extends BaseQueryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 稽核月份
     */
    private String monthId;

    /**
     * 稽核地市
     */
    private String city;
}