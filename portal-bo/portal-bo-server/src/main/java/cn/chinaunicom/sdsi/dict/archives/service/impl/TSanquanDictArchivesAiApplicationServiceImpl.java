package cn.chinaunicom.sdsi.dict.archives.service.impl;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesAiApplication;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesAiApplicationQueryVo;
import cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesAiApplicationMapper;
import cn.chinaunicom.sdsi.dict.archives.service.TSanquanDictArchivesAiApplicationService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * AI应用场景 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class TSanquanDictArchivesAiApplicationServiceImpl extends ServiceImpl<TSanquanDictArchivesAiApplicationMapper, TSanquanDictArchivesAiApplication> implements TSanquanDictArchivesAiApplicationService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesAiApplication
     * @return IPage<TSanquanDictArchivesAiApplication>
     **/
    @Override
    public IPage<TSanquanDictArchivesAiApplication> findPage(TSanquanDictArchivesAiApplicationQueryVo tSanquanDictArchivesAiApplicationVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDictArchivesAiApplicationVo);
        return baseMapper.findPage(page, tSanquanDictArchivesAiApplicationVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return TSanquanDictArchivesAiApplication
     **/
    @Override
    public TSanquanDictArchivesAiApplication findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @return List<TSanquanDictArchivesAiApplication>
     **/
    @Override
    public List<TSanquanDictArchivesAiApplication> findList(TSanquanDictArchivesAiApplicationQueryVo tSanquanDictArchivesAiApplicationVo) {
        return baseMapper.findList(tSanquanDictArchivesAiApplicationVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesAiApplication
     * @return int
     **/
    @Override
    public int add(TSanquanDictArchivesAiApplication tSanquanDictArchivesAiApplication) {
        return baseMapper.insert(tSanquanDictArchivesAiApplication);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesAiApplication
     * @return int
     **/
    @Override
    public int update(TSanquanDictArchivesAiApplication tSanquanDictArchivesAiApplication) {
        return baseMapper.updateById(tSanquanDictArchivesAiApplication);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 根据档案id删除
     * @param archivesId
     * @return
     */
    @Override
    public Boolean deleteByArchivesId(String archivesId) {
        return this.remove(Wrappers.<TSanquanDictArchivesAiApplication>lambdaQuery()
                .eq(TSanquanDictArchivesAiApplication::getArchivesId, archivesId));
    }

    /**
     * 批量插入
     * @param batchList
     */
    @Override
    public int insertBatchSomeColumn(List<TSanquanDictArchivesAiApplication> batchList){
        return baseMapper.insertBatchSomeColumn(batchList);
    }

    /**
     * 根据档案id查询
     * @param id
     * @return
     */
    @Override
    public List<TSanquanDictArchivesAiApplication> findListByArchivesId(String id) {
        if (id != null) {
            return baseMapper.selectList(Wrappers.<TSanquanDictArchivesAiApplication>lambdaQuery()
                    .eq(TSanquanDictArchivesAiApplication::getArchivesId, id));
        }
        return null;
    }
}
