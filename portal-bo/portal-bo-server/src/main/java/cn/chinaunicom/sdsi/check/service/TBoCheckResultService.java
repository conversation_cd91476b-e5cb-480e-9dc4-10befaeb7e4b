package cn.chinaunicom.sdsi.check.service;

import cn.chinaunicom.sdsi.check.entity.TBoCheckResult;
import cn.chinaunicom.sdsi.check.vo.TBoCheckResultQuery;
import cn.chinaunicom.sdsi.check.vo.TBoCheckResultVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * BO稽核结果展示 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
public interface TBoCheckResultService extends IService<TBoCheckResult> {

    // 分页查询
    IPage<TBoCheckResult> findPage(TBoCheckResultQuery tBoCheckResultVo);

    // 根据id查询
    TBoCheckResult findOne(String id);

    // 查询列表
    List<TBoCheckResult> findList(TBoCheckResultQuery tBoCheckResultVo);

    // 新增
    int add(TBoCheckResult tBoCheckResult);

    // 修改
    int update(TBoCheckResult tBoCheckResult);

    // 删除
    int delete(String id);

    // 根据稽核结果编码查询
    TBoCheckResult getByCode(String obResultId, String status);

    // 根据稽核结果编码查询稽核列表
    List<TBoCheckResult> getByCodeList(List<String> obResultList, String status);

    // 导出数据
    Boolean exportData(TBoCheckResultQuery tBoCheckResultQuery);

    // 批量修改
    Integer batchUpdate(TBoCheckResultQuery tBoCheckResultQuery);

    // 获取批次列表
    List<Map<String,String>> getPatchList();

    // 获取最新批次
    String getMaxPatch();

    // 根据编码查询
    TBoCheckResultVO findByCode(String code);
}
