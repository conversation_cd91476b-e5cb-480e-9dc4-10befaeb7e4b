package cn.chinaunicom.sdsi.gzlbf.controller;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityList;
import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityReport;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfCityListQueryVo;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfCityReportQueryVo;
import cn.chinaunicom.sdsi.gzlbf.service.TSanquanGzlbfCityListService;
import cn.chinaunicom.sdsi.gzlbf.vo.TSanquanGzlbfCityListExcelVO;
import cn.chinaunicom.sdsi.gzlbf.vo.TSanquanGzlbfCityReportExcelVO;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.ExcelWriter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
/**
 * <p>
 * 高质量拜访-客户经理 拜访明细 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@RestController
@RequestMapping("/gzlbf/cityList")
@Slf4j
public class TSanquanGzlbfCityListController extends BaseController {

    @Autowired
    private TSanquanGzlbfCityListService listService;


    @PostMapping("exportData")
    public void exportData(@RequestBody TSanquanGzlbfCityListQueryVo queryVo) {
        if("全部".equals(queryVo.getAreaName())){
            queryVo.setAreaName(null);
        }
        if("全部".equals(queryVo.getFlag())){
            queryVo.setFlag(null);
        }
        List<TSanquanGzlbfCityList> reportList = listService.findList(queryVo);
        List<TSanquanGzlbfCityListExcelVO> resultList = Lists.newArrayList();
        reportList.forEach(item -> {
            TSanquanGzlbfCityListExcelVO excelVO = new TSanquanGzlbfCityListExcelVO();
            BeanUtil.copyProperties(item,excelVO);
            resultList.add(excelVO);
        });
        ExcelWriter excelWriter = null;
        try{
            excelWriter = ExcelUtils.initExcelWriter(TSanquanGzlbfCityListExcelVO.class,"地市维度清单");
            ExcelUtils.exportExcelMoreSheet(excelWriter,resultList,TSanquanGzlbfCityListExcelVO.class, "地市维度清单",0);
            ExcelUtils.closeExcelWriter(excelWriter);
        }catch (Exception e){
            log.error("系统错误：",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }

    }
    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-03-25
     * @param tSanquanGzlbfDsHygzlbfListVO
     * @return BasePageResponse<TSanquanGzlbfDsHygzlbfList>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanGzlbfCityList> findPage(TSanquanGzlbfCityListQueryVo tSanquanGzlbfDsHygzlbfListVo){
        if("全部".equals(tSanquanGzlbfDsHygzlbfListVo.getAreaName())){
            tSanquanGzlbfDsHygzlbfListVo.setAreaName(null);
        }
        if("全部".equals(tSanquanGzlbfDsHygzlbfListVo.getFlag())){
            tSanquanGzlbfDsHygzlbfListVo.setFlag(null);
        }
        return pageOk(listService.findPage(tSanquanGzlbfDsHygzlbfListVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-03-25
     * @param id
     * @return BaseResponse<TSanquanGzlbfDsHygzlbfList>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanGzlbfCityList> findOne(String id) {
        return ok(listService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-03-25
     * @return BaseResponse<List<TSanquanGzlbfDsHygzlbfList>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanGzlbfCityList>> findList(TSanquanGzlbfCityListQueryVo tSanquanGzlbfDsHygzlbfListVo) {
        return ok(listService.findList(tSanquanGzlbfDsHygzlbfListVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-03-25
     * @param tSanquanGzlbfDsHygzlbfList
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanGzlbfCityList tSanquanGzlbfDsHygzlbfList){
        return ok(listService.add(tSanquanGzlbfDsHygzlbfList));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-03-25
     * @param tSanquanGzlbfDsHygzlbfList
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanGzlbfCityList tSanquanGzlbfDsHygzlbfList) {
        return ok(listService.update(tSanquanGzlbfDsHygzlbfList));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-03-25
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(listService.delete(id));
    }
}
