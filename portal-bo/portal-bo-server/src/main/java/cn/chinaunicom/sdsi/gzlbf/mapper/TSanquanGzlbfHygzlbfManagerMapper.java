package cn.chinaunicom.sdsi.gzlbf.mapper;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfHygzlbfManager;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfHygzlbfManagerQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 高质量拜访-客户经理统计报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Mapper
public interface TSanquanGzlbfHygzlbfManagerMapper extends BaseMapper<TSanquanGzlbfHygzlbfManager> {

    // 分页查询
    IPage<TSanquanGzlbfHygzlbfManager> findPage(@Param("page") IPage page, @Param("query") TSanquanGzlbfHygzlbfManagerQueryVo tSanquanGzlbfHygzlbfManagerVo);

    // 查询列表
    List<TSanquanGzlbfHygzlbfManager> findList(@Param("query") TSanquanGzlbfHygzlbfManagerQueryVo tSanquanGzlbfHygzlbfManagerVo);

    // 获取批次列表
    List<Map<String,String>> getPatchList();

    // 获取最新批次
    String getMaxPatch();
}
