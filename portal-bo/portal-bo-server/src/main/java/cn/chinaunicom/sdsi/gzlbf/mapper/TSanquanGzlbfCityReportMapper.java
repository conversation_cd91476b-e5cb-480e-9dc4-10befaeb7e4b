package cn.chinaunicom.sdsi.gzlbf.mapper;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityReport;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfCityReportQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 高质量拜访-地市维度高质量拜访报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Mapper
public interface TSanquanGzlbfCityReportMapper extends BaseMapper<TSanquanGzlbfCityReport> {

    // 分页查询
    IPage<TSanquanGzlbfCityReport> findPage(@Param("page") IPage page, @Param("query") TSanquanGzlbfCityReportQueryVo tSanquanGzlbfHygzlbfDsbfVo);

    // 查询列表
    List<TSanquanGzlbfCityReport> findList(@Param("query") TSanquanGzlbfCityReportQueryVo tSanquanGzlbfHygzlbfDsbfVo);

    String getMaxPatch();
}
