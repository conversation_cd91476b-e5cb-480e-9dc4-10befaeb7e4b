package cn.chinaunicom.sdsi.aiAuditOpportunity.service;

import cn.chinaunicom.sdsi.aiAuditOpportunity.vo.AiAuditOpportunityQueryVO;
import cn.chinaunicom.sdsi.aiAuditOpportunity.vo.AiAuditOpportunityVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

public interface AiAuditOpportunityService {


    void markBusinessOpportunity(int group);

    void syncOpportunities();

    IPage<AiAuditOpportunityVO> findPage(AiAuditOpportunityQueryVO vo);

    AiAuditOpportunityVO details(String id);
}
