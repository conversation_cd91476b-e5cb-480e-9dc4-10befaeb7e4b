package cn.chinaunicom.sdsi.dict.archives.service.impl;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesTechnicalTeam;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesTechnicalTeamQueryVo;
import cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesTechnicalTeamMapper;
import cn.chinaunicom.sdsi.dict.archives.service.TSanquanDictArchivesTechnicalTeamService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * 技术团队现状 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class TSanquanDictArchivesTechnicalTeamServiceImpl extends ServiceImpl<TSanquanDictArchivesTechnicalTeamMapper, TSanquanDictArchivesTechnicalTeam> implements TSanquanDictArchivesTechnicalTeamService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesTechnicalTeam
     * @return IPage<TSanquanDictArchivesTechnicalTeam>
     **/
    @Override
    public IPage<TSanquanDictArchivesTechnicalTeam> findPage(TSanquanDictArchivesTechnicalTeamQueryVo tSanquanDictArchivesTechnicalTeamVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDictArchivesTechnicalTeamVo);
        return baseMapper.findPage(page, tSanquanDictArchivesTechnicalTeamVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return TSanquanDictArchivesTechnicalTeam
     **/
    @Override
    public TSanquanDictArchivesTechnicalTeam findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @return List<TSanquanDictArchivesTechnicalTeam>
     **/
    @Override
    public List<TSanquanDictArchivesTechnicalTeam> findList(TSanquanDictArchivesTechnicalTeamQueryVo tSanquanDictArchivesTechnicalTeamVo) {
        return baseMapper.findList(tSanquanDictArchivesTechnicalTeamVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesTechnicalTeam
     * @return int
     **/
    @Override
    public int add(TSanquanDictArchivesTechnicalTeam tSanquanDictArchivesTechnicalTeam) {
        return baseMapper.insert(tSanquanDictArchivesTechnicalTeam);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesTechnicalTeam
     * @return int
     **/
    @Override
    public int update(TSanquanDictArchivesTechnicalTeam tSanquanDictArchivesTechnicalTeam) {
        return baseMapper.updateById(tSanquanDictArchivesTechnicalTeam);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 根据档案id删除
     * @param archivesId
     * @return
     */
    @Override
    public Boolean deleteByArchivesId(String archivesId) {
        return this.remove(Wrappers.<TSanquanDictArchivesTechnicalTeam>lambdaQuery()
                .eq(TSanquanDictArchivesTechnicalTeam::getArchivesId, archivesId));
    }

    /**
     * 批量插入
     * @param batchList
     */
    @Override
    public int insertBatchSomeColumn(List<TSanquanDictArchivesTechnicalTeam> batchList){
        return baseMapper.insertBatchSomeColumn(batchList);
    }


    /**
     * 根据档案id查询
     * @param id
     * @return
     */
    @Override
    public List<TSanquanDictArchivesTechnicalTeam> findListByArchivesId(String id) {
        if (id != null) {
            return baseMapper.selectList(Wrappers.<TSanquanDictArchivesTechnicalTeam>lambdaQuery()
                    .eq(TSanquanDictArchivesTechnicalTeam::getArchivesId, id));
        }
        return null;
    }

}
