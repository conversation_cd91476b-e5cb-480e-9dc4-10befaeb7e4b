package cn.chinaunicom.sdsi.platform.menu.controller;


import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.menus.vo.MenuVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.platform.menu.entity.Menu;

import cn.chinaunicom.sdsi.platform.menu.entity.MenuQuery;

import cn.chinaunicom.sdsi.platform.menu.entity.MenuVo;
import cn.chinaunicom.sdsi.platform.menu.service.IMenuService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@RestController
@RequestMapping("/core/menu")
public class CoreMenuController extends BaseController {

    @Autowired
    private IMenuService menuService;


    /**
     * 新增
     *
     * @param vo
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "保存")
    public BaseResponse<Boolean> save(@RequestBody @Valid Menu vo) {
        return ok(menuService.saveOrUpdate(vo));
    }

    /**
     * 逻辑删除
     */
    @GetMapping("/delete")
    @Operation(summary = "删除")
    public BaseResponse<Boolean> delete(String id) {
        return ok(menuService.removeById(id));
    }

    @GetMapping("/findInfo")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<Menu> findInfo(String id) {
        return ok(menuService.getById(id));
    }

    /****
     * 检查该菜单下是否已有子菜单
     * @param parentId
     * @return
     */
    @PostMapping(value = "/checkHasChildren")
    public BaseResponse<Boolean> checkHasChildren(String parentId) {
        return ok(menuService.checkHasChildren(parentId));
    }

    @GetMapping("/findPage")
    @Operation(summary = "查询列表", description = "查询列表")
    public BasePageResponse<MenuVo> findPage(MenuQuery query) {
        return pageOk(menuService.findPage(query));
    }

    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<MenuVo>> findList(MenuQuery query) {
        return ok(menuService.findList(query));
    }


    @GetMapping("/findTreeList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<MenuVo>> findTreeList(MenuQuery query) {
        return ok(menuService.findTreeList(query));
    }


    @GetMapping("/findByRoleId")
    @Operation(summary = "根据角色查询菜单", description = "根据角色查询菜单")
    public BaseResponse<List<MenuVo>> findByRoleId(String roleId) {
        return ok(menuService.findByRoleId(roleId));
    }

    @GetMapping("/findByUserId")
    @Operation(summary = "根据角色查询菜单", description = "根据角色查询菜单")
    public List<MenuVo> findByUserId(String userId, String menuType) {
        return menuService.findByUserId(userId, menuType);
    }

    @GetMapping("/findUserHasPermission")
    @Operation(summary = "根据角色查询菜单", description = "根据角色查询菜单")
    public List<MenuVo> findUserHasPermission(String userId) {
        return menuService.userHasPermission(userId);
    }

    @Operation(summary = "菜单列表", description = "菜单列表")
    @GetMapping("/findTree")
    public BaseResponse<List<MenuVo>> findTree(MenuQuery query) {
        return ok(menuService.findUserHasMenu(query ,UserUtils.getUser().getUserId()));
    }

    @Operation(summary = "菜单系统配置参数", description = "菜单系统配置参数")
    @GetMapping("/findMenusSystem")
    public BaseResponse<List<Map<String, String>>> findMenusSystem() {
        return ok(menuService.findMenusSystem());
    }
}
