package cn.chinaunicom.sdsi.dbcp.controller;

import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpIncomeAmountQueryVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.dbcp.service.TSanquanDbcpIncomeAmountService;
import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeAmount;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 等保测评收入明细(月) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@RestController
@RequestMapping("/dbcp/incomeAmount")
public class TSanquanDbcpIncomeAmountController extends BaseController {

    @Autowired
    private TSanquanDbcpIncomeAmountService tSanquanDbcpIncomeAmountService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-03-17
     * @param tSanquanDbcpIncomeAmountVO
     * @return BasePageResponse<TSanquanDbcpIncomeAmount>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDbcpIncomeAmount> findPage(TSanquanDbcpIncomeAmountQueryVo tSanquanDbcpIncomeAmountVo){
        if("全部".equals(tSanquanDbcpIncomeAmountVo.getCity())){
            tSanquanDbcpIncomeAmountVo.setCity(null);
        }
        return pageOk(tSanquanDbcpIncomeAmountService.findPage(tSanquanDbcpIncomeAmountVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-03-17
     * @param id
     * @return BaseResponse<TSanquanDbcpIncomeAmount>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanDbcpIncomeAmount> findOne(String id) {
        return ok(tSanquanDbcpIncomeAmountService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-03-17
     * @return BaseResponse<List<TSanquanDbcpIncomeAmount>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDbcpIncomeAmount>> findList(TSanquanDbcpIncomeAmountQueryVo tSanquanDbcpIncomeAmountVo) {
        if("全部".equals(tSanquanDbcpIncomeAmountVo.getCity())){
            tSanquanDbcpIncomeAmountVo.setCity(null);
        }
        return ok(tSanquanDbcpIncomeAmountService.findList(tSanquanDbcpIncomeAmountVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-03-17
     * @param tSanquanDbcpIncomeAmount
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanDbcpIncomeAmount tSanquanDbcpIncomeAmount){
        return ok(tSanquanDbcpIncomeAmountService.add(tSanquanDbcpIncomeAmount));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-03-17
     * @param tSanquanDbcpIncomeAmount
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanDbcpIncomeAmount tSanquanDbcpIncomeAmount) {
        return ok(tSanquanDbcpIncomeAmountService.update(tSanquanDbcpIncomeAmount));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-03-17
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanDbcpIncomeAmountService.delete(id));
    }

    /**
     * 导出模版
     */
    @PostMapping("/exportTemplate")
    @Operation(summary = "导出模版", description = "导出模版")
    public void exportTemplate() {
        tSanquanDbcpIncomeAmountService.exportTemplate();
    }

    /**
     * 导入数据(上传excel文件)
     */
    @PostMapping("/uploadIncomeAmount")
    @Operation(summary = "导入数据", description = "导入数据")
    public BaseResponse<Map<String, Object>> uploadIncomeAmount(@RequestParam("file") MultipartFile file) {
        return ok(tSanquanDbcpIncomeAmountService.uploadIncomeAmount(file));
    }

    /**
     * 导出明细
     */
    @PostMapping("/exportData")
    @Operation(summary = "导出明细", description = "导出明细")
    public void exportData(@RequestBody TSanquanDbcpIncomeAmountQueryVo tSanquanDbcpIncomeAmountVo) {
        tSanquanDbcpIncomeAmountService.exportData(tSanquanDbcpIncomeAmountVo);
    }

}
