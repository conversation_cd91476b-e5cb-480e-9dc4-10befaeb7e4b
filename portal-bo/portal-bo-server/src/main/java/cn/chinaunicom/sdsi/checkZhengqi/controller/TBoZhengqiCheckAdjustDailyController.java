package cn.chinaunicom.sdsi.checkZhengqi.controller;

import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalService;
import cn.chinaunicom.sdsi.checkZhengqi.entity.TBoZhengqiCheckAdjustDaily;
import cn.chinaunicom.sdsi.checkZhengqi.service.TBoZhengqiCheckAdjustDailyService;
import cn.chinaunicom.sdsi.checkZhengqi.vo.*;
import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApproval;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.file.entity.Attachments;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * BO稽核结果展示 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@RestController
@RequestMapping("/checkZhengqi")
@Slf4j
public class TBoZhengqiCheckAdjustDailyController extends BaseController {

    @Autowired
    private TBoZhengqiCheckAdjustDailyService justDailyService;

    @Autowired
    private TSanquanExportApprovalService exportApprovalService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-02-13
     * @param tBoCheckResultVO
     * @return BasePageResponse<TBoCheckResult>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TBoZhengqiCheckAdjustDaily> findPage(TBoZhengqiCheckAdjustDailyQueryVO tBoCheckResultVo){
        return pageOk(justDailyService.findPage(tBoCheckResultVo));
    }

    @GetMapping("/findAdjustStaticsPage")
    public BasePageResponse<TBoZhengqiCheckAdjustStaticsVO> findAdjustStaticsPage(TBoZhengqiCheckAdjustDailyQueryVO tBoCheckResultVo){
        return pageOk(justDailyService.findAdjustStaticsPage(tBoCheckResultVo));
    }

    @PostMapping("/exportData")
    @Operation(summary = "导出数据", description = "导出数据")
    public BaseResponse<Boolean> exportData(@RequestBody TBoZhengqiCheckAdjustDailyQueryVO detail) {
        detail.setPageNum(-1);
        detail.setPageSize(Integer.MAX_VALUE);
        IPage<TBoZhengqiCheckAdjustDaily> pageList = justDailyService.findPage(detail);
        List<TBoZhengqiCheckAdjustDaily> list = pageList.getRecords();
        List<TBoZhengqiCheckAdjustDailyExcelVO> excelList = Lists.newArrayList();
        list.forEach(item -> {
            TBoZhengqiCheckAdjustDailyExcelVO excelVo = new TBoZhengqiCheckAdjustDailyExcelVO();
            BeanUtils.copyProperties(item,excelVo);
            excelList.add(excelVo);
        });
        try {
            // 修改后的导出方法，直接上传到文件服务器
            Attachments attachment = ExcelUtils.exportAndUploadToFileService(
                    excelList,
                    TBoZhengqiCheckAdjustDailyExcelVO.class,
                    "调账稽核明细",
                    "调账稽核明细",
                    "exportApproval"
            );
            if(attachment != null){
                // 添加审核信息
                addExportApproval(detail, attachment);
            }
            return ok(attachment != null);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 添加审核信息
     * @param tBoCheckResultVo
     * @param attachment
     */
    private void addExportApproval(TBoZhengqiCheckAdjustDailyQueryVO tBoCheckResultVo, Attachments attachment) {
        MallUser user = UserUtils.getUser();
        TSanquanExportApproval tSanquanExportApproval = new TSanquanExportApproval();
        tSanquanExportApproval.setModuleCode("1");
        tSanquanExportApproval.setModuleName("调账稽核明细");
        // 审批人信息
        tSanquanExportApproval.setApproverId(tBoCheckResultVo.getApproverId());
        tSanquanExportApproval.setApproverName(tBoCheckResultVo.getApproverName());
        tSanquanExportApproval.setApproverTel(tBoCheckResultVo.getApproverTel());
        // 申请人信息
        tSanquanExportApproval.setApplicantId(user.getStaffId());
        tSanquanExportApproval.setApplicantName(user.getStaffName());
        tSanquanExportApproval.setApplyTime(DateUtils.formatDateTime(new Date()));
        // 文件信息
        tSanquanExportApproval.setFileId(attachment.getId());
        tSanquanExportApproval.setFileName(attachment.getFileName());
        tSanquanExportApproval.setTaskName("导出调账稽核明细");
        tSanquanExportApproval.setStatus("0");
        tSanquanExportApproval.setCreateTime(DateUtils.formatDateTime(new Date()));
        exportApprovalService.save(tSanquanExportApproval);
    }

    @PostMapping("/exportAdjustStaticsData")
    @Operation(summary = "导出数据", description = "导出数据")
    public void exportAdjustStaticsData(@RequestBody TBoZhengqiCheckAdjustDailyQueryVO detail) {
        detail.setPageNum(-1);
        detail.setPageSize(Integer.MAX_VALUE);
        IPage<TBoZhengqiCheckAdjustStaticsVO> pageList = justDailyService.findAdjustStaticsPage(detail);
        List<TBoZhengqiCheckAdjustStaticsVO> list = pageList.getRecords();
        List<TBoZhengqiCheckAdjustStaticsExcelVo> excelList = Lists.newArrayList();
        list.forEach(item -> {
            TBoZhengqiCheckAdjustStaticsExcelVo excelVo = new TBoZhengqiCheckAdjustStaticsExcelVo();
            BeanUtils.copyProperties(item,excelVo);
            excelList.add(excelVo);
        });
        ExcelWriter excelWriter = null;
        try{
            String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            excelWriter = ExcelUtils.initExcelWriter(TBoZhengqiCheckAdjustStaticsExcelVo.class,"稽核结果"+dateStr);
            ExcelUtils.exportExcelMoreSheet(excelWriter,excelList, TBoZhengqiCheckAdjustStaticsExcelVo.class,"稽核结果"+dateStr,0);
            ExcelUtils.closeExcelWriter(excelWriter);

        }catch (Exception e){
            log.error("系统异常:",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }
    }

    @GetMapping("/getMaxDate")
    public BaseResponse<String> getMaxDate(){
        return ok(justDailyService.getMaxDate());
    }

    @GetMapping("/updateAdjust")
    public BaseResponse<String> updateAdjust(TBoZhengqiCheckAdjustDaily dailyQueryVO){
        if(dailyQueryVO.getId() == null){
            return ok("失败，id为空");
        }
        if("1".equals(dailyQueryVO.getUpdateType())){
            dailyQueryVO.setAuditDate(DateUtils.formatDateTime(new Date()));
            dailyQueryVO.setAuditName(UserUtils.getUser().getStaffName());
            dailyQueryVO.setAuditId(UserUtils.getUser().getStaffId());
        }
        int so = this.justDailyService.getBaseMapper().updateById(dailyQueryVO);
        return ok(so+"");
    }

    /**
     * 查询详情信息
     *
     * @param id
     * @return
     */
    @GetMapping("/findOne")
    public BaseResponse<TBoZhengqiCheckAdjustDaily> findOne(String id){
        return ok(justDailyService.findOne(id));
    }


    /**
     * 自动派单部分（尚无触发条件）
     */
    @GetMapping("/check")
    public BaseResponse<String> checkOrder(TBoZhengqiCheckAdjustDailyQueryVO tBoCheckResultVo) {
        justDailyService.checkOrder(tBoCheckResultVo);
        return ok("SUCCESS");
    }

    /**
     * 调账工单查询
     * @param tBoCheckResultVo 调账工单查询
     * @return
     */
    @GetMapping("/findWorkOrderPage")
    public BasePageResponse<TBoZhengqiCheckAdjustDailyVO> findWorkOrderPage(TBoZhengqiCheckAdjustDailyQueryVO tBoCheckResultVo){
        return pageOk(justDailyService.findWorkOrderPage(tBoCheckResultVo));
    }

    /**
     * 调账工单详情信息
     * @param id 调账工单id
     */
    @GetMapping("findOneById")
    public BaseResponse<TBoZhengqiCheckAdjustDailyVO> findOneById(String id){
        return ok(justDailyService.findOneById(id));
    }

    /**
     * 导出工单数据
     * @param tBoCheckResultVo 调账工单
     * @return
     */
    @PostMapping("/exportWorkOrderData")
    public BaseResponse<Boolean> exportWorkOrderData(@RequestBody TBoZhengqiCheckAdjustDailyQueryVO tBoCheckResultVo){
        return ok(justDailyService.exportWorkOrderData(tBoCheckResultVo));
    }

}
