package cn.chinaunicom.sdsi.dict.commons.mapper;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictDistrictCompanies;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictDistrictCompaniesQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 地市-区县公司对应关系 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface TSanquanDictDistrictCompaniesMapper extends BaseMapper<TSanquanDictDistrictCompanies> {

    // 分页查询
    IPage<TSanquanDictDistrictCompanies> findPage(@Param("page") IPage page, @Param("query") TSanquanDictDistrictCompaniesQueryVo tSanquanDictDistrictCompaniesVo);

    // 查询列表
    List<TSanquanDictDistrictCompanies> findList(@Param("query") TSanquanDictDistrictCompaniesQueryVo tSanquanDictDistrictCompaniesVo);
}
