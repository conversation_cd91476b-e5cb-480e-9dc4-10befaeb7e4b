package cn.chinaunicom.sdsi.dict.archives.mapper;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesNetwarkInfo;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesNetwarkInfoQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * IT基础设施现状-现有网络情况 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface TSanquanDictArchivesNetwarkInfoMapper extends BaseMapper<TSanquanDictArchivesNetwarkInfo> {

    // 分页查询
    IPage<TSanquanDictArchivesNetwarkInfo> findPage(@Param("page") IPage page, @Param("query") TSanquanDictArchivesNetwarkInfoQueryVo tSanquanDictArchivesNetwarkInfoVo);

    // 查询列表
    List<TSanquanDictArchivesNetwarkInfo> findList(@Param("query") TSanquanDictArchivesNetwarkInfoQueryVo tSanquanDictArchivesNetwarkInfoVo);

    // 批量插入
    int insertBatchSomeColumn(List<TSanquanDictArchivesNetwarkInfo> batchList);
}
