package cn.chinaunicom.sdsi.check.controller;

import cn.chinaunicom.sdsi.check.vo.TBoCheckResultQuery;
import cn.chinaunicom.sdsi.check.vo.TBoCheckResultVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.chinaunicom.sdsi.check.service.TBoCheckResultService;
import cn.chinaunicom.sdsi.check.entity.TBoCheckResult;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import cn.chinaunicom.sdsi.framework.base.BaseController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * BO稽核结果展示 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@RestController
@RequestMapping("/checkResult")
public class TBoCheckResultController extends BaseController {

    @Autowired
    private TBoCheckResultService tBoCheckResultService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-02-13
     * @param tBoCheckResultVO
     * @return BasePageResponse<TBoCheckResult>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TBoCheckResult> findPage(TBoCheckResultQuery tBoCheckResultVo){
        return pageOk(tBoCheckResultService.findPage(tBoCheckResultVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-02-13
     * @param id
     * @return BaseResponse<TBoCheckResult>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TBoCheckResult> findOne(String id) {
        return ok(tBoCheckResultService.findOne(id));
    }


    /**
     * 根据code查询
     * @param code
     * @return
     */
    @GetMapping("/findByCode")
    public BaseResponse<TBoCheckResultVO> findByCode(String code) {
        return ok(tBoCheckResultService.findByCode(code));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-02-13
     * @return BaseResponse<List<TBoCheckResult>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TBoCheckResult>> findList(TBoCheckResultQuery tBoCheckResultVo) {
        return ok(tBoCheckResultService.findList(tBoCheckResultVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-02-13
     * @param tBoCheckResult
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TBoCheckResult tBoCheckResult){
        return ok(tBoCheckResultService.add(tBoCheckResult));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-02-13
     * @param tBoCheckResult
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TBoCheckResult tBoCheckResult) {
        return ok(tBoCheckResultService.update(tBoCheckResult));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-02-13
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tBoCheckResultService.delete(id));
    }

    /**
     * 导出数据
     * @param tBoCheckResultQuery
     */
    @PostMapping("/exportData")
    @Operation(summary = "导出数据", description = "导出数据")
    private BaseResponse<Boolean> exportData(@RequestBody TBoCheckResultQuery tBoCheckResultQuery) {
       return ok(tBoCheckResultService.exportData(tBoCheckResultQuery));
    }

    /*
     * <AUTHOR>
     * @description 批量修改
     * @since 2025-02-19
     * @param tBoCheckResultQuery
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/batchUpdate")
    public BaseResponse<Integer> batchUpdate(@RequestBody TBoCheckResultQuery tBoCheckResultQuery) {
        return ok(tBoCheckResultService.batchUpdate(tBoCheckResultQuery));
    }

    /**
     * 获取批次列表
     * @return
     */
    @GetMapping("/getPatchList")
    public BaseResponse<List<Map<String,String>>> getPatchList( ){
        return ok(tBoCheckResultService.getPatchList());
    }

    /**
     * 获取最新批次
     * @return
     */
    @GetMapping("/getMaxPatch")
    public BaseResponse<String> getMaxPatch(){
        return ok(tBoCheckResultService.getMaxPatch());
    }


}
