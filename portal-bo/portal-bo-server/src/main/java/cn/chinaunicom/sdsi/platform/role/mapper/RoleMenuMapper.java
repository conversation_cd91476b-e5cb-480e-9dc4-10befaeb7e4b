package cn.chinaunicom.sdsi.platform.role.mapper;

import cn.chinaunicom.sdsi.platform.role.entity.RoleMenu;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Mapper
public interface RoleMenuMapper extends BaseMapper<RoleMenu> {
    /**
     * 批量插入
     *
     * <AUTHOR>
     * @Date 2024/7/22 13:47
     */
    void insertBatchSomeColumn(@Param("list") List<RoleMenu> batchList);


    void deleteByRoleId(@Param("roleId") String roleId);

}
