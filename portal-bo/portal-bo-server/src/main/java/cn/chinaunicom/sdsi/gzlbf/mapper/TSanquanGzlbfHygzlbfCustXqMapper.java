package cn.chinaunicom.sdsi.gzlbf.mapper;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfHygzlbfCustXq;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfHygzlbfCustXqQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 高质量拜访-清单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Mapper
public interface TSanquanGzlbfHygzlbfCustXqMapper extends BaseMapper<TSanquanGzlbfHygzlbfCustXq> {

    // 分页查询
    IPage<TSanquanGzlbfHygzlbfCustXq> findPage(@Param("page") IPage page, @Param("query") TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo);

    // 查询列表
    List<TSanquanGzlbfHygzlbfCustXq> findList(@Param("query") TSanquanGzlbfHygzlbfCustXqQueryVo tSanquanGzlbfHygzlbfCustXqVo);

    // 获取批次列表
    List<Map<String,String>> getPatchList();

    // 获取最新批次
    String getMaxPatch();
}
