package cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("t_sanquan_zq_m_yw_jh_list")
public class UserAuditDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("HX_AREA_ID")
    private String hxAreaId;

    @TableField("HX_AREA_NAME")
    private String hxAreaName;

    @TableField("TARIFF_PROCESS_NAME")
    private String tariffProcessName;

    @TableField("CUSTOMER_NAME")
    private String customerName;

    @TableField("CLUSTER_CODE")
    private String clusterCode;

    @TableField("TARIFF_DURATION")
    private String tariffDuration;

    @TableField("START_DATE")
    private String startDate;

    @TableField("END_DATE")
    private String endDate;

    @TableField("NEW_USER_COUNT")
    private String newUserCount;

    @TableField("MAIN_PACKAGE")
    private String mainPackage;

    @TableField("ARPU_VALUE")
    private String arpuValue;

    @TableField("MONTHLY_INCOME")
    private String monthlyIncome;

    @TableField("POLICY_VOICE")
    private String policyVoice;

    @TableField("POLICY_FLUX")
    private String policyFlux;

    @TableField("TOTAL_VOICE")
    private String totalVoice;

    @TableField("TOTAL_FLUX")
    private String totalFlux;

    @TableField("CURRENT_NEW_USER_COUNT")
    private Long currentNewUserCount;

    @TableField("AVG_VOICE_QUOTA")
    private BigDecimal avgVoiceQuota;

    @TableField("AVG_FLUX_QUOTA")
    private BigDecimal avgFluxQuota;

    @TableField("MOU_VALUE")
    private BigDecimal mouValue;

    @TableField("DOU_VALUE")
    private BigDecimal douValue;

    @TableField("THREE_NON_LOW_USER_COUNT")
    private Long threeNonLowUserCount;

    @TableField("CURRENT_INTEGRATION_COUNT")
    private Long currentIntegrationCount;

    @TableField("CURRENT_MONTHLY_INCOME")
    private BigDecimal currentMonthlyIncome;

    @TableField("THREE_NON_LOW_USER_RATIO")
    private Double threeNonLowUserRatio;

    @TableField("CURRENT_INTEGRATION_RATE")
    private Double currentIntegrationRate;

    @TableField("CURRENT_ARPU_VALUE")
    private BigDecimal currentArpuValue;

    @TableField("AUDIT_ACCOUNT_PROGRESS")
    private BigDecimal auditAccountProgress;

    @TableField("AUDIT_ARPU")
    private String auditArpu;

    @TableField("MONTH_ID")
    private String monthId;

    @TableField("ANCHOR_PRODUCT_CODE1")
    private String anchorProductCode1;

    @TableField("ANCHOR_PRODUCT_CODE2")
    private String anchorProductCode2;

    @TableField("AUDIT_SUSPECTED_OVERFLOW")
    private String auditSuspectedOverflow;
}