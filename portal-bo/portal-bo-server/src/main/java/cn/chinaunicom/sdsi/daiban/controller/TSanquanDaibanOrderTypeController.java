package cn.chinaunicom.sdsi.daiban.controller;

import cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrderType;
import cn.chinaunicom.sdsi.cloud.daiban.query.TSanquanDaibanOrderTypeQueryVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.chinaunicom.sdsi.daiban.service.TSanquanDaibanOrderTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import cn.chinaunicom.sdsi.framework.base.BaseController;
/**
 * <p>
 * 待办类型表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@RestController
@RequestMapping("/daiban/orderType")
public class TSanquanDaibanOrderTypeController extends BaseController {

    @Autowired
    private TSanquanDaibanOrderTypeService tSanquanDaibanOrderTypeService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-05-21
     * @param tSanquanDaibanOrderTypeVO
     * @return BasePageResponse<TSanquanDaibanOrderType>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDaibanOrderType> findPage(TSanquanDaibanOrderTypeQueryVo tSanquanDaibanOrderTypeVo){
        return pageOk(tSanquanDaibanOrderTypeService.findPage(tSanquanDaibanOrderTypeVo));
    }

    /**
     * 根据订单ID查询
     * @param orderId
     * @return
     */
    @GetMapping("/findOneByOrderId")
    public BaseResponse<TSanquanDaibanOrderType> findOneByOrderId(String orderId) {
        return ok(tSanquanDaibanOrderTypeService.findOneByOrderId(orderId));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-05-21
     * @param id
     * @return BaseResponse<TSanquanDaibanOrderType>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanDaibanOrderType> findOne(String id) {
        return ok(tSanquanDaibanOrderTypeService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-05-21
     * @return BaseResponse<List<TSanquanDaibanOrderType>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDaibanOrderType>> findList(TSanquanDaibanOrderTypeQueryVo tSanquanDaibanOrderTypeVo) {
        return ok(tSanquanDaibanOrderTypeService.findList(tSanquanDaibanOrderTypeVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-05-21
     * @param tSanquanDaibanOrderType
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanDaibanOrderType tSanquanDaibanOrderType){
        return ok(tSanquanDaibanOrderTypeService.add(tSanquanDaibanOrderType));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-05-21
     * @param tSanquanDaibanOrderType
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanDaibanOrderType tSanquanDaibanOrderType) {
        return ok(tSanquanDaibanOrderTypeService.update(tSanquanDaibanOrderType));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-05-21
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanDaibanOrderTypeService.delete(id));
    }
}
