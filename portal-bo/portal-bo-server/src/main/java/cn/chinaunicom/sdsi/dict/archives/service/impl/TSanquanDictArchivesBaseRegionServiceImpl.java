package cn.chinaunicom.sdsi.dict.archives.service.impl;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesBaseRegion;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesBaseRegionQueryVo;
import cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesBaseRegionMapper;
import cn.chinaunicom.sdsi.dict.archives.service.TSanquanDictArchivesBaseRegionService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * IT基础设施现状-基础区域情况 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class TSanquanDictArchivesBaseRegionServiceImpl extends ServiceImpl<TSanquanDictArchivesBaseRegionMapper, TSanquanDictArchivesBaseRegion> implements TSanquanDictArchivesBaseRegionService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesBaseRegion
     * @return IPage<TSanquanDictArchivesBaseRegion>
     **/
    @Override
    public IPage<TSanquanDictArchivesBaseRegion> findPage(TSanquanDictArchivesBaseRegionQueryVo tSanquanDictArchivesBaseRegionVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDictArchivesBaseRegionVo);
        return baseMapper.findPage(page, tSanquanDictArchivesBaseRegionVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return TSanquanDictArchivesBaseRegion
     **/
    @Override
    public TSanquanDictArchivesBaseRegion findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @return List<TSanquanDictArchivesBaseRegion>
     **/
    @Override
    public List<TSanquanDictArchivesBaseRegion> findList(TSanquanDictArchivesBaseRegionQueryVo tSanquanDictArchivesBaseRegionVo) {
        return baseMapper.findList(tSanquanDictArchivesBaseRegionVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesBaseRegion
     * @return int
     **/
    @Override
    public int add(TSanquanDictArchivesBaseRegion tSanquanDictArchivesBaseRegion) {
        return baseMapper.insert(tSanquanDictArchivesBaseRegion);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchivesBaseRegion
     * @return int
     **/
    @Override
    public int update(TSanquanDictArchivesBaseRegion tSanquanDictArchivesBaseRegion) {
        return baseMapper.updateById(tSanquanDictArchivesBaseRegion);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 根据档案id删除
     * @param archivesId
     * @return
     */
    @Override
    public Boolean deleteByArchivesId(String archivesId) {
        return this.remove(Wrappers.<TSanquanDictArchivesBaseRegion>lambdaQuery()
                .eq(TSanquanDictArchivesBaseRegion::getArchivesId, archivesId));
    }

    /**
     * 批量插入
     * @param batchList
     */
    @Override
    public int insertBatchSomeColumn(List<TSanquanDictArchivesBaseRegion> batchList){
        return baseMapper.insertBatchSomeColumn(batchList);
    }

    @Override
    public List<TSanquanDictArchivesBaseRegion> findListByArchivesId(String id) {
        if (id != null) {
            return baseMapper.selectList(Wrappers.<TSanquanDictArchivesBaseRegion>lambdaQuery()
                    .eq(TSanquanDictArchivesBaseRegion::getArchivesId, id));
        }
        return null;
    }
}
