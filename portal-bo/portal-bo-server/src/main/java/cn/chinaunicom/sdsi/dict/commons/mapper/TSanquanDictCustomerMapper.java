package cn.chinaunicom.sdsi.dict.commons.mapper;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictCustomer;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictCustomerQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 客户DICT数字档案客户 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Mapper
public interface TSanquanDictCustomerMapper extends BaseMapper<TSanquanDictCustomer> {

    // 分页查询
    IPage<TSanquanDictCustomer> findPage(@Param("page") IPage page, @Param("query") TSanquanDictCustomerQueryVo tSanquanDictCustomerVo);

    // 查询列表
    List<TSanquanDictCustomer> findList(@Param("query") TSanquanDictCustomerQueryVo tSanquanDictCustomerVo);
}
