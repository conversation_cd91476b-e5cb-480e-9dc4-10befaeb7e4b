package cn.chinaunicom.sdsi.permissionConfig.mapper;

import cn.chinaunicom.sdsi.permissionConfig.entity.PermissionConfig;
import cn.chinaunicom.sdsi.permissionConfig.entity.PermissionConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 系统数据权限细化粒度补充表配置 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Mapper
public interface PermissionConfigMapper extends BaseMapper<PermissionConfig> {

    List<PermissionConfig> getUserInfo(PermissionConfig permissionConfig);

    List<PermissionConfigVO> getUserRolesDetail(PermissionConfigVO permissionConfig);

    List<PermissionConfigVO> getUserRolesOaLevel(PermissionConfigVO permissionConfig);

}
