package cn.chinaunicom.sdsi.approval.service.impl;

import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalRecordService;
import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApproval;
import cn.chinaunicom.sdsi.approval.mapper.TSanquanExportApprovalMapper;
import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalService;
import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApprovalRecord;
import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportApprovalQueryVo;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
/**
 * <p>
 * 导出审批表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class TSanquanExportApprovalServiceImpl extends ServiceImpl<TSanquanExportApprovalMapper, TSanquanExportApproval> implements TSanquanExportApprovalService {

    @Autowired
    public UnifastContext unifastContext;

    @Autowired
    private TSanquanExportApprovalRecordService exportApprovalRecordService;


    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportApproval
     * @return IPage<TSanquanExportApproval>
     **/
    @Override
    public IPage<TSanquanExportApproval> findPage(TSanquanExportApprovalQueryVo tSanquanExportApprovalVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanExportApprovalVo);
        return baseMapper.findPage(page, tSanquanExportApprovalVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param id
     * @return TSanquanExportApproval
     **/
    @Override
    public TSanquanExportApproval findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @return List<TSanquanExportApproval>
     **/
    @Override
    public List<TSanquanExportApproval> findList(TSanquanExportApprovalQueryVo tSanquanExportApprovalVo) {
        return baseMapper.findList(tSanquanExportApprovalVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportApproval
     * @return int
     **/
    @Override
    public int add(TSanquanExportApproval tSanquanExportApproval) {
        return baseMapper.insert(tSanquanExportApproval);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportApproval
     * @return int
     **/
    @Override
    public int update(TSanquanExportApproval tSanquanExportApproval) {
        return baseMapper.updateById(tSanquanExportApproval);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    // 审核
    @Override
    public Integer auditApproval(TSanquanExportApproval tSanquanExportApproval) {
        MallUser user = UserUtils.getUser();
        tSanquanExportApproval.setApproveTime(DateUtils.formatDateTime(new Date()));
        TSanquanExportApprovalRecord tSanquanExportApprovalRecord = new TSanquanExportApprovalRecord();
        tSanquanExportApprovalRecord.setApprovalId(tSanquanExportApproval.getId());
        tSanquanExportApprovalRecord.setApproverId(user.getStaffId());
        tSanquanExportApprovalRecord.setApproverName(user.getStaffName());
        tSanquanExportApprovalRecord.setApproveResult(tSanquanExportApproval.getStatus());
        tSanquanExportApprovalRecord.setApproveTime(DateUtils.formatDateTime(new Date()));
        tSanquanExportApprovalRecord.setComment(tSanquanExportApproval.getRemark());
        exportApprovalRecordService.add(tSanquanExportApprovalRecord);
        return baseMapper.updateById(tSanquanExportApproval);
    }

}
