package cn.chinaunicom.sdsi.mobileNetworkTariffAudit.controller;

import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity.TariffDiscount;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.service.TariffDiscountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mobileNetworkTariff/tariffDiscount")
public class TariffDiscountController  extends BaseController  {


    @Autowired
    private TariffDiscountService tariffDiscountService;

    @GetMapping("/simpleList")
    public BaseResponse<List<TariffDiscount>> simpleList(){
        // 获取当前月份
        YearMonth currentMonth = YearMonth.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");

        for (int i = 0; i < 6; i++) {
            currentMonth = currentMonth.minusMonths(1);
            String currentMonthStr = currentMonth.format(formatter);
            List<TariffDiscount> simpleList = tariffDiscountService.findSimpleList(currentMonthStr);
            if (simpleList != null && !simpleList.isEmpty()) {
                return ok(simpleList);
            }
        }

        return notOk();
    }



}
