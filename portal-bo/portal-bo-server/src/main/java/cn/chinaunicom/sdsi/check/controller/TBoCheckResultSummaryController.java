package cn.chinaunicom.sdsi.check.controller;

import cn.chinaunicom.sdsi.check.service.TBoCheckResultService;
import cn.chinaunicom.sdsi.check.vo.*;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.response.BasePageVO;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.permissionConfig.entity.PermissionConfig;
import cn.chinaunicom.sdsi.permissionConfig.service.PermissionConfigService;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.spring.util.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.check.service.TBoCheckResultSummaryService;
import cn.chinaunicom.sdsi.check.entity.TBoCheckResultSummary;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * BO稽核跟踪报表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Slf4j
@RestController
@RequestMapping("/checkSummay")
public class TBoCheckResultSummaryController extends BaseController {

    @Autowired
    private TBoCheckResultSummaryService tBoCheckResultSummaryService;

    @Autowired
    PermissionConfigService permissionConfigService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-02-13
     * @param tBoCheckResultSummaryVO
     * @return BasePageResponse<TBoCheckResultSummary>
     **/
    @GetMapping("/findPage")
    public BasePageResponse findPage( TBoCheckResultSummaryQuery tBoCheckResultSummaryVO){
        if("全部".equals(tBoCheckResultSummaryVO.getCity())){
            tBoCheckResultSummaryVO.setCity(null);
        }
        if(StringUtils.isNotEmpty(tBoCheckResultSummaryVO.getCity())){
            tBoCheckResultSummaryVO.setCity(tBoCheckResultSummaryVO.getCity());
        }else{
            if("2".equals(tBoCheckResultSummaryVO.getLevels()) || "3".equals(tBoCheckResultSummaryVO.getLevels())){
                MallUser mallUser = UserUtils.getUser();
                tBoCheckResultSummaryVO.setCity(mallUser.getCity());
            }
        }
        if(StringUtils.isNotEmpty(tBoCheckResultSummaryVO.getDistrict())){
            tBoCheckResultSummaryVO.setDistrict(tBoCheckResultSummaryVO.getDistrict());
        }else{
            if("3".equals(tBoCheckResultSummaryVO.getLevels())){
                MallUser mallUser = UserUtils.getUser();
                PermissionConfig config = new PermissionConfig();
                config.setOa(mallUser.getStaffId());// 工号
                List<PermissionConfig> list = permissionConfigService.getUserInfo(config);
                if(list.size()>0){
                    tBoCheckResultSummaryVO.setDistrict(list.get(0).getDistrictName());
                }else{
                    log.error("未查询到当前登录人的区县信息：{}",mallUser.getStaffId());
                    tBoCheckResultSummaryVO.setDistrict("未知区县");
                }
            }
        }
        if(!StringUtils.isNotEmpty(tBoCheckResultSummaryVO.getPatch())){
            String maxPatch = tBoCheckResultSummaryService.getMaxPatch();
            tBoCheckResultSummaryVO.setPatch(maxPatch);
        }
        if("3".equals(tBoCheckResultSummaryVO.getBoType())){
            String lastPatch = tBoCheckResultSummaryService.getLastMaxPatch(tBoCheckResultSummaryVO.getPatch());
            if(!StringUtils.isNotEmpty(lastPatch)){
                return pageOk(new Page<TBoCheckResultSummary>());
            }
            IPage page = QueryVoToPageUtil.toPage(tBoCheckResultSummaryVO);
            TBoCheckTraceSummaryVO summaryVO = new TBoCheckTraceSummaryVO();
            summaryVO.setLevels(tBoCheckResultSummaryVO.getLevels());
            summaryVO.setCity(tBoCheckResultSummaryVO.getCity());
            summaryVO.setDistrict(tBoCheckResultSummaryVO.getDistrict());
            summaryVO.setLevels(tBoCheckResultSummaryVO.getLevels());
            summaryVO.setPatch(lastPatch);

            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate date = LocalDate.parse(lastPatch, inputFormatter);
            String formattedDate = date.format(outputFormatter);
            summaryVO.setCreateTime(formattedDate);

            if("3".equals(tBoCheckResultSummaryVO.getLevels())){
                summaryVO.setColumns("grid_cbss");
                summaryVO.setGroupColumns("city,district_cbss,grid_cbss");
                summaryVO.setGroupTotalColumns("b.city,b.district_cbss");
            }else if("2".equals(tBoCheckResultSummaryVO.getLevels())){
                summaryVO.setColumns("district_cbss");
                summaryVO.setGroupColumns("city,district_cbss");
                summaryVO.setGroupTotalColumns("b.city");
            }else{
                summaryVO.setColumns("city");
                summaryVO.setGroupColumns("city");
                summaryVO.setGroupTotalColumns("'1'");
            }
            IPage<TBoCheckTraceSummaryVO> summaryVOIPage = tBoCheckResultSummaryService.getBoSummaryList(page,summaryVO);
            return pageOk(summaryVOIPage);

        }
        IPage<TBoCheckResultSummary> list = tBoCheckResultSummaryService.findPage(tBoCheckResultSummaryVO);
        list.getRecords().forEach(item -> {
            item.setArea("");
        });
        return pageOk(list);
    }

    @GetMapping("/getPatchList")
    public BaseResponse<List<TBoCheckResultSummary>> getPatchList( ){
        TBoCheckResultSummary summary = new TBoCheckResultSummary();
        return ok(tBoCheckResultSummaryService.getPatchList(summary));
    }

    @GetMapping("/getDistrictList")
    public BaseResponse<List<String>> getDistrictList( TBoCheckResultSummaryQuery tBoCheckResultSummaryVO){
        TBoCheckResultSummary summary = new TBoCheckResultSummary();
        MallUser mallUser = UserUtils.getUser();

        if(StringUtils.isNotEmpty(tBoCheckResultSummaryVO.getCity())){
            summary.setCity(tBoCheckResultSummaryVO.getCity());
        }else{
            summary.setCity(mallUser.getCity());
        }

        return ok(tBoCheckResultSummaryService.getDistrictList(summary));
    }

    @GetMapping("/getGridList")
    public BaseResponse<List<String>> getGridList(TBoCheckResultSummaryQuery tBoCheckResultSummaryVO ){
        MallUser mallUser = UserUtils.getUser();
        TBoCheckResultSummary summary = new TBoCheckResultSummary();
        if(StringUtils.isNotEmpty(tBoCheckResultSummaryVO.getCity())){
            summary.setCity(tBoCheckResultSummaryVO.getCity());
        }else{
            summary.setCity(mallUser.getCity());
            if("2".equals(tBoCheckResultSummaryVO.getLevels()) || "3".equals(tBoCheckResultSummaryVO.getLevels())){
                summary.setCity(mallUser.getCity());
            }
        }
        if(StringUtils.isNotEmpty(tBoCheckResultSummaryVO.getDistrict())){
            summary.setDistrict(tBoCheckResultSummaryVO.getDistrict());
        }else{
            if("3".equals(tBoCheckResultSummaryVO.getLevels())){
                PermissionConfig config = new PermissionConfig();
                config.setOa(mallUser.getStaffId());// 工号
                List<PermissionConfig> list = permissionConfigService.getUserInfo(config);
                if(list.size()>0){
                    summary.setDistrict(list.get(0).getDistrictName());
                }
            }
        }
        return ok(tBoCheckResultSummaryService.getGridList(summary));
    }

    @PostMapping("/exportData")
    public void exportData(@RequestBody TBoCheckResultSummaryQuery tBoCheckResultSummaryVO) {
        tBoCheckResultSummaryVO.setPageNum(0);
        tBoCheckResultSummaryVO.setPageSize(10000);
        ExcelWriter excelWriter = null;
        try{
            tBoCheckResultSummaryVO.setBoType("1");
            BasePageResponse pageResponse1 = this.findPage(tBoCheckResultSummaryVO);
            List<Object> records1 = ((BasePageVO)pageResponse1.getData()).getRecords();
            List list1 = transferEntity(TBoCheckResultSummaryExcel.class.getName(),records1);

            tBoCheckResultSummaryVO.setBoType("2");
            BasePageResponse pageResponse2 = this.findPage(tBoCheckResultSummaryVO);
            List<Object> records2 = ((BasePageVO)pageResponse2.getData()).getRecords();
            List list2 = transferEntity(TBoCheckResultSummaryExcel.class.getName(),records2);

            tBoCheckResultSummaryVO.setBoType("3");
            BasePageResponse pageResponse3 = this.findPage(tBoCheckResultSummaryVO);
            List<Object> records3 = ((BasePageVO)pageResponse3.getData()).getRecords();
            List list3 = transferEntity(TBoCheckResultTraceSummaryExcel.class.getName(),records3);

            String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            excelWriter = ExcelUtils.initExcelWriter(TBoCheckResultSummaryExcel.class,"稽核跟踪报表"+dateStr);
            ExcelUtils.exportExcelMoreSheet(excelWriter,list1, TBoCheckResultSummaryExcel.class,"BO状态不一致报表",0,getHeader1(tBoCheckResultSummaryVO));
            ExcelUtils.exportExcelMoreSheet(excelWriter,list2, TBoCheckResultSummaryExcel.class,"BO速率不一致报表",1,getHeader1(tBoCheckResultSummaryVO));
            ExcelUtils.exportExcelMoreSheet(excelWriter,list3, TBoCheckResultTraceSummaryExcel.class,"汇总统计表",2,getHeader2(tBoCheckResultSummaryVO));
            ExcelUtils.closeExcelWriter(excelWriter);

        }catch (Exception e){
            log.error("系统异常:",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }

    }

    @GetMapping("/getMaxPatch")
    public BaseResponse getMaxPatch(){
        return ok(tBoCheckResultSummaryService.getMaxPatch());
    }

    private static List transferEntity(String classNameDes,List<?> record) {
        List list = Lists.newArrayList();
        record.forEach(item -> {
            try {
                Class clzD = Class.forName(classNameDes);
                Object des = clzD.newInstance();
                BeanUtil.copyProperties(item,des);
                list.add(des);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return list;
    }

    private List getHeader1(TBoCheckResultSummaryQuery tBoCheckResultSummaryVO){
        List<List<String>> headList = Lists.newArrayList();
        if("3".equals(tBoCheckResultSummaryVO.getLevels())){
            headList.add(Lists.newArrayList("营服"));
        }else if("2".equals(tBoCheckResultSummaryVO.getLevels())){
            headList.add(Lists.newArrayList("区县"));
        }else{
            headList.add(Lists.newArrayList("地市"));
        }
        headList.add(Lists.newArrayList("BO状态一致总数"));
        headList.add(Lists.newArrayList("BO状态不一致问题总数"));
        headList.add(Lists.newArrayList("状态一致率"));
        headList.add(Lists.newArrayList("环比"));
        headList.add(Lists.newArrayList("其中-互联网专线","BO状态一致总数"));
        headList.add(Lists.newArrayList("其中-互联网专线","BO状态不一致总数"));
        headList.add(Lists.newArrayList("其中-互联网专线","BO状态一致率"));
        headList.add(Lists.newArrayList("其中-互联网专线","环比"));
        headList.add(Lists.newArrayList("其中-数据网元","BO速率一致总数"));
        headList.add(Lists.newArrayList("其中-数据网元","BO速率不一致总数"));
        headList.add(Lists.newArrayList("其中-数据网元","速率一致率"));
        headList.add(Lists.newArrayList("其中-数据网元","环比"));
        return headList;
    }

    private List getHeader2(TBoCheckResultSummaryQuery tBoCheckResultSummaryVO){
        List<List<String>> headList = Lists.newArrayList();
        if("3".equals(tBoCheckResultSummaryVO.getLevels())){
            headList.add(Lists.newArrayList("营服"));
        }else if("2".equals(tBoCheckResultSummaryVO.getLevels())){
            headList.add(Lists.newArrayList("区县"));
        }else{
            headList.add(Lists.newArrayList("地市"));
        }
        headList.add(Lists.newArrayList("上一期问题派单反馈数"));
        headList.add(Lists.newArrayList("派单反馈率"));
        headList.add(Lists.newArrayList("上一期BO状态整改完成数"));
        headList.add(Lists.newArrayList("上一期BO状态整改完成率"));
        headList.add(Lists.newArrayList("上一期BO速率整改完成数"));
        headList.add(Lists.newArrayList("上一期BO速率整改完成率"));
        return headList;
    }











    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-02-13
     * @param id
     * @return BaseResponse<TBoCheckResultSummary>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TBoCheckResultSummary> findOne(String id) {
        return ok(tBoCheckResultSummaryService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-02-13
     * @param tBoCheckResultSummary
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TBoCheckResultSummary tBoCheckResultSummary){
        return ok(tBoCheckResultSummaryService.add(tBoCheckResultSummary));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-02-13
     * @param tBoCheckResultSummary
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TBoCheckResultSummary tBoCheckResultSummary) {
        return ok(tBoCheckResultSummaryService.update(tBoCheckResultSummary));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-02-13
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tBoCheckResultSummaryService.delete(id));
    }
}
