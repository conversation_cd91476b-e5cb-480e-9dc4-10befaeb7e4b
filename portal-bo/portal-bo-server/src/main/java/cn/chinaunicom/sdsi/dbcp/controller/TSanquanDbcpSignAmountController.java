package cn.chinaunicom.sdsi.dbcp.controller;

import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpSignAmountQueryVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.dbcp.service.TSanquanDbcpSignAmountService;
import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignAmount;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 等保测评签约明细(月) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
@RestController
@RequestMapping("/dbcp/signAmount")
public class TSanquanDbcpSignAmountController extends BaseController {

    @Autowired
    private TSanquanDbcpSignAmountService tSanquanDbcpSignAmountService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-03-17
     * @param tSanquanDbcpSignAmountVO
     * @return BasePageResponse<TSanquanDbcpSignAmount>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDbcpSignAmount> findPage(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo){
        if("全部".equals(tSanquanDbcpSignAmountVo.getCity())){
            tSanquanDbcpSignAmountVo.setCity(null);
        }
        return pageOk(tSanquanDbcpSignAmountService.findPage(tSanquanDbcpSignAmountVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-03-17
     * @param id
     * @return BaseResponse<TSanquanDbcpSignAmount>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanDbcpSignAmount> findOne(String id) {
        return ok(tSanquanDbcpSignAmountService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-03-17
     * @return BaseResponse<List<TSanquanDbcpSignAmount>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDbcpSignAmount>> findList(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo) {
        if("全部".equals(tSanquanDbcpSignAmountVo.getCity())){
            tSanquanDbcpSignAmountVo.setCity(null);
        }
        return ok(tSanquanDbcpSignAmountService.findList(tSanquanDbcpSignAmountVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-03-17
     * @param tSanquanDbcpSignAmount
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanDbcpSignAmount tSanquanDbcpSignAmount){
        return ok(tSanquanDbcpSignAmountService.add(tSanquanDbcpSignAmount));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-03-17
     * @param tSanquanDbcpSignAmount
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanDbcpSignAmount tSanquanDbcpSignAmount) {
        return ok(tSanquanDbcpSignAmountService.update(tSanquanDbcpSignAmount));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-03-17
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanDbcpSignAmountService.delete(id));
    }


    /**
     * 导出模版
     */
    @PostMapping("/exportTemplate")
    public void exportTemplate(){
        tSanquanDbcpSignAmountService.exportTemplate();
    }

    /**
     * 导入数据(上传excel文件)
     */
    @PostMapping("/uploadSignAmount")
    @Operation(summary = "导入数据", description = "导入数据")
    public BaseResponse<Map<String, Object>> uploadSignAmount(@RequestParam("file") MultipartFile file) {
        return ok(tSanquanDbcpSignAmountService.uploadSignAmount(file));
    }

    /**
     * 导出明细
     */
    @PostMapping("/exportData")
    @Operation(summary = "导出明细", description = "导出明细")
    public void exportData(@RequestBody TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo) {
        tSanquanDbcpSignAmountService.exportData(tSanquanDbcpSignAmountVo);
    }

}
