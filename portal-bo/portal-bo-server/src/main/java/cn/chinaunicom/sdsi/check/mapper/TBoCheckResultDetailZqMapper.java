package cn.chinaunicom.sdsi.check.mapper;

import cn.chinaunicom.sdsi.check.entity.TBoCheckResultDetailZq;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Mapper
public interface TBoCheckResultDetailZqMapper extends BaseMapper<TBoCheckResultDetailZq> {

    // 查询列表
    List<TBoCheckResultDetailZq> findList(@Param("query") TBoCheckResultDetailZq tBoCheckResultDetailZqVo);
}
