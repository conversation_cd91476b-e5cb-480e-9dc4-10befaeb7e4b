package cn.chinaunicom.sdsi.util.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;

/**
 * 自动换行单元格
 */
@Slf4j
public class MySheetWrapWriteHandler implements CellWriteHandler {

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {

        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        int cellStyleLen = workbook.getNumCellStyles();
        for(int i=0;i<cellStyleLen;i++){
            if(!isHead){
                // 设置自动换行
                workbook.getCellStyleAt(i).setWrapText(true);

                // 可选：设置垂直居中
                workbook.getCellStyleAt(i).setVerticalAlignment(VerticalAlignment.CENTER);
            }

        }
    }
}
