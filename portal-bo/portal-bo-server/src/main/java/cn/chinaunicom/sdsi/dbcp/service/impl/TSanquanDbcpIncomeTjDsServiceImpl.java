package cn.chinaunicom.sdsi.dbcp.service.impl;

import cn.chinaunicom.sdsi.check.vo.TBoCheckResultExcel;
import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeTjDs;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpIncomeTjDsQueryVo;
import cn.chinaunicom.sdsi.dataScope.annotation.DataScope;
import cn.chinaunicom.sdsi.dataScope.annotation.DataScopeQuery;
import cn.chinaunicom.sdsi.dbcp.mapper.TSanquanDbcpIncomeTjDsMapper;
import cn.chinaunicom.sdsi.dbcp.service.TSanquanDbcpIncomeTjDsService;
import cn.chinaunicom.sdsi.dbcp.vo.TSanquanDbcpIncomeTjDsExcelVo;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * DS等保测评收入完成情况统计（地市） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Service
public class TSanquanDbcpIncomeTjDsServiceImpl extends ServiceImpl<TSanquanDbcpIncomeTjDsMapper, TSanquanDbcpIncomeTjDs> implements TSanquanDbcpIncomeTjDsService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-18
     * @param tSanquanDbcpIncomeTjDs
     * @return IPage<TSanquanDbcpIncomeTjDs>
     **/
    @Override
    public IPage<TSanquanDbcpIncomeTjDs> findPage(TSanquanDbcpIncomeTjDsQueryVo tSanquanDbcpIncomeTjDsVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDbcpIncomeTjDsVo);
        return baseMapper.findPage(page, tSanquanDbcpIncomeTjDsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-18
     * @param id
     * @return TSanquanDbcpIncomeTjDs
     **/
    @Override
    public TSanquanDbcpIncomeTjDs findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-18
     * @return List<TSanquanDbcpIncomeTjDs>
     **/
    @Override
    public List<TSanquanDbcpIncomeTjDs> findList(TSanquanDbcpIncomeTjDsQueryVo tSanquanDbcpIncomeTjDsVo) {
        return baseMapper.findList(tSanquanDbcpIncomeTjDsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-18
     * @param tSanquanDbcpIncomeTjDs
     * @return int
     **/
    @Override
    public int add(TSanquanDbcpIncomeTjDs tSanquanDbcpIncomeTjDs) {
        return baseMapper.insert(tSanquanDbcpIncomeTjDs);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-18
     * @param tSanquanDbcpIncomeTjDs
     * @return int
     **/
    @Override
    public int update(TSanquanDbcpIncomeTjDs tSanquanDbcpIncomeTjDs) {
        return baseMapper.updateById(tSanquanDbcpIncomeTjDs);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-18
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 获取批次列表
     * @return
     */
    @Override
    public List<Map<String, String>> getAcctIdList() {
        return this.baseMapper.getAcctIdList();
    }

    /**
     * 获取最新账期
     * @return
     */
    @Override
    public String getMaxAcctId() {
        return this.baseMapper.getMaxAcctId();
    }

    /**
     * 导出数据
     * @param tSanquanDbcpIncomeTjDsVo
     */
    @Override
    public void exportData(TSanquanDbcpIncomeTjDsQueryVo tSanquanDbcpIncomeTjDsVo) {
        TSanquanDbcpIncomeTjDsServiceImpl proxy = (TSanquanDbcpIncomeTjDsServiceImpl) AopContext.currentProxy();
        List<TSanquanDbcpIncomeTjDs> list = proxy.findList(tSanquanDbcpIncomeTjDsVo);
        List<TSanquanDbcpIncomeTjDsExcelVo> collect = list.stream().map(item -> {
            TSanquanDbcpIncomeTjDsExcelVo tSanquanDbcpIncomeTjDsExcelVo = new TSanquanDbcpIncomeTjDsExcelVo();
            tSanquanDbcpIncomeTjDsExcelVo.setCity(item.getCity());
            if(item.getIncomeTarget() != null){
                tSanquanDbcpIncomeTjDsExcelVo.setIncomeTarget(item.getIncomeTarget().toString());
            }
            if(item.getProjectIncome() != null){
                tSanquanDbcpIncomeTjDsExcelVo.setProjectIncome(item.getProjectIncome().toString());
            }
            if(item.getProjectIncomeLastyear() != null){
                tSanquanDbcpIncomeTjDsExcelVo.setProjectIncomeLastyear(item.getProjectIncomeLastyear().toString());
            }
            if(item.getYearOnYearGrowthRate() != null){
                tSanquanDbcpIncomeTjDsExcelVo.setYearOnYearGrowthRate(item.getYearOnYearGrowthRate() + "%");
            }
            if(item.getTargetCompletionRate() != null){
                tSanquanDbcpIncomeTjDsExcelVo.setTargetCompletionRate(item.getTargetCompletionRate() + "%");
            }
            if(item.getSequentialCompletionRate() != null){
                tSanquanDbcpIncomeTjDsExcelVo.setSequentialCompletionRate(item.getSequentialCompletionRate() + "%");
            }
//            if(item.getRatetOrder() != null){
//                tSanquanDbcpIncomeTjDsExcelVo.setRatetOrder(item.getRatetOrder().toString());
//            }
            return tSanquanDbcpIncomeTjDsExcelVo;
        }).collect(Collectors.toList());
        // 从查询条件中提取acctId
        String acctId = tSanquanDbcpIncomeTjDsVo.getAcctId();
        // 生成动态标题
        String dynamicTitle = generateDynamicTitle(acctId);
        try {
            // 导出数据
            ExcelUtils.exportExcelTitle(collect, TSanquanDbcpIncomeTjDsExcelVo.class, "导出数据信息", "等保测评收入完成情况统计", dynamicTitle);
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException("导出数据失败");
        }
    }

    /**
     * 根据acctId生成动态标题
     * acctId格式示例：202502 -> 2025年等保测评项目新签约情况统计【2月1日-2月28日】
     */
    private String generateDynamicTitle(String acctId) {
        // 校验acctId格式
        if (acctId == null || acctId.length() != 6 || !acctId.matches("\\d{6}")) {
            throw new IllegalArgumentException("acctId格式错误，应为6位数字");
        }
        // 解析年份和月份
        int year = Integer.parseInt(acctId.substring(0, 4));
        int month = Integer.parseInt(acctId.substring(4, 6));
        // 校验月份有效性
        if (month < 1 || month > 12) {
            throw new IllegalArgumentException("acctId中包含无效月份");
        }
        // 生成日期范围
        String dateRange = generateDateRange(month);
        return String.format("%d年等保测评收入完成情况统计【%s】", year, dateRange);
    }

    /**
     * 根据月份生成日期范围
     */
    private String generateDateRange(int month) {
        Map<Integer, String> monthEndDay = new HashMap<>();
        monthEndDay.put(1, "31");
        monthEndDay.put(2, "28");
        monthEndDay.put(3, "31");
        monthEndDay.put(4, "30");
        monthEndDay.put(5, "31");
        monthEndDay.put(6, "30");
        monthEndDay.put(7, "31");
        monthEndDay.put(8, "31");
        monthEndDay.put(9, "30");
        monthEndDay.put(10, "31");
        monthEndDay.put(11, "30");
        monthEndDay.put(12, "31");

        return String.format("1月1日-%d月%s日",
                month,
                monthEndDay.getOrDefault(month, "30"));
    }


}
