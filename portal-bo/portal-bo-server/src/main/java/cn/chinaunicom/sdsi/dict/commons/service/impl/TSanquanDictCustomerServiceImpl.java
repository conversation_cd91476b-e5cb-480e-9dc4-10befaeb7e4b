package cn.chinaunicom.sdsi.dict.commons.service.impl;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictCustomer;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictCustomerQueryVo;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.dict.commons.mapper.TSanquanDictCustomerMapper;
import cn.chinaunicom.sdsi.dict.commons.service.TSanquanDictCustomerService;
import cn.chinaunicom.sdsi.natureCust.service.TSanquanSrcSdDZqztCustTNatureCustService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * 客户DICT数字档案客户 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
public class TSanquanDictCustomerServiceImpl extends ServiceImpl<TSanquanDictCustomerMapper, TSanquanDictCustomer> implements TSanquanDictCustomerService {

    @Autowired
    public UnifastContext unifastContext;

    @Autowired
    private TSanquanSrcSdDZqztCustTNatureCustService srcSdDZqztCustTNatureCustService;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-21
     * @param tSanquanDictCustomer
     * @return IPage<TSanquanDictCustomer>
     **/
    @Override
    public IPage<TSanquanDictCustomer> findPage(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDictCustomerVo);
        if (Boolean.TRUE.equals(tSanquanDictCustomerVo.getIsFull())) {
            return srcSdDZqztCustTNatureCustService.findPageAll(tSanquanDictCustomerVo);
        }
        return baseMapper.findPage(page, tSanquanDictCustomerVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-21
     * @param id
     * @return TSanquanDictCustomer
     **/
    @Override
    public TSanquanDictCustomer findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-21
     * @return List<TSanquanDictCustomer>
     **/
    @Override
    public List<TSanquanDictCustomer> findList(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo) {
        return baseMapper.findList(tSanquanDictCustomerVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-21
     * @param tSanquanDictCustomer
     * @return int
     **/
    @Override
    public int add(TSanquanDictCustomer tSanquanDictCustomer) {
        if(StrUtil.isEmpty(tSanquanDictCustomer.getCustomerId())){
            throw new ServiceErrorException("客户ID不能为空");
        }
        TSanquanDictCustomerQueryVo tSanquanDictCustomerQueryVo = new TSanquanDictCustomerQueryVo();
        tSanquanDictCustomerQueryVo.setCustomerId(tSanquanDictCustomer.getCustomerId());
        List<TSanquanDictCustomer> list = this.findList(tSanquanDictCustomerQueryVo);
        if(CollUtil.isNotEmpty(list) && list.size()>0){
            return 0;
        }
        // 查询地市信息
        if(StrUtil.isNotEmpty(tSanquanDictCustomer.getCustomerCity())){
            String cityCodeName = srcSdDZqztCustTNatureCustService.findCityName(tSanquanDictCustomer.getCustomerCity());
            tSanquanDictCustomer.setCustomerCity(cityCodeName);
        }
        return baseMapper.insert(tSanquanDictCustomer);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-21
     * @param tSanquanDictCustomer
     * @return int
     **/
    @Override
    public int update(TSanquanDictCustomer tSanquanDictCustomer) {
        return baseMapper.updateById(tSanquanDictCustomer);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-21
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
