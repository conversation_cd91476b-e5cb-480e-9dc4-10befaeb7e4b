package cn.chinaunicom.sdsi.dbcp.mapper;

import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeTjDs;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpIncomeTjDsQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * DS等保测评收入完成情况统计（地市） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Mapper
public interface TSanquanDbcpIncomeTjDsMapper extends BaseMapper<TSanquanDbcpIncomeTjDs> {

    // 分页查询
    IPage<TSanquanDbcpIncomeTjDs> findPage(@Param("page") IPage page, @Param("query") TSanquanDbcpIncomeTjDsQueryVo tSanquanDbcpIncomeTjDsVo);

    // 查询列表
    List<TSanquanDbcpIncomeTjDs> findList(@Param("query") TSanquanDbcpIncomeTjDsQueryVo tSanquanDbcpIncomeTjDsVo);

    // 获取批次列表
    List<Map<String, String>> getAcctIdList();

    // 获取最新账期
    String getMaxAcctId();
}
