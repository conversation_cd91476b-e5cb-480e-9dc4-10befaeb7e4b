package cn.chinaunicom.sdsi.check.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * BO稽核结果工单导出excel
 * </p>
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(30) // 标题单元格高度
public class TBoCheckResultOrderExcel  {

    private static final long serialVersionUID = 1L;

    /* 稽核批次 */
    @ExcelProperty("稽核批次")
    @ColumnWidth(15)
    String patch;

    /* 业务名称 */
    @ExcelProperty("业务名称")
    @ColumnWidth(20)
    String businessName;

    /* 业务号码 */
    @ExcelProperty("业务号码")
    @ColumnWidth(20)
    String businessCode;

    /* 产品名称 */
    @ExcelProperty("产品名称")
    @ColumnWidth(20)
    String productName;

    /* 受理系统，1，CBSS系统，2，政企云平台 */
    @ExcelProperty("受理系统")
    @ColumnWidth(20)
    String acceptSys;

    /* 电路代号 */
    @ExcelProperty("电路代号")
    @ColumnWidth(15)
    String circuit;

    /* 客户名称 */
    @ExcelProperty("客户名称")
    @ColumnWidth(20)
    String customerName;

    /* 区县CBSS */
    @ExcelProperty("区县")
    @ColumnWidth(15)
    String districtCbss;

    /* 发展渠道 */
    @ExcelProperty("发展渠道")
    @ColumnWidth(15)
    String channel;

    /* 发展人 */
    @ExcelProperty("发展人")
    @ColumnWidth(15)
    String development;

    /* 合同编码 */
    @ExcelProperty("合同编码")
    @ColumnWidth(20)
    String contractCode;

    /* 服务状态，1正常使用，2变更中，3已开通，4已激活，5拆机申请中 */
    @ExcelProperty("服务状态")
    @ColumnWidth(15)
    String serviceStatus;

    /* 速率CBSS */
/*    @ExcelProperty("速率CBSS")
    String rateCbss;*/

    /* 系统资费 */
    @ExcelProperty("系统资费")
    @ColumnWidth(15)
    String sysFee;

    /* A端装机地址CBSS */
    @ExcelProperty("A端装机地址CBSS")
    @ColumnWidth(20)
    String addressACbss;

    /* Z端装机地址CBSS */
    @ExcelProperty("Z端装机地址CBSS")
    @ColumnWidth(20)
    String addressZCbss;

    /* 开户时间 */
    @ExcelProperty("开户时间")
    @ColumnWidth(25)
    String accountOpenTime;

    /* 起租时间 */
    @ExcelProperty("起租时间")
    @ColumnWidth(25)
    String rentStartTime;

    /* 指派的整改责任人 */
    @ExcelProperty("整改责任人")
    @ColumnWidth(15)
    String rectificatePerson;

    /* 电话 */
    @ExcelProperty("整改责任人电话")
    @ColumnWidth(20)
    String telphone;

    /* 派单状态 ，0，未派单，1已派单*/
    @ExcelProperty("派单状态")
    @ColumnWidth(20)
    String status;

    /* 业务开通清单-网元 */
    @ExcelProperty("业务开通清单")
    @ColumnWidth(15)
    String businessDetail;

    /* 电路编码 */
    @ExcelProperty("电路编码")
    @ColumnWidth(15)
    String circuitCode;

    /* 区县-网元 */
    @ExcelProperty("区县")
    @ColumnWidth(15)
    String districtNet;

    /* A端装机地址-网元 */
    @ExcelProperty("A端装机地址")
    @ColumnWidth(20)
    String addressANet;

    /* Z端装机地址-网元 */
    @ExcelProperty("Z端装机地址")
    @ColumnWidth(20)
    String addressZNet;

    /* 业务范围 */
    @ExcelProperty("业务范围")
    @ColumnWidth(15)
    String businessArea;

    /* 是否测试用户 */
    @ExcelProperty("是否测试用户")
    @ColumnWidth(15)
    String isTestCustomer;

    /* 设备用户状态 */
    @ExcelProperty("设备用户状态")
    @ColumnWidth(15)
    String customerStatus;

    /* 速率-网元 */
    @ExcelProperty("速率")
    @ColumnWidth(15)
    String rateNet;

    /* BO状态是否一致，0，不一致，1一致 */
    @ExcelProperty("BO状态是否一致")
    @ColumnWidth(15)
    String boState;

    /* BO速率是否一致，0，不一致，1一致 */
    @ExcelProperty("BO速率是否一致")
    @ColumnWidth(15)
    String boRate;

    /* 上一期BO状态是否一致，0，不一致，1一致 */
    @ExcelProperty("上一期BO状态是否一致")
    @ColumnWidth(15)
    String boStateLast;

    /* 上一期BO速率是否一致，0，不一致，1一致 */
    @ExcelProperty("上一期BO速率是否一致")
    @ColumnWidth(15)
    String boRateLast;

    /* 累计BO状态不一致期数 */
    @ExcelProperty("累计BO状态不一致期数")
    @ColumnWidth(15)
    String boStateNum;

    /* 累计BO速率不一致期数 */
    @ExcelProperty("累计BO速率不一致期数")
    @ColumnWidth(15)
    String boRateNum;

    /* 工单状态 ，1已反馈，2未反馈, 3已转派*/
    @ExcelProperty("工单状态")
    @ColumnWidth(15)
    String orderStatus;

    public String getAcceptSys() {
        if("1".equals(this.acceptSys)){
            return "CBSS系统";
        }else if("2".equals(this.acceptSys)){
            return "政企云平台";
        }
        return acceptSys;
    }

    public String getServiceStatus() {
        if("1".equals(this.serviceStatus)){
            return "正常使用";
        }else if("2".equals(this.serviceStatus)){
            return "变更中";
        }else if("3".equals(this.serviceStatus)){
            return "已开通";
        }else if("4".equals(this.serviceStatus)){
            return "已激活";
        }else if("5".equals(this.serviceStatus)){
            return "拆机申请中";
        }
        return serviceStatus;
    }

    public String getStatus() {
        if("1".equals(this.status)){
            return "已派单";
        }else if("0".equals(this.status)){
            return "未派单";
        }
        return status;
    }

    public String getBoState() {
        if("1".equals(this.boState)){
            return "一致";
        }else if("0".equals(this.boState)){
            return "不一致";
        }
        return boState;
    }

    public String getBoRate() {
        if("1".equals(this.boRate)){
            return "一致";
        }else if("0".equals(this.boRate)){
            return "不一致";
        }
        return boRate;
    }

    public String getBoStateLast() {
        if("1".equals(this.boStateLast)){
            return "一致";
        }else if("0".equals(this.boStateLast)){
            return "不一致";
        }
        return boStateLast;
    }

    public String getBoRateLast() {
        if("1".equals(this.boRateLast)){
            return "一致";
        }else if("0".equals(this.boRateLast)){
            return "不一致";
        }
        return boRateLast;
    }

    public String getOrderStatus() {
        if("1".equals(this.orderStatus)){
            return "已反馈";
        }else if("2".equals(this.orderStatus)){
            return "未反馈";
        }else if("3".equals(this.orderStatus)){
            return "已派单";
        }
        return orderStatus;
    }

    // 隐藏业务号码
    public String getBusinessCode() {
        if (businessCode == null) {
            return businessCode;
        }
        if(businessCode.length() <= 7){
            return businessCode.substring(0, 3) + "****";
        }
        return businessCode.substring(0, 3) + "****" + businessCode.substring(businessCode.length() - 4);
    }
}
