package cn.chinaunicom.sdsi.prefectureInterfacePerson.controller;

import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity.TSanquanPrefectureInterfacePerson;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity.TSanquanPrefectureInterfaceRules;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.query.TSanquanPrefectureInterfacePersonQuery;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.query.TSanquanPrefectureInterfaceRulesQuery;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfaceRulesVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.platform.user.service.IUserService;
import cn.chinaunicom.sdsi.prefectureInterfacePerson.service.TSanquanPrefectureInterfacePersonService;
import cn.chinaunicom.sdsi.prefectureInterfacePerson.service.TSanquanPrefectureInterfaceRulesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 地市接口规则配置
*
* <AUTHOR> 
* @since  2024-05-30
*/
@RestController
@RequestMapping("/prefecture/interfaceRules")
@Tag(name="地市接口规则配置")
public class TSanquanPrefectureInterfaceRulesController extends BaseController {

    @Autowired
    private TSanquanPrefectureInterfaceRulesService interfaceRulesService;
    @Autowired
    private IUserService userService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since ${date}
     * @param TSanquanPrefectureInterfacePersonQuery
     * @return BasePageResponse<TSanquanPrefectureInterfacePerson>
     **/
    @GetMapping("/findPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<TSanquanPrefectureInterfaceRulesVO> findPage(TSanquanPrefectureInterfaceRulesQuery query){
        if("0".equals(query.getAuditScene())){
            query.setAuditScene(null);
        }
        return pageOk(interfaceRulesService.findPage(query));
    }

    @PostMapping("/addOrUpdate")
    @Operation(summary = "新增", description = "新增")
    public BaseResponse<String> addOrUpdate(@RequestBody TSanquanPrefectureInterfaceRules tSanquanPrefectureInterfacePersonVO){
        String id = interfaceRulesService.addOrUpdate(tSanquanPrefectureInterfacePersonVO);
        if(StringUtils.isNotEmpty(id)){
            return ok(id);
        }
        return notOk();
    }

    @GetMapping("/deleteById")
    @Operation(summary = "删除", description = "删除")
    public BaseResponse<Integer> deleteById(String id){
        return ok(interfaceRulesService.deleteById(id));
    }

    /**
     * 判断当前规则是否存在
     * @param listCustomerCity
     * @param listCustomerDistrict
     * @param auditScene
     * @return
     */
    @GetMapping("/verifyRulesExist")
    @Operation(summary = "verify", description = "verify")
    public BaseResponse<Boolean> verifyRulesExist(
            String listCustomerCity,
            String listCustomerDistrict,
            String auditScene,
            String id
    ){
        return ok(interfaceRulesService.verifyRulesExist(listCustomerCity, listCustomerDistrict, auditScene, id));
    }

}