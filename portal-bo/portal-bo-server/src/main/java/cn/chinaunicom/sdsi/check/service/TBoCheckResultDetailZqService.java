package cn.chinaunicom.sdsi.check.service;

import cn.chinaunicom.sdsi.check.entity.TBoCheckResultDetailZq;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
public interface TBoCheckResultDetailZqService extends IService<TBoCheckResultDetailZq> {

    // 根据id查询
    TBoCheckResultDetailZq findOne(String id);

    // 查询列表
    List<TBoCheckResultDetailZq> findList(TBoCheckResultDetailZq tBoCheckResultDetailZqVo);

    // 新增
    int add(TBoCheckResultDetailZq tBoCheckResultDetailZq);

    // 修改
    int update(TBoCheckResultDetailZq tBoCheckResultDetailZq);

    // 删除
    int delete(String id);

}
