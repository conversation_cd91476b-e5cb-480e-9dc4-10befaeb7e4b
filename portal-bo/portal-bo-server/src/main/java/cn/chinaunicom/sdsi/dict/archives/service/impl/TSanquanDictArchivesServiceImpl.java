package cn.chinaunicom.sdsi.dict.archives.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.dict.archives.entity.*;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesQueryVo;
import cn.chinaunicom.sdsi.cloud.dict.archives.vo.DictArchivesLogVo;
import cn.chinaunicom.sdsi.cloud.dict.archives.vo.TSanquanDictArchivesVo;
import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictDistrictCompanies;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictDistrictCompaniesQueryVo;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesMapper;
import cn.chinaunicom.sdsi.dict.archives.service.TSanquanDictArchivesBaseRegionService;
import cn.chinaunicom.sdsi.dict.archives.service.TSanquanDictArchivesNetwarkInfoService;
import cn.chinaunicom.sdsi.dict.archives.service.TSanquanDictArchivesService;
import cn.chinaunicom.sdsi.dict.commons.service.TSanquanDictDistrictCompaniesService;
import cn.chinaunicom.sdsi.file.service.MinioService;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 * 客户DICT数字档案 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
@Slf4j
public class TSanquanDictArchivesServiceImpl extends ServiceImpl<TSanquanDictArchivesMapper, TSanquanDictArchives> implements TSanquanDictArchivesService {

    @Autowired
    public UnifastContext unifastContext;

    @Autowired
    private TSanquanDictArchivesNetwarkInfoService netwarkInfoService;

    @Autowired
    private TSanquanDictArchivesBaseRegionService baseRegionService;

    @Autowired
    private TSanquanDictArchivesMiddlePlatformServiceImpl middlePlatformService;

    @Autowired
    private TSanquanDictArchivesBusinessSystemServiceImpl businessSystemService;

    @Autowired
    private TSanquanDictArchivesAiApplicationServiceImpl aiApplicationService;

    @Autowired
    private TSanquanDictArchivesTechnicalTeamServiceImpl technicalTeamService;

    @Autowired
    private MinioService minioService;

    @Autowired
    private TSanquanDictDistrictCompaniesService districtCompaniesService;


    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchives
     * @return IPage<TSanquanDictArchives>
     **/
    @Override
    public IPage<TSanquanDictArchives> findPage(TSanquanDictArchivesQueryVo tSanquanDictArchivesVo) {
        MallUser user = UserUtils.getUser();
        // 查询自己的按钮
        if("own".equals(tSanquanDictArchivesVo.getCreatedSign())){
            tSanquanDictArchivesVo.setCreateBy(user.getStaffId());
        }
        IPage page = QueryVoToPageUtil.toPage(tSanquanDictArchivesVo);
        IPage<TSanquanDictArchives> pageDb = baseMapper.findPage(page, tSanquanDictArchivesVo);
        List<TSanquanDictDistrictCompanies> districtCompanies = districtCompaniesService.findList(new TSanquanDictDistrictCompaniesQueryVo());
        pageDb.getRecords().forEach(archive -> {
            districtCompanies.forEach(dc -> {
                if (dc.getRegionsValue() != null &&
                        dc.getRegionsValue().equals(archive.getCountyCompany())) {
                    archive.setCountyCompany(dc.getRegionsLabel());
                }
            });
        });
        return pageDb;
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return TSanquanDictArchives
     **/
    @Override
    public TSanquanDictArchivesVo findOne(String id) {
        TSanquanDictArchives tSanquanDictArchives = baseMapper.selectById(id);
        TSanquanDictArchivesVo tSanquanDictArchivesVo = new TSanquanDictArchivesVo();
        BeanUtils.copyProperties(tSanquanDictArchives, tSanquanDictArchivesVo);
        tSanquanDictArchivesVo.setNetworkinfoList(netwarkInfoService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setBaseRegionList(baseRegionService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setMiddlePlatformList(middlePlatformService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setBusinessSystemList(businessSystemService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setAiApplicationList(aiApplicationService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setTechnicalTeamList(technicalTeamService.findListByArchivesId(id));
        return tSanquanDictArchivesVo;
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @return List<TSanquanDictArchives>
     **/
    @Override
    public List<TSanquanDictArchives> findList(TSanquanDictArchivesQueryVo tSanquanDictArchivesVo) {
        return baseMapper.findList(tSanquanDictArchivesVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchives
     * @return int
     **/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addOrUpdate(TSanquanDictArchivesVo tSanquanDictArchivesVo) {
        // 判断操作类型
        boolean isAdd = StrUtil.isEmpty(tSanquanDictArchivesVo.getId());
        String operateType = isAdd ? "新增" : "修改";
        // 创建人组织添加
        if(isAdd){
            tSanquanDictArchivesVo.setCreateByOrg(UserUtils.getUser().getOrgName());
        }

        // 同一个客户只能录入一次，已录入为修改
        if(StrUtil.isEmpty(tSanquanDictArchivesVo.getId())){
            TSanquanDictArchives dbCustomer = this.getOne(Wrappers.<TSanquanDictArchives>lambdaQuery()
                    .eq(TSanquanDictArchives::getCustomerId, tSanquanDictArchivesVo.getCustomerId())
                    .eq(TSanquanDictArchives::getDeleteFlag, "normal")
            );
            if (dbCustomer != null) {
                tSanquanDictArchivesVo.setId(dbCustomer.getId());
            }
        }
        // 1. 保存主表信息
        TSanquanDictArchives archives = tSanquanDictArchivesVo.convertVo(tSanquanDictArchivesVo);
        boolean success = this.saveOrUpdate(archives);
        if (!success) {
            throw new ServiceErrorException("保存档案主表信息失败");
        }
        String archivesId = archives.getId();
        // 2. 处理关联表信息
        processNetworkInfo(archivesId, tSanquanDictArchivesVo.getNetworkinfoList());
        processBaseRegion(archivesId, tSanquanDictArchivesVo.getBaseRegionList());
        processMiddlePlatform(archivesId, tSanquanDictArchivesVo.getMiddlePlatformList());
        processBusinessSystem(archivesId, tSanquanDictArchivesVo.getBusinessSystemList());
        processAiApplication(archivesId, tSanquanDictArchivesVo.getAiApplicationList());
        processTechnicalTeam(archivesId, tSanquanDictArchivesVo.getTechnicalTeamList());
        // 添加日志
        addDictArchivesLog(archives, operateType);
        return success;
    }

    // 添加日志信息
    private void addDictArchivesLog(TSanquanDictArchives archives, String operateType) {
        MallUser user = UserUtils.getUser();
        DictArchivesLogVo logVo = new DictArchivesLogVo();
        logVo.setOperate(operateType);
        logVo.setArchivesId(archives.getId());
        logVo.setCustomerId(archives.getCustomerId());
        logVo.setCustomerName(archives.getCustomerName());
        logVo.setUpdateBy(user.getStaffId());
        logVo.setUpdateName(user.getStaffName());
        logVo.setUpdateDate(DateUtils.formatDateTime(new Date()));
        baseMapper.saveDictArchivesLog(logVo);
    }

    // 处理网络信息
    private void processNetworkInfo(String archivesId, List<TSanquanDictArchivesNetwarkInfo> list) {
        netwarkInfoService.deleteByArchivesId(archivesId);
        list = list.stream()
            .filter(item -> StrUtil.isNotEmpty(item.getOperator()))
            .map(item -> {
                item.setArchivesId(archivesId);
                item.setId(null);
                return item;
            })
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) return;
        netwarkInfoService.insertBatchSomeColumn(list);
    }

    // 处理基础区域信息
    private void processBaseRegion(String archivesId, List<TSanquanDictArchivesBaseRegion> list) {
        baseRegionService.deleteByArchivesId(archivesId);
        list = list.stream()
            .filter(item -> StrUtil.isNotEmpty(item.getCloudType()))
            .map(item -> {
                item.setArchivesId(archivesId);
                item.setId(null);
                return item;
            })
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) return;
        baseRegionService.insertBatchSomeColumn(list);
    }

    // 处理中台平台信息
    private void processMiddlePlatform(String archivesId, List<TSanquanDictArchivesMiddlePlatform> list) {
        middlePlatformService.deleteByArchivesId(archivesId);
        list = list.stream()
            .filter(item -> StrUtil.isNotEmpty(item.getPlatformType()))
            .map(item -> {
                item.setArchivesId(archivesId);
                item.setId(null);
                return item;
            })
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) return;
        middlePlatformService.insertBatchSomeColumn(list);
    }

    // 处理业务系统信息
    private void processBusinessSystem(String archivesId, List<TSanquanDictArchivesBusinessSystem> list) {
        businessSystemService.deleteByArchivesId(archivesId);
        list = list.stream()
            .filter(item -> StrUtil.isNotEmpty(item.getApplyName()))
            .map(item -> {
                item.setArchivesId(archivesId);
                item.setId(null);
                return item;
            })
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) return;
        businessSystemService.insertBatchSomeColumn(list);
    }

    // 处理AI应用场景信息
    private void processAiApplication(String archivesId, List<TSanquanDictArchivesAiApplication> list) {
        aiApplicationService.deleteByArchivesId(archivesId);
        list = list.stream()
            .filter(item -> StrUtil.isNotEmpty(item.getApplicationScenario()))
            .map(item -> {
                item.setArchivesId(archivesId);
                item.setId(null);
                return item;
            })
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) return;
        aiApplicationService.insertBatchSomeColumn(list);
    }

    // 处理技术团队信息
    private void processTechnicalTeam(String archivesId, List<TSanquanDictArchivesTechnicalTeam> list) {
        technicalTeamService.deleteByArchivesId(archivesId);
        list = list.stream()
            .filter(item -> StrUtil.isNotEmpty(item.getTeamType()))
            .map(item -> {
                item.setArchivesId(archivesId);
                item.setId(null);
                return item;
            })
            .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) return;
        technicalTeamService.insertBatchSomeColumn(list);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictArchives
     * @return int
     **/
    @Override
    public int update(TSanquanDictArchives tSanquanDictArchives) {
        return baseMapper.updateById(tSanquanDictArchives);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        if(StrUtil.isEmpty(id)){
            throw new ServiceErrorException("id不能为空");
        }
        TSanquanDictArchives dbCustomer = this.getById(id);
        // 删除其他信息
        netwarkInfoService.deleteByArchivesId(id);
        baseRegionService.deleteByArchivesId(id);
        middlePlatformService.deleteByArchivesId(id);
        businessSystemService.deleteByArchivesId(id);
        aiApplicationService.deleteByArchivesId(id);
        technicalTeamService.deleteByArchivesId(id);
        // 删除附件信息
        if(StrUtil.isNotEmpty(dbCustomer.getAttachmentId())){
            String[] split = dbCustomer.getAttachmentId().split(",");
            for (String s : split) {
                minioService.deleteFile(s);
            }
        }
        // 删除日志信息
        addDictArchivesLog(dbCustomer, "删除");
        return baseMapper.deleteById(id);
    }

    /**
     * 根据客户id查询档案信息
     * @param customerId
     * @return
     */
    @Override
    public TSanquanDictArchivesVo getInfoByCustomerId(String customerId) {
        if(StrUtil.isEmpty(customerId)){
            throw new ServiceErrorException("客户id不能为空");
        }
        TSanquanDictArchivesVo tSanquanDictArchivesVo = baseMapper.getInfoByCustomerId(customerId);
        if (tSanquanDictArchivesVo == null) {
            return null;
        }
        String id = tSanquanDictArchivesVo.getId();
        tSanquanDictArchivesVo.setNetworkinfoList(netwarkInfoService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setBaseRegionList(baseRegionService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setMiddlePlatformList(middlePlatformService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setBusinessSystemList(businessSystemService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setAiApplicationList(aiApplicationService.findListByArchivesId(id));
        tSanquanDictArchivesVo.setTechnicalTeamList(technicalTeamService.findListByArchivesId(id));
        return tSanquanDictArchivesVo;
    }

    /**
     * 导入数据
     * @param file
     * @return
     */
    @Override
    public Map<String, Object> importData(MultipartFile file) {
        Map<String, Object> failureDetails = new HashMap<>();
        try {
            ExcelDataListener listener = new ExcelDataListener();
            EasyExcel.read(file.getInputStream(), listener).sheet().doRead();
            List<Map<String, Object>> dataList = listener.getDataList();
            failureDetails.put("excelList", dataList);
        } catch (IOException e) {
            failureDetails.put("error", "文件读取失败: " + e.getMessage());
            throw new RuntimeException(e);
        }
        return failureDetails;
    }

    // 处理 Excel 数据的监听器
    public static class ExcelDataListener extends AnalysisEventListener<Map<Integer, String>> {
        private final List<Map<String, Object>> dataList = new ArrayList<>();
        private List<String> headList;
        @Override
        public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
            headList = new ArrayList<>();
            for (int i = 0; i < headMap.size(); i++) {
                headList.add(headMap.get(i));
            }
        }
        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            Map<String, Object> rowData = new HashMap<>();
            for (int i = 0; i < data.size(); i++) {
                rowData.put(headList.get(i), data.get(i));
            }
            dataList.add(rowData);
        }
        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            // 所有数据解析完成后的操作
        }
        public List<Map<String, Object>> getDataList() {
            return dataList;
        }
    }

}
