package cn.chinaunicom.sdsi.check.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 *  BO稽核结果展示
 * </p>
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@TableName("t_bo_check_result")
public class TBoCheckResult implements Serializable {

    private static final long serialVersionUID = 1L;

    /*  */
    String id;

    /* 稽核结果编码 */
    String code;

    /* 稽核批次 */
    String patch;

    /* 业务号码 */
    String businessCode;

    /* 派单状态 ，0，未派单，1已派单*/
    String status;

    /* 指派的整改责任人 */
    String rectificatePerson;

    /* 电话 */
    String telphone;

    /* oa工号 */
    String jobNum;

    /* 地市 */
    String city;

    /* 业务名称 */
    String businessName;

    /* 产品名称 */
    String productName;

    /* 受理系统，1，CBSS系统，2，政企云平台 */
    String acceptSys;

    /* 电路代号 */
    String circuit;

    /* 客户名称 */
    String customerName;

    /* 区县CBSS */
    String districtCbss;

    /* 营服CBSS */
    String gridCbss;

    /* 发展渠道 */
    String channel;

    /* 发展人 */
    String development;

    /* 发展人电话 */
    String developmentTel;

    /* 发展人工号 */
    String developmentOa;

    /* 合同编码 */
    String contractCode;

    /* 服务状态，1正常使用，2变更中，3已开通，4已激活，5拆机申请中 */
    String serviceStatus;

    /* 速率CBSS */
    String rateCbss;

    /* 系统资费 */
    String sysFee;

    /* A端装机地址CBSS */
    String addressACbss;

    /* Z端装机地址CBSS */
    String addressZCbss;

    /* 开户时间 */
    String accountOpenTime;

    /* 起租时间 */
    String rentStartTime;

    /* 注销/拆机时间 */
    String cancelTime;

    /* 计费状态最后更新时间 */
    String feeTime;

    /* 业务开通清单-网元 */
    String businessDetail;

    /* 电路编码 */
    String circuitCode;

    /* 区县-网元 */
    String districtNet;

    /* 营服CBSS */
    String gridNet;

    /* A端装机地址-网元 */
    String addressANet;

    /* Z端装机地址-网元 */
    String addressZNet;

    /* 业务范围 */
    String businessArea;

    /* 是否测试用户 */
    String isTestCustomer;

    /* 设备用户状态 */
    String customerStatus;

    /* 速率-网元 */
    String rateNet;

    /* 业务类型，1互联网专线，2数据网元 */
    String businessType;

    /* BO状态是否一致，0，不一致，1一致 */
    String boState;

    /* BO速率是否一致，0，不一致，1一致 */
    String boRate;

    /* 上一期BO状态是否一致，0，不一致，1一致 */
    String boStateLast;

    /* 上一期BO速率是否一致，0，不一致，1一致 */
    String boRateLast;

    /* 累计BO状态不一致期数 */
    String boStateNum;

    /* 累计BO速率不一致期数 */
    String boRateNum;

    /* 备注 */
    String remark;

    /* 派单时间 */
    String sendTime;

    /* 创建时间 */
    String createTime;

    /* 推送人 */
    String pushName;

    /* 推送人工号 */
    String pushOa;

    /* 推送人电话 */
    String pushTel;

    // 工单id
    String resultOrder;

    // 落格人oa
    String gridManagerOa;

    // 落格人姓名
    String gridManager;

    // 落格人手机号
    String gridManagerTel;
}
