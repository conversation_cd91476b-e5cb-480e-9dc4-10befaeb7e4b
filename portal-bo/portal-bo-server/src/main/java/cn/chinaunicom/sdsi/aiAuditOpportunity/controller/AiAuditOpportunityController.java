package cn.chinaunicom.sdsi.aiAuditOpportunity.controller;

import cn.chinaunicom.sdsi.aiAuditOpportunity.service.AiAuditOpportunityService;
import cn.chinaunicom.sdsi.aiAuditOpportunity.vo.AiAuditOpportunityQueryVO;
import cn.chinaunicom.sdsi.aiAuditOpportunity.vo.AiAuditOpportunityVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


@Slf4j
@RestController
@RequestMapping("aiAuditOpportunity")
public class AiAuditOpportunityController extends BaseController {

    @Autowired
    private AiAuditOpportunityService aiAuditOpportunityService;

//    @Scheduled(cron = "0 30 22 10 6 ?")
    public void handle2025BusinessOppo() {
        log.error("【handle2025BusinessOppo】任务开始执行");
        for (int i = 0; i < 5; i++) {
            aiAuditOpportunityService.markBusinessOpportunity(i);
        }
        log.error("【handle2025BusinessOppo】任务已执行");
    }

    /**
     * 分页查询
     * @param vo 查询条件
     * @return BasePageResponse<AiAuditOpportunityVO>
     */
    @Operation(summary = "分页查询AI稽核商机", description ="分页查询AI稽核商机")
    @GetMapping("/findPage")
    public BasePageResponse<AiAuditOpportunityVO> findPage(AiAuditOpportunityQueryVO vo) {
        return pageOk(aiAuditOpportunityService.findPage(vo));
    }


    /**
     * 详情页
     * @param id
     * @return
     */
    @Operation(summary = "详情查询AI稽核商机")
    @GetMapping("/details/{id}")
    public BaseResponse<AiAuditOpportunityVO> details(@PathVariable String id) {
        return ok(aiAuditOpportunityService.details(id));
    }


    @Scheduled(cron = "0 0 7 * * ?")
    public void syncOpportunities() {
        log.error("【syncOpportunities】任务开始执行");
        aiAuditOpportunityService.syncOpportunities();
        log.error("【syncOpportunities】同步完成，开始执行稽核");
//        for (int i = 0; i < 10; i++) {
//            aiAuditOpportunityService.markBusinessOpportunity(i);
//        }
    }



}
