package cn.chinaunicom.sdsi.common.sms;

import cn.chinaunicom.sdsi.common.http.UniHttpClient;
import cn.chinaunicom.sdsi.common.sms.entity.IndustrySmsReq;
import cn.chinaunicom.sdsi.common.sms.entity.SmsReqBssBody;
import cn.chinaunicom.sdsi.common.sms.entity.SmsTransmitReq;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/8/16 9:19
 */

@Slf4j
@Component
public class SendSms {

    @Value("${sms.link}")
    private String sendSmsLink;

    @Value("${sms.appId}")
    private String APP_ID;

    @Value("${sms.appSecret}")
    private String APP_SECRET;

    /* 指挥仓的appID appSecret 用于UNI_BSS_HEAD中*/
    @Value("${sms.zhcAppId}")
    private String ZHC_APP_ID;

    @Value("${sms.zhcAppSecret}")
    private String ZHC_APP_SECRET;
    private static Random rand = new Random();
    public Object sendSmsContent(String smsPhone, String content) {
        try {
            JSONObject rspObject=new JSONObject();
            Map<String, Object> map = new HashMap<>();
            Map<String, Object> UNI_BSS_BODY = new HashMap<>();
            Map<String, Object> UNI_BSS_HEAD = new HashMap<>();
            Map<String, Object> SMSINDUSTRY_REQ = new HashMap<>();
            Map<String, Object> INDUSTRY_SMS_REQ = new HashMap<>();

            // 请求体参数
            INDUSTRY_SMS_REQ.put("APP_ID", APP_ID);
            INDUSTRY_SMS_REQ.put("APP_SECRET", APP_SECRET);
            INDUSTRY_SMS_REQ.put("MSG_TYPE", "9");
            INDUSTRY_SMS_REQ.put("CIP", "false");
            INDUSTRY_SMS_REQ.put("MOBILE_NUMBER", smsPhone);
            INDUSTRY_SMS_REQ.put("CONTENT", content);

            SMSINDUSTRY_REQ.put("INDUSTRY_SMS_REQ", INDUSTRY_SMS_REQ);
            UNI_BSS_BODY.put("SMSINDUSTRY_REQ", SMSINDUSTRY_REQ);

            // 当前时间戳
            String timestamp = getCurrentTimestamp();
            // 随机数
            String randomNumber = generateRandomNumber();
            // 构造TRANS_ID
            String transId = timestamp.replace("-", "").replace(" ", "").replace(":", "").substring(0, 17) + randomNumber;
            // ZHC_APP_ID 和 ZHC_APP_SECRET
            String zhcAppId = ZHC_APP_ID;
            String zhcAppSecret = ZHC_APP_SECRET;
            // 构造TOKEN
            String token = generateToken(zhcAppId, timestamp, transId, zhcAppSecret);
            // 请求HEAD中的参数
            UNI_BSS_HEAD.put("APP_ID", zhcAppId);
            UNI_BSS_HEAD.put("TIMESTAMP", timestamp);
            UNI_BSS_HEAD.put("TRANS_ID", transId);
            UNI_BSS_HEAD.put("TOKEN", token);

            map.put("UNI_BSS_BODY", UNI_BSS_BODY);
            map.put("UNI_BSS_HEAD", UNI_BSS_HEAD);
            // 在这里打印 map 的结构
            System.err.println("请求参数: " + map);

            System.err.println("发送短信地址：" + sendSmsLink);
            String str = UniHttpClient.postDataSms(sendSmsLink, map);
            System.err.println("发送短信返回的参数：" + str);
            JSONObject smsRspBodyObj = JSONObject.parseObject(str);
            System.err.println("解析返回的数据：" + smsRspBodyObj);
            // 如果返回的结果为空
            if (smsRspBodyObj == null) {
                rspObject.put("RSP_CODE", "9999");
                rspObject.put("RSP_DESC", "发送短信返回结果转换异常");
            } else {
                JSONObject bssBody = smsRspBodyObj.getJSONObject("UNI_BSS_BODY");
                System.err.println("解析返回的数据UNI_BSS_BODY：" + bssBody);
                if (bssBody != null) {
                    JSONObject rsp = bssBody.getJSONObject("SMSINDUSTRY_RSP");
                    System.err.println("解析返回的数据SMSINDUSTRY_RSP：" + rsp);
                    if (rsp != null) {
                        // 获取 RSP 节点
                        JSONObject rspNode = rsp.getJSONObject("RSP");
                        System.err.println("解析返回的数据RSP节点：" + rspNode);
                        if (rspNode != null) {
                            // 获取 DATA 节点的内容
                            JSONObject data = rspNode.getJSONObject("DATA");
                            System.err.println("解析返回的数据DATA：" + data);
                            String rspCode = rspNode.getString("RSP_CODE");
                            System.err.println("解析返回的数据RSP_CODE：" + rspCode);
                            String rspDesc = rspNode.getString("RSP_DESC");
                            if ("0000".equals(rspCode)) {
                                rspObject.put("RSP_CODE", "0000");
                                rspObject.put("RSP_DESC", "成功");
                                rspObject.put("DATA", data);
                            } else {
                                rspObject.put("RSP_CODE", "9999");
                                rspObject.put("RSP_DESC", "错误代码：" + rspCode + " 描述：" + rspDesc);
                            }
                        } else {
                            rspObject.put("RSP_CODE", "9999");
                            rspObject.put("RSP_DESC", "RSP节点中的RSP字段为空");
                        }
                    } else {
                        rspObject.put("RSP_CODE", "9999");
                        rspObject.put("RSP_DESC", "RSP节点为空");
                    }
                } else {
                    rspObject.put("RSP_CODE", "9999");
                    rspObject.put("RSP_DESC", "返回的UNI_BSS_BODY节点为空");
                }
            }
            // Debug日志
            if (log.isDebugEnabled()) {
                log.debug("smsID:{}, 请求流水:{}, 发送短信结束", smsPhone);
            }
            return rspObject;
        }catch (Exception e) {
            log.error("smsID:{},发送短信失败,{}", smsPhone, e.getMessage(), e);
            return e.getMessage();
        }
    }

    // 获取当前时间戳，格式为"yyyy-MM-dd hh:mm:ss SSS"
    private static String getCurrentTimestamp() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss SSS");
        return sdf.format(new Date());
    }

    // 生成6位随机数
    private static String generateRandomNumber() {
        int randomNum = rand.nextInt(1000000); // 生成[0, 999999]之间的随机数
        return String.format("%06d", randomNum); // 格式化为6位数字
    }

    // 生成TOKEN，使用APP_ID + abc + TIMESTAMP + TRANS_ID + APP_SECRET进行MD5加密
    private static String generateToken(String appId, String timestamp, String transId, String appSecret) throws NoSuchAlgorithmException {
        String stringToEncrypt = "APP_ID" + appId + "TIMESTAMP" + timestamp + "TRANS_ID" + transId + appSecret;
        // 使用MD5加密
        System.err.println( "加密之前的信息"+  stringToEncrypt);
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(stringToEncrypt.getBytes());
        byte[] digest = md.digest();
        // 将MD5字节数组转换为16进制字符串，并确保结果为32位小写
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b & 0xff));
        }
        return sb.toString();
    }

}
