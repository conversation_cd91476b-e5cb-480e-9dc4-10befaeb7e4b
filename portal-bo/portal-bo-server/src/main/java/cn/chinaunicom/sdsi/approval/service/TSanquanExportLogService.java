package cn.chinaunicom.sdsi.approval.service;

import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportLog;
import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportLogQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
/**
 * <p>
 * 导出日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
public interface TSanquanExportLogService extends IService<TSanquanExportLog> {

    // 分页查询
    IPage<TSanquanExportLog> findPage(TSanquanExportLogQueryVo tSanquanExportLogVo);

    // 根据id查询
    TSanquanExportLog findOne(String id);

    // 查询列表
    List<TSanquanExportLog> findList(TSanquanExportLogQueryVo tSanquanExportLogVo);

    // 新增
    int add(TSanquanExportLog tSanquanExportLog, HttpServletRequest request);

    // 修改
    int update(TSanquanExportLog tSanquanExportLog);

    // 删除
    int delete(String id);

}
