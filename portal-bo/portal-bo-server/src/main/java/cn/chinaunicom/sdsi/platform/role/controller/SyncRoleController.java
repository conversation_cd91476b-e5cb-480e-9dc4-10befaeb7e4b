package cn.chinaunicom.sdsi.platform.role.controller;


import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.platform.role.entity.*;
import cn.chinaunicom.sdsi.platform.role.service.IRoleMenuService;
import cn.chinaunicom.sdsi.platform.role.service.IRoleService;
import cn.chinaunicom.sdsi.platform.role.service.SyncRoleService;
import cn.chinaunicom.sdsi.platform.user.entity.UserRole;
import cn.chinaunicom.sdsi.platform.user.service.IUserRoleService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  同步中台下发角色人员信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@RestController
@RequestMapping("/syncRole")
public class SyncRoleController extends BaseController {
    @Autowired
    private SyncRoleService syncRoleService;


}
