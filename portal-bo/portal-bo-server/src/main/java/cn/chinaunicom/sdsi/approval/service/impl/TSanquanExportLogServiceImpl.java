package cn.chinaunicom.sdsi.approval.service.impl;

import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportLog;
import cn.chinaunicom.sdsi.approval.mapper.TSanquanExportLogMapper;
import cn.chinaunicom.sdsi.approval.service.TSanquanExportLogService;
import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportLogQueryVo;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
/**
 * <p>
 * 导出日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
public class TSanquanExportLogServiceImpl extends ServiceImpl<TSanquanExportLogMapper, TSanquanExportLog> implements TSanquanExportLogService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportLog
     * @return IPage<TSanquanExportLog>
     **/
    @Override
    public IPage<TSanquanExportLog> findPage(TSanquanExportLogQueryVo tSanquanExportLogVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanExportLogVo);
        return baseMapper.findPage(page, tSanquanExportLogVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param id
     * @return TSanquanExportLog
     **/
    @Override
    public TSanquanExportLog findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @return List<TSanquanExportLog>
     **/
    @Override
    public List<TSanquanExportLog> findList(TSanquanExportLogQueryVo tSanquanExportLogVo) {
        return baseMapper.findList(tSanquanExportLogVo);
    }

    /*
     * <AUTHOR>
     * @Description 新增
     * @Date 2025-06-10
     * @param tSanquanExportLog
     * @return int
     **/
    @Override
    public int add(TSanquanExportLog tSanquanExportLog, HttpServletRequest request) {
        MallUser user = UserUtils.getUser();
        tSanquanExportLog.setOperatorId(user.getStaffId());
        tSanquanExportLog.setOperatorName(user.getStaffName());
        tSanquanExportLog.setOperationType("3");
        tSanquanExportLog.setOperationTime(DateUtils.formatDateTime(new Date()));
        tSanquanExportLog.setCreateTime(DateUtils.formatDateTime(new Date()));
        tSanquanExportLog.setIpAddress(request.getRemoteAddr());
        return baseMapper.insert(tSanquanExportLog);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param tSanquanExportLog
     * @return int
     **/
    @Override
    public int update(TSanquanExportLog tSanquanExportLog) {
        return baseMapper.updateById(tSanquanExportLog);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-10
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
