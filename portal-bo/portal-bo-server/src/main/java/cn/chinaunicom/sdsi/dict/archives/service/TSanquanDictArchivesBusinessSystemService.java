package cn.chinaunicom.sdsi.dict.archives.service;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesBusinessSystem;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesBusinessSystemQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
/**
 * <p>
 * 业务系统建设现状 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
public interface TSanquanDictArchivesBusinessSystemService extends IService<TSanquanDictArchivesBusinessSystem> {

    // 分页查询
    IPage<TSanquanDictArchivesBusinessSystem> findPage(TSanquanDictArchivesBusinessSystemQueryVo tSanquanDictArchivesBusinessSystemVo);

    // 根据id查询
    TSanquanDictArchivesBusinessSystem findOne(String id);

    // 查询列表
    List<TSanquanDictArchivesBusinessSystem> findList(TSanquanDictArchivesBusinessSystemQueryVo tSanquanDictArchivesBusinessSystemVo);

    // 新增
    int add(TSanquanDictArchivesBusinessSystem tSanquanDictArchivesBusinessSystem);

    // 修改
    int update(TSanquanDictArchivesBusinessSystem tSanquanDictArchivesBusinessSystem);

    // 删除
    int delete(String id);

    // 批量新增
    int insertBatchSomeColumn(List<TSanquanDictArchivesBusinessSystem> middlePlatformList);

    // 根据档案id删除
    Boolean deleteByArchivesId(String archivesId);

    // 根据档案id查询
    List<TSanquanDictArchivesBusinessSystem> findListByArchivesId(String id);

}
