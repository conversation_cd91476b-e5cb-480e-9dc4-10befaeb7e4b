package cn.chinaunicom.sdsi.permissionConfig.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *  系统数据权限细化粒度补充表配置
 * </p>
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@TableName("t_sanquan_permission_config")
public class PermissionConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /*  */
    String id;

    /* 人员姓名 */
    String userName;

    /* 人员oa工号 */
    String oa;

    /* 手机号 */
    String phone;

    /* 层级，1地市，2区县，3营服，4其它 */
    String level;

    /* 是否客户经理，1是，0否 */
    String isExecutor;

    /* 营服机构号 */
    String gridCode;

    /* 营服名称 */
    String gridName;

    /* 区县code */
    String districtCode;

    /* 所属区县 */
    String districtName;

    /* 地市code */
    String city;

    /* 所属地市 */
    String cityName;

    /* 创建时间 */
    String createTime;

    /*  */
    String updateTime;

}
