package cn.chinaunicom.sdsi.approval.controller;

import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportApprovalUserQueryVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalUserService;
import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApprovalUser;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 导出审批用户表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/approval/exportUser")
public class TSanquanExportApprovalUserController extends BaseController {

    @Autowired
    private TSanquanExportApprovalUserService tSanquanExportApprovalUserService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-06-10
     * @param tSanquanExportApprovalUserVO
     * @return BasePageResponse<TSanquanExportApprovalUser>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanExportApprovalUser> findPage(TSanquanExportApprovalUserQueryVo tSanquanExportApprovalUserVo){
        if("全部".equals(tSanquanExportApprovalUserVo.getCity())){
            tSanquanExportApprovalUserVo.setCity(null);
        }
        if("全部".equals(tSanquanExportApprovalUserVo.getModuleCode())){
            tSanquanExportApprovalUserVo.setModuleCode(null);
        }
        return pageOk(tSanquanExportApprovalUserService.findPage(tSanquanExportApprovalUserVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-06-10
     * @param id
     * @return BaseResponse<TSanquanExportApprovalUser>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanExportApprovalUser> findOne(String id) {
        return ok(tSanquanExportApprovalUserService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-06-10
     * @return BaseResponse<List<TSanquanExportApprovalUser>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanExportApprovalUser>> findList(TSanquanExportApprovalUserQueryVo tSanquanExportApprovalUserVo) {
        return ok(tSanquanExportApprovalUserService.findList(tSanquanExportApprovalUserVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-06-10
     * @param tSanquanExportApprovalUser
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/addOrUpdate")
    public BaseResponse<Boolean> add(@RequestBody TSanquanExportApprovalUser tSanquanExportApprovalUser){
        return ok(tSanquanExportApprovalUserService.add(tSanquanExportApprovalUser));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-06-10
     * @param tSanquanExportApprovalUser
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanExportApprovalUser tSanquanExportApprovalUser) {
        return ok(tSanquanExportApprovalUserService.update(tSanquanExportApprovalUser));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-06-10
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanExportApprovalUserService.delete(id));
    }
}
