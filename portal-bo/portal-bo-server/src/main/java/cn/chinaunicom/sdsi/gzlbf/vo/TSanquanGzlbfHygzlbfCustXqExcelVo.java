package cn.chinaunicom.sdsi.gzlbf.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 高质量拜访-清单表
 * </p>
 * <AUTHOR>
 * @since 2025-03-25
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(30) // 标题单元格高度
public class TSanquanGzlbfHygzlbfCustXqExcelVo implements Serializable {

    /**
     * 行业
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "行业")
    private String hangye;

    /**
     * 客户经理
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "客户经理姓名")
    private String custManagerName;

    /**
     * 客户经理工号
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "客户经理工号")
    private String custManagerCode;

    /**
     * 拜访ID
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "拜访ID")
    private String recordId;

    /**
     * 拜访主题
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "拜访主题")
    private String visitTitle;

    /**
     * 拜访形式
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "拜访形式")
    private String visitType;

    /**
     * 拜访时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "拜访时间")
    private String visitTime;

    /**
     * 签到时间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "签到时间")
    private String createTime;

    /**
     * 签到地点
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "签到地点")
    private String signInPlace;

    /**
     * 参访人员角色/参访人员
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "参访人员角色/参访人员")
    private String cfPerson;

    /**
     * 拜访客户名称
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "拜访客户名称")
    private String customName;

    /**
     * (拜访客户的)自然客户编码
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "自然客户编码")
    private String customerId;

    /**
     * 受访人姓名
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "受访人姓名")
    private String interviewName;

    /**
     * 受访人电话
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "受访人电话")
    private String interviewTel;

    /**
     * 受访人岗位
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "受访人岗位")
    private String interviewJob;

    /**
     * 访谈内容
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "访谈内容")
    private String visitSummary;

    /**
     * 商机编码
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "商机编码")
    private String oppoNumber;

    /**
     * 商机名称
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "商机名称")
    private String oppoName;

}
