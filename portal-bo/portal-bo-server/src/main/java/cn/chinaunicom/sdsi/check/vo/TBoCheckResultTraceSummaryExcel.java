package cn.chinaunicom.sdsi.check.vo;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 稽核统计结果导出
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(30) // 标题单元格高度
public class TBoCheckResultTraceSummaryExcel {


    /* 区域值 */
    @ColumnWidth(20)
    @ExcelProperty(value = "地市")
    String area;

    /* 上一期问题派单反馈数 */
    @ColumnWidth(30)
    @ExcelProperty(value = "上一期问题派单反馈数")
    String feedbackNum;

    /* 派单反馈率 */
    @ColumnWidth(20)
    @ExcelProperty(value = "派单反馈率")
    String feedbackRate;

    /* 上一期BO状态整改完成数 */
    @ColumnWidth(30)
    @ExcelProperty(value = "上一期BO状态整改完成数")
    String boStateRepairNum;

    /* 上一期BO状态整改完成率 */
    @ColumnWidth(30)
    @ExcelProperty(value = "上一期BO状态整改完成率")
    String boStateRepairRate;

    /* 上一期BO速率整改完成数 */
    @ColumnWidth(30)
    @ExcelProperty(value = "上一期BO速率整改完成数")
    String boRateRepairNum;

    /* 上一期BO速率整改完成率 */
    @ColumnWidth(30)
    @ExcelProperty(value = "上一期BO速率整改完成率")
    String boRateRepairRate;

}
