package cn.chinaunicom.sdsi.approval.controller;

import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportApprovalRecordQueryVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalRecordService;
import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApprovalRecord;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 审批记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/approval/exportRecord")
public class TSanquanExportApprovalRecordController extends BaseController {

    @Autowired
    private TSanquanExportApprovalRecordService tSanquanExportApprovalRecordService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-06-10
     * @param tSanquanExportApprovalRecordVO
     * @return BasePageResponse<TSanquanExportApprovalRecord>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanExportApprovalRecord> findPage(TSanquanExportApprovalRecordQueryVo tSanquanExportApprovalRecordVo){
        return pageOk(tSanquanExportApprovalRecordService.findPage(tSanquanExportApprovalRecordVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-06-10
     * @param id
     * @return BaseResponse<TSanquanExportApprovalRecord>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanExportApprovalRecord> findOne(String id) {
        return ok(tSanquanExportApprovalRecordService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-06-10
     * @return BaseResponse<List<TSanquanExportApprovalRecord>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanExportApprovalRecord>> findList(TSanquanExportApprovalRecordQueryVo tSanquanExportApprovalRecordVo) {
        return ok(tSanquanExportApprovalRecordService.findList(tSanquanExportApprovalRecordVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-06-10
     * @param tSanquanExportApprovalRecord
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanExportApprovalRecord tSanquanExportApprovalRecord){
        return ok(tSanquanExportApprovalRecordService.add(tSanquanExportApprovalRecord));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-06-10
     * @param tSanquanExportApprovalRecord
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanExportApprovalRecord tSanquanExportApprovalRecord) {
        return ok(tSanquanExportApprovalRecordService.update(tSanquanExportApprovalRecord));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-06-10
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanExportApprovalRecordService.delete(id));
    }
}
