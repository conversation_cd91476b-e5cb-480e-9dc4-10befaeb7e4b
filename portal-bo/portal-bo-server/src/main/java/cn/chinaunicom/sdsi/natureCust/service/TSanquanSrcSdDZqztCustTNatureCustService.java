package cn.chinaunicom.sdsi.natureCust.service;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictCustomer;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictCustomerQueryVo;
import cn.chinaunicom.sdsi.cloud.natureCust.entity.TSanquanSrcSdDZqztCustTNatureCust;
import cn.chinaunicom.sdsi.cloud.natureCust.query.TSanquanSrcSdDZqztCustTNatureCustQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-06
 */
public interface TSanquanSrcSdDZqztCustTNatureCustService extends IService<TSanquanSrcSdDZqztCustTNatureCust> {

    // 分页查询dict数字档案使用查询全量客户
    IPage<TSanquanDictCustomer> findPageAll(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo);

    // 分页查询
    IPage<TSanquanSrcSdDZqztCustTNatureCust> findPage(TSanquanSrcSdDZqztCustTNatureCustQueryVo tSanquanSrcSdDZqztCustTNatureCustVo);

    // 根据id查询
    TSanquanSrcSdDZqztCustTNatureCust findOne(String id);

    // 查询列表
    List<TSanquanSrcSdDZqztCustTNatureCust> findList(TSanquanSrcSdDZqztCustTNatureCustQueryVo tSanquanSrcSdDZqztCustTNatureCustVo);

    // 新增
    int add(TSanquanSrcSdDZqztCustTNatureCust tSanquanSrcSdDZqztCustTNatureCust);

    // 修改
    int update(TSanquanSrcSdDZqztCustTNatureCust tSanquanSrcSdDZqztCustTNatureCust);

    // 删除
    int delete(String id);

    // 将地市名称切换为地市编码
    String findCityCode(String customerCity);

    // 将地市编码切换为地市名称
    String findCityName(String customerCity);
}
