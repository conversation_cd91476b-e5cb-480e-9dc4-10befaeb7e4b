package cn.chinaunicom.sdsi.dict.commons.service.impl;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictVisitTags;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictVisitTagsQueryVo;
import cn.chinaunicom.sdsi.dict.commons.mapper.TSanquanDictVisitTagsMapper;
import cn.chinaunicom.sdsi.dict.commons.service.TSanquanDictVisitTagsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * 走访标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Service
public class TSanquanDictVisitTagsServiceImpl extends ServiceImpl<TSanquanDictVisitTagsMapper, TSanquanDictVisitTags> implements TSanquanDictVisitTagsService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictVisitTags
     * @return IPage<TSanquanDictVisitTags>
     **/
    @Override
    public IPage<TSanquanDictVisitTags> findPage(TSanquanDictVisitTagsQueryVo tSanquanDictVisitTagsVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDictVisitTagsVo);
        return baseMapper.findPage(page, tSanquanDictVisitTagsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return TSanquanDictVisitTags
     **/
    @Override
    public TSanquanDictVisitTags findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @return List<TSanquanDictVisitTags>
     **/
    @Override
    public List<TSanquanDictVisitTags> findList(TSanquanDictVisitTagsQueryVo tSanquanDictVisitTagsVo) {
        return baseMapper.findList(tSanquanDictVisitTagsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictVisitTags
     * @return int
     **/
    @Override
    public int add(TSanquanDictVisitTags tSanquanDictVisitTags) {
        return baseMapper.insert(tSanquanDictVisitTags);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param tSanquanDictVisitTags
     * @return int
     **/
    @Override
    public int update(TSanquanDictVisitTags tSanquanDictVisitTags) {
        return baseMapper.updateById(tSanquanDictVisitTags);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-04-16
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
