package cn.chinaunicom.sdsi.dict.commons.mapper;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictIndustryProduct;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictIndustryProductQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 行业与应用名称对应表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface TSanquanDictIndustryProductMapper extends BaseMapper<TSanquanDictIndustryProduct> {

    // 分页查询
    IPage<TSanquanDictIndustryProduct> findPage(@Param("page") IPage page, @Param("query") TSanquanDictIndustryProductQueryVo tSanquanDictIndustryProductVo);

    // 查询列表
    List<TSanquanDictIndustryProduct> findList(@Param("query") TSanquanDictIndustryProductQueryVo tSanquanDictIndustryProductVo);

    // 查询行业
    List<Map<String, String>> selectIndustry();
}
