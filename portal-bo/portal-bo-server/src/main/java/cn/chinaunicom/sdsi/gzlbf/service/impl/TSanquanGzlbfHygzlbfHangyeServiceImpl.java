package cn.chinaunicom.sdsi.gzlbf.service.impl;

import cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfHygzlbfHangye;
import cn.chinaunicom.sdsi.cloud.gzlbf.query.TSanquanGzlbfHygzlbfHangyeQueryVo;
import cn.chinaunicom.sdsi.gzlbf.mapper.TSanquanGzlbfHygzlbfHangyeMapper;
import cn.chinaunicom.sdsi.gzlbf.service.TSanquanGzlbfHygzlbfHangyeService;
import cn.chinaunicom.sdsi.gzlbf.vo.TSanquanGzlbfHygzlbfHangyeExcelVo;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 高质量拜访-行业统计报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
@Service
public class TSanquanGzlbfHygzlbfHangyeServiceImpl extends ServiceImpl<TSanquanGzlbfHygzlbfHangyeMapper, TSanquanGzlbfHygzlbfHangye> implements TSanquanGzlbfHygzlbfHangyeService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-25
     * @param tSanquanGzlbfHygzlbfHangye
     * @return IPage<TSanquanGzlbfHygzlbfHangye>
     **/
    @Override
    public IPage<TSanquanGzlbfHygzlbfHangye> findPage(TSanquanGzlbfHygzlbfHangyeQueryVo tSanquanGzlbfHygzlbfHangyeVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanGzlbfHygzlbfHangyeVo);
        return baseMapper.findPage(page, tSanquanGzlbfHygzlbfHangyeVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-25
     * @param id
     * @return TSanquanGzlbfHygzlbfHangye
     **/
    @Override
    public TSanquanGzlbfHygzlbfHangye findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-25
     * @return List<TSanquanGzlbfHygzlbfHangye>
     **/
    @Override
    public List<TSanquanGzlbfHygzlbfHangye> findList(TSanquanGzlbfHygzlbfHangyeQueryVo tSanquanGzlbfHygzlbfHangyeVo) {
        return baseMapper.findList(tSanquanGzlbfHygzlbfHangyeVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-25
     * @param tSanquanGzlbfHygzlbfHangye
     * @return int
     **/
    @Override
    public int add(TSanquanGzlbfHygzlbfHangye tSanquanGzlbfHygzlbfHangye) {

        return baseMapper.insert(tSanquanGzlbfHygzlbfHangye);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-25
     * @param tSanquanGzlbfHygzlbfHangye
     * @return int
     **/
    @Override
    public int update(TSanquanGzlbfHygzlbfHangye tSanquanGzlbfHygzlbfHangye) {
        return baseMapper.updateById(tSanquanGzlbfHygzlbfHangye);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-03-25
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    // 获取批次列表
    @Override
    public List<Map<String,String>> getPatchList() {
        return this.baseMapper.getPatchList();
    }

    // 获取最新批次
    @Override
    public String getMaxPatch() {
        return this.baseMapper.getMaxPatch();
    }

    // 导出数据
    @Override
    public void exportData(TSanquanGzlbfHygzlbfHangyeQueryVo tSanquanGzlbfHygzlbfHangyeVo) {
        TSanquanGzlbfHygzlbfHangyeServiceImpl tSanquanGzlbfHygzlbfHangyeService = (TSanquanGzlbfHygzlbfHangyeServiceImpl) AopContext.currentProxy();
        List<TSanquanGzlbfHygzlbfHangye> list = tSanquanGzlbfHygzlbfHangyeService.findList(tSanquanGzlbfHygzlbfHangyeVo);
        List<TSanquanGzlbfHygzlbfHangyeExcelVo> collect = list.stream().map(item -> {
            TSanquanGzlbfHygzlbfHangyeExcelVo tSanquanGzlbfHygzlbfHangyeExcelVo = new TSanquanGzlbfHygzlbfHangyeExcelVo();
            BeanUtils.copyProperties(item, tSanquanGzlbfHygzlbfHangyeExcelVo);
            tSanquanGzlbfHygzlbfHangyeExcelVo.setFglKhzfRate(item.getFglKhzfRate() + "%");
            tSanquanGzlbfHygzlbfHangyeExcelVo.setSjKhfgRate(item.getSjKhfgRate() + "%");
            return tSanquanGzlbfHygzlbfHangyeExcelVo;
        }).collect(Collectors.toList());
        try {
            ExcelUtils.exportExcel(collect, TSanquanGzlbfHygzlbfHangyeExcelVo.class,"行业统计报表", "行业统计报表");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
