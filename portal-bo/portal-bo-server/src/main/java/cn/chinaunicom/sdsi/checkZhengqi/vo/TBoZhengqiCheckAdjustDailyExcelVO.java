package cn.chinaunicom.sdsi.checkZhengqi.vo;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *  BO稽核结果展示
 * </p>
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(40) // 标题单元格高度
public class TBoZhengqiCheckAdjustDailyExcelVO {

    @ColumnWidth(15)
    @ExcelProperty(value = {"地市"})
    private String city;                // 地市

    @ColumnWidth(15)
    @ExcelProperty(value = {"调账方式"})
    private String adjustMethod;        // 调账方式

    @ColumnWidth(15)
    @ExcelProperty(value = {"调账类型"})
    private String adjustType;          // 调账类型

    @ColumnWidth(20)
    @ExcelProperty(value = {"客户名称"})
    private String customerName;        // 客户名称

    @ColumnWidth(30)
    @ExcelProperty(value = {"客户编码"})
    private String customerId;          // 客户编码

    @ColumnWidth(25)
    @ExcelProperty(value = {"调账账号id"})
    private String acctId;              // 调账账号id

    @ColumnWidth(25)
    @ExcelProperty(value = {"调账用户id"})
    private String userId;              // 调账用户id

    @ColumnWidth(20)
    @ExcelProperty(value = {"业务号码"})
    private String deviceNumber;        // 业务号码

    @ColumnWidth(20)
    @ExcelProperty(value = {"网别"})
    private String networkType;         // 网别

    @ColumnWidth(20)
    @ExcelProperty(value = {"产品名称"})
    private String productName;         // 产品名称

    @ColumnWidth(20)
    @ExcelProperty(value = {"产品编码"})
    private String productId;           // 产品编码

    @ColumnWidth(20)
    @ExcelProperty(value = {"调账金额（元）"})
    private String adjustFee;           // 调账金额（元）

    @ColumnWidth(20)
    @ExcelProperty(value = {"调账账期"})
    private String cycleId;             // 调账账期

    @ColumnWidth(20)
    @ExcelProperty(value = {"调账日期"})
    private String adjustmentDate;      // 调账日期

    @ColumnWidth(20)
    @ExcelProperty(value = {"调账需求发起人"})
    private String requester;           // 调账需求发起人

    @ColumnWidth(20)
    @ExcelProperty(value = {"电话"})
    private String phone;               // 电话

    @ColumnWidth(20)
    @ExcelProperty(value = {"调账依据（审批流程单号）"})
    private String approvalNo;          // 调账依据（审批流程单号）

    @ColumnWidth(20)
    @ExcelProperty(value = {"收入归属单位/归属人"})
    private String revenueOwner;        // 收入归属单位/归属人

    @ColumnWidth(20)
    @ExcelProperty(value = {"稽核状态"})
    private String auditStatus;         // 稽核(0、否，1、是)

    @ColumnWidth(20)
    @ExcelProperty(value = {"本期结合结果反馈"})
    private String auditResult;         // 本期结合结果反馈

    @ColumnWidth(20)
    @ExcelProperty(value = {"稽核反馈日期"})
    private String auditResultDate;     // 稽核反馈日期

    @ColumnWidth(20)
    @ExcelProperty(value = {"其他"})
    private String auditOther;          // 其他

    @ColumnWidth(20)
    @ExcelProperty(value = {"是否大额"})
    private String isLarge;             // 是否大额（0、否，1、是）

    public String getAdjustType() {
        if(StringUtils.isNotEmpty(adjustType)){
            if("1".equals(adjustType)){
                return "调减";
            }else if("0".equals(adjustType)){
                return "调增";
            }
        }
        return adjustType;
    }

    public String getAuditStatus() {
        if(StringUtils.isNotEmpty(auditStatus)){
            if("1".equals(auditStatus)){
                return "是";
            }else if("0".equals(auditStatus)){
                return "否";
            }
        }
        return auditStatus;
    }

    public String getIsLarge() {
        if(StringUtils.isNotEmpty(isLarge)){
            if("1".equals(isLarge)){
                return "是";
            }else if("0".equals(isLarge)){
                return "否";
            }
        }
        return isLarge;
    }

    public String getDeviceNumber(){
        if (deviceNumber == null) {
            return deviceNumber;
        }
        if(deviceNumber.length() <= 7){
            return deviceNumber.substring(0, 3) + "****";
        }
        return deviceNumber.substring(0, 3) + "****" + deviceNumber.substring(deviceNumber.length() - 4);
    }
}
