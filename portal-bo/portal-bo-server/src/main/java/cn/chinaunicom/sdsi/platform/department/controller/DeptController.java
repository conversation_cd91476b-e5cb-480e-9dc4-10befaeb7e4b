package cn.chinaunicom.sdsi.platform.department.controller;

import cn.chinaunicom.sdsi.platform.department.entity.Dept;
import cn.chinaunicom.sdsi.platform.department.entity.DeptQuery;
import cn.chinaunicom.sdsi.platform.department.entity.DeptVo;
import cn.chinaunicom.sdsi.platform.department.service.IDeptService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import cn.chinaunicom.sdsi.framework.base.BaseController;

import java.util.List;

/**
 * <p>
 * 部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@RestController
@RequestMapping("/dept")
public class DeptController extends BaseController {

    @Autowired
    private IDeptService deptService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2024-10-25
     * @param tSanquanPlatformDeptVO
     * @return BasePageResponse<TSanquanPlatformDept>
     **/
    @PostMapping("/findPage")
    public BasePageResponse<Dept> findPage(@RequestBody DeptQuery deptQuery){
        return pageOk(deptService.findPage(deptQuery));
    }

    /*
     * <AUTHOR>
     * @description 查询树形结构
     * @since 2024-10-25
     * @param tSanquanPlatformDeptVO
     * @return BasePageResponse<TSanquanPlatformDept>
     **/
    @GetMapping("/findTree")
    public BaseResponse<List<DeptVo>> findTree(String deptParentId){
        return ok(deptService.findTree(deptParentId));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2024-10-25
     * @param id
     * @return BaseResponse<TSanquanPlatformDept>
     **/
    @GetMapping("/findOne")
    public BaseResponse<Dept> findOne(String id) {
        return ok(deptService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2024-10-25
     * @return BaseResponse<List<TSanquanPlatformDept>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<Dept>> findList() {
        return ok(deptService.findList());
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2024-10-25
     * @param tSanquanPlatformDept
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/save")
    public BaseResponse<Boolean> saveAndUpdate(@RequestBody Dept tSanquanPlatformDept){
        return ok(deptService.saveAndUpdate(tSanquanPlatformDept));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2024-10-25
     * @param tSanquanPlatformDept
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody Dept tSanquanPlatformDept) {
        return ok(deptService.update(tSanquanPlatformDept));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2024-10-25
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Boolean> delete(String id) {
        return ok(deptService.delete(id));
    }
}
