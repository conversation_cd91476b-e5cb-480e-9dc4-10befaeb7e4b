package cn.chinaunicom.sdsi.plugins.office.excel.annotation;

import org.springframework.web.bind.annotation.Mapping;

import java.lang.annotation.*;

/**
 * excel 列 目前只支持 单一的excel列
 *
 * <AUTHOR>
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Mapping
public @interface ImportExcelCell {

    /**
     * 排序
     *
     * @return
     */
    int cellIndex() default 0;

    boolean image() default false;

}
