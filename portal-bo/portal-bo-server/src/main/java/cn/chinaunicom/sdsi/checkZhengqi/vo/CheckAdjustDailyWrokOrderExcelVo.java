package cn.chinaunicom.sdsi.checkZhengqi.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

/**
 * 调账日工单数据
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class CheckAdjustDailyWrokOrderExcelVo {

    // 工单数据
    /**
     * 主键id
     */
    @ExcelProperty(value = "工单编码")
    private String workOrderId;

    /**
     * 流程工单状态，1已反馈，2未反馈, 3已转派
     */
    @ExcelProperty(value = "工单状态")
    private String workStatus;

    /**
     * 反馈时间
     */
    @ExcelProperty(value = "反馈时间")
    private String feedbackTime;

    /**
     * 反馈结果
     */
    @ExcelProperty(value = "反馈结果")
    private String feedbackInfo;

    /**
     * 反馈结果说明
     */
    @ExcelProperty(value = "反馈结果说明")
    private String feedbackResultDescribe;

    @ExcelProperty(value = "地市")
    private String city;                // 地市

    @ExcelProperty(value = "调账方式")
    private String adjustMethod;        // 调账方式

    @ExcelProperty(value = "调账类型")
    private String adjustType;          // 调账类型

    @ExcelProperty(value = "客户名称")
    private String customerName;        // 客户名称

    @ExcelProperty(value = "客户编码")
    private String customerId;          // 客户编码

    @ExcelProperty(value = "调账账号id")
    private String acctId;              // 调账账号id

    @ExcelProperty(value = "调账用户id")
    private String userId;              // 调账用户id

    @ExcelProperty(value = "业务号码")
    private String deviceNumber;        // 业务号码

    @ExcelProperty(value = "网别")
    private String networkType;         // 网别

    @ExcelProperty(value = "产品名称")
    private String productName;         // 产品名称

    @ExcelProperty(value = "产品编码")
    private String productId;           // 产品编码

    @ExcelProperty(value = "调账金额（元）")
    private String adjustFee;           // 调账金额（元）

    @ExcelProperty(value = "调账账期")
    private String cycleId;             // 调账账期

    @ExcelProperty(value = "调账日期")
    private String adjustmentDate;      // 调账日期

    @ExcelProperty(value = "是否大额")
    private String isLarge;             // 是否大额（0、否，1、是）

    public String getWorkStatus(){
        if("1".equals(workStatus)){
            return "已反馈";
        }else if("2".equals(workStatus)){
            return "未反馈";
        }else if("3".equals(workStatus)){
            return "已转派";
        }else {
            return "其他";
        }
    }

    public String getFeedbackInfo(){
        if("1".equals(feedbackInfo)){
            return "已核实，无问题";
        }else if("2".equals(feedbackInfo)){
            return "已核实问题，已整改";
        }else if("3".equals(feedbackInfo)){
            return "已核实问题，未完成整改";
        }else {
            return "其他";
        }
    }

    public String getAdjustType(){
        if("1".equals(adjustType)){
            return "调减";
        }else{
            return "调增";
        }
    }

    public String getIsLarge(){
        if("1".equals(isLarge)){
            return "是";
        }else{
            return "否";
        }
    }

    public String getDeviceNumber(){
        if (deviceNumber == null) {
            return deviceNumber;
        }
        if(deviceNumber.length() <= 7){
            return deviceNumber.substring(0, 3) + "****";
        }
        return deviceNumber.substring(0, 3) + "****" + deviceNumber.substring(deviceNumber.length() - 4);
    }
}
