package cn.chinaunicom.sdsi.mobileNetworkTariffAudit.controller;

import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.entity.UserAuditDetails;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.service.UserAuditDetailsService;
import cn.chinaunicom.sdsi.mobileNetworkTariffAudit.vo.UserAuditDetailsQueryVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("mobileNetworkTariff/userAudit")
public class UserAuditDetailsController extends BaseController {

    @Autowired
    private UserAuditDetailsService userAuditDetailsService;


    /**
     * 分页查询
     * @param vo 查询条件
     * @return BasePageResponse<UserAuditDetailsQueryVo>
     */
    @Operation(summary = "分页查询稽核用户明细", description ="分页查询AI稽核商机")
    @GetMapping("/findPage")
    public BasePageResponse<UserAuditDetails> findPage(UserAuditDetailsQueryVo vo) {
        return pageOk(userAuditDetailsService.findPage(vo));
    }




}
