package cn.chinaunicom.sdsi.check.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * BO稽核跟踪报表
 * </p>
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
public class TBoCheckResultSummaryQuery extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    /*  */
    String id;

    /* 稽核结果编码 */
    String resultCode;

    /* 稽核批次 */
    String patch;

    /* 级别，1地市，2区县，3营服 */
    String levels;

    /* 比对类型，1，BO状态，2，BO速率，3，汇总 */
    String boType;

    /* BO状态一致总数*/
    String boMatchTotal;

    /* BO状态一致总数 */
    String boDifferenceTotal;

    /* BO状态一致率 */
    String boMatchBilv;

    /* BO状态环比 */
    String boMatchHuanbi;

    /* BO状态一致总数-互联网专线 */
    String boNetMatchTotal;

    /* BO状态不一致总数-互联网专线 */
    String boNetDifferenceTotal;

    /* BO状态一致率-互联网专线 */
    String boNetMatchBilv;

    /* BO状态环比-互联网专线 */
    String boNetMatchHuanbi;

    /* BO状态一致总数-数据网元*/
    String boDataMatchTotal;

    /* BO状态不一致总数-数据网元*/
    String boDataDifferenceTotal;

    /* BO状态一致率-数据网元 */
    String boDataMatchBilv;

    /* BO状态环比-数据网元 */
    String boDataMatchHuanbi;

    /* 地市 */
    String city;

    /* 区县 */
    String district;

    /* 营服 */
    String grid;

    /* 创建时间 */
    String createTime;

    /* 修改时间 */
    String updateTime;

    /**
     * 分页汇总查询 分组字段条件
     */
    String groupColumn;

}
