package cn.chinaunicom.sdsi.common;

import cn.chinaunicom.sdsi.check.entity.TBoCheckResult;
import cn.chinaunicom.sdsi.check.entity.TBoCheckResultLog;
import cn.chinaunicom.sdsi.check.entity.TBoCheckResultOrder;
import cn.chinaunicom.sdsi.check.service.TBoCheckResultLogService;
import cn.chinaunicom.sdsi.check.service.TBoCheckResultOrderService;
import cn.chinaunicom.sdsi.check.service.TBoCheckResultService;
import cn.chinaunicom.sdsi.checkZhengqi.entity.TBoZhengqiCheckAdjustDaily;
import cn.chinaunicom.sdsi.checkZhengqi.service.TBoZhengqiCheckAdjustDailyService;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrder;
import cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrderType;
import cn.chinaunicom.sdsi.common.entity.*;
import cn.chinaunicom.sdsi.common.service.ZqDbService;
import cn.chinaunicom.sdsi.common.sms.SendSmsUtils;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.daiban.constant.BoCheckUrlConstant;
import cn.chinaunicom.sdsi.daiban.constant.OrderTypeConstant;
import cn.chinaunicom.sdsi.daiban.service.TSanquanDaibanOrderService;
import cn.chinaunicom.sdsi.daiban.service.TSanquanDaibanOrderTypeService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.platform.user.service.IUserService;
import cn.chinaunicom.sdsi.tokenValidate.AuthTokenVaildate;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.v3.oas.annotations.Operation;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/02/12 11:23
 */
// 通用类
@RestController
@RequestMapping("/commons")
public class commonsController extends BaseController {

    @Autowired
    private IUserService userService;

    @Autowired
    private TBoCheckResultOrderService tBoCheckResultOrderService;

    @Autowired
    private TBoCheckResultService tBoCheckResultService;

    @Autowired
    private SendSmsUtils sendSmsUtils;

    @Autowired
    private TBoCheckResultLogService tBoCheckResultLogService;

    @Autowired
    private TSanquanDaibanOrderTypeService tSanquanDaibanOrderTypeService;

    @Autowired
    private TBoZhengqiCheckAdjustDailyService justDailyService;

    @Autowired
    private TSanquanDaibanOrderService tSanquanDaibanOrderService;

    // 日期格式化作为常量
    private static final SimpleDateFormat MY_FMT = new SimpleDateFormat("yyyyMMddHHmmssSSS");

    // 短信模板
    private static final String smsTemplate = "您好，政企双线稽核patch期，customer（客户）businessCode（业务号码）BO状态/速率不一致，请登录政企运营平台签收，并予核实整改。";

    // 调账短信模板
    private static final String smsTemplateAdjust = "您好，政企调账稽核cycleId期，customer（客户）businessCode（业务号码）调账金额adjustFee（元），请登录政企稽核系统签收，并予核实整改";

    /**
     * 查询地市下的所有区县信息
     * @return
     */
    @GetMapping("/findDistrict")
    @Operation(summary = "行业信息")
    public BaseResponse<List<Map<String, String>>> findDistrict(String cityName){
        return ok(userService.findDistrict(cityName));
    }

    /**
     * 查询所有的行业信息
     * @return
     */
    @GetMapping("/findIndustry")
    @Operation(summary = "行业信息")
    public BaseResponse<List<IndustryVO>> findIndustry(){
        return ok(userService.findIndustry());
    }

    /**
     * 根据行业查询细分行业
     * @return
     */
    @Operation(summary = "查询当前行业的细分行业", description = "查询当前行业的细分行业")
    @PostMapping("/getSegments")
    public BaseResponse<List<Map<String,String>>> getSegments(@RequestBody List<String> industry) {
        try {
            return new BaseResponse<>(userService.getSegments(industry));
        } catch (Exception e) {
            return new BaseResponse<>(null);
        }
    }

    /**
     * 查询地市信息
     * @return
     */
    @GetMapping("/findCityGroup")
    @Operation(summary = "查询地市", description = "查询地市")
    public BaseResponse<List<String>> findCityGroup() {
        return new BaseResponse<>(userService.findCityGroup());
    }

    /**
     * 推送bo待办内容
     * @param dbEntity
     * @return
     */
    @Operation(summary = "推送bo待办内容", description = "推送bo待办内容")
    @PostMapping("/push/boDb/content")
    public BaseResponse<Map<String, Integer>> pushDb(@RequestBody DbEntity dbEntity) {
        Map<String, Integer> result = new HashMap<>();
        try {
            // 单个推送
            if ("0".equals(dbEntity.getType()) && StrUtil.isNotEmpty(dbEntity.getObResultId())) {
                // 查询要推送的数据
                TBoCheckResult one = tBoCheckResultService.getByCode(dbEntity.getObResultId(), "0");
                if (Objects.nonNull(one)) {
                    pushResult(one);
                }
                // 多选推送
            } else if ("1".equals(dbEntity.getType()) && CollUtil.isNotEmpty(dbEntity.getObResultIdList())) {
                // 查询要推送的数据
                List<TBoCheckResult> byCodeList = tBoCheckResultService.getByCodeList(dbEntity.getObResultIdList(), "0");
                if (CollUtil.isNotEmpty(byCodeList)) {
                    for (TBoCheckResult one : byCodeList) {
                        pushResult(one);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            // 捕获任何异常并抛出一个 ServiceErrorException
            throw new ServiceErrorException("操作失败，请联系管理员！");
        }
        return ok(result);
    }

    /**
     * 推送bo待办
     * @param one
     */
    private void pushResult(TBoCheckResult one) {
        // 当前用户消息
        MallUser user = UserUtils.getUser();
        // 处理待办ID
        String todoCode = generateTodoCode();
        // 修改bo稽核表中的派单状态
        updateBoResult(one, user, todoCode);
        // 插入待办工单类型
        TSanquanDaibanOrderType tSanquanDaibanOrderType = new TSanquanDaibanOrderType();
        tSanquanDaibanOrderType.setOrderId(one.getCode());
        tSanquanDaibanOrderType.setTodoCode(todoCode);
        tSanquanDaibanOrderType.setTodoType(OrderTypeConstant.BO_CHECK_TYPE);
        tSanquanDaibanOrderTypeService.add(tSanquanDaibanOrderType);
        String resStr = ZqDbService.sendTodo(
                one.getCode(),
                one.getBusinessName() + one.getBusinessCode() + "BO稽核任务单",
                "BO稽核",
                one.getJobNum(),
                todoCode,
                BoCheckUrlConstant.DB_URL,
        "BO稽核待办处理",
                "bo_check_handle",
                user.getStaffId()
        );
        System.err.println("发送待办返回的响应参数" + resStr);
        // 解析返回数据
        JSONObject smsRspBodyObj = JSONObject.parseObject(resStr);
        System.err.println("解析返回的数据：" + smsRspBodyObj);
        JSONObject bssBody = smsRspBodyObj.getJSONObject("UNI_BSS_BODY");
        System.err.println("解析返回的数据UNI_BSS_BODY：{}" + bssBody);
        JSONObject SWSISRsp = bssBody.getJSONObject("SYNC_WAIT_SOLVE_INFO_SER_RSP");
        String CODE = SWSISRsp.getString("CODE");
        if ("00000".equals(CODE) || "000".equals(CODE)) {
            try {
                // 修改bo稽核表中的派单状态
                updateBoResultStatus(one, user, todoCode);
            } catch (Exception e) {
                throw new ServiceErrorException("工单处理失败，请联系管理员！");
            }
        } else {
            throw new ServiceErrorException("推送待办失败，请联系管理员！");
        }
    }

    // 填写信息
    private void updateBoResult(TBoCheckResult one, MallUser user, String todoCode) {
        TBoCheckResult tBoCheckResult = new TBoCheckResult();
        tBoCheckResult.setId(one.getId());
        tBoCheckResult.setSendTime(String.valueOf(DateTime.now()));
        tBoCheckResult.setPushName(user.getStaffName());
        tBoCheckResult.setPushOa(user.getStaffId());
        tBoCheckResult.setPushTel(user.getTel());
        tBoCheckResult.setResultOrder(todoCode);
        tBoCheckResultService.update(tBoCheckResult);
    }

    // 修改bo稽核表中的派单状态
    private void updateBoResultStatus(TBoCheckResult one, MallUser user, String todoCode) {
        TBoCheckResult tBoCheckResult = new TBoCheckResult();
        tBoCheckResult.setId(one.getId());
        tBoCheckResult.setStatus("1");
        tBoCheckResult.setSendTime(String.valueOf(DateTime.now()));
        tBoCheckResultService.update(tBoCheckResult);
        // 添加日志
        addCheckResultLog(todoCode, one.getCode(), "派单");
    }

    /**
     * 生成待办ID
     */
    private String generateTodoCode() {
        int randomNum = ThreadLocalRandom.current().nextInt(100000, 999999);
        String formattedDate = MY_FMT.format(new Date());
        return formattedDate + randomNum;
    }

    /**
     * 创建工单信息
     */
    private void createTBoCheckResultOrder(TBoCheckResult one, String todoCode) {
        TBoCheckResultOrder existingOrder = tBoCheckResultOrderService.getByCode(one.getCode());
        TBoCheckResultOrder newOrder = new TBoCheckResultOrder();
        // 设置公共基础属性
        setupCommonAttributes(newOrder, one, todoCode);
        // 设置流程顺序等级
        newOrder.setOrderLevel(calculateOrderLevel(existingOrder));
        // 设置差异属性
        if (existingOrder == null) {
            setAttributesForNewOrder(newOrder, one);
        } else {
            setAttributesForExistingOrder(newOrder, existingOrder);
        }
        tBoCheckResultOrderService.save(newOrder);
    }

    // 基本信息
    private void setupCommonAttributes(TBoCheckResultOrder order, TBoCheckResult source, String todoCode) {
        // 基础信息
        order.setResultCode(source.getCode());
        order.setProcessCode(source.getCode());
        order.setStatus("0");
        order.setId(todoCode);
        order.setOrderCode(todoCode);
        order.setDeleted("0");
        // 时间信息
        order.setCreateTime(String.valueOf(DateTime.now()));
        // 整改人信息（固定从源头获取）
        order.setCreatorRepair(source.getRectificatePerson());
        order.setCreatorRepairOa(source.getJobNum());
        order.setCreatorRepairTel(source.getTelphone());
    }

    // 订单处理的工单等级
    private String calculateOrderLevel(TBoCheckResultOrder existingOrder) {
        return existingOrder == null ?
                "0" :
                String.valueOf(Integer.parseInt(existingOrder.getOrderLevel()) + 1);
    }

    // 没有工单时的操作
    private void setAttributesForNewOrder(TBoCheckResultOrder order, TBoCheckResult source) {
        // 初始创建人信息
        order.setCreator(source.getPushName());
        order.setCreatorOa(source.getPushOa());
        // 初始来源信息（同创建人）
        order.setCreatorOrgin(source.getPushName());
        order.setCreatorOrginOa(source.getPushOa());
        order.setCreatorOrginTel(source.getPushTel());
    }

    // 已有工单时的操作
    private void setAttributesForExistingOrder(TBoCheckResultOrder order, TBoCheckResultOrder existingOrder) {
        // 继承前序工单的创建人
        order.setCreator(existingOrder.getCreator());
        order.setCreatorOa(existingOrder.getCreatorOa());
        // 来源信息继承前序工单的整改人
        order.setCreatorOrgin(existingOrder.getCreatorRepair());
        order.setCreatorOrginOa(existingOrder.getCreatorRepairOa());
        order.setCreatorOrginTel(existingOrder.getCreatorRepairTel());
    }

    /**
     * 发送待办回调接口处理
     * @param notificationRequest
     * @return
     */
    @Operation(summary = "能开注册回调接口", description = "能开注册回调接口")
    @PostMapping("/AsynNotificationResults")
//    @AuthTokenVaildate(tokenClass = NotificationRequest.class)
    public ResponseEntity<NotificationResponse> handleNotification(@RequestBody NotificationRequest notificationRequest) {
        System.err.println("回调接口接收的参数：" +  notificationRequest);
        // 假设处理成功，构建响应对象
        NotificationResponse response = new NotificationResponse();
        response.setRespCode("00000");
        response.setRespMsg("处理成功！");
        if(CollUtil.isNotEmpty(notificationRequest.getTaskAttrs())){
            if("000".equals(notificationRequest.getCode())){
                // 成功推送
                processSuccessNotification(response,notificationRequest);
            }else{
                // 推送失败
                handleFailure(notificationRequest);
            }
        }
        // 返回响应对象
        return ResponseEntity.ok(response);
    }

    /**
     * 推送失败
     */
    private void handleFailure(NotificationRequest notificationRequest) {
        System.err.println("失败编码:" + notificationRequest.getCode());
        System.err.println("失败信息:" + notificationRequest.getMessage());
        String orderId = notificationRequest.getOrderId();
        if (StrUtil.isNotEmpty(orderId)) {
            TSanquanDaibanOrderType order = tSanquanDaibanOrderTypeService.findOneByOrderId(orderId);
            if (order != null) {
                handleOrderFailure(notificationRequest, order);
            }
        }
    }

    // 订单类型区分
    private void handleOrderFailure(NotificationRequest notificationRequest, TSanquanDaibanOrderType order) {
        // BO稽核处理
        if (OrderTypeConstant.BO_CHECK_TYPE.equals(order.getTodoType())) {
            handleBoCheckFailure(notificationRequest);
        }
        // 稽核平台处理（可以在这里处理其他类型）
        else if (OrderTypeConstant.ZQPT_JH_TYPE.equals(order.getTodoType())) {
            handleAdjustFailure(notificationRequest);
        }
    }

    // 调账处理
    private void handleAdjustFailure(NotificationRequest notificationRequest) {
        for (TaskAttr taskAttr : notificationRequest.getTaskAttrs()) {
            String taskState = taskAttr.getTaskState();
            if ("0".equals(taskState)) {
                // 待办失败处理
                handleAdjustFail(notificationRequest, taskAttr.getTaskId());
            } else if ("1".equals(taskState)) {
                // 已办失败处理
                handleAdjustFailYB(notificationRequest, taskAttr.getTaskId());
            }
        }
    }

    // 调账已办失败处理
    private void handleAdjustFailYB(NotificationRequest notificationRequest, String taskId) {
        // 已办失败处理
        TSanquanDaibanOrder tSanquanDaibanOrder = new TSanquanDaibanOrder();
        tSanquanDaibanOrder.setId(taskId);
        tSanquanDaibanOrder.setStatus("2");
        tSanquanDaibanOrderService.update(tSanquanDaibanOrder);
        // 添加日志
        addCheckResultLog(taskId, notificationRequest.getOrderId(), "已办关闭失败");
    }

    // 调账待办失败的回调
    private void handleAdjustFail(NotificationRequest notificationRequest, String taskId) {
        // 工单状态（待办状态）
        if(StrUtil.isNotEmpty(taskId)){
            addCheckResultLog(taskId, notificationRequest.getOrderId(), "待办派单失败");
            // 修改派发的状态
            justDailyService.update(Wrappers.<TBoZhengqiCheckAdjustDaily>lambdaUpdate()
                    .set(TBoZhengqiCheckAdjustDaily::getStatus, "0")
                    .eq(StrUtil.isNotEmpty(notificationRequest.getOrderId()),TBoZhengqiCheckAdjustDaily::getId, notificationRequest.getOrderId())
            );
        }
    }

    // BO稽核处理
    private void handleBoCheckFailure(NotificationRequest notificationRequest) {
        for (TaskAttr taskAttr : notificationRequest.getTaskAttrs()) {
            String taskState = taskAttr.getTaskState();
            if ("0".equals(taskState)) {
                // 待办失败处理
                handleFail(notificationRequest, taskAttr.getTaskId());
            } else if ("1".equals(taskState)) {
                // 已办失败处理
                handleFailYB(notificationRequest, taskAttr.getTaskId());
            }
        }
    }

    // 已办失败处理
    private void handleFailYB(NotificationRequest notificationRequest, String taskId) {
        // 已办失败处理
        TBoCheckResultOrder tBoCheckResultOrder = new TBoCheckResultOrder();
        tBoCheckResultOrder.setId(taskId);
        tBoCheckResultOrder.setRemark("推送失败！");
        tBoCheckResultOrder.setStatus("2");
        tBoCheckResultOrderService.update(tBoCheckResultOrder);
        // 添加日志
        addCheckResultLog(taskId, notificationRequest.getOrderId(), "已办关闭失败");
    }

    // 待办失败的回调
    private void handleFail(NotificationRequest notificationRequest, String taskId) {
        // 工单状态（待办状态）
        if(StrUtil.isNotEmpty(taskId)){
            addCheckResultLog(taskId, notificationRequest.getOrderId(), "待办派单失败");
            // 修改派发的状态
            tBoCheckResultService.update(Wrappers.<TBoCheckResult>lambdaUpdate()
                    .set(TBoCheckResult::getStatus, "0")
                    .eq(StrUtil.isNotEmpty(notificationRequest.getOrderId()),TBoCheckResult::getCode, notificationRequest.getOrderId())
            );
        }
    }

    // 处理成功的回调通知
    private void processSuccessNotification(NotificationResponse response,NotificationRequest notificationRequest) {
        // 稽核编码结果
        String orderId = notificationRequest.getOrderId();
        if (StrUtil.isEmpty(orderId)) {
            System.err.println("订单编码为空");
            response.setRespCode("11111");
            response.setRespMsg("订单编码为空");
            this.handleFailure(notificationRequest);
            return;
        }
        TSanquanDaibanOrderType oneByOrderId = tSanquanDaibanOrderTypeService.findOneByOrderId(orderId);
        if (oneByOrderId == null) {
            System.err.println("如果未找到订单，直接返回");
            response.setRespCode("11111");
            response.setRespMsg("未找到订单!");
            this.handleFailure(notificationRequest);
            return;
        }
        // BO稽核待办
        if(OrderTypeConstant.BO_CHECK_TYPE.equals(oneByOrderId.getTodoType())){
            TBoCheckResult byCode = tBoCheckResultService.getByCode(orderId, "");
            if(byCode != null){
                for (TaskAttr taskAttr : notificationRequest.getTaskAttrs()) {
                    // 处理信息（待办、已办）
                    handleInformation(taskAttr, orderId, byCode);
                }
            }
        // 稽核平台待办
        }else if (OrderTypeConstant.ZQPT_JH_TYPE.equals(oneByOrderId.getTodoType())){
            TBoZhengqiCheckAdjustDaily byOrderId = justDailyService.getByOrderId(orderId, "");
            if(byOrderId != null){
                for (TaskAttr taskAttr : notificationRequest.getTaskAttrs()) {
                    // 处理信息（待办、已办）
                    handleInformationAdjust(taskAttr, orderId, byOrderId);
                }
            }
        }
    }

    // 调账的信息处理（待办、已办）
    private void handleInformationAdjust(TaskAttr taskAttr, String orderId, TBoZhengqiCheckAdjustDaily byOrderId) {
        // 待办ID（工单ID）
        String taskId = taskAttr.getTaskId();
        TSanquanDaibanOrder one = tSanquanDaibanOrderService.getOne(Wrappers.<TSanquanDaibanOrder>lambdaQuery()
                .eq(TSanquanDaibanOrder::getId, taskId)
        );
        if(one == null){
            createTBoZhengqiCheckAdjustDailyOrder(byOrderId, taskId);
        }
        // 发送的已办信息
        if("1".equals(taskAttr.getTaskState())){
//            finalized(taskId, orderId);
            // 发送待办信息
        }else if("0".equals(taskAttr.getTaskState())){
            todoHandleAdjust(taskId, orderId, byOrderId);
        }
    }

    // 发送待办信息成功
    private void todoHandleAdjust(String taskId, String orderId, TBoZhengqiCheckAdjustDaily byOrderId) {
        TSanquanDaibanOrder tSanquanDaibanOrder = new TSanquanDaibanOrder();
        tSanquanDaibanOrder.setId(taskId);
        tSanquanDaibanOrder.setProcessCode(orderId);
        tSanquanDaibanOrder.setStatus("2");
        tSanquanDaibanOrderService.update(tSanquanDaibanOrder);
        addCheckResultLog(taskId, orderId, "待办回调成功");
        // TODO 短信模版处理
        String customizedSms = smsTemplateHandleAdjust(byOrderId);
        // 发送短信信息
        if(StrUtil.isNotEmpty(byOrderId.getAuditManagerTel())){
            sendSmsUtils.sendSMSNotStore(byOrderId.getAuditManagerTel(), customizedSms);
        }
    }

    // 短信模版
    private static String smsTemplateHandleAdjust(TBoZhengqiCheckAdjustDaily byCode) {
        // 对业务号码脱敏：长度≥7时保留前4位+***+后3位；长度<7时只保留前4位
        String deviceNumber = byCode.getDeviceNumber();
        String maskedDeviceNumber = deviceNumber != null
                ? (deviceNumber.length() >= 7
                ? deviceNumber.substring(0, 4) + "***" + deviceNumber.substring(deviceNumber.length() - 3)
                : deviceNumber.substring(0, Math.min(4, deviceNumber.length())) + "***")
                : null;
        // 使用replace方法进行字符串替换
        String customizedSms = smsTemplateAdjust
                .replace("cycleId", byCode.getCycleId())
                .replace("customer", byCode.getCustomerName())
                .replace("businessCode", maskedDeviceNumber != null ? maskedDeviceNumber : "")
                .replace("adjustFee", byCode.getAdjustFee());
        return customizedSms;
    }

    // 创建调账待办工单
    private void createTBoZhengqiCheckAdjustDailyOrder(TBoZhengqiCheckAdjustDaily byOrderId, String taskId) {
        // 判断是否存在，存在是已办不存在是待办
        TSanquanDaibanOrder existingOrder = tSanquanDaibanOrderService.getByProcessCode(byOrderId.getId());
        TSanquanDaibanOrder newOrder = new TSanquanDaibanOrder();
        // 设置公共基础属性
        setupCommonAttributesAdjust(newOrder, byOrderId, taskId);
        // 设置流程顺序等级
        newOrder.setOrderLevel(calculateOrderLevelAdjust(existingOrder));
        // 设置差异属性
        if (existingOrder == null) {
            setAttributesForNewOrderAdjust(newOrder, byOrderId);
        } else {
            setAttributesForExistingOrderAdjust(newOrder, existingOrder);
        }
        tSanquanDaibanOrderService.save(newOrder);
    }

    // 已有工单时的操作
    private void setAttributesForExistingOrderAdjust(TSanquanDaibanOrder order, TSanquanDaibanOrder existingOrder) {
        // 继承前序工单的创建人
        order.setCreator(existingOrder.getCreator());
        order.setCreatorOa(existingOrder.getCreatorOa());
        // 来源信息继承前序工单的整改人
        order.setCreatorOrgin(existingOrder.getCreatorRepair());
        order.setCreatorOrginOa(existingOrder.getCreatorRepairOa());
        order.setCreatorOrginTel(existingOrder.getCreatorRepairTel());
    }

    // 处理创建人信息
    private void setAttributesForNewOrderAdjust(TSanquanDaibanOrder order, TBoZhengqiCheckAdjustDaily source) {

    }

    // 订单处理的工单等级
    private String calculateOrderLevelAdjust(TSanquanDaibanOrder existingOrder) {
        return existingOrder == null ?
                "0" :
                String.valueOf(Integer.parseInt(existingOrder.getOrderLevel()) + 1);
    }

    // 基本信息
    private void setupCommonAttributesAdjust(TSanquanDaibanOrder order, TBoZhengqiCheckAdjustDaily source, String todoCode) {
        // 基础信息
        order.setProcessCode(source.getId());
        order.setStatus("0");
        order.setId(todoCode);
        order.setOrderCode(todoCode);
        order.setDeleted("0");
        // 时间信息
        order.setCreateTime(String.valueOf(DateTime.now()));
        // 整改人信息（固定从源头获取）
        order.setCreatorRepair(source.getAuditManager());
        order.setCreatorRepairOa(source.getAuditManagerId());
        order.setCreatorRepairTel(source.getAuditManagerTel());
        order.setBusinessType(OrderTypeConstant.ZQPT_JH_TYPE);
    }

    // 处理信息（待办、已办）
    private void handleInformation(TaskAttr taskAttr, String orderId, TBoCheckResult byCode) {
        // 待办ID（工单ID）
        String taskId = taskAttr.getTaskId();
        TBoCheckResultOrder one = tBoCheckResultOrderService.getOne(Wrappers.<TBoCheckResultOrder>lambdaQuery()
                .eq(TBoCheckResultOrder::getId, taskId)
        );
        if(one == null){
            // 插入工单信息
            createTBoCheckResultOrder(byCode, taskId);
        }
        // 发送的已办信息
        if("1".equals(taskAttr.getTaskState())){
            finalized(taskId, orderId);
            // 发送待办信息
        }else if("0".equals(taskAttr.getTaskState())){
            todoHandle(taskId, orderId, byCode);
        }
    }

    // 发送已办信息成功
    private void finalized(String taskId, String orderId) {
        TBoCheckResultOrder tBoCheckResultOrder = new TBoCheckResultOrder();
        tBoCheckResultOrder.setId(taskId);
        tBoCheckResultOrder.setResultCode(orderId);
        tBoCheckResultOrder.setRemark("已办信息已添加！");
        tBoCheckResultOrderService.update(tBoCheckResultOrder);
        addCheckResultLog(taskId, orderId, "已办回调成功");
    }

    // 添加日志
    private void addCheckResultLog(String taskId, String orderId, String message) {
        // 添加日志
        TBoCheckResultLog log = new TBoCheckResultLog();
        log.setOperation(message);
        log.setResultCode(orderId);
        log.setResultWorkId(taskId);
        log.setCreateDate(String.valueOf(DateTime.now()));
        tBoCheckResultLogService.save(log);
    }

    // 发送待办信息成功
    private void todoHandle(String taskId, String orderId, TBoCheckResult byCode) {
        TBoCheckResultOrder tBoCheckResultOrder = new TBoCheckResultOrder();
        tBoCheckResultOrder.setId(taskId);
        tBoCheckResultOrder.setResultCode(orderId);
        tBoCheckResultOrder.setStatus("2");
        tBoCheckResultOrderService.update(tBoCheckResultOrder);
        addCheckResultLog(taskId, orderId, "待办回调成功");
        String customizedSms = smsTemplateHandle(byCode);
        // 发送短信信息
        if(StrUtil.isNotEmpty(byCode.getTelphone())){
            sendSmsUtils.sendSMSNotStore(byCode.getTelphone(), customizedSms);
        }
    }

    // 短信模版处理
    @NotNull
    private static String smsTemplateHandle(TBoCheckResult byCode) {
        // 对业务号码脱敏：长度≥7时保留前4位+***+后3位；长度<7时只保留前4位
        String businessCode = byCode.getBusinessCode();
        String maskedDeviceNumber = businessCode != null
                ? (businessCode.length() >= 7
                ? businessCode.substring(0, 4) + "***" + businessCode.substring(businessCode.length() - 3)
                : businessCode.substring(0, Math.min(4, businessCode.length())) + "***")
                : null;
        // 使用replace方法进行字符串替换
        String customizedSms = smsTemplate
                .replace("patch", byCode.getPatch())
                .replace("customer", byCode.getCustomerName())
                .replace("businessCode",  maskedDeviceNumber != null ? maskedDeviceNumber : "");
        return customizedSms;
    }

}
