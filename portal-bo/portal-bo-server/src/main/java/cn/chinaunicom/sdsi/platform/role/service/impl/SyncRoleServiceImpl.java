package cn.chinaunicom.sdsi.platform.role.service.impl;

import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.platform.role.entity.*;
import cn.chinaunicom.sdsi.platform.role.mapper.CoreRoleMapper;
import cn.chinaunicom.sdsi.platform.role.mapper.SyncRoleMapper;
import cn.chinaunicom.sdsi.platform.role.service.IRoleService;
import cn.chinaunicom.sdsi.platform.role.service.SyncRoleService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * @since 2024-07-22
 */
@Service
@Slf4j
public class SyncRoleServiceImpl extends ServiceImpl<SyncRoleMapper, SyncRoleVo> implements SyncRoleService {

    /* 执行同步中台角色信息任务 */

    @Transactional(rollbackFor = Exception.class)
    public int executeSchedule(){
        log.info("同步中台人员角色信息开始了");
        SyncRoleVo roleVo = new SyncRoleVo();
        roleVo.setExecuteType("1");
        List<SyncRoleVo> roleVoList = this.getExecuteSql(roleVo);
        if(roleVoList.size() > 0){
            for (SyncRoleVo syncRoleVo : roleVoList) {
                if(StringUtils.isNotEmpty(syncRoleVo.getExecuteSql())){
                    SyncRoleHisVo roleHisVo = new SyncRoleHisVo();
                    roleHisVo.setExecuteSql(syncRoleVo.getExecuteSql());
                    roleHisVo.setTaskId(syncRoleVo.getId());
                    roleHisVo.setRemark(syncRoleVo.getRemark());
                    if("select".equals(syncRoleVo.getTaskType())){
                        List<SyncRoleVo> list = this.baseMapper.executeScheduleSelect(syncRoleVo);
                        roleHisVo.setExecuteRes("成功查询【"+list.size()+"】条数据");
                    }else if("update".equals(syncRoleVo.getTaskType())){
                        int list = this.baseMapper.executeScheduleUpdate(syncRoleVo);
                        roleHisVo.setExecuteRes("成功删除【"+list+"】条数据");
                    }else if("insert".equals(syncRoleVo.getTaskType())){
                        if(StringUtils.isNotEmpty(syncRoleVo.getRoleCodeSys()) && StringUtils.isNotEmpty(syncRoleVo.getRoleCodeRemote())){
                            String sql = syncRoleVo.getExecuteSql();
                            sql = sql.replace("${sysRoleCode}","'"+syncRoleVo.getRoleCodeSys()+"'");
                            sql = sql.replace("${remoRoleCode}","'"+syncRoleVo.getRoleCodeRemote()+"'");
                            syncRoleVo.setExecuteSql(sql);
                            roleHisVo.setExecuteSql(syncRoleVo.getExecuteSql());
                        }
                        int list = this.baseMapper.executeScheduleInsert(syncRoleVo);
                        roleHisVo.setExecuteRes("成功插入【"+list+"】条数据");
                    }else if("delete".equals(syncRoleVo.getTaskType())){
                        int list = this.baseMapper.executeScheduleDelete(syncRoleVo);
                        roleHisVo.setExecuteRes("成功删除【"+list+"】条数据");
                    }
                    this.taskHisRescord(roleHisVo);
                }
            }
        }
        log.info("同步中台人员角色信息结束了");
        return 0;
    }

    public List<SyncRoleVo> getExecuteSql(SyncRoleVo roleVo){
        return this.baseMapper.getExecuteSql(roleVo);
    }

    public int executeScheduleInsert(SyncRoleVo roleVo){
        return this.baseMapper.executeScheduleInsert(roleVo);
    }

    public int executeScheduleUpdate(SyncRoleVo roleVo){
        return this.baseMapper.executeScheduleUpdate(roleVo);
    }

    public List<SyncRoleVo> executeScheduleSelect(SyncRoleVo roleVo){
        return this.baseMapper.executeScheduleSelect(roleVo);
    }

    public int executeScheduleDelete(SyncRoleVo roleVo){
        return this.baseMapper.executeScheduleDelete(roleVo);
    }

    public int taskHisRescord(SyncRoleHisVo roleVo){
        return this.baseMapper.taskHisRescord(roleVo);
    }

}
