package cn.chinaunicom.sdsi.dbcp.service;

import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignAmount;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpSignAmountQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 等保测评签约明细(月) 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-17
 */
public interface TSanquanDbcpSignAmountService extends IService<TSanquanDbcpSignAmount> {

    // 分页查询
    IPage<TSanquanDbcpSignAmount> findPage(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo);

    // 根据id查询
    TSanquanDbcpSignAmount findOne(String id);

    // 查询列表
    List<TSanquanDbcpSignAmount> findList(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo);

    // 新增
    int add(TSanquanDbcpSignAmount tSanquanDbcpSignAmount);

    // 修改
    int update(TSanquanDbcpSignAmount tSanquanDbcpSignAmount);

    // 删除
    int delete(String id);

    // 导出模版
    void exportTemplate();

    // 导入数据
    Map<String, Object> uploadSignAmount(MultipartFile file);

    void exportData(TSanquanDbcpSignAmountQueryVo tSanquanDbcpSignAmountVo);
}
