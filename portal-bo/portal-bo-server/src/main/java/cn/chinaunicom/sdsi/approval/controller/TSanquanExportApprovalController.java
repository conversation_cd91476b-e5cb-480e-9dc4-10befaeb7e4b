package cn.chinaunicom.sdsi.approval.controller;

import cn.chinaunicom.sdsi.cloud.approval.query.TSanquanExportApprovalQueryVo;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.hutool.core.util.StrUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.approval.service.TSanquanExportApprovalService;
import cn.chinaunicom.sdsi.cloud.approval.entity.TSanquanExportApproval;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 导出审批表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@RestController
@RequestMapping("/approval/export")
public class TSanquanExportApprovalController extends BaseController {

    @Autowired
    private TSanquanExportApprovalService tSanquanExportApprovalService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-06-10
     * @param tSanquanExportApprovalVO
     * @return BasePageResponse<TSanquanExportApproval>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanExportApproval> findPage(TSanquanExportApprovalQueryVo tSanquanExportApprovalVo){
        if(StrUtil.isNotEmpty(tSanquanExportApprovalVo.getFindType())){
            String staffId = UserUtils.getUser().getStaffId();
            if("1".equals(tSanquanExportApprovalVo.getFindType())){
                tSanquanExportApprovalVo.setApproverId(staffId);
            }
            if ("2".equals(tSanquanExportApprovalVo.getFindType())){
                tSanquanExportApprovalVo.setApplicantId(staffId);
            }
        }
        return pageOk(tSanquanExportApprovalService.findPage(tSanquanExportApprovalVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-06-10
     * @param id
     * @return BaseResponse<TSanquanExportApproval>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanExportApproval> findOne(String id) {
        return ok(tSanquanExportApprovalService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-06-10
     * @return BaseResponse<List<TSanquanExportApproval>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanExportApproval>> findList(TSanquanExportApprovalQueryVo tSanquanExportApprovalVo) {
        return ok(tSanquanExportApprovalService.findList(tSanquanExportApprovalVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-06-10
     * @param tSanquanExportApproval
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanExportApproval tSanquanExportApproval){
        return ok(tSanquanExportApprovalService.add(tSanquanExportApproval));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-06-10
     * @param tSanquanExportApproval
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanExportApproval tSanquanExportApproval) {
        return ok(tSanquanExportApprovalService.update(tSanquanExportApproval));
    }

    /**
     * 审核
     * @param tSanquanExportApproval
     * @return
     */
    @PostMapping("/auditApproval")
    public BaseResponse<Integer> auditApproval(@RequestBody TSanquanExportApproval tSanquanExportApproval) {
        return ok(tSanquanExportApprovalService.auditApproval(tSanquanExportApproval));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-06-10
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanExportApprovalService.delete(id));
    }
}
