package cn.chinaunicom.sdsi.dict.commons.controller;

import cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictCustomer;
import cn.chinaunicom.sdsi.cloud.dict.commons.query.TSanquanDictCustomerQueryVo;
import cn.chinaunicom.sdsi.dict.commons.service.TSanquanDictCustomerService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import cn.chinaunicom.sdsi.framework.base.BaseController;
/**
 * <p>
 * 客户DICT数字档案客户 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@RequestMapping("/dict/customer")
public class TSanquanDictCustomerController extends BaseController {

    @Autowired
    private TSanquanDictCustomerService tSanquanDictCustomerService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-04-21
     * @param tSanquanDictCustomerVO
     * @return BasePageResponse<TSanquanDictCustomer>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDictCustomer> findPage(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo){
        return pageOk(tSanquanDictCustomerService.findPage(tSanquanDictCustomerVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-04-21
     * @param id
     * @return BaseResponse<TSanquanDictCustomer>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanDictCustomer> findOne(String id) {
        return ok(tSanquanDictCustomerService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-04-21
     * @return BaseResponse<List<TSanquanDictCustomer>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDictCustomer>> findList(TSanquanDictCustomerQueryVo tSanquanDictCustomerVo) {
        return ok(tSanquanDictCustomerService.findList(tSanquanDictCustomerVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-04-21
     * @param tSanquanDictCustomer
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody TSanquanDictCustomer tSanquanDictCustomer){
        return ok(tSanquanDictCustomerService.add(tSanquanDictCustomer));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-04-21
     * @param tSanquanDictCustomer
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanDictCustomer tSanquanDictCustomer) {
        return ok(tSanquanDictCustomerService.update(tSanquanDictCustomer));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-04-21
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanDictCustomerService.delete(id));
    }
}
