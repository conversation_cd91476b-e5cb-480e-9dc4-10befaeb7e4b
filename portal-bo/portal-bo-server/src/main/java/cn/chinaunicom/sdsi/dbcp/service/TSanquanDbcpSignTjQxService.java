package cn.chinaunicom.sdsi.dbcp.service;

import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignTjQx;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpSignTjQxQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * DS等保测评新签约情况统计（区县） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface TSanquanDbcpSignTjQxService extends IService<TSanquanDbcpSignTjQx> {

    // 分页查询
    IPage<TSanquanDbcpSignTjQx> findPage(TSanquanDbcpSignTjQxQueryVo tSanquanDbcpSignTjQxVo);

    // 根据id查询
    TSanquanDbcpSignTjQx findOne(String id);

    // 查询列表
    List<TSanquanDbcpSignTjQx> findList(TSanquanDbcpSignTjQxQueryVo tSanquanDbcpSignTjQxVo);

    // 新增
    int add(TSanquanDbcpSignTjQx tSanquanDbcpSignTjQx);

    // 修改
    int update(TSanquanDbcpSignTjQx tSanquanDbcpSignTjQx);

    // 删除
    int delete(String id);


    // 获取账期列表
    List<Map<String, String>> getAcctIdList();

    // 获取最大账期
    String getMaxAcctId();

    // 导出数据
    void exportData(TSanquanDbcpSignTjQxQueryVo tSanquanDbcpSignTjQxQueryVo);

}
