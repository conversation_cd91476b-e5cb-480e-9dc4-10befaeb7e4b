package cn.chinaunicom.sdsi.plugins.office.excel.utils;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.*;
import cn.chinaunicom.sdsi.plugins.office.excel.entity.DataParseCell;
import cn.chinaunicom.sdsi.plugins.office.excel.entity.DataParseResult;
import cn.chinaunicom.sdsi.plugins.office.excel.entity.ExcelContent;
import cn.chinaunicom.sdsi.plugins.office.excel.entity.ImportProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/8/17 20:08
 */
@Slf4j
public class ExcelParseUtils {


    /**
     * 解析class 获取属性排序
     */
    public static ExcelContent parseContent(Class<?> mClassEntity) {
        Field[] fields = mClassEntity.getDeclaredFields();
        ExcelContent excelContent = new ExcelContent();
        TitleStyle titleStyle = mClassEntity.getAnnotation(TitleStyle.class);
        if (titleStyle != null) {
            excelContent.setBackgroundColor(titleStyle.backgroundColor());
            excelContent.setFontSize(titleStyle.fontSize());
        }
        for (Field field : fields) {
            ExcelProperty cellProperty = field.getAnnotation(ExcelProperty.class);
            if (cellProperty != null) {
                ExcelTitle fistRowTitle = cellProperty.firstRowCell();
                int rowSpan = 0;
                DataParseCell dataParseFirstCell = new DataParseCell();
                if (fistRowTitle != null && StringUtils.isNotEmpty(fistRowTitle.title())) {
                    rowSpan = 1;
                    dataParseFirstCell = new DataParseCell(fistRowTitle.title(), fistRowTitle.rowSpan(), fistRowTitle.colSpan());
                }
                ExcelTitle secondCell = cellProperty.secondRowCell();
                DataParseCell secondCellValue = new DataParseCell();
                if (secondCell != null && StringUtils.isNotEmpty(secondCell.title())) {
                    rowSpan = 2;
                    secondCellValue = new DataParseCell(secondCell.title(), secondCell.rowSpan(), secondCell.colSpan());
                }
                excelContent.addMaxRow(rowSpan);
                // 拼接关联的SQL
                StringBuilder sqlFilter = new StringBuilder();
                CellQueryTable cellQueryTable = cellProperty.cellQueryTable();
                if (cellQueryTable != null && StringUtils.isNotEmpty(cellQueryTable.tableName()) && StringUtils.isNotEmpty(cellQueryTable.columnName()) && StringUtils.isNotEmpty(cellQueryTable.labelName())) {
                    sqlFilter.append("select ").append(cellQueryTable.labelName()).append(" as 'labelName' from ").append(cellQueryTable.tableName());
                    sqlFilter.append(" where ").append(cellQueryTable.columnName()).append(" = ");
                    if ("int".equals(cellQueryTable.valueType())) {
                        sqlFilter.append("#value#");
                    } else {
                        sqlFilter.append("'#value#'");
                    }
                    sqlFilter.append(cellQueryTable.andWhere());
                }
                DataParseResult annotationData = new DataParseResult(cellProperty.title(), field.getName(),
                        cellProperty.cellSort(), cellProperty.width(), cellProperty.height(), dataParseFirstCell,
                        secondCellValue, cellProperty.rowSpan(), sqlFilter.toString(), cellProperty.fillChildrenValue());
                excelContent.addDataParseResult(annotationData);
            }
        }
        return excelContent;
    }

    /**
     * 创建表格样式
     */
    public static XSSFCellStyle createCellStyle(XSSFWorkbook xssfWorkbook) {
        XSSFCellStyle alignStyle = xssfWorkbook.createCellStyle();
        alignStyle.setAlignment(HorizontalAlignment.CENTER);
        alignStyle.setBorderBottom(BorderStyle.THIN);
        alignStyle.setBorderTop(BorderStyle.THIN);
        alignStyle.setBorderLeft(BorderStyle.THIN);
        alignStyle.setBorderRight(BorderStyle.THIN);
        alignStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return alignStyle;
    }

    /**
     * 获取值
     *
     * @param cell 列
     * @return 内容
     */
    public static String getCellValue(Cell cell) {
        String cellValue = "";
        DecimalFormat df = new DecimalFormat("#");
        System.err.println();
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getRichStringCellValue().getString().trim();
                break;
            case NUMERIC:
                cellValue = df.format(cell.getNumericCellValue());
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue()).trim();
                break;
            case FORMULA:
                cellValue = cell.getCellFormula();
                break;
        }
        return cellValue;
    }

    /**
     * 获取导入excel 数据开始的行
     */
    public static int importDataRowStartIndex(Class<?> mClassEntity) {
        TitleStyle titleStyle = mClassEntity.getAnnotation(TitleStyle.class);
        if (titleStyle != null && titleStyle.importDataRowIndex() >= 0) {
            return titleStyle.importDataRowIndex();
        }
        return 1;
    }

    /**
     * 导入列的索引
     */
    public static Map<String, Integer> importDataCellIndex(Class<?> clazz) {
        Map<String, Integer> map = new HashMap<>();
        try {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                ImportExcelCell cell = field.getAnnotation(ImportExcelCell.class);
                if (cell != null) {
                    map.put(field.getName(), cell.cellIndex());
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return map;
    }

    public static List<ImportProperty> importDataCell(Class<?> clazz) {
        List<ImportProperty> list = new ArrayList<>();
        try {
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                ImportExcelCell cell = field.getAnnotation(ImportExcelCell.class);
                if (cell != null) {
                    list.add(new ImportProperty(field.getName(), cell.cellIndex(), cell.image()));
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return list;
    }
}
