package cn.chinaunicom.sdsi.platform.department.mapper;

import cn.chinaunicom.sdsi.platform.department.entity.Dept;
import cn.chinaunicom.sdsi.platform.department.entity.DeptQuery;
import cn.chinaunicom.sdsi.platform.department.entity.DeptVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 部门表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@Mapper
public interface DeptMapper extends BaseMapper<Dept> {

    /**
     * 分页查询
     * @param page
     * @param query
     * @return
     */
    IPage<Dept> findPage(@Param("page") IPage page,@Param("query") DeptQuery query);

    // 查询部门
    List<DeptVo> findDept(@Param("deptParentId") String deptParentId);
}
