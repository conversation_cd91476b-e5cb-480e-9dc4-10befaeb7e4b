package cn.chinaunicom.sdsi.check.mapper;

import cn.chinaunicom.sdsi.check.entity.TBoCheckResultLog;
import cn.chinaunicom.sdsi.check.vo.TBoCheckResultLogQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * bo稽核推送日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-27
 */
@Mapper
public interface TBoCheckResultLogMapper extends BaseMapper<TBoCheckResultLog> {

    // 分页查询
    IPage<TBoCheckResultLog> findPage(@Param("page") IPage page, @Param("query") TBoCheckResultLogQueryVo tBoCheckResultLogVo);

    // 查询列表
    List<TBoCheckResultLog> findList(@Param("query") TBoCheckResultLogQueryVo tBoCheckResultLogVo);
}
