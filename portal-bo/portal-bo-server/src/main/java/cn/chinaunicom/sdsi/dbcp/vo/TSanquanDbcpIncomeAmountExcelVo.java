package cn.chinaunicom.sdsi.dbcp.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-03-17
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class TSanquanDbcpIncomeAmountExcelVo implements Serializable  {

    private static final long serialVersionUID = 1L;

    /**
     * 期间
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "期间")
    private String period;

    /**
     * 项目编号
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "项目编号")
    private String projectNum;

    /**
     * 项目名称
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "项目名称")
    private String projectName;

    /**
     * 地市
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "地市")
    private String city;

    /**
     * 计收金额
     */
    @ColumnWidth(20)
    @ExcelProperty(value = "计收金额")
    private String jsAmount;

}
