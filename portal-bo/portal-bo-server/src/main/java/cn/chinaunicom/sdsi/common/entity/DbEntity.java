package cn.chinaunicom.sdsi.common.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Setter
@Getter
public class DbEntity {

    /**
     * 推送类型:  0: 单推
     * 1. 多选推
     */
    String type;
    /**
     * 推送名字
     */
    String todoName;
    /**
     * 要客经理工号
     */
    String ownerSubject;

    /**
     * 客户编码(id),客户id
     */
    String custCode;

    /**
     * 客户名称
     */
    String custName;

    /**
     * ob稽核结果id
     */
    String obResultId;

    /**
     * ob稽核结果多选情况下ids
     */
    List<String> obResultIdList;

    /**
     * 创建人
     */
    String createName;
}
