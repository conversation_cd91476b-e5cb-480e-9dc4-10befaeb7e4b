package cn.chinaunicom.sdsi.aiAuditOpportunity.mapper;

import cn.chinaunicom.sdsi.aiAuditOpportunity.entity.AiAuditOpportunity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商机池实体类
 */
@Mapper
public interface AiAuditOpportunityMapper extends BaseMapper<AiAuditOpportunity> {

//    /**
//     * 分页查询商机（手动分页）
//     * @param page 分页参数
//     * @return 分页结果
//     */
//    List<CustomerInsight> selectPageListManual(@Param("page") Integer page, @Param("size") Integer size, @Param("lastId") Integer lastId);


}