package cn.chinaunicom.sdsi.dbcp.service;

import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignTjDs;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanDbcpSignTjDsQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * DS等保测评新签约情况统计（地市） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
public interface TSanquanDbcpSignTjDsService extends IService<TSanquanDbcpSignTjDs> {

    // 分页查询
    IPage<TSanquanDbcpSignTjDs> findPage(TSanquanDbcpSignTjDsQueryVo tSanquanDbcpSignTjDsVo);

    // 根据id查询
    TSanquanDbcpSignTjDs findOne(String id);

    // 查询列表
    List<TSanquanDbcpSignTjDs> findList(TSanquanDbcpSignTjDsQueryVo tSanquanDbcpSignTjDsVo);

    // 新增
    int add(TSanquanDbcpSignTjDs tSanquanDbcpSignTjDs);

    // 修改
    int update(TSanquanDbcpSignTjDs tSanquanDbcpSignTjDs);

    // 删除
    int delete(String id);

    // 获取账期列表
    List<Map<String, String>> getAcctIdList();

    // 获取最大账期
    String getMaxAcctId();

    // 导出数据
    void exportData(TSanquanDbcpSignTjDsQueryVo tSanquanDbcpSignTjDsVo);
}
