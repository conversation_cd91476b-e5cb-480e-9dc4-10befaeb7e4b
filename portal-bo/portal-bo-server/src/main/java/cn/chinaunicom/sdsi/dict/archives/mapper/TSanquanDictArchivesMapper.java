package cn.chinaunicom.sdsi.dict.archives.mapper;

import cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchives;
import cn.chinaunicom.sdsi.cloud.dict.archives.query.TSanquanDictArchivesQueryVo;
import cn.chinaunicom.sdsi.cloud.dict.archives.vo.DictArchivesLogVo;
import cn.chinaunicom.sdsi.cloud.dict.archives.vo.TSanquanDictArchivesVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 客户DICT数字档案 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@Mapper
public interface TSanquanDictArchivesMapper extends BaseMapper<TSanquanDictArchives> {

    // 分页查询
    IPage<TSanquanDictArchives> findPage(@Param("page") IPage page, @Param("query") TSanquanDictArchivesQueryVo tSanquanDictArchivesVo);

    // 查询列表
    List<TSanquanDictArchives> findList(@Param("query") TSanquanDictArchivesQueryVo tSanquanDictArchivesVo);

    // 查询详情
    TSanquanDictArchivesVo getInfoByCustomerId(@Param("customerId") String customerId);

    // 添加
    void saveDictArchivesLog(@Param("logVo") DictArchivesLogVo logVo);
}
