package cn.chinaunicom.sdsi.ssoPath.controller;

import cn.chinaunicom.sdsi.cloud.ssoPath.vo.TSanquanSsoPathVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.ssoPath.service.TSanquanSsoPathService;
import cn.chinaunicom.sdsi.cloud.ssoPath.entity.TSanquanSsoPath;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 单点登录地址 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@RestController
@RequestMapping("/ssoPath")
public class TSanquanSsoPathController extends BaseController {

    @Autowired
    private TSanquanSsoPathService tSanquanSsoPathService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2024-09-29
     * @param tSanquanSsoPathVO
     * @return BasePageResponse<TSanquanSsoPath>
     **/
    @PostMapping("/findPage")
    public BasePageResponse<TSanquanSsoPath> findPage(@RequestBody TSanquanSsoPathVo tSanquanSsoPathVO){
        return pageOk(tSanquanSsoPathService.findPage(tSanquanSsoPathVO));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2024-09-29
     * @param id
     * @return BaseResponse<TSanquanSsoPath>
     **/
    @GetMapping("/findOne")
    public BaseResponse<TSanquanSsoPath> findOne(String id) {
        return ok(tSanquanSsoPathService.findOne(id));
    }


    /*
     * <AUTHOR>
     * @description 根据type查询
     * @since 2024-09-29
     * @param type
     * @return BaseResponse<TSanquanSsoPath>
     **/
    @GetMapping("/findByType")
    public TSanquanSsoPath findByType(String ssoType) {
        return tSanquanSsoPathService.findByType(ssoType);
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2024-09-29
     * @return BaseResponse<List<TSanquanSsoPath>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanSsoPath>> findList() {
        return ok(tSanquanSsoPathService.findList());
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2024-09-29
     * @param tSanquanSsoPath
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Boolean> add(@RequestBody TSanquanSsoPath tSanquanSsoPath){
        return ok(tSanquanSsoPathService.add(tSanquanSsoPath));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2024-09-29
     * @param tSanquanSsoPath
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody TSanquanSsoPath tSanquanSsoPath) {
        return ok(tSanquanSsoPathService.update(tSanquanSsoPath));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2024-09-29
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanSsoPathService.delete(id));
    }

    /*
     * <AUTHOR>
     * @description 检查该类型是否已经存在
     * @since 2024-09-29
     * @param tSanquanSsoPath
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/verifyType")
    public BaseResponse<Boolean> verifyType(String ssoType,String id) {
        return ok(tSanquanSsoPathService.verifyType(ssoType,id) > 0);
    }

}
