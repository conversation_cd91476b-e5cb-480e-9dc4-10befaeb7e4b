package cn.chinaunicom.sdsi.platform.role.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import lombok.experimental.Delegate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_sanquan_platform_role")
public class Role implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    /**
     * 角色名称
     */
    private String roleName;

    private String roleId;

    private String sanquanName;

    /**
     * 角色编码
     */
    private String roleCode;

    private String described;

    private String deleted = "0";

    private String dataScope;

    private String products;

    private String industries;

    private String cityArr;

    private String countyArr;

    private String tempCity;

}
