package cn.chinaunicom.sdsi.check.service.impl;

import cn.chinaunicom.sdsi.check.entity.TBoCheckResultDetailZq;
import cn.chinaunicom.sdsi.check.mapper.TBoCheckResultDetailZqMapper;
import cn.chinaunicom.sdsi.check.service.TBoCheckResultDetailZqService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-27
 */
@Service
public class TBoCheckResultDetailZqServiceImpl extends ServiceImpl<TBoCheckResultDetailZqMapper, TBoCheckResultDetailZq> implements TBoCheckResultDetailZqService {

    @Autowired
    public UnifastContext unifastContext;


    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-05-27
     * @param id
     * @return TBoCheckResultDetailZq
     **/
    @Override
    public TBoCheckResultDetailZq findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-05-27
     * @return List<TBoCheckResultDetailZq>
     **/
    @Override
    public List<TBoCheckResultDetailZq> findList(TBoCheckResultDetailZq tBoCheckResultDetailZqVo) {
        return baseMapper.findList(tBoCheckResultDetailZqVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-05-27
     * @param tBoCheckResultDetailZq
     * @return int
     **/
    @Override
    public int add(TBoCheckResultDetailZq tBoCheckResultDetailZq) {
        return baseMapper.insert(tBoCheckResultDetailZq);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-05-27
     * @param tBoCheckResultDetailZq
     * @return int
     **/
    @Override
    public int update(TBoCheckResultDetailZq tBoCheckResultDetailZq) {
        return baseMapper.updateById(tBoCheckResultDetailZq);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-05-27
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
