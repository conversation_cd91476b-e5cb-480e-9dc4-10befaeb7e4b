package cn.chinaunicom.sdsi.permissionConfig.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *  系统数据权限细化粒度补充表配置
 * </p>
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
public class PermissionConfigVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /*  */
    String id;

    /* 人员姓名 */
    String userName;

    /* 人员oa工号 */
    String oa;

    /* 手机号 */
    String phone;

    /* 层级，1地市，2区县，3营服，4其它 */
    String level;

    /* 是否客户经理，1是，0否 */
    String isExecutor;

    /* 营服机构号 */
    String gridCode;

    /* 营服名称 */
    String gridName;

    /* 区县code */
    String districtCode;

    /* 所属区县 */
    String districtName;

    /* 地市code */
    String city;

    /* 所属地市 */
    String cityName;

    /* 角色名称 */
    String roleName;

    /* 角色编码,可能是中台角色表t_sanquan_permission_role_user */
    String roleCode;

    /* 行业名称 */
    String detailIndustry;

    /* 工号级别 10：省级，20：市级，30：区县级，40：网格级*/
    String mainLevel;

    /* 创建时间 */
    String createTime;

    /*  */
    String updateTime;



}
