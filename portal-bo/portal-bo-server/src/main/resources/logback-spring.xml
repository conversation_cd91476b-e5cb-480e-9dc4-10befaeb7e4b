<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">

    <!-- Log file path -->
    <property name="log.path" value="/home/<USER>/logs/bo" />

    <!-- Console log output -->
    <appender name="console"
              class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level [%logger{50}] - %msg%n
            </pattern>
        </encoder>
    </appender>

<!--        <appender name="console" class="ch.qos.logback.core.ConsoleAppender">-->
<!--            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--                <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/console.%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--                <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                    <maxFileSize>50MB</maxFileSize>-->
<!--                </TimeBasedFileNamingAndTriggeringPolicy>-->
<!--            </rollingPolicy>-->
<!--            <encoder>-->
<!--                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%logger{50}] - %msg%n-->
<!--                </pattern>-->
<!--            </encoder>-->
<!--        </appender>-->

    <!-- Log file debug output -->
    <appender name="fileRolling_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/info.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <TimeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--每个文件最多100MB-->
                <maxFileSize>50MB</maxFileSize>
            </TimeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数-->
            <maxHistory>365</maxHistory>
            <!--每个文件最多100MB，保留15天的历史记录，但最多20GB-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n
            </pattern>
        </encoder>
        <!--<filter class="ch.qos.logback.classic.filter.LevelFilter"> <level>ERROR</level>
            <onMatch>DENY</onMatch> <onMismatch>NEUTRAL</onMismatch> </filter> -->
    </appender>
    <!-- Log file error output -->
    <appender name="fileRolling_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <!--每个文件最多100MB-->
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <!--日志文件保留天数-->
            <maxHistory>365</maxHistory>
            <!--每个文件最多100MB，保留15天的历史记录，但最多20GB-->
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n
            </pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>
<!--    <appender name="file_sql" class="ch.qos.logback.core.FileAppender">-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            <fileNamePattern>${log.path}/%d{yyyy-MM-dd}/sql.%d{yyyy-MM-dd}.%i.log</fileNamePattern>-->
<!--            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">-->
<!--                <maxFileSize>50MB</maxFileSize>-->
<!--            </timeBasedFileNamingAndTriggeringPolicy>-->
<!--        </rollingPolicy>-->
<!--        <encoder>-->
<!--            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n-->
<!--            </pattern>-->
<!--        </encoder>-->
<!--    </appender>-->

    <!-- Level: FATAL 0 ERROR 3 WARN 4 INFO 6 DEBUG 7 -->
    <root level="info">
        <!--{dev.start}-->
<!--        <appender-ref ref="console" />-->
        <!--{dev.end}-->
        <!--{alpha.start}
        <appender-ref ref="fileRolling_info" />
        {alpha.end}-->
        <!--        {release.start}-->
<!--        <appender-ref ref="file_sql" />-->
        <appender-ref ref="fileRolling_info" />
        <!--        {release.end}-->
        <appender-ref ref="fileRolling_error" />
    </root>
    <!-- Framework level setting -->
    <include resource="config/logger-core.xml" />

    <!-- Project level setting -->
    <!-- <logger name="your.package" level="DEBUG" /> -->
    <logger name="org.springframework" level="INFO"></logger>
    <logger name="org.mybatis" level="INFO"></logger>
</configuration>
