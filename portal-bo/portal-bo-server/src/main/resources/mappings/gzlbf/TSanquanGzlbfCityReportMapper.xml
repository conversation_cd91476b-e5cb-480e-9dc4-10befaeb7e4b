<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.gzlbf.mapper.TSanquanGzlbfCityReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityReport">
        <result column="day_id" property="dayId" />
        <result column="rh_index" property="rhIndex" />
        <result column="area_id" property="areaId" />
        <result column="area_name" property="areaName" />
        <result column="city_id" property="cityId" />
        <result column="city_name" property="cityName" />
        <result column="camp_id" property="campId" />
        <result column="camp_name" property="campName" />
        <result column="zf_youxiao_cn" property="zfYouxiaoCn" />
        <result column="zf_khs_lweek" property="zfKhsLweek" />
        <result column="sj_khs_lweek" property="sjKhsLweek" />
        <result column="fgl_zfkhs_jd" property="fglZfkhsJd" />
        <result column="fgl_khzf_rate_jd" property="fglKhzfRateJd" />
        <result column="sj_fee_jd" property="sjFeeJd" />
        <result column="sj_khfg_rate_year" property="sjKhfgRateYear" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        day_id, rh_index, area_id, area_name, city_id, city_name, camp_id, camp_name, zf_youxiao_cn, zf_khs_lweek, sj_khs_lweek, fgl_zfkhs_jd, fgl_khzf_rate_jd, sj_fee_jd, sj_khfg_rate_year
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityReport">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_gzlbf_hygzlbf_dsbf
        <where>
            <if test="query.dayId != null and query.dayId != ''">
                and day_id = #{query.dayId}
            </if>
            <if test="query.rhIndex != null and query.rhIndex != ''">
                and rh_index = #{query.rhIndex}
            </if>
            <if test="query.areaId != null and query.areaId != ''">
                and area_id = #{query.areaId}
            </if>
            <if test="query.areaName != null and query.areaName != ''">
                and area_name = #{query.areaName}
            </if>
            <if test="query.areaName2 != null and query.areaName2 != ''">
                and area_name = #{query.areaName2}
            </if>
            <if test="query.cityId != null and query.cityId != ''">
                and city_id = #{query.cityId}
            </if>
            <if test="query.cityName != null and query.cityName != ''">
                and city_name = #{query.cityName}
            </if>

            <if test="query.campId != null and query.campId != ''">
                and camp_id = #{query.campId}
            </if>
            <if test="query.campName != null and query.campName != ''">
                and camp_name = #{query.campName}
            </if>
            <if test="query.zfYouxiaoCn != null and query.zfYouxiaoCn != ''">
                and zf_youxiao_cn = #{query.zfYouxiaoCn}
            </if>
            <if test="query.zfKhsLweek != null and query.zfKhsLweek != ''">
                and zf_khs_lweek = #{query.zfKhsLweek}
            </if>
            <if test="query.sjKhsLweek != null and query.sjKhsLweek != ''">
                and sj_khs_lweek = #{query.sjKhsLweek}
            </if>
            <if test="query.fglZfkhsJd != null and query.fglZfkhsJd != ''">
                and fgl_zfkhs_jd = #{query.fglZfkhsJd}
            </if>
            <if test="query.fglKhzfRateJd != null and query.fglKhzfRateJd != ''">
                and fgl_khzf_rate_jd = #{query.fglKhzfRateJd}
            </if>
            <if test="query.sjFeeJd != null and query.sjFeeJd != ''">
                and sj_fee_jd = #{query.sjFeeJd}
            </if>
            <if test="query.sjKhfgRateYear != null and query.sjKhfgRateYear != ''">
                and sj_khfg_rate_year = #{query.sjKhfgRateYear}
            </if>
            ${query.dataScopeSqlFilter}
        </where>
        order by day_id desc, rh_index
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.gzlbf.entity.TSanquanGzlbfCityReport">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_gzlbf_hygzlbf_dsbf
        <where>
            <if test="query.dayId != null and query.dayId != ''">
                and day_id = #{query.dayId}
            </if>
            <if test="query.rhIndex != null and query.rhIndex != ''">
                and rh_index = #{query.rhIndex}
            </if>
            <if test="query.areaId != null and query.areaId != ''">
                and area_id = #{query.areaId}
            </if>
            <if test="query.areaName != null and query.areaName != ''">
                and area_name = #{query.areaName}
            </if>
            <if test="query.areaName2 != null and query.areaName2 != ''">
                and area_name = #{query.areaName2}
            </if>
            <if test="query.cityId != null and query.cityId != ''">
                and city_id = #{query.cityId}
            </if>
            <if test="query.cityName != null and query.cityName != ''">
                and city_name = #{query.cityName}
            </if>
            <if test="query.campId != null and query.campId != ''">
                and camp_id = #{query.campId}
            </if>
            <if test="query.campName != null and query.campName != ''">
                and camp_name = #{query.campName}
            </if>
            <if test="query.zfYouxiaoCn != null and query.zfYouxiaoCn != ''">
                and zf_youxiao_cn = #{query.zfYouxiaoCn}
            </if>
            <if test="query.zfKhsLweek != null and query.zfKhsLweek != ''">
                and zf_khs_lweek = #{query.zfKhsLweek}
            </if>
            <if test="query.sjKhsLweek != null and query.sjKhsLweek != ''">
                and sj_khs_lweek = #{query.sjKhsLweek}
            </if>
            <if test="query.fglZfkhsJd != null and query.fglZfkhsJd != ''">
                and fgl_zfkhs_jd = #{query.fglZfkhsJd}
            </if>
            <if test="query.fglKhzfRateJd != null and query.fglKhzfRateJd != ''">
                and fgl_khzf_rate_jd = #{query.fglKhzfRateJd}
            </if>
            <if test="query.sjFeeJd != null and query.sjFeeJd != ''">
                and sj_fee_jd = #{query.sjFeeJd}
            </if>
            <if test="query.sjKhfgRateYear != null and query.sjKhfgRateYear != ''">
                and sj_khfg_rate_year = #{query.sjKhfgRateYear}
            </if>
            ${query.dataScopeSqlFilter}
        </where>
        order by day_id desc, rh_index
    </select>

    <select id="getMaxPatch" resultType="java.lang.String">
        select max(day_id) dayId from t_sanquan_gzlbf_hygzlbf_dsbf
    </select>

    <select id="getCityList" resultType="java.lang.String">
        select DISTINCT area_name from t_sanquan_gzlbf_hygzlbf_dsbf a

    </select>

</mapper>
