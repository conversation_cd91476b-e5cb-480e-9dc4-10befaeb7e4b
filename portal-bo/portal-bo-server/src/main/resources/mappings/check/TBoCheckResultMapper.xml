<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.check.mapper.TBoCheckResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.check.entity.TBoCheckResult">
        <result column="ID" property="id" />
        <result column="code" property="code" />
        <result column="patch" property="patch" />
        <result column="business_code" property="businessCode" />
        <result column="status" property="status" />
        <result column="rectificate_person" property="rectificatePerson" />
        <result column="telphone" property="telphone" />
        <result column="job_num" property="jobNum" />
        <result column="city" property="city" />
        <result column="business_name" property="businessName" />
        <result column="product_name" property="productName" />
        <result column="accept_sys" property="acceptSys" />
        <result column="circuit" property="circuit" />
        <result column="customer_name" property="customerName" />
        <result column="district_cbss" property="districtCbss" />
        <result column="grid_cbss" property="gridCbss" />
        <result column="channel" property="channel" />
        <result column="development" property="development" />
        <result column="development_tel" property="developmentTel" />
        <result column="development_oa" property="developmentOa" />
        <result column="contract_code" property="contractCode" />
        <result column="service_status" property="serviceStatus" />
        <result column="rate_cbss" property="rateCbss" />
        <result column="sys_fee" property="sysFee" />
        <result column="address_a_cbss" property="addressACbss" />
        <result column="address_z_cbss" property="addressZCbss" />
        <result column="account_open_time" property="accountOpenTime" />
        <result column="rent_start_time" property="rentStartTime" />
        <result column="cancel_time" property="cancelTime" />
        <result column="fee_time" property="feeTime" />
        <result column="business_detail" property="businessDetail" />
        <result column="circuit_code" property="circuitCode" />
        <result column="district_net" property="districtNet" />
        <result column="grid_net" property="gridNet" />
        <result column="address_a_net" property="addressANet" />
        <result column="address_z_net" property="addressZNet" />
        <result column="business_area" property="businessArea" />
        <result column="is_test_customer" property="isTestCustomer" />
        <result column="customer_status" property="customerStatus" />
        <result column="rate_net" property="rateNet" />
        <result column="business_type" property="businessType" />
        <result column="bo_state" property="boState" />
        <result column="bo_rate" property="boRate" />
        <result column="bo_state_last" property="boStateLast" />
        <result column="bo_rate_last" property="boRateLast" />
        <result column="bo_state_num" property="boStateNum" />
        <result column="bo_rate_num" property="boRateNum" />
        <result column="remark" property="remark" />
        <result column="send_time" property="sendTime" />
        <result column="create_time" property="createTime" />
        <result column="push_name" property="pushName" />
        <result column="push_oa" property="pushOa" />
        <result column="push_tel" property="pushTel" />
        <result column="result_order" property="resultOrder" />
        <result column="grid_manager_oa" property="gridManagerOa" />
        <result column="grid_manager" property="gridManager" />
        <result column="grid_manager_tel" property="gridManagerTel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, code, patch, business_code, status, rectificate_person, telphone, job_num, city, business_name, product_name, accept_sys, circuit, customer_name, district_cbss, grid_cbss, channel, development, contract_code, service_status, rate_cbss, sys_fee, address_a_cbss, address_z_cbss, account_open_time, rent_start_time, cancel_time, fee_time, business_detail, circuit_code, district_net, grid_net, address_a_net, address_z_net, business_area, is_test_customer, customer_status, rate_net, business_type, bo_state, bo_rate, bo_state_last, bo_rate_last, bo_state_num, bo_rate_num, remark, send_time, create_time
          , development_tel, development_oa, push_name, push_oa, push_tel, result_order, grid_manager_oa, grid_manager, grid_manager_tel
    </sql>
    <select id="selectPageList" resultType="cn.chinaunicom.sdsi.check.entity.TBoCheckResult">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        <where>
            (
                bo_state != '2' AND bo_rate != '2'
            )
            <if test="query.id != null and query.id != ''">
                AND ID = #{query.id}
            </if>
            <if test="query.code != null and query.code != ''">
                AND code = #{query.code}
            </if>
            <if test="query.patch != null and query.patch != ''">
                AND patch = #{query.patch}
            </if>
            <if test="query.businessName != null and query.businessName != ''">
                AND business_name like concat('%',#{query.businessName},'%')
            </if>
            <if test="query.businessCode != null and query.businessCode != ''">
                AND business_code = #{query.businessCode}
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.rectificate != null and query.rectificate != ''">
                AND (rectificate_person like concat('%',#{query.rectificate},'%') OR job_num like concat('%',#{query.rectificate},'%'))
            </if>
            <if test="query.city != null and query.city != ''">
                AND city = #{query.city}
            </if>
            <if test="query.acceptSys != null and query.acceptSys != ''">
                AND accept_sys = #{query.acceptSys}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name like concat('%',#{query.customerName},'%')
            </if>
            <if test="query.development != null and query.development != ''">
                AND development like concat('%',#{query.development},'%')
            </if>
            <if test="query.serviceStatus != null and query.serviceStatus != ''">
                AND service_status = #{query.serviceStatus}
            </if>
            <if test="query.boState != null and query.boState != ''">
                AND bo_state = #{query.boState}
            </if>
        </where>
        ${query.dataScopeSqlFilter}
        order by create_time, code desc
    </select>
    <select id="getByCode" resultType="cn.chinaunicom.sdsi.check.entity.TBoCheckResult">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        where code = #{obResultId}
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>
    <select id="getByCodeList" resultType="cn.chinaunicom.sdsi.check.entity.TBoCheckResult">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        where code in
        <foreach collection="obResultList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.check.entity.TBoCheckResult">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        <where>
            <if test="query.id != null and query.id != ''">
                AND ID = #{query.id}
            </if>
            <if test="query.code != null and query.code != ''">
                AND code = #{query.code}
            </if>
            <if test="query.patch != null and query.patch != ''">
                AND patch = #{query.patch}
            </if>
            <if test="query.businessName != null and query.businessName != ''">
                AND business_name like concat('%',#{query.businessName},'%')
            </if>
            <if test="query.businessCode != null and query.businessCode != ''">
                AND business_code = #{query.businessCode}
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.rectificate != null and query.rectificate != ''">
                AND (rectificate_person like concat('%',#{query.rectificate},'%') OR job_num like concat('%',#{query.rectificate},'%'))
            </if>
            <if test="query.city != null and query.city != ''">
                AND city = #{query.city}
            </if>
            <if test="query.acceptSys != null and query.acceptSys != ''">
                AND accept_sys = #{query.acceptSys}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name like concat('%',#{query.customerName},'%')
            </if>
            <if test="query.development != null and query.development != ''">
                AND development like concat('%',#{query.development},'%')
            </if>
            <if test="query.serviceStatus != null and query.serviceStatus != ''">
                AND service_status = #{query.serviceStatus}
            </if>
            <if test="query.boState != null and query.boState != ''">
                AND bo_state = #{query.boState}
            </if>
        </where>
        order by create_time, code desc
    </select>
    <select id="getByIdList" resultType="cn.chinaunicom.sdsi.check.entity.TBoCheckResult">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        where id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and `status` != '1'
    </select>

    <select id="getMaxPatch" resultType="java.lang.String">
        SELECT
        max(patch)
        FROM t_bo_check_result
    </select>
    <select id="getPatchList" resultType="java.util.Map">
        SELECT
            patch as patch
        FROM t_bo_check_result
        group by patch
        order by patch desc
    </select>
    <select id="findByCode" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultVO">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        where code = #{code}
        limit 1
    </select>
    <select id="findListExcel" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultExcel">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_bo_check_result
        <where>
            (
                bo_state != '2' AND bo_rate != '2'
            )
            <if test="query.id != null and query.id != ''">
                AND ID = #{query.id}
            </if>
            <if test="query.code != null and query.code != ''">
                AND code = #{query.code}
            </if>
            <if test="query.patch != null and query.patch != ''">
                AND patch = #{query.patch}
            </if>
            <if test="query.businessName != null and query.businessName != ''">
                AND business_name like concat('%',#{query.businessName},'%')
            </if>
            <if test="query.businessCode != null and query.businessCode != ''">
                AND business_code = #{query.businessCode}
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.rectificate != null and query.rectificate != ''">
                AND (rectificate_person like concat('%',#{query.rectificate},'%') OR job_num like concat('%',#{query.rectificate},'%'))
            </if>
            <if test="query.city != null and query.city != ''">
                AND city = #{query.city}
            </if>
            <if test="query.acceptSys != null and query.acceptSys != ''">
                AND accept_sys = #{query.acceptSys}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name like concat('%',#{query.customerName},'%')
            </if>
            <if test="query.development != null and query.development != ''">
                AND development like concat('%',#{query.development},'%')
            </if>
            <if test="query.serviceStatus != null and query.serviceStatus != ''">
                AND service_status = #{query.serviceStatus}
            </if>
            <if test="query.boState != null and query.boState != ''">
                AND bo_state = #{query.boState}
            </if>
        </where>
        ${query.dataScopeSqlFilter}
        order by create_time, code desc
        limit 10000
    </select>


</mapper>
