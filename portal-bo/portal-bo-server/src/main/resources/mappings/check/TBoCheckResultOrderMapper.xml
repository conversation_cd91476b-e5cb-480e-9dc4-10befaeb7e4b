<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.check.mapper.TBoCheckResultOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.check.entity.TBoCheckResultOrder">
        <result column="ID" property="id" />
        <result column="result_code" property="resultCode" />
        <result column="process_code" property="processCode" />
        <result column="order_code" property="orderCode" />
        <result column="order_level" property="orderLevel" />
        <result column="repair_status" property="repairStatus" />
        <result column="status" property="status" />
        <result column="feedback_time" property="feedbackTime" />
        <result column="repair_time" property="repairTime" />
        <result column="feedback_info" property="feedbackInfo" />
        <result column="repair_info" property="repairInfo" />
        <result column="remark" property="remark" />
        <result column="creator_orgin" property="creatorOrgin" />
        <result column="creator_repair" property="creatorRepair" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="updator" property="updator" />
        <result column="creator_orgin_oa" property="creatorOrginOa" />
        <result column="creator_repair_oa" property="creatorRepairOa" />
        <result column="creator_oa" property="creatorOa" />
        <result column="deleted" property="deleted" />
        <result column="creator_orgin_tel" property="creatorOrginTel" />
        <result column="creator_repair_tel" property="creatorRepairTel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, result_code, process_code, order_code, order_level, repair_status, status, feedback_time, repair_time, feedback_info, repair_info, remark, creator_orgin, creator_repair, creator, create_time, update_time, updator
          , creator_orgin_oa, creator_repair_oa, creator_oa, deleted, creator_orgin_tel, creator_repair_tel
    </sql>
    <select id="findOneById" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultOrderVO">
        select
            cro.id,
            cro.result_code,
            cro.process_code,
            cro.order_code,
            cro.order_level,
            cro.repair_status,
            cro.status,
            cro.feedback_time,
            cro.repair_time,
            cro.feedback_info,
            cro.repair_info,
            cro.remark,
            cro.creator_orgin,
            cro.creator_repair,
            cro.creator,
            cro.create_time,
            cro.update_time,
            cro.updator,
            cro.creator_orgin_oa,
            cro.creator_repair_oa,
            cro.creator_oa,
            cro.deleted,
            cro.creator_orgin_tel,
            cr.patch,
            cr.business_code,
            cr.rectificate_person,
            cr.telphone,
            cr.job_num,
            cr.city,
            cr.business_name,
            cr.product_name,
            cr.accept_sys,
            cr.circuit,
            cr.customer_name,
            cr.district_cbss,
            cr.grid_cbss,
            cr.channel,
            cr.development,
            cr.contract_code,
            cr.service_status,
            cr.rate_cbss,
            cr.sys_fee,
            cr.address_a_cbss,
            cr.address_z_cbss,
            cr.account_open_time,
            cr.rent_start_time,
            cr.cancel_time,
            cr.fee_time,
            cr.business_detail,
            cr.circuit_code,
            cr.district_net,
            cr.grid_net,
            cr.address_a_net,
            cr.address_z_net,
            cr.business_area,
            cr.is_test_customer,
            cr.customer_status,
            cr.rate_net,
            cr.business_type,
            cr.bo_state,
            cr.bo_rate,
            cr.bo_state_last,
            cr.bo_rate_last,
            cr.bo_state_num,
            cr.bo_rate_num,
            cr.send_time,
            cr.development_tel,
            cr.development_oa
        from t_bo_check_result_order cro
        left join t_bo_check_result cr on cro.result_code = cr.code
        where cro.id = #{id}
        and cr.status = '1'
    </select>

    <select id="selectOrderPage" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultQuery">
        select a.id,a.result_code,a.order_code, a.repair_status, a.status as orderStatus,a.feedback_time,
        a.repair_time, a.feedback_info, a.repair_info,  a.creator,b.patch,
        b.business_code, b.status, b.rectificate_person, b.telphone, b.job_num, b.city, b.business_name,
        b.product_name, b.accept_sys, b.circuit, b.customer_name, b.district_cbss, b.grid_cbss, b.channel,
        b.development, b.contract_code, b.service_status, b.rate_cbss, b.sys_fee, b.address_a_cbss,
        b.address_z_cbss, b.account_open_time, b.rent_start_time, b.cancel_time, b.fee_time, b.business_detail,
        b.circuit_code, b.district_net, b.grid_net, b.address_a_net, b.address_z_net,
        b.business_area, b.is_test_customer, b.customer_status, b.rate_net, b.business_type, b.bo_state,
        b.bo_rate, b.bo_state_last, b.bo_rate_last, b.bo_state_num, b.bo_rate_num,  a.create_time as send_time, b.create_time
        , b.development_tel, b.development_oa
        from t_bo_check_result_order a
        left join t_bo_check_result b on a.result_code = b.code
        <where>  b.bo_state in (1,0) and b.bo_rate in (1,0)
            <if test="query.id != null and query.id != '' ">
                and a.id = #{query.id}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != '' ">
                and a.status = #{query.orderStatus}
            </if>
            <if test="query.creator != null and query.creator != '' ">
                and a.creator = #{query.creator}
            </if>
            <if test="query.patch != null and query.patch != '' ">
                 and b.patch = #{query.patch}
            </if>
            <if test="query.businessName != null and query.businessName != '' ">
                and b.business_name = #{query.businessName}
            </if>
            <if test="query.city != null and query.city != '' ">
                and b.city = #{query.city}
            </if>
            <if test="query.acceptSys != null and query.acceptSys != '' ">
                and b.accept_sys = #{query.acceptSys}
            </if>
            <if test="query.customerName != null and query.customerName != '' ">
                and b.customer_name = #{query.customerName}
            </if>
            <if test="query.serviceStatus != null and query.serviceStatus != '' ">
                and b.service_status = #{query.serviceStatus}
            </if>
            <if test="query.sendTime != null and query.sendTime != '' ">
                and b.send_time = #{query.sendTime}
            </if>
            <if test="query.businessCode != null and query.businessCode != '' ">
                and b.business_code = #{query.businessCode}
            </if>
            <if test="query.startTime != null and query.endTime != null ">
                and b.send_time between #{query.startTime} and #{query.endTime}
            </if>
        </where>
        ${query.dataScopeSqlFilter}
        order by a.id desc
    </select>

    <select id="selectOrderList" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultOrderExcel">
        select a.id,a.result_code,a.order_code, a.repair_status, a.status as orderStatus,a.feedback_time,
        a.repair_time, a.feedback_info, a.repair_info,  a.creator,b.patch,
        b.business_code, b.status, b.rectificate_person, b.telphone, b.job_num, b.city, b.business_name,
        b.product_name, b.accept_sys, b.circuit, b.customer_name, b.district_cbss, b.grid_cbss, b.channel,
        b.development, b.contract_code, b.service_status, b.rate_cbss, b.sys_fee, b.address_a_cbss,
        b.address_z_cbss, b.account_open_time, b.rent_start_time, b.cancel_time, b.fee_time, b.business_detail,
        b.circuit_code, b.district_net, b.grid_net, b.address_a_net, b.address_z_net,
        b.business_area, b.is_test_customer, b.customer_status, b.rate_net, b.business_type, b.bo_state,
        b.bo_rate, b.bo_state_last, b.bo_rate_last, b.bo_state_num, b.bo_rate_num,  b.send_time, b.create_time
        , b.development_tel, b.development_oa
        from t_bo_check_result_order a
        join t_bo_check_result b on a.result_code = b.code
        <where> b.bo_state in (1,0) and b.bo_rate in (1,0)
            <if test="query.id != null and query.id != '' ">
                and a.id = #{query.id}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != '' ">
                and a.status = #{query.orderStatus}
            </if>
            <if test="query.creator != null and query.creator != '' ">
                and a.creator = #{query.creator}
            </if>
            <if test="query.patch != null and query.patch != '' ">
                and b.patch = #{query.patch}
            </if>
            <if test="query.businessName != null and query.businessName != '' ">
                and b.business_name = #{query.businessName}
            </if>
            <if test="query.city != null and query.city != '' ">
                and b.city = #{query.city}
            </if>
            <if test="query.acceptSys != null and query.acceptSys != '' ">
                and b.accept_sys = #{query.acceptSys}
            </if>
            <if test="query.customerName != null and query.customerName != '' ">
                and b.customer_name = #{query.customerName}
            </if>
            <if test="query.serviceStatus != null and query.serviceStatus != '' ">
                and b.service_status = #{query.serviceStatus}
            </if>
            <if test="query.sendTime != null and query.sendTime != '' ">
                and b.send_time = #{query.sendTime}
            </if>
            <if test="query.businessCode != null and query.businessCode != '' ">
                and b.business_code = #{query.businessCode}
            </if>
            <if test="query.startTime != null and query.endTime != null ">
                and b.send_time between #{query.startTime} and #{query.endTime}
            </if>
        </where>
        ${query.dataScopeSqlFilter}
        limit 10000
    </select>

    <select id="selectOrderRepairInfo" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultQuery">
        WITH orderResult AS (
            SELECT *,ROW_NUMBER() OVER(PARTITION BY process_code ORDER BY order_level desc) as rn
            FROM t_bo_check_result_order
            where result_code = #{query.resultCode}
        )
        select * from orderResult
        where rn = 1
    </select>

    <select id="findOrderHistoryPage" resultType="cn.chinaunicom.sdsi.check.vo.TBoCheckResultOrderQuery">
        select * from t_bo_check_result_order
        where result_code = #{query.resultCode}
    </select>
    <select id="getByCode" resultType="cn.chinaunicom.sdsi.check.entity.TBoCheckResultOrder">
        select * from t_bo_check_result_order
                 where result_code = #{code}
                order by order_level desc
        limit 1
    </select>

</mapper>
