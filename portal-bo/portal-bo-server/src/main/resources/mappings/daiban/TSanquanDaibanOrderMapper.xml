<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.daiban.mapper.TSanquanDaibanOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrder">
        <result column="ID" property="id" />
        <result column="business_id" property="businessId" />
        <result column="business_type" property="businessType" />
        <result column="process_code" property="processCode" />
        <result column="order_code" property="orderCode" />
        <result column="order_level" property="orderLevel" />
        <result column="repair_status" property="repairStatus" />
        <result column="status" property="status" />
        <result column="feedback_time" property="feedbackTime" />
        <result column="repair_time" property="repairTime" />
        <result column="feedback_info" property="feedbackInfo" />
        <result column="repair_info" property="repairInfo" />
        <result column="remark" property="remark" />
        <result column="creator_orgin" property="creatorOrgin" />
        <result column="creator_repair" property="creatorRepair" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="updator" property="updator" />
        <result column="creator_orgin_oa" property="creatorOrginOa" />
        <result column="creator_repair_oa" property="creatorRepairOa" />
        <result column="creator_oa" property="creatorOa" />
        <result column="deleted" property="deleted" />
        <result column="creator_orgin_tel" property="creatorOrginTel" />
        <result column="creator_repair_tel" property="creatorRepairTel" />
        <result column="feedback_result_describe" property="feedbackResultDescribe" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ID, business_id, business_type, process_code, order_code, order_level, repair_status, status, feedback_time, repair_time, feedback_info, repair_info, remark, creator_orgin, creator_repair, creator, create_time, update_time, updator, creator_orgin_oa, creator_repair_oa, creator_oa, deleted, creator_orgin_tel, creator_repair_tel
          ,feedback_result_describe
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrder">
        select * from t_sanquan_daiban_order
        where 1=1
        <if test="query.id != null and query.id != ''">
            and ID = #{query.id}
        </if>
        <if test="query.businessId != null and query.businessId != ''">
            and business_id = #{query.businessId}
        </if>
        <if test="query.businessType != null and query.businessType != ''">
            and business_type = #{query.businessType}
        </if>
        <if test="query.processCode != null and query.processCode != ''">
            and process_code = #{query.processCode}
        </if>
        <if test="query.orderCode != null and query.orderCode != ''">
            and order_code = #{query.orderCode}
        </if>
        <if test="query.orderLevel != null and query.orderLevel != ''">
            and order_level = #{query.orderLevel}
        </if>
        <if test="query.repairStatus != null and query.repairStatus != ''">
            and repair_status = #{query.repairStatus}
        </if>
        <if test="query.status != null and query.status != ''">
            and status = #{query.status}
        </if>
        <if test="query.feedbackTime != null and query.feedbackTime != ''">
            and feedback_time = #{query.feedbackTime}
        </if>
        <if test="query.repairTime != null and query.repairTime != ''">
            and repair_time = #{query.repairTime}
        </if>
        <if test="query.feedbackInfo != null and query.feedbackInfo != ''">
            and feedback_info = #{query.feedbackInfo}
        </if>
        <if test="query.repairInfo != null and query.repairInfo != ''">
            and repair_info = #{query.repairInfo}
        </if>
        <if test="query.remark != null and query.remark != ''">
            and remark = #{query.remark}
        </if>
        <if test="query.creatorOrgin != null and query.creatorOrgin != ''">
            and creator_orgin = #{query.creatorOrgin}
        </if>
        <if test="query.creatorRepair != null and query.creatorRepair != ''">
            and creator_repair = #{query.creatorRepair}
        </if>
        <if test="query.creator != null and query.creator != ''">
            and creator = #{query.creator}
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            and create_time = #{query.createTime}
        </if>
        <if test="query.updateTime != null and query.updateTime != ''">
            and update_time = #{query.updateTime}
        </if>
        <if test="query.updator != null and query.updator != ''">
            and updator = #{query.updator}
        </if>
        <if test="query.creatorOrginOa != null and query.creatorOrginOa != ''">
            and creator_orgin_oa = #{query.creatorOrginOa}
        </if>
        <if test="query.creatorRepairOa != null and query.creatorRepairOa != ''">
            and creator_repair_oa = #{query.creatorRepairOa}
        </if>
        <if test="query.creatorOa != null and query.creatorOa != ''">
            and creator_oa = #{query.creatorOa}
        </if>
        <if test="query.deleted != null and query.deleted != ''">
            and deleted = #{query.deleted}
        </if>
        <if test="query.creatorOrginTel != null and query.creatorOrginTel != ''">
            and creator_orgin_tel = #{query.creatorOrginTel}
        </if>
        <if test="query.creatorRepairTel != null and query.creatorRepairTel != ''">
            and creator_repair_tel = #{query.creatorRepairTel}
        </if>
        order by create_time, ID desc
    </select>
    <select id="getByProcessCode" resultType="cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrder">
        select * from t_sanquan_daiban_order
        where process_code = #{orderCode}
        order by order_level desc
        limit 1
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.daiban.entity.TSanquanDaibanOrder">
        select * from t_sanquan_daiban_order
        where 1=1
        <if test="query.id != null and query.id != ''">
            and ID = #{query.id}
        </if>
        <if test="query.businessId != null and query.businessId != ''">
            and business_id = #{query.businessId}
        </if>
        <if test="query.businessType != null and query.businessType != ''">
            and business_type = #{query.businessType}
        </if>
        <if test="query.processCode != null and query.processCode != ''">
            and process_code = #{query.processCode}
        </if>
        <if test="query.orderCode != null and query.orderCode != ''">
            and order_code = #{query.orderCode}
        </if>
        <if test="query.orderLevel != null and query.orderLevel != ''">
            and order_level = #{query.orderLevel}
        </if>
        <if test="query.repairStatus != null and query.repairStatus != ''">
            and repair_status = #{query.repairStatus}
        </if>
        <if test="query.status != null and query.status != ''">
            and status = #{query.status}
        </if>
        <if test="query.feedbackTime != null and query.feedbackTime != ''">
            and feedback_time = #{query.feedbackTime}
        </if>
        <if test="query.repairTime != null and query.repairTime != ''">
            and repair_time = #{query.repairTime}
        </if>
        <if test="query.feedbackInfo != null and query.feedbackInfo != ''">
            and feedback_info = #{query.feedbackInfo}
        </if>
        <if test="query.repairInfo != null and query.repairInfo != ''">
            and repair_info = #{query.repairInfo}
        </if>
        <if test="query.remark != null and query.remark != ''">
            and remark = #{query.remark}
        </if>
        <if test="query.creatorOrgin != null and query.creatorOrgin != ''">
            and creator_orgin = #{query.creatorOrgin}
        </if>
        <if test="query.creatorRepair != null and query.creatorRepair != ''">
            and creator_repair = #{query.creatorRepair}
        </if>
        <if test="query.creator != null and query.creator != ''">
            and creator = #{query.creator}
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            and create_time = #{query.createTime}
        </if>
        <if test="query.updateTime != null and query.updateTime != ''">
            and update_time = #{query.updateTime}
        </if>
        <if test="query.updator != null and query.updator != ''">
            and updator = #{query.updator}
        </if>
        <if test="query.creatorOrginOa != null and query.creatorOrginOa != ''">
            and creator_orgin_oa = #{query.creatorOrginOa}
        </if>
        <if test="query.creatorRepairOa != null and query.creatorRepairOa != ''">
            and creator_repair_oa = #{query.creatorRepairOa}
        </if>
        <if test="query.creatorOa != null and query.creatorOa != ''">
            and creator_oa = #{query.creatorOa}
        </if>
        <if test="query.deleted != null and query.deleted != ''">
            and deleted = #{query.deleted}
        </if>
        <if test="query.creatorOrginTel != null and query.creatorOrginTel != ''">
            and creator_orgin_tel = #{query.creatorOrginTel}
        </if>
        <if test="query.creatorRepairTel != null and query.creatorRepairTel != ''">
            and creator_repair_tel = #{query.creatorRepairTel}
        </if>
        order by create_time, ID desc
    </select>

</mapper>
