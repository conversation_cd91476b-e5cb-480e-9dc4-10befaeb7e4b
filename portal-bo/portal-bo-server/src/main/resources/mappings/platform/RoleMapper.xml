<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.platform.role.mapper.CoreRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.platform.role.entity.RoleVo">
        <id column="id" property="id"/>
        <result column="role_name" property="roleName"/>
        <result column="role_id" property="roleId"/>
        <result column="sanquan_name" property="sanquanName"/>
        <result column="role_code" property="roleCode"/>
        <result column="deleted" property="deleted"/>
        <result column="priority_level" property="priorityLevel"/>
    </resultMap>
    <select id="findList" resultType="cn.chinaunicom.sdsi.platform.role.entity.RoleVo">
        select * from t_sanquan_platform_role
        <where>
            deleted='0'
            <if test="query.roleName != null and query.roleName != ''">
                AND role_name LIKE concat('%', #{query.roleName}, '%')
            </if>
            <if test="query.roleId != null and query.roleId != ''">
                AND role_id LIKE concat('%', #{query.roleId}, '%')
            </if>
        </where>
    </select>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.platform.role.entity.RoleVo">
        select * from t_sanquan_platform_role
        <where>
            deleted='0'
            <if test="query.roleName != null and query.roleName != ''">
                AND role_name LIKE concat('%', #{query.roleName}, '%')
            </if>
            <if test="query.roleId != null and query.roleId != ''">
                AND role_id LIKE concat('%', #{query.roleId}, '%')
            </if>
        </where>
    </select>

    <select id="findByUserId" resultType="cn.chinaunicom.sdsi.platform.role.entity.RoleVo">
        select a.* from t_sanquan_platform_role a,t_sanquan_platform_user_role b
        <where>
            a.deleted='0' and a.id=b.role_id
            AND b.user_id LIKE #{userId}
        </where>
    </select>
	
	<select id="findOne" resultType="cn.chinaunicom.sdsi.platform.role.entity.RoleVo">
        select * from t_sanquan_platform_role 
        <where>
            deleted='0' and id=#{id}
        </where>
    </select>

    <select id="getUserOALevel" resultType="java.lang.String">
        select main_level from t_sanquan_permission_role_user_temp where login = #{jobNum} group by login
    </select>

</mapper>
