<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.platform.user.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.platform.user.entity.User">
        <id column="id" property="id"/>
        <result column="job_num" property="jobNum"/>
        <result column="name" property="name"/>
        <result column="city" property="city"/>
        <result column="county" property="county"/>
        <result column="org_type" property="orgType"/>
        <result column="industry" property="industry"/>
        <result column="phone" property="phone"/>
        <result column="deleted" property="deleted"/>
        <result column="user_type" property="userType"/>
        <result column="dept_id" property="deptId"/>
        <result column="city_id" property="cityId"/>
        <result column="county_id" property="countyId"/>
        <result column="segments" property="segments"/>
    </resultMap>

    <select id="findByJobNum" resultType="cn.chinaunicom.sdsi.platform.user.entity.UserVo">
        select * from t_sanquan_platform_user
        <where>
            deleted='normal'
            AND job_num = #{jobNum}

        </where>
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.platform.user.entity.UserVo">
        select * from t_sanquan_platform_user
        <where>
            deleted='normal'
            <if test="query.userType != null and query.userType != ''">
                AND user_type = #{query.userType}
            </if>
            <if test="query.jobNum != null and query.jobNum != ''">
                AND job_num = #{query.jobNum}
            </if>
            <if test="query.city != null and query.city != ''">
                AND city = #{query.city}
            </if>
            <if test="query.county != null and query.county != ''">
                AND county = #{query.county}
            </if>
            <if test="query.industry != null and query.industry != ''">
                AND industry = #{query.industry}
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND phone = #{query.phone}
            </if>
        </where>
    </select>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.platform.user.entity.UserVo">
        select pu.*,pd.dept_name as deptName from t_sanquan_platform_user pu
            left join t_sanquan_platform_dept pd
        on pu.dept_id = pd.id
        <where>
            pu.deleted='normal'
            <if test="query.userType != null and query.userType != ''">
                AND pu.user_type = #{query.userType}
            </if>
            <if test="query.jobNum != null and query.jobNum != ''">
                AND pu.job_num = #{query.jobNum}
            </if>
            <if test="query.city != null and query.city != ''">
                AND pu.city LIKE CONCAT('%', REPLACE(#{query.city}, '市', ''), '%')
            </if>
            <if test="query.county != null and query.county != ''">
                AND pu.county = #{query.county}
            </if>
            <if test="query.industry != null and query.industry != ''">
                AND pu.industry = #{query.industry}
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND pu.phone = #{query.phone}
            </if>
            <if test="query.name != null and query.name != ''">
                AND pu.name like concat(concat('%',#{query.name}),'%')
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND pu.dept_id = #{query.deptId}
            </if>
            <if test="query.deptName != null and query.deptName != ''">
                AND pu.dept_name like concat(concat('%',#{query.deptName}),'%')
            </if>
        </where>
        ORDER BY job_num DESC
    </select>

    <select id="findOne" resultType="cn.chinaunicom.sdsi.platform.user.entity.UserVo">
        select * from t_sanquan_platform_user
        <where>
            deleted='normal' and id=#{id}
        </where>
    </select>

    <select id="findPageByRole" resultType="cn.chinaunicom.sdsi.platform.user.entity.UserVo">
        select a.* from t_sanquan_platform_user a,t_sanquan_platform_user_role b
        <where>
            a.deleted='normal' and b.user_id=a.id and b.role_id=#{query.roleId}
            <if test="query.userType != null and query.userType != ''">
                AND user_type = #{query.userType}
            </if>
            <if test="query.jobNum != null and query.jobNum != ''">
                AND job_num = #{query.jobNum}
            </if>
            <if test="query.city != null and query.city != ''">
                AND city = #{query.city}
            </if>
            <if test="query.county != null and query.county != ''">
                AND county = #{query.county}
            </if>
            <if test="query.industry != null and query.industry != ''">
                AND industry = #{query.industry}
            </if>
            <if test="query.phone != null and query.phone != ''">
                AND phone = #{query.phone}
            </if>
            <if test="query.name != null and query.name != ''">
                AND name like concat(concat("%",#{query.name}),"%")
            </if>
        </where>
    </select>

    <select id="findPageByNotThisRole" resultType="cn.chinaunicom.sdsi.platform.user.entity.UserVo">
        select * from t_sanquan_platform_user where id not in (  select a.id  from  t_sanquan_platform_user a
        LEFT JOIN t_sanquan_platform_user_role b on a.id=b.user_id where b.role_id=#{query.roleId}) and deleted='normal'

        <if test="query.userType != null and query.userType != ''">
            AND user_type = #{query.userType}
        </if>
        <if test="query.jobNum != null and query.jobNum != ''">
            AND job_num = #{query.jobNum}
        </if>
        <if test="query.city != null and query.city != ''">
            AND city = #{query.city}
        </if>
        <if test="query.county != null and query.county != ''">
            AND county = #{query.county}
        </if>
        <if test="query.industry != null and query.industry != ''">
            AND industry = #{query.industry}
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND phone = #{query.phone}
        </if>
        <if test="query.name != null and query.name != ''">
            AND name like concat(concat("%",#{query.name}),"%")
        </if>

    </select>
    <select id="getIndustryList" resultType="cn.chinaunicom.sdsi.common.entity.IndustryVO">
        select distinct (INDUSTRY_ID) as `value`,INDUSTRY_NAME as label from td_b_industry_prov where PARENT_INDUSTRY_ID='0' and INDUSTRY_LEVEL='0'
    </select>
    <select id="selectSegments" resultType="java.util.Map">
        select segments_name as label, segments_name as `value` from t_sanquan_industry_segments
        where industry in
        <foreach collection="industry" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectCityGroup" resultType="java.lang.String">
        select city_name cityName from t_sanquan_city where is_status = 1
        order by city_id
    </select>
    <select id="selectDistrict" resultType="java.util.Map">
        select
            distinct
            DISTRICT_NAME as label,
            DISTRICT_CODE as `value`
        from t_sanquan_permission_config
        where CITY_NAME like concat( #{cityName}, '%' )
    </select>

</mapper>

