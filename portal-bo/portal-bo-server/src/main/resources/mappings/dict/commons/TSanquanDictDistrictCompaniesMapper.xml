<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dict.commons.mapper.TSanquanDictDistrictCompaniesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictDistrictCompanies">
        <id column="id" property="id" />
        <result column="parent_id" property="parentId" />
        <result column="regions_label" property="regionsLabel" />
        <result column="regions_value" property="regionsValue" />
        <result column="level" property="level" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_id, regions_label, regions_value, level
    </sql>
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictDistrictCompanies">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_district_companies
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.parentId != null and query.parentId != ''">
                and parent_id = #{query.parentId}
            </if>
            <if test="query.regionsLabel != null and query.regionsLabel != ''">
                and regions_label = #{query.regionsLabel}
            </if>
            <if test="query.regionsValue != null and query.regionsValue != ''">
                and regions_value = #{query.regionsValue}
            </if>
            <if test="query.level != null and query.level != ''">
                and level = #{query.level}
            </if>
        </where>
        order by id
    </select>
    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictDistrictCompanies">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_district_companies
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.parentId != null and query.parentId != ''">
                and parent_id = #{query.parentId}
            </if>
            <if test="query.regionsLabel != null and query.regionsLabel != ''">
                and regions_label = #{query.regionsLabel}
            </if>
            <if test="query.regionsValue != null and query.regionsValue != ''">
                and regions_value = #{query.regionsValue}
            </if>
            <if test="query.level != null and query.level != ''">
                and level = #{query.level}
            </if>
        </where>
        order by id
    </select>

</mapper>
