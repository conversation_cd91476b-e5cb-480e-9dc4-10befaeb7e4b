<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dict.commons.mapper.TSanquanDictIndustryProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictIndustryProduct">
        <id column="id" property="id" />
        <result column="industry" property="industry" />
        <result column="product_name" property="productName" />
        <result column="product_value" property="productValue" />
        <result column="product_id" property="productId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, industry, product_name, product_value, product_id
    </sql>
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictIndustryProduct">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_industry_product
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.industry != null and query.industry != ''">
                and industry = #{query.industry}
            </if>
            <if test="query.productName != null and query.productName != ''">
                and product_name = #{query.productName}
            </if>
            <if test="query.productValue != null and query.productValue != ''">
                and product_value = #{query.productValue}
            </if>
            <if test="query.productId != null and query.productId != ''">
                and product_id = #{query.productId}
            </if>
        </where>
        order by id desc
    </select>
    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.dict.commons.entity.TSanquanDictIndustryProduct">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_industry_product
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.industry != null and query.industry != ''">
                and industry = #{query.industry}
            </if>
            <if test="query.productName != null and query.productName != ''">
                and product_name = #{query.productName}
            </if>
            <if test="query.productValue != null and query.productValue != ''">
                and product_value = #{query.productValue}
            </if>
            <if test="query.productId != null and query.productId != ''">
                and product_id = #{query.productId}
            </if>
        </where>
        order by id desc
    </select>
    <select id="selectIndustry" resultType="java.util.Map">
        select distinct industry as label, industry as value from t_sanquan_dict_industry_product
    </select>

</mapper>
