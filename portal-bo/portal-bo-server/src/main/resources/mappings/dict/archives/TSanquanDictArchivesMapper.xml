<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchives">
        <id column="id" property="id" />
        <result column="versions" property="versions" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="customer_industry" property="customerIndustry" />
        <result column="customer_contacts" property="customerContacts" />
        <result column="customer_contact_tel" property="customerContactTel" />
        <result column="customer_type" property="customerType" />
        <result column="customer_manager" property="customerManager" />
        <result column="customer_manager_oa" property="customerManagerOa" />
        <result column="customer_manager_tel" property="customerManagerTel" />
        <result column="city" property="city" />
        <result column="county" property="county" />
        <result column="county_company" property="countyCompany" />
        <result column="visit_tags" property="visitTags" />
        <result column="is_mobai" property="isMobai" />
        <result column="marketing_products" property="marketingProducts" />
        <result column="network_problem_requirement" property="networkProblemRequirement" />
        <result column="base_region_problem_requirement" property="baseRegionProblemRequirement" />
        <result column="middle_platform_problem_requirement" property="middlePlatformProblemRequirement" />
        <result column="business_system_problem_requirement" property="businessSystemProblemRequirement" />
        <result column="ai_application_problem_requirement" property="aiApplicationProblemRequirement" />
        <result column="ai_model_is_expect" property="aiModelIsExpect" />
        <result column="ai_model_scene_target" property="aiModelSceneTarget" />
        <result column="ai_model_problem_requirement" property="aiModelProblemRequirement" />
        <result column="technical_team_problem_requirement" property="technicalTeamProblemRequirement" />
        <result column="business_pain_points" property="businessPainPoints" />
        <result column="attachment_id" property="attachmentId" />
        <result column="attachment_name" property="attachmentName" />
        <result column="create_date" property="createDate" />
        <result column="create_by" property="createBy" />
        <result column="update_date" property="updateDate" />
        <result column="update_by" property="updateBy" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="tenant_id" property="tenantId" />
        <result column="is_xinchuang_demand" property="isXinchuangDemand" />
        <result column="xinchuang_content" property="xinchuangContent" />
        <result column="is_dengbao_demand" property="isDengbaoDemand" />
        <result column="dengbao_content" property="dengbaoContent" />
        <result column="is_operation_demand" property="isOperationDemand" />
        <result column="operation_content" property="operationContent" />
        <result column="is_wyydn_demand" property="isWyydnDemand" />
        <result column="terminal_num_wyydn" property="terminalNumWyydn" />
        <result column="budget_wyydn" property="budgetWyydn" />
        <result column="demand_wyydn" property="demandWyydn" />
        <result column="is_ylzj_demand" property="isYlzjDemand" />
        <result column="employees_ylzj" property="employeesYlzj" />
        <result column="budget_ylzj" property="budgetYlzj" />
        <result column="demand_ylzj" property="demandYlzj" />
        <result column="is_transfer_oppo" property="isTransferOppo" />
        <result column="customer_oppo_scale" property="customerOppoScale" />
        <result column="customer_oppo_demand" property="customerOppoDemand" />
        <result column="create_by_org" property="createByOrg" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        versions,
        id, customer_id, customer_name, customer_industry, customer_contacts, customer_contact_tel, customer_type
        , customer_manager, customer_manager_oa, customer_manager_tel, city, county
        , visit_tags, is_mobai, marketing_products, network_problem_requirement, base_region_problem_requirement
        , middle_platform_problem_requirement, business_system_problem_requirement, ai_application_problem_requirement
        , ai_model_is_expect, ai_model_scene_target, ai_model_problem_requirement, technical_team_problem_requirement
        , business_pain_points, attachment_id, attachment_name, create_date, create_by, update_date, update_by, delete_flag, tenant_id
        , is_xinchuang_demand, xinchuang_content, is_dengbao_demand, dengbao_content, is_operation_demand
        , operation_content, is_wyydn_demand, terminal_num_wyydn, budget_wyydn, demand_wyydn, is_ylzj_demand
        , employees_ylzj, budget_ylzj, demand_ylzj, county_company
        ,  is_transfer_oppo, customer_oppo_scale, customer_oppo_demand
        , create_by_org
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchives">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_archives
            where delete_flag='normal'
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                and customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName !=''">
                and customer_name like concat('%',#{query.customerName},'%')
            </if>
            <if test="query.customerIndustry != null and query.customerIndustry !=''">
                and customer_industry like concat('%',#{query.customerIndustry},'%')
            </if>
            <if test="query.customerContacts != null and query.customerContacts !=''">
                and customer_contacts like concat('%',#{query.customerContacts},'%')
            </if>
            <if test="query.customerContactTel != null and query.customerContactTel !=''">
                and customer_contact_tel like concat('%',#{query.customerContactTel},'%')
            </if>
            <if test="query.customerType != null and query.customerType !=''">
                and customer_type = #{query.customerType}
            </if>
            <if test="query.customerManager != null and query.customerManager !=''">
                and customer_manager like concat('%',#{query.customerManager},'%')
            </if>
            <if test="query.customerManagerOa != null and query.customerManagerOa !=''">
                and customer_manager_oa = #{query.customerManagerOa}
            </if>
            <if test="query.customerManagerTel != null and query.customerManagerTel !=''">
                and customer_manager_tel like concat('%',#{query.customerManagerTel},'%')
            </if>
            <if test="query.city != null and query.city !=''">
                and city = #{query.city}
            </if>
            <if test="query.county != null and query.county !=''">
                and county = #{query.county}
            </if>
            <if test="query.visitTags != null and query.visitTags !=''">
                and visit_tags = #{query.visitTags}
            </if>
            <if test="query.isMobai != null and query.isMobai !=''">
                and is_mobai = #{query.isMobai}
            </if>
            <if test="query.marketingProducts != null and query.marketingProducts !=''">
                and marketing_products like concat('%',#{query.marketingProducts},'%')
            </if>
            <if test="query.createBy != null and query.createBy !=''">
                and create_by = #{query.createBy}
            </if>
            <if test="query.isTransferOppo != null and query.isTransferOppo !=''">
                and is_transfer_oppo = #{query.isTransferOppo}
            </if>
            <if test="query.customerOppoScale != null and query.customerOppoScale !=''">
                and customer_oppo_scale = #{query.customerOppoScale}
            </if>
        ${query.dataScopeSqlFilter}
        order by create_date desc, id
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchives">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_archives
            where delete_flag='normal'
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                and customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName !=''">
                and customer_name like concat('%',#{query.customerName},'%')
            </if>
            <if test="query.customerIndustry != null and query.customerIndustry !=''">
                and customer_industry like concat('%',#{query.customerIndustry},'%')
            </if>
            <if test="query.customerContacts != null and query.customerContacts !=''">
                and customer_contacts like concat('%',#{query.customerContacts},'%')
            </if>
            <if test="query.customerContactTel != null and query.customerContactTel !=''">
                and customer_contact_tel like concat('%',#{query.customerContactTel},'%')
            </if>
            <if test="query.customerType != null and query.customerType !=''">
                and customer_type = #{query.customerType}
            </if>
            <if test="query.customerManager != null and query.customerManager !=''">
                and customer_manager like concat('%',#{query.customerManager},'%')
            </if>
            <if test="query.customerManagerOa != null and query.customerManagerOa !=''">
                and customer_manager_oa = #{query.customerManagerOa}
            </if>
            <if test="query.customerManagerTel != null and query.customerManagerTel !=''">
                and customer_manager_tel like concat('%',#{query.customerManagerTel},'%')
            </if>
            <if test="query.city != null and query.city !=''">
                and city = #{query.city}
            </if>
            <if test="query.county != null and query.county !=''">
                and county = #{query.county}
            </if>
            <if test="query.visitTags != null and query.visitTags !=''">
                and visit_tags = #{query.visitTags}
            </if>
            <if test="query.isMobai != null and query.isMobai !=''">
                and is_mobai = #{query.isMobai}
            </if>
            <if test="query.marketingProducts != null and query.marketingProducts !=''">
                and marketing_products like concat('%',#{query.marketingProducts},'%')
            </if>
            <if test="query.isTransferOppo != null and query.isTransferOppo !=''">
                and is_transfer_oppo = #{query.isTransferOppo}
            </if>
            <if test="query.customerOppoScale != null and query.customerOppoScale !=''">
                and customer_oppo_scale = #{query.customerOppoScale}
            </if>
        ${query.dataScopeSqlFilter}
        order by create_date desc, id
    </select>
    <select id="getInfoByCustomerId"
            resultType="cn.chinaunicom.sdsi.cloud.dict.archives.vo.TSanquanDictArchivesVo">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dict_archives
        where delete_flag='normal'
        and customer_id = #{customerId}
    </select>
    <insert id="saveDictArchivesLog">
        insert into t_sanquan_dict_archives_log(
            archives_id, customer_id, customer_name, update_date, update_by, update_name, operate
        )
        values(
          #{logVo.archivesId}, #{logVo.customerId}, #{logVo.customerName}, #{logVo.updateDate}, #{logVo.updateBy}, #{logVo.updateName}, #{logVo.operate}
        )
    </insert>
</mapper>
