<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesBusinessDemandMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesBusinessDemand">
        <id column="id" property="id" />
        <result column="archives_id" property="archivesId" />
        <result column="problem_label" property="problemLabel" />
        <result column="is_demand" property="isDemand" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, archives_id, problem_label, is_demand, product_id, product_name
    </sql>

</mapper>
