<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dict.archives.mapper.TSanquanDictArchivesAiApplicationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesAiApplication">
        <id column="id" property="id" />
        <result column="archives_id" property="archivesId" />
        <result column="sort" property="sort" />
        <result column="application_scenario" property="applicationScenario" />
        <result column="construction_manufacturer" property="constructionManufacturer" />
        <result column="construction_date" property="constructionDate" />
        <result column="application_scenario_other" property="applicationScenarioOther" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, archives_id, sort, application_scenario, construction_manufacturer, construction_date
        , application_scenario_other
    </sql>
    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesAiApplication">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_dict_archives_ai_application
        <where>
            <if test="query.archivesId != null and query.archivesId != ''">
                and archives_id = #{query.archivesId}
            </if>
            <if test="query.sort != null and query.sort != ''">
                and sort = #{query.sort}
            </if>
            <if test="query.applicationScenario !=null and query.applicationScenario != ''">
                and application_scenario = #{query.applicationScenario}
            </if>
        </where>
        order by id desc
    </select>
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.dict.archives.entity.TSanquanDictArchivesAiApplication">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_dict_archives_ai_application
        <where>
            <if test="query.archivesId != null and query.archivesId != ''">
                and archives_id = #{query.archivesId}
            </if>
            <if test="query.sort != null and query.sort != ''">
                and sort = #{query.sort}
            </if>
            <if test="query.applicationScenario !=null and query.applicationScenario != ''">
                and application_scenario = #{query.applicationScenario}
            </if>
        </where>
        order by id desc
    </select>

</mapper>
