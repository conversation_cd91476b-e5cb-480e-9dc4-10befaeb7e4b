<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dbcp.mapper.TSanquanDbcpSignAmountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignAmount">
        <id column="id" property="id" />
        <result column="seq" property="seq" />
        <result column="acct_id" property="acctId" />
        <result column="city" property="city" />
        <result column="contract_name" property="contractName" />
        <result column="city_com_sign_amount" property="cityComSignAmount" />
        <result column="otherside" property="otherside" />
        <result column="district" property="district" />
        <result column="industry" property="industry" />
        <result column="subdivision_industry" property="subdivisionIndustry" />
        <result column="remark" property="remark" />
        <result column="import_industry_cust" property="importIndustryCust" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, seq, acct_id, city, contract_name, city_com_sign_amount, otherside, district, industry, subdivision_industry, remark, import_industry_cust
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignAmount">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_sign_amount
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.seq != null and query.seq != ''">
                and seq = #{query.seq}
            </if>
            <if test="query.acctId != null and query.acctId != ''">
                and acct_id = #{query.acctId}
            </if>
            <if test="query.city != null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                and contract_name = #{query.contractName}
            </if>
            <if test="query.cityComSignAmount != null and query.cityComSignAmount != ''">
                and city_com_sign_amount = #{query.cityComSignAmount}
            </if>
            <if test="query.otherside != null and query.otherside != ''">
                and otherside = #{query.otherside}
            </if>
            <if test="query.district != null and query.district != ''">
                and district = #{query.district}
            </if>
            <if test="query.industry != null and query.industry != ''">
                and industry = #{query.industry}
            </if>
            <if test="query.subdivisionIndustry != null and query.subdivisionIndustry != ''">
                and subdivision_industry = #{query.subdivisionIndustry}
            </if>
            <if test="query.remark != null and query.remark != ''">
                and remark = #{query.remark}
            </if>
            <if test="query.importIndustryCust != null and query.importIndustryCust != ''">
                and import_industry_cust = #{query.importIndustryCust}
            </if>
        </where>
        order by seq,id desc
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpSignAmount">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_sign_amount
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.seq != null and query.seq != ''">
                and seq = #{query.seq}
            </if>
            <if test="query.acctId != null and query.acctId != ''">
                and acct_id = #{query.acctId}
            </if>
            <if test="query.city != null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                and contract_name = #{query.contractName}
            </if>
            <if test="query.cityComSignAmount != null and query.cityComSignAmount != ''">
                and city_com_sign_amount = #{query.cityComSignAmount}
            </if>
            <if test="query.otherside != null and query.otherside != ''">
                and otherside = #{query.otherside}
            </if>
            <if test="query.district != null and query.district != ''">
                and district = #{query.district}
            </if>
            <if test="query.industry != null and query.industry != ''">
                and industry = #{query.industry}
            </if>
            <if test="query.subdivisionIndustry != null and query.subdivisionIndustry != ''">
                and subdivision_industry = #{query.subdivisionIndustry}
            </if>
            <if test="query.remark != null and query.remark != ''">
                and remark = #{query.remark}
            </if>
            <if test="query.importIndustryCust != null and query.importIndustryCust != ''">
                and import_industry_cust = #{query.importIndustryCust}
            </if>
        </where>
        order by seq,id desc
    </select>

</mapper>
