<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dbcp.mapper.TSanquanDbcpPersonLevelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpPersonLevel">
        <id column="id" property="id" />
        <result column="job_number" property="jobNumber" />
        <result column="person_name" property="personName" />
        <result column="person_level" property="personLevel" />
        <result column="person_tel" property="personTel" />
        <result column="person_city" property="personCity" />
        <result column="person_district" property="personDistrict" />
        <result column="person_status" property="personStatus" />
        <result column="person_type" property="personType" />
        <result column="job_title" property="jobTitle" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, job_number, person_name, person_level, person_tel, person_city, person_district, person_status, person_type, job_title
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpPersonLevel">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_person_level
        <where>
            <if test="query.personName != null and query.personName != ''">
                and person_name like concat('%', #{query.personName}, '%')
            </if>
            <if test="query.personLevel != null and query.personLevel != ''">
                and person_level = #{query.personLevel}
            </if>
            <if test="query.personCity != null and query.personCity != ''">
                and person_city = #{query.personCity}
            </if>
            <if test="query.personDistrict != null and query.personDistrict != ''">
                and person_district = #{query.personDistrict}
            </if>
            <if test="query.personStatus != null and query.personStatus != ''">
                and person_status = #{query.personStatus}
            </if>
            <if test="query.personTel != null and query.personTel != ''">
                and person_tel = #{query.personTel}
            </if>
            <if test="query.jobNumber != null and query.jobNumber != ''">
                and job_number = #{query.jobNumber}
            </if>
            <if test="query.personType != null and query.personType != ''">
                and person_type = #{query.personType}
            </if>
            <if test="query.jobTitle != null and query.jobTitle != ''">
                and job_title = #{query.jobTitle}
            </if>
        </where>
        order by job_number desc
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpPersonLevel">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_person_level
        <where>
            <if test="query.personName != null and query.personName != ''">
                and person_name like concat('%', #{query.personName}, '%')
            </if>
            <if test="query.personLevel != null and query.personLevel != ''">
                and person_level = #{query.personLevel}
            </if>
            <if test="query.personCity != null and query.personCity != ''">
                and person_city = #{query.personCity}
            </if>
            <if test="query.personDistrict != null and query.personDistrict != ''">
                and person_district = #{query.personDistrict}
            </if>
            <if test="query.personStatus != null and query.personStatus != ''">
                and person_status = #{query.personStatus}
            </if>
            <if test="query.personTel != null and query.personTel != ''">
                and person_tel = #{query.personTel}
            </if>
            <if test="query.jobNumber != null and query.jobNumber != ''">
                and job_number = #{query.jobNumber}
            </if>
            <if test="query.personType != null and query.personType != ''">
                and person_type = #{query.personType}
            </if>
            <if test="query.jobTitle != null and query.jobTitle != ''">
                and job_title = #{query.jobTitle}
            </if>
        </where>
        order by job_number desc
    </select>
    <select id="selectByJobNumber" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpPersonLevel">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_person_level
        where job_number = #{jobNumber}
            <if test="personType != null and personType != ''">
                and person_type = #{personType}
            </if>
            and person_status = '1'
        limit 1
    </select>

</mapper>
