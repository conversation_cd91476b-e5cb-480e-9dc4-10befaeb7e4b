<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dbcp.mapper.TSanquanDbcpIncomeAmountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeAmount">
        <id column="id" property="id" />
        <result column="period" property="period" />
        <result column="acct_id" property="acctId" />
        <result column="project_num" property="projectNum" />
        <result column="project_name" property="projectName" />
        <result column="city" property="city" />
        <result column="js_amount" property="jsAmount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, period, acct_id, project_num, project_name, city, js_amount
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeAmount">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_income_amount
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.period != null and query.period != ''">
                and period = #{query.period}
            </if>
            <if test="query.acctId != null and query.acctId != ''">
                and acct_id = #{query.acctId}
            </if>
            <if test="query.projectNum != null and query.projectNum != ''">
                and project_num = #{query.projectNum}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                and project_name = #{query.projectName}
            </if>
            <if test="query.city != null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.jsAmount != null and query.jsAmount != ''">
                and js_amount = #{query.jsAmount}
            </if>
        </where>
        order by acct_id,id desc
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpIncomeAmount">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_income_amount
        <where>
            <if test="query.id != null and query.id != ''">
                and id = #{query.id}
            </if>
            <if test="query.period != null and query.period != ''">
                and period = #{query.period}
            </if>
            <if test="query.acctId != null and query.acctId != ''">
                and acct_id = #{query.acctId}
            </if>
            <if test="query.projectNum != null and query.projectNum != ''">
                and project_num = #{query.projectNum}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                and project_name = #{query.projectName}
            </if>
            <if test="query.city != null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.jsAmount != null and query.jsAmount != ''">
                and js_amount = #{query.jsAmount}
            </if>
        </where>
        order by acct_id,id desc
    </select>

</mapper>
