package cn.chinaunicom.sdsi.cloud.system.tenant.entity;

import cn.chinaunicom.sdsi.core.interfaces.Add;
import cn.chinaunicom.sdsi.core.interfaces.Edit;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 用于页面展示数据
 *
 * <AUTHOR>
 */
@Data
@Tag(name="租户展示对象")
public class SysTenantVO implements Serializable {

    @Schema(hidden = true)
    private static final long serialVersionUID = 2L;

    @Schema(name = "租户ID")
    @NotNull(message = "租户ID不能为空", groups = {Edit.class})
    private String tenantId;

    @Schema(name = "租户名称")
    @Length(max = 128)
    @NotBlank(message = "租户名称不能为空", groups = {Add.class})
    private String tenantName;

    @Schema(name = "租户登录名")
    @Length(max = 20)
    @NotBlank(message = "租户登录名不能为空", groups = {Add.class})
    private String tenantLoginName;

    @Schema(name = "租户管理员ID")
    private String tenantAdminId;

    @Schema(name = "租户创建人")
    @Length(max = 36)
    private String tenantCreateBy;

    @Schema(name = "租户域名")
    @Length(max = 128)
    private String tenantDomain;

    @Schema(name = "业务ID")
    @Length(max = 36)
    private String businessId;

    @Schema(name = "最大用户数")
    private Integer maxStaff;

    @Schema(name = "有效截止时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date effectiveDate;

    @Schema(name = "状态 - valid-正常 invalid-冻结 reject-审批拒绝")
    @Length(max = 16)
    private String tenantStatus;

    @Schema(name = "前台位置")
    private String tenantHtmlPath;

    @Schema(name = "租户管理员登录名")
    private String displayName;
    @Schema(name = "租户管理员姓名")
    private String staffName;

    @Schema(name = "租户创建时间")
    private String createDate;
    @Schema(name = "组织名称")
    private String orgName;
    @Schema(name = "组织全称")
    private String fullName;
    @Schema(name = "说明")
    private String description;
    @Schema(name = "坐席数量")
    private String agentNum;
    @Schema(name = "登录名")
    private String loginName;
}
