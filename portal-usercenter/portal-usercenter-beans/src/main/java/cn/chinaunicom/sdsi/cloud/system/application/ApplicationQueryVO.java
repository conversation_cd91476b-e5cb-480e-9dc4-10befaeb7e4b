package cn.chinaunicom.sdsi.cloud.system.application;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 钉钉应用查询对象类
 * <AUTHOR>
 * @return
 * @create 2022-02-15 14:27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name="钉钉应用查询对象")
public class ApplicationQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 1L;
	  
    @Schema(name = "租户id")
    private String tenantId;


    @Schema(name = "应用名称")
    private String applicationName;

    /**
     * 应用状态
     */
    @Schema(name = "应用状态")
    private String appStatus;
}
