package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人员岗位查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Tag(name = "人员岗位查询对象", description = "查询条件使用")
public class SysStaffRoleQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    @Schema(name="姓名")
    private String staffName;

    @Schema(name="人员ID")
    private String staffId;

    @Schema(name="角色ID")
    private String roleId;

    @Schema(name="登录名")
    private String loginName;

    @Schema(name="公司ID")
    private String companyId;

    @Schema(hidden = true)
    private String[] roleIds;

}
