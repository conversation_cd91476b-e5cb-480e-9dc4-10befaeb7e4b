package cn.chinaunicom.sdsi.cloud.system.config.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统配置功能 - 系统配置分页查询视图对象
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/16
 * -
 * @description 内容描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name = "系统配置功能 - 系统配置分页查询视图对象", description = "系统配置")
public class SysConfigQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    @Schema(name = "系统参数ID")
    private String configId;

    @Schema(name = "系统参数编码")
    private String configCode;

    @Schema(name = "系统参数值")
    private String configValue;

    @Schema(name = "岗位ID", hidden = true, description ="结合岗位查询参数配置，此属性不向前端暴露")
    private String staffOrgId;
}
