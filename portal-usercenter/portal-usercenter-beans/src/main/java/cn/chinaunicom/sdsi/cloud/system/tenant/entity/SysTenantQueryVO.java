package cn.chinaunicom.sdsi.cloud.system.tenant.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 用于接收分页查询参数
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name="租户查询对象")
public class SysTenantQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    @Schema(name = "租户ID")
    private String tenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name = "租户管理员ID")
    private String tenantAdminId;

    @Schema(name = "租户创建人")
    private String tenantCreateBy;

    @Schema(name = "租户域名")
    private String tenantDomain;

    @Schema(name = "状态 - valid-正常 invalid-冻结 reject-审批拒绝")
    private String tenantStatus;

    @Schema(name = "租户登录名称")
    private String tenantLoginName;

    @Schema(name = "租户登录名称-安扫漏洞扫描出了tenantLoginName所以用此参数替代")
    private String tenant;

    @Schema(name = "管理员名称")
    private String displayName;

    @Schema(name = "组织名称")
    private String orgName;

    @Schema(name = "组织全程")
    private String fullName;

    @Schema(name = "登陆账号")
    private String loginName;

    @Schema(name = "有效时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date effectiveDate;

    @Schema(name = "有效时间参数区间")
    private String queryEffectiveDate;

    @Schema(name = "有效开始时间")
    private Date startEffectiveDate;

    @Schema(name = "有效结束时间")
    private Date endEffectiveDate;

    @Schema(name = "有效开始时间")
    private String beginTime;

    @Schema(name = "有效结束时间")
    private String endTime;

    @Schema(name = "排除某个租户查询")
    private String excludeTenantId;
}
