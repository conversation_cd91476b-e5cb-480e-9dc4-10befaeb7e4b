package cn.chinaunicom.sdsi.cloud.system.theme.vo;

import java.io.Serializable;
import lombok.Data;

/**
 * <p>项目名称: unified-portal-saas </p>
 * <p>文件名称: SysThemeVO </p>
 * <p>描述: 启用的主题配置视图 </p>
 * <p>创建时间: 2022/4/14 16:57</p>
 *
 * <AUTHOR>
 */
@Data
public class SysEnabledThemeVO implements Serializable {

  /**
   * 是否开启topnav：0未启用，1启用
   */
  private Boolean topNav;

  /**
   * 是否开启tags-views：0未启用，1启用
   */
  private Boolean tagsView;

  /**
   * 是否固定header：0未启用，1启用
   */
  private Boolean fixedHeader;

  /**
   * 是否显示Logo：0未启用，1启用
   */
  private Boolean sidebarLogo;

  /**
   * 主题风格设置：深色主题theme-dark，浅色主题theme-light
   */
  private String sideTheme;

  /**
   * 主题颜色
   */
  private String theme;
}