package cn.chinaunicom.sdsi.cloud.system.role.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 角色-权限关联表
 * </p>
 *
 * <AUTHOR>
 * @date 2019-01-16
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Data
@Accessors(chain = true)
@TableName("sys_role_permission")
@Tag(name="SysRolePermissionPO对象", description="角色-权限关联表")
public class SysRolePermissionPO implements Serializable {

    private static final long serialVersionUID = 2L;

    @Schema(name = "角色ID")
    private String roleId;

    @Schema(name = "权限ID")
    private String permissionId;

    @Schema(name = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private transient Date createDate;

    @Schema(name = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

}
