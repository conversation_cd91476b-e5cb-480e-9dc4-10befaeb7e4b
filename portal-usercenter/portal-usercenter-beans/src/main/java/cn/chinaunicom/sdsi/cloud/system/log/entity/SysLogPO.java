package cn.chinaunicom.sdsi.cloud.system.log.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
* <p>项目名称: unified-portal-saas </p>
* <p>文件名称: SysLog </p>
* <p>描述: 日志 </p>
* <p>创建时间: 2022/1/4 10:13</p>
* <AUTHOR>
*/
@Data
@Tag(name = "系统日志-基本信息")
@TableName("sys_log")
public class SysLogPO implements Serializable {
   /**
   * 主键
   */
   @TableId(value = "log_id", type = IdType.ASSIGN_ID)
   private String logId;

   /**
   * 用户ID
   */
   private String userId;

   /**
   * 用户帐号
   */
   private String account;

   @TableField(exist = false)
   private String accountName;

   /**
   * IP信息
   */
   private String userIp;

   /**
   * 被访问主机IP
   */
   private String hostIp;

   /**
   * 被访问主机名
   */
   private String hostName;

   /**
   * 操作时间
   */
   private Date logTime;

   /**
   * 操作时长
   */
   private Long costTime;

   /**
   * 访问路径
   */
   private String url;

   /**
   * 权限名
   */
   private String permissionName;

   /**
   * 权限编码
   */
   private String permissionCode;

   /**
   * 参数
   */
   private String parameter;

   /**
   * 操作状态(success登录成功、logout退出、fail登录失败)
   */
   private String logStatus;

   /**
   * 错误日志
   */
   private String exception;

   /**
   * 浏览器类型
   */
   private String browser;

   /**
   * 操作系统
   */
   private String os;

   /**
    * 地点
    */
   private String location;

   /**
    * 租户Id
    */
   private String tenantId;
   /**
    * 登陆类型
    */
   private String loginType;
}
