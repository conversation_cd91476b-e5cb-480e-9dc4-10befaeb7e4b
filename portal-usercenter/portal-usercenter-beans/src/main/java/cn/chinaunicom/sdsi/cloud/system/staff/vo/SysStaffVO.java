package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import cn.chinaunicom.sdsi.core.interfaces.Add;
import cn.chinaunicom.sdsi.core.interfaces.Delete;
import cn.chinaunicom.sdsi.core.interfaces.Edit;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员视图对象（含主岗ID）
 * 展现/提交时使用
 *
 * <AUTHOR>
 */
@Data
@Tag(name = "人员视图对象", description = "展现/提交时使用")
public class SysStaffVO implements Serializable {
    private static final long serialVersionUID = 2L;

    @Schema(name = "人员ID", hidden = true)
    @Length(max = 36)
    @NotNull(message = "人员ID不能为空", groups = {Edit.class, Delete.class})
    private String staffId;

    @NotBlank(message = "登录名必须输入", groups = {Add.class})
    @Schema(name="登录名")
    private String loginName;

//    @NotBlank(message = "登录密码必须输入", groups = {Add.class})
    @Schema(name="密码")
    private String passwd;

    @Schema(name="姓名")
    @NotBlank(message = "姓名不能为空", groups = {Add.class})
    @Length(max = 30)
    private String staffName;

    @Schema(name="主岗组织ID")
    @NotNull(message = "组织id不能为空", groups = {Add.class})
    @Length(max = 36)
    private String orgId;

    @Schema(name="当前用户所在的公司ID")
    private String companyId;

    @Schema(name="当前用户所在的地市公司ID")
    private String cityCompanyId;

    @Schema(name="行政区划层级")
    @Length(max = 1)
    private String orgLevel;

    @Schema(name="人员编码")
    @Length(max = 240)
    private String staffCode;

    @Schema(name="状态:valid正常,invalid禁用")
    @Pattern(regexp = "valid|invalid", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "人员状态只能为 valid 或 invalid")
    private String staffStatus;

    @Schema(name="身份证号")
    @Length(max = 64)
    private String employeeCode;

    @Schema(name="性别:male-男,female-女")
    @Pattern(regexp = "male|female", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "性别只能为male或female")
    private String sex;

    @Schema(name="人员类别:own内部人员,partner合作伙伴，customer客户")
    @Pattern(regexp = "own|partner|customer", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "人员类型只能为 own 或 partner、customer")
    private String staffKind;

    @Schema(name="排序")
    @Max(Integer.MAX_VALUE)
    private Integer staffSort;

    @Schema(name="生日")
    private Date birthday;

    @Schema(name="入职日期")
    private Date hireDate;

    @Schema(name="邮编")
    @Length(max = 32)
    private String postcode;

    @Schema(name="通信地址")
    @Length(max = 255)
    private String address;

    @Schema(name="手机号码")
    @Length(max = 64)
    private String cellphone;

    @Schema(name="座机号码")
    @Length(max = 64)
    private String telephone;

    @Schema(name="电子邮件")
    @Email
    @Length(max = 128)
    private String email;

    @Schema(name = "主岗ID", hidden = true)
    @Length(max = 36)
    private String staffOrgId;

    @Schema(name = "岗位类型")
    @Length(max = 36)
    private String staffOrgType;

    @Schema(name="部门name")
    private String orgName;

    @Schema(name="部门name")
    private String allOrgName;

    @Schema(name="头像图片")
    private String headImg;

    @Schema(name="状态:valid正常,invalid禁用")
    @Pattern(regexp = "valid|invalid", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "岗位状态只能为 valid 或 invalid")
    @Length(max = 16)
    private String staffOrgStatus;

    @Schema(name = "是否需要修改密码  0：否  1：是")
    private Integer userPwdFlag;

    @Schema(name="租户id")
    private String tenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name = "备用字段a:禁用原因")
    private String attra;

    @Schema(name = "获取验证码类型：1、忘记密码时的获取验证码 2、个人中心绑定手机号时获取的验证码")
    private String getVerificationCodeType;

    @Schema(name = "修改密码-获取的短信验证码")
    private String verificationCode;

    @Schema(name = "修改密码-输入的新密码")
    private String newPassWord;

    @Schema(name="租户管理员id")
    private String tenantAdminId;

    @Schema(name="租户有效期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date effectiveDate;

    @Schema(name="租户是否删除")
    private String tenantDeleteFlag;

    @Schema(name="租户loginName")
    private String tenantLoginName;

    @Schema(name="用户微信全局唯一标识")
    private String unionid;

    @Schema(name="短信验证码")
    private String smscode;

    @Schema(name="网页授权接口调用凭证")
    private String accessToken;

    @Schema(name="授权码")
    private String code;

    @Schema(name = "用户扩展表实体类")
    private SysStaffExtVO staffExt;
}
