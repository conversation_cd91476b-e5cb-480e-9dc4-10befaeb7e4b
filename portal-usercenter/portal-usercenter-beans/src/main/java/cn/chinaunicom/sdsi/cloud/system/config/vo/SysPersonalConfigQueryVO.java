package cn.chinaunicom.sdsi.cloud.system.config.vo;

import cn.chinaunicom.sdsi.core.request.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用于接收分页查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name="个人参数接收对象")
public class SysPersonalConfigQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    @Schema(name = "系统参数ID")
    private String configId;

    @Schema(name = "个人参数值")
    private String personalValue;

    @Schema(name = "岗位ID")
    private String staffOrgId;
}
