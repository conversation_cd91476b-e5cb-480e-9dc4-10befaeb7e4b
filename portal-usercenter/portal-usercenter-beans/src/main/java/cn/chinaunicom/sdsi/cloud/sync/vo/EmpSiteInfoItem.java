
package cn.chinaunicom.sdsi.cloud.sync.vo;

import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for EmpSiteInfoItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="EmpSiteInfoItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PRI_KEY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="BATCH_ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="HEADER_ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SITE_ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SITE_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CHANGE_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DIST_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CURR_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT1" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT2" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT3" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT4" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT5" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT6" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT7" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT8" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SEGMENT9" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEPT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEPT_SITE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEPT_POST" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="HR_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEPT_SORT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MAIN_SITE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ORG_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="START_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="END_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="RESERVED_1" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_2" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_3" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_4" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_5" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_6" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_7" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_8" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_9" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_10" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_11" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_12" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_13" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_14" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_15" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmpSiteInfoItem", propOrder = {
    "prikey",
    "batchid",
    "headerid",
    "siteid",
    "sitetype",
    "changetype",
    "distcode",
    "currflag",
    "segment1",
    "segment2",
    "segment3",
    "segment4",
    "segment5",
    "segment6",
    "segment7",
    "segment8",
    "segment9",
    "deptname",
    "deptcode",
    "deptsite",
    "deptpost",
    "hrtype",
    "deptsort",
    "mainsite",
    "orgcode",
    "startdate",
    "enddate",
    "reserved1",
    "reserved2",
    "reserved3",
    "reserved4",
    "reserved5",
    "reserved6",
    "reserved7",
    "reserved8",
    "reserved9",
    "reserved10",
    "reserved11",
    "reserved12",
    "reserved13",
    "reserved14",
    "reserved15"
})
@ToString
public class EmpSiteInfoItem {

    @XmlElement(name = "PRI_KEY", required = true, nillable = true)
    protected String prikey;
    @XmlElement(name = "BATCH_ID", required = true, nillable = true)
    protected String batchid;
    @XmlElement(name = "HEADER_ID", required = true, nillable = true)
    protected String headerid;
    @XmlElement(name = "SITE_ID", required = true, nillable = true)
    protected String siteid;
    @XmlElement(name = "SITE_TYPE", required = true, nillable = true)
    protected String sitetype;
    @XmlElement(name = "CHANGE_TYPE", required = true, nillable = true)
    protected String changetype;
    @XmlElement(name = "DIST_CODE", required = true, nillable = true)
    protected String distcode;
    @XmlElement(name = "CURR_FLAG", required = true, nillable = true)
    protected String currflag;
    @XmlElement(name = "SEGMENT1", required = true, nillable = true)
    protected String segment1;
    @XmlElement(name = "SEGMENT2", required = true, nillable = true)
    protected String segment2;
    @XmlElement(name = "SEGMENT3", required = true, nillable = true)
    protected String segment3;
    @XmlElement(name = "SEGMENT4", required = true, nillable = true)
    protected String segment4;
    @XmlElement(name = "SEGMENT5", required = true, nillable = true)
    protected String segment5;
    @XmlElement(name = "SEGMENT6", required = true, nillable = true)
    protected String segment6;
    @XmlElement(name = "SEGMENT7", required = true, nillable = true)
    protected String segment7;
    @XmlElement(name = "SEGMENT8", required = true, nillable = true)
    protected String segment8;
    @XmlElement(name = "SEGMENT9", required = true, nillable = true)
    protected String segment9;
    @XmlElement(name = "DEPT_NAME", required = true, nillable = true)
    protected String deptname;
    @XmlElement(name = "DEPT_CODE", required = true, nillable = true)
    protected String deptcode;
    @XmlElement(name = "DEPT_SITE", required = true, nillable = true)
    protected String deptsite;
    @XmlElement(name = "DEPT_POST", required = true, nillable = true)
    protected String deptpost;
    @XmlElement(name = "HR_TYPE", required = true, nillable = true)
    protected String hrtype;
    @XmlElement(name = "DEPT_SORT", required = true, nillable = true)
    protected String deptsort;
    @XmlElement(name = "MAIN_SITE", required = true, nillable = true)
    protected String mainsite;
    @XmlElement(name = "ORG_CODE", required = true, nillable = true)
    protected String orgcode;
    @XmlElement(name = "START_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected String startdate;
    @XmlElement(name = "END_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected String enddate;
    @XmlElement(name = "RESERVED_1", required = true, nillable = true)
    protected String reserved1;
    @XmlElement(name = "RESERVED_2", required = true, nillable = true)
    protected String reserved2;
    @XmlElement(name = "RESERVED_3", required = true, nillable = true)
    protected String reserved3;
    @XmlElement(name = "RESERVED_4", required = true, nillable = true)
    protected String reserved4;
    @XmlElement(name = "RESERVED_5", required = true, nillable = true)
    protected String reserved5;
    @XmlElement(name = "RESERVED_6", required = true, nillable = true)
    protected String reserved6;
    @XmlElement(name = "RESERVED_7", required = true, nillable = true)
    protected String reserved7;
    @XmlElement(name = "RESERVED_8", required = true, nillable = true)
    protected String reserved8;
    @XmlElement(name = "RESERVED_9", required = true, nillable = true)
    protected String reserved9;
    @XmlElement(name = "RESERVED_10", required = true, nillable = true)
    protected String reserved10;
    @XmlElement(name = "RESERVED_11", required = true, nillable = true)
    protected String reserved11;
    @XmlElement(name = "RESERVED_12", required = true, nillable = true)
    protected String reserved12;
    @XmlElement(name = "RESERVED_13", required = true, nillable = true)
    protected String reserved13;
    @XmlElement(name = "RESERVED_14", required = true, nillable = true)
    protected String reserved14;
    @XmlElement(name = "RESERVED_15", required = true, nillable = true)
    protected String reserved15;

    /**
     * Gets the value of the prikey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPRIKEY() {
        return prikey;
    }

    /**
     * Sets the value of the prikey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPRIKEY(String value) {
        this.prikey = value;
    }

    /**
     * Gets the value of the batchid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBATCHID() {
        return batchid;
    }

    /**
     * Sets the value of the batchid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBATCHID(String value) {
        this.batchid = value;
    }

    /**
     * Gets the value of the headerid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHEADERID() {
        return headerid;
    }

    /**
     * Sets the value of the headerid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHEADERID(String value) {
        this.headerid = value;
    }

    /**
     * Gets the value of the siteid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSITEID() {
        return siteid;
    }

    /**
     * Sets the value of the siteid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSITEID(String value) {
        this.siteid = value;
    }

    /**
     * Gets the value of the sitetype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSITETYPE() {
        return sitetype;
    }

    /**
     * Sets the value of the sitetype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSITETYPE(String value) {
        this.sitetype = value;
    }

    /**
     * Gets the value of the changetype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCHANGETYPE() {
        return changetype;
    }

    /**
     * Sets the value of the changetype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCHANGETYPE(String value) {
        this.changetype = value;
    }

    /**
     * Gets the value of the distcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDISTCODE() {
        return distcode;
    }

    /**
     * Sets the value of the distcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDISTCODE(String value) {
        this.distcode = value;
    }

    /**
     * Gets the value of the currflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCURRFLAG() {
        return currflag;
    }

    /**
     * Sets the value of the currflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCURRFLAG(String value) {
        this.currflag = value;
    }

    /**
     * Gets the value of the segment1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT1() {
        return segment1;
    }

    /**
     * Sets the value of the segment1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT1(String value) {
        this.segment1 = value;
    }

    /**
     * Gets the value of the segment2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT2() {
        return segment2;
    }

    /**
     * Sets the value of the segment2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT2(String value) {
        this.segment2 = value;
    }

    /**
     * Gets the value of the segment3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT3() {
        return segment3;
    }

    /**
     * Sets the value of the segment3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT3(String value) {
        this.segment3 = value;
    }

    /**
     * Gets the value of the segment4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT4() {
        return segment4;
    }

    /**
     * Sets the value of the segment4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT4(String value) {
        this.segment4 = value;
    }

    /**
     * Gets the value of the segment5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT5() {
        return segment5;
    }

    /**
     * Sets the value of the segment5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT5(String value) {
        this.segment5 = value;
    }

    /**
     * Gets the value of the segment6 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT6() {
        return segment6;
    }

    /**
     * Sets the value of the segment6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT6(String value) {
        this.segment6 = value;
    }

    /**
     * Gets the value of the segment7 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT7() {
        return segment7;
    }

    /**
     * Sets the value of the segment7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT7(String value) {
        this.segment7 = value;
    }

    /**
     * Gets the value of the segment8 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT8() {
        return segment8;
    }

    /**
     * Sets the value of the segment8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT8(String value) {
        this.segment8 = value;
    }

    /**
     * Gets the value of the segment9 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSEGMENT9() {
        return segment9;
    }

    /**
     * Sets the value of the segment9 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSEGMENT9(String value) {
        this.segment9 = value;
    }

    /**
     * Gets the value of the deptname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPTNAME() {
        return deptname;
    }

    /**
     * Sets the value of the deptname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPTNAME(String value) {
        this.deptname = value;
    }

    /**
     * Gets the value of the deptcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPTCODE() {
        return deptcode;
    }

    /**
     * Sets the value of the deptcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPTCODE(String value) {
        this.deptcode = value;
    }

    /**
     * Gets the value of the deptsite property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPTSITE() {
        return deptsite;
    }

    /**
     * Sets the value of the deptsite property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPTSITE(String value) {
        this.deptsite = value;
    }

    /**
     * Gets the value of the deptpost property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPTPOST() {
        return deptpost;
    }

    /**
     * Sets the value of the deptpost property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPTPOST(String value) {
        this.deptpost = value;
    }

    /**
     * Gets the value of the hrtype property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHRTYPE() {
        return hrtype;
    }

    /**
     * Sets the value of the hrtype property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHRTYPE(String value) {
        this.hrtype = value;
    }

    /**
     * Gets the value of the deptsort property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDEPTSORT() {
        return deptsort;
    }

    /**
     * Sets the value of the deptsort property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDEPTSORT(String value) {
        this.deptsort = value;
    }

    /**
     * Gets the value of the mainsite property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMAINSITE() {
        return mainsite;
    }

    /**
     * Sets the value of the mainsite property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMAINSITE(String value) {
        this.mainsite = value;
    }

    /**
     * Gets the value of the orgcode property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getORGCODE() {
        return orgcode;
    }

    /**
     * Sets the value of the orgcode property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setORGCODE(String value) {
        this.orgcode = value;
    }

    /**
     * Gets the value of the startdate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public String getSTARTDATE() {
        return startdate;
    }

    /**
     * Sets the value of the startdate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSTARTDATE(XMLGregorianCalendar value) {
        this.startdate = value.toString();
    }

    /**
     * Gets the value of the enddate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public String getENDDATE() {
        return enddate;
    }

    /**
     * Sets the value of the enddate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setENDDATE(XMLGregorianCalendar value) {
        this.enddate = value.toString();
    }

    /**
     * Gets the value of the reserved1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED1() {
        return reserved1;
    }

    /**
     * Sets the value of the reserved1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED1(String value) {
        this.reserved1 = value;
    }

    /**
     * Gets the value of the reserved2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED2() {
        return reserved2;
    }

    /**
     * Sets the value of the reserved2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED2(String value) {
        this.reserved2 = value;
    }

    /**
     * Gets the value of the reserved3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED3() {
        return reserved3;
    }

    /**
     * Sets the value of the reserved3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED3(String value) {
        this.reserved3 = value;
    }

    /**
     * Gets the value of the reserved4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED4() {
        return reserved4;
    }

    /**
     * Sets the value of the reserved4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED4(String value) {
        this.reserved4 = value;
    }

    /**
     * Gets the value of the reserved5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED5() {
        return reserved5;
    }

    /**
     * Sets the value of the reserved5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED5(String value) {
        this.reserved5 = value;
    }

    /**
     * Gets the value of the reserved6 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED6() {
        return reserved6;
    }

    /**
     * Sets the value of the reserved6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED6(String value) {
        this.reserved6 = value;
    }

    /**
     * Gets the value of the reserved7 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED7() {
        return reserved7;
    }

    /**
     * Sets the value of the reserved7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED7(String value) {
        this.reserved7 = value;
    }

    /**
     * Gets the value of the reserved8 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED8() {
        return reserved8;
    }

    /**
     * Sets the value of the reserved8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED8(String value) {
        this.reserved8 = value;
    }

    /**
     * Gets the value of the reserved9 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED9() {
        return reserved9;
    }

    /**
     * Sets the value of the reserved9 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED9(String value) {
        this.reserved9 = value;
    }

    /**
     * Gets the value of the reserved10 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED10() {
        return reserved10;
    }

    /**
     * Sets the value of the reserved10 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED10(String value) {
        this.reserved10 = value;
    }

    /**
     * Gets the value of the reserved11 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED11() {
        return reserved11;
    }

    /**
     * Sets the value of the reserved11 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED11(String value) {
        this.reserved11 = value;
    }

    /**
     * Gets the value of the reserved12 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED12() {
        return reserved12;
    }

    /**
     * Sets the value of the reserved12 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED12(String value) {
        this.reserved12 = value;
    }

    /**
     * Gets the value of the reserved13 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED13() {
        return reserved13;
    }

    /**
     * Sets the value of the reserved13 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED13(String value) {
        this.reserved13 = value;
    }

    /**
     * Gets the value of the reserved14 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED14() {
        return reserved14;
    }

    /**
     * Sets the value of the reserved14 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED14(String value) {
        this.reserved14 = value;
    }

    /**
     * Gets the value of the reserved15 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED15() {
        return reserved15;
    }

    /**
     * Sets the value of the reserved15 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED15(String value) {
        this.reserved15 = value;
    }

}
