package cn.chinaunicom.sdsi.cloud.sync.vo;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 组织信息同步实体
 * <AUTHOR>
 * @date 2021-3-5
 * @version V1.0
 */
@Data
@Accessors(chain = true)
@Tag(name="用户组织同步实体", description="用户组织同步实体")
public class SbSoaSoaImportEmpBpelInfoSrvInputItem {
    @Schema(name = "记录唯一关键字，由外部调用系统给出，需确保该值在该批次导入的全局唯一。用于记录服务消费方对于该记录的唯一标识，方便后续排查使用")
    private String PRI_KEY;
    @Schema(name = "批次号")
    private String BATCH_ID;
    @Schema(name = "内部ID，取用户中心人员主键id填充下发")
    private String HEADER_ID;
    @Schema(name = "业务类型：1新增 2修改 3删除")
    private BigDecimal COMMANDTYPE;
    @Schema(name = "主数据人员编码，取用户中心人员编码（也叫主数据人员编码）")
    private String MDM_EMP_CODE;
    @Schema(name = "HR人员编码或渠道社会人员编码，取HR编码hr_staff_code填充下发")
    private String HR_EMP_CODE;
    @Schema(name = "人员类型，1:HR人员、2:临时人员、3营业性外包人员（挂渠道组织）、4:劳务派遣人员、5:紧密型外包人员、6:退休人员")
    private String CUNC_PERSON_TYPE;
    @Schema(name = "身份证号")
    private String EMP_CODE;
    @Schema(name = "员工姓名")
    private String EMP_NAME;
    @Schema(name = "员工姓")
    private String FIRST_NAME;
    @Schema(name = "员工名")
    private String LAST_NAME;
    @Schema(name = "性别：传M(男)或F（女）（1--男  0--女）")
    private String SEX;
    @Schema(name = "出生日期")
    private String DATE_OF_BIRTH;
    @Schema(name = "入职日期")
    private String ORIGINAL_HIRE_DATE;
    @Schema(name = "生效日期")
    private String EFFECTIVE_START_DATE;
    @Schema(name = "结束日期")
    private String EFFECTIVE_END_DATE;
    @Schema(name = "记录创建日期")
    private String CREATION_DATE;
    @Schema(name = "责任部门编号")
    private String HIGH_EDPT_CODE;
    @Schema(name = "责任部门名称")
    private String HIGH_EDPT_NAME;
    @Schema(name = "责任人姓名")
    private String HIGH_NAME;
    @Schema(name = "责任人邮箱")
    private String HIGH_NAME_MAIL;
    @Schema(name = "是否在通讯录中显示")
    private String HZ_YES_NO;
    @Schema(name = "邮政编码")
    private String POSTCODE;
    @Schema(name = "离职标志：渠道社会人员时为必填项0－正常，1－已经离职， 2 - 暂时冻")
    private String RESERVED_1;
    @Schema(name = "联系地址")
    private String RESERVED_2;
    @Schema(name = "渠道编码")
    private String RESERVED_3;
    @Schema(name = "云门户人员编码，用户中心人员账号填充下发，云门户下发时必填")
    private String RESERVED_4;
    @Schema(name = "是否虚拟人员，1：虚拟人员，0或空：非虚拟人员（用户中心提供的都是非虚拟人员，默认0）")
    private String RESERVED_5;
    @Schema(name = "主账号邮箱地址，云门户系统下发时必填（用户中心人员的邮箱地址填充）")
    private String RESERVED_6;
    @Schema(name = "责任人账号")
    private String RESERVED_7;
    @Schema(name = "责任人邮箱")
    private String RESERVED_8;
    @Schema(name = "责任人身份证号")
    private String RESERVED_9;
    @Schema(name = "责任人手机号码")
    private String RESERVED_10;
    @Schema(name = "责任人是否与所有人一致")
    private String RESERVED_11;
    @Schema(name = "工号状态")
    private String RESERVED_12;
    @Schema(name = "自助设备工号标识：0-否 1-是")
    private String RESERVED_13;
    @Schema(name = "是否划小1:划小，0：非划小")
    private String RESERVED_14;
    @Schema(name = "划小岗位描述")
    private String RESERVED_15;
    @Schema(name = "制单人员工工号")
    private String EMPLOYEE_NUMBER;
}