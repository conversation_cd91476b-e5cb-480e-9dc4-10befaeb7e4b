package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 人员岗位查询对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Tag(name = "人员岗位查询对象",description = "查询条件使用")
public class SysStaffOrgQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    @Schema(name="姓名")
    private String staffName;

    @Schema(name="登录名")
    private String loginName;

    @Schema(name="主岗组织ID")
    private String orgId;

    @Schema(name="多个组织")
    private List<String> orgIdList;

    @Schema(name="主岗组织名称")
    private String orgName;

    @Schema(name="岗位状态:valid正常,invalid禁用")
    private String staffOrgStatus;

    @Schema(name="人员类别:own内部人员,partner合作伙伴")
    private String staffKind;

    @Schema(name = "岗位ID",hidden = true)
    private String staffOrgId;

    @Schema(name = "岗位类别：F-主岗，T-兼岗，J-借调")
    private String staffOrgType;

    @Schema(name = "手机号码")
    private String cellphone;

    @Schema(name = "人员ID",hidden = true)
    private String staffId;

    @Schema(name = "默认查询状态标志",hidden = true)
    private String status;

    @Schema(name="电子邮件")
    private String email;

    /**
     * 组织类别：org-组织，dept-部门，group-用户组
     */
    @Schema(name="组织类别：org-组织，dept-部门，group-用户组")
    private String orgType;

    @Schema(name = "用户uidList")
    private List<String> staffIdList;

    @Schema(name="切岗组织ID")
    private String jobSelectOrgId;

    @Schema(name = "租户ID")
    private String tenantId;
}
