package cn.chinaunicom.sdsi.cloud.system.role.entity;

import cn.chinaunicom.sdsi.core.request.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 租户系统角色查询对象
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/3/12
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name="租户系统角色查询对象")
public class SysRoleTenantQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    @Schema(name = "角色id")
    @TableId(value = "role_id")
    private String roleId;

    @Schema(name = "租户id")
    private String tenantId;

    @Schema(name="创建人")
    private String createBy;

    @Schema(name="创建时间")
    private Date createDate;

}