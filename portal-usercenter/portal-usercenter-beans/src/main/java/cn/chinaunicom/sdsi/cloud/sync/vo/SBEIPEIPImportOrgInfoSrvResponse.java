package cn.chinaunicom.sdsi.cloud.sync.vo;


import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SERVICE_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SERVICE_MESSAGE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="INSTANCE_ID" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="ErrorCollection" type="{http://mss.unicom.com/SB_EIP_EIP_ImportOrgInfoSrv}ErrorCollection"/>
 *         &lt;element name="ResponseCollection" type="{http://mss.unicom.com/SB_EIP_EIP_ImportOrgInfoSrv}ResponseCollection"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "serviceflag",
    "servicemessage",
    "instanceid",
    "errorCollection",
    "responseCollection"
})
@XmlRootElement(name = "SB_EIP_EIP_ImportOrgInfoSrvResponse")
public class SBEIPEIPImportOrgInfoSrvResponse {

    @XmlElement(name = "SERVICE_FLAG", required = true)
    protected String serviceflag;
    @XmlElement(name = "SERVICE_MESSAGE", required = true)
    protected String servicemessage;
    @XmlElement(name = "INSTANCE_ID", required = true)
    protected BigDecimal instanceid;
    @XmlElement(name = "ErrorCollection", required = true)
    protected ErrorCollection errorCollection;
    @XmlElement(name = "ResponseCollection", required = true)
    protected ResponseCollection responseCollection;

    /**
     * 获取serviceflag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSERVICEFLAG() {
        return serviceflag;
    }

    /**
     * 设置serviceflag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSERVICEFLAG(String value) {
        this.serviceflag = value;
    }

    /**
     * 获取servicemessage属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSERVICEMESSAGE() {
        return servicemessage;
    }

    /**
     * 设置servicemessage属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSERVICEMESSAGE(String value) {
        this.servicemessage = value;
    }

    /**
     * 获取instanceid属性的值。
     * 
     * @return
     *     possible object is
     *     {@link BigDecimal }
     *     
     */
    public BigDecimal getINSTANCEID() {
        return instanceid;
    }

    /**
     * 设置instanceid属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link BigDecimal }
     *     
     */
    public void setINSTANCEID(BigDecimal value) {
        this.instanceid = value;
    }

    /**
     * 获取errorCollection属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ErrorCollection }
     *     
     */
    public ErrorCollection getErrorCollection() {
        return errorCollection;
    }

    /**
     * 设置errorCollection属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ErrorCollection }
     *     
     */
    public void setErrorCollection(ErrorCollection value) {
        this.errorCollection = value;
    }

    /**
     * 获取responseCollection属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseCollection }
     *     
     */
    public ResponseCollection getResponseCollection() {
        return responseCollection;
    }

    /**
     * 设置responseCollection属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseCollection }
     *     
     */
    public void setResponseCollection(ResponseCollection value) {
        this.responseCollection = value;
    }

}
