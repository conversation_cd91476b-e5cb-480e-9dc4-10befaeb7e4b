package cn.chinaunicom.sdsi.cloud.system.tenantApply.entity;

import cn.chinaunicom.sdsi.core.interfaces.Edit;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 租户审核功能 - 租户申请单视图对象
 * 
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/20
 * -
 * @description 内容描述
 */
@Tag(name = "租户审核功能 - 租户申请单视图对象", description = "系统配置")
@Data
public class SysTenantApplyVO implements Serializable {

    @Schema(hidden = true)
    private static final long serialVersionUID = 2L;

    @Schema(name = "租户申请单")
    @NotNull(message = "租户申请单主键不能为空", groups = {Edit.class})
    private String tenantApplyId;

    @Schema(name = "租户申请单单号")
    @Length(max = 50)
    private String tenantApplyNo;

    @Schema(name = "租户Id")
    @Length(max = 36)
    private String tenantId;

    @Schema(name = "租户名称")
    @Length(max = 128)
    private String tenantName;

    @Schema(name = "租户管理员ID")
    private String tenantAdminId;

    @Schema(name = "租户创建人")
    @Length(max = 36)
    private String tenantCreateBy;

    @Schema(name = "租户域名")
    @Length(max = 128)
    private String tenantDomain;

    @Schema(name = "业务ID")
    @Length(max = 36)
    private String businessId;

    @Schema(name = "最大用户数")
    private Integer maxStaff;

    @Schema(name = "有效截止时间")
    private LocalDateTime effectiveDate;

    @Schema(name = "前台位置")
    private String tenantHtmlPath;

    @Schema(name = "审核人")
    private String auditBy;

    @Schema(name = "审核时间")
    private LocalDateTime auditDate;

    @Schema(name = "申请单状态 - init 申请 ok 通过 reject 拒绝")
    private String tenantApplyStatus;

    @Schema(name = "审核意见")
    private String auditInfo;
}
