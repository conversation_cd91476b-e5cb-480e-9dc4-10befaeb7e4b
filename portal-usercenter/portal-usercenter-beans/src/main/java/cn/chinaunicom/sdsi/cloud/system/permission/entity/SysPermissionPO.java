package cn.chinaunicom.sdsi.cloud.system.permission.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统权限表（菜单、操作等）
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Tag(name="权限 持久层对象")
@TableName("sys_permission")
@KeySequence(value = "SYS_PERMISSION_SEQ", clazz = String.class)
public class SysPermissionPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(name="权限ID")
    @TableId(value = "permission_id")
    private String permissionId;

    /**
     * 编码（自定义）
     */
    @Schema(name="权限编码")
    private String code;

    /**
     * 权限名称
     */
    @Schema(name="权限名称")
    private String permissionName;

    /**
     * 路径
     */
    @Schema(name="权限路径")
    private String uri;

    /**
     * 类型：menu-菜单、operation-操作
     */
    @Schema(name="权限类型")
    private String permissionType;

    /**
     * 打开类型：tab-标签页、blank-新页面
     */
    @Schema(name="打开类型")
    private String openType;

    /**
     * 父节点ID
     */
    @Schema(name="权限父节点ID")
    private String parentId;

    /**
     * 级别（未使用）
     */
    @Schema(name = "权限等级", hidden = true)
    private Integer grade;

    /**
     * 状态：valid有效，invalid无效
     */
    @Schema(name="有效状态")
    private String permissionStatus;

    /**
     * 菜单图标
     */
    @Schema(name="菜单图标")
    private String icon;

    /**
     * 权限字
     */
    @Schema(name="校验字符")
    private String checkCode;

    /**
     * 描述
     */
    @Schema(name="描述")
    private String description;

    /**
     * 显示顺序
     */
    @Schema(name="展示顺序")
    private Integer permissionSort;

    /**
     * 标识,字典: scope.
     */
    @Schema(name = "系统标识")
    private String permissionScope;

    /**
     * 菜单显隐
     */
    @Schema(name = "菜单显隐")
    private String permissionVisible;

    /**
     * 是否外链
     */
    @Schema(name = "是否外链")
    private String permissionFrame;

    /**
     * 终端类型
     */
    @Schema(name = "终端类型")
    private String terminal;
}
