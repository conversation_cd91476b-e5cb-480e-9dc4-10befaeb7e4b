package cn.chinaunicom.sdsi.cloud.system.client.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <b>客户端配置功能 - 客户端配置分页查询视图对象</b><br>
 * <b>描述: </b>
 * 客户端配置功能 - 客户端配置分页查询视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name = "客户端配置功能 - 客户端配置分页查询视图对象", description = "客户端配置")
public class SysClientQueryVO extends BaseQueryVO {

    @Schema(name = "客户端参数ID")
    private String configId;

    @Schema(name = "配置名称")
    private String configName;

    @Schema(name = "配置编码")
    private String configCode;

    @Schema(name = "配置类型")
    private String configType;

    @Schema(name = "配置值")
    private String configValue;

    @Schema(name = "配置描述")
    private String configDescribe;

    @Schema(name = "客户端类型")
    private String clientType;

    @Schema(name = "配置编码")
    private List<String> configCodes;

    @Schema(name = "租户ID")
    private String tenantId;

    @Schema(name = "钉钉组织id")
    private String corpId;

    @Schema(name = "钉钉应用标识")
    private String appLabel;
}
