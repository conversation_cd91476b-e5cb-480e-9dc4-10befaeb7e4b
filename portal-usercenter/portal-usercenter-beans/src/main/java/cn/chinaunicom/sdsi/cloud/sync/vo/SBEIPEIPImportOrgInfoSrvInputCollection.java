package cn.chinaunicom.sdsi.cloud.sync.vo;



import lombok.ToString;

import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>SB_EIP_EIP_ImportOrgInfoSrvInputCollection complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="SB_EIP_EIP_ImportOrgInfoSrvInputCollection">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="SB_EIP_EIP_ImportOrgInfoSrvInputItem" type="{http://mss.unicom.com/SB_EIP_EIP_ImportOrgInfoSrv}SB_EIP_EIP_ImportOrgInfoSrvInputItem" maxOccurs="unbounded" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@ToString
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SB_EIP_EIP_ImportOrgInfoSrvInputCollection", propOrder = {
    "sbeipeipImportOrgInfoSrvInputItem"
})
public class SBEIPEIPImportOrgInfoSrvInputCollection {

    @XmlElement(name = "SB_EIP_EIP_ImportOrgInfoSrvInputItem")
    protected List<SBEIPEIPImportOrgInfoSrvInputItem> sbeipeipImportOrgInfoSrvInputItem;
    /**
     * Gets the value of the sbeipeipImportOrgInfoSrvInputItem property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the sbeipeipImportOrgInfoSrvInputItem property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSbeipeipImportOrgInfoSrvInputItem().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link SBEIPEIPImportOrgInfoSrvInputItem }
     * 
     * 
     */
    public List<SBEIPEIPImportOrgInfoSrvInputItem> getSbeipeipImportOrgInfoSrvInputItem() {
        if (sbeipeipImportOrgInfoSrvInputItem == null) {
            sbeipeipImportOrgInfoSrvInputItem = new ArrayList<SBEIPEIPImportOrgInfoSrvInputItem>();
        }
        return this.sbeipeipImportOrgInfoSrvInputItem;
    }

    public void setSbeipeipImportOrgInfoSrvInputItem(List<SBEIPEIPImportOrgInfoSrvInputItem> sbeipeipImportOrgInfoSrvInputItem) {
        this.sbeipeipImportOrgInfoSrvInputItem = sbeipeipImportOrgInfoSrvInputItem;
    }
}
