package cn.chinaunicom.sdsi.cloud.system.dict.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 数据字典功能 - 字典类型(主字典)数据同步查询视图对象
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/15
 * -
 * @description 内容描述
 */
@Data
@Accessors(chain = true)
@Tag(name = "数据字典功能 - 字典类型(主字典)数据同步视图对象", description = "数据同步")
public class SysDictDateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(name = "开始时间")
    private String startTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Schema(name = "结束时间")
    private String endTime;

    @Schema(name = "编码")
    private String dictLabel;
}
