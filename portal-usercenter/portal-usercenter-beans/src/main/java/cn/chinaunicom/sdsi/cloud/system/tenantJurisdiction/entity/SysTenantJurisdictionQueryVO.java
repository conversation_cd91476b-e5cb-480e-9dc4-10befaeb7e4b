package cn.chinaunicom.sdsi.cloud.system.tenantJurisdiction.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 用于接收分页查询参数
 *
 * <AUTHOR>
 */

@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name="租户管辖查询对象")
public class SysTenantJurisdictionQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    /**
     * 管理租户ID
     */
    private String manageTenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name = "租户域名")
    private String tenantDomain;

    @Schema(name = "租户登录名称")
    private String tenantLoginName;

    @Schema(name = "管理员名称")
    private String displayName;

    @Schema(name = "有效时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date effectiveDate;

    @Schema(name = "有效时间参数区间")
    private String queryEffectiveDate;

    @Schema(name = "有效开始时间")
    private Date startEffectiveDate;

    @Schema(name = "有效结束时间")
    private Date endEffectiveDate;

    @Schema(name = "有效开始时间")
    private String beginTime;

    @Schema(name = "有效结束时间")
    private String endTime;
}
