package cn.chinaunicom.sdsi.cloud.system.org.entity;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 组织人员树筛选对象 - 查询条件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Tag(name = "组织人员树筛选对象", description = "传递查询条件时使用")
public class SysOrgStaffTreeQueryVO {
    @Schema(hidden = true)
    private static final long serialVersionUID = 2L;

    @Schema(name = "角色ID", description ="多个id用逗号连接")
    private String[] roleIds;

    @Schema(name="组织ID")
    private String orgId;
}