package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import cn.chinaunicom.sdsi.core.interfaces.Delete;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * 岗位视图对象
 * 展现/提交时使用
 *
 * <AUTHOR>
 */
@Data
@Tag(name = "岗位视图对象", description = "展现/提交时使用")
public class SysStaffOrgSaveVO implements Serializable {
    private static final long serialVersionUID = 2L;

    @Schema(name = "岗位ID", hidden = true)
    @Length(max = 36)
    @NotNull(groups = Delete.class,message = "岗位ID不能为空")
    private String staffOrgId;

    @Schema(name="人员ID")
    @Length(max = 36)
    private String staffId;

    @Schema(name="组织ID")
    @NotNull(message = "组织不能为空")
    @Length(max = 36)
    private String orgId;

    @Schema(name="岗位类型:F主岗,T兼职,J借调")
    @Length(max = 8)
    private String staffOrgType;

    @Schema(name="职务")
    @Length(max = 128)
    private String duty;

    @Schema(name="状态:valid正常,invalid禁用")
    @Pattern(regexp = "valid|invalid", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "人员状态只能为 valid 或 invalid")
    @Length(max = 16)
    private String staffOrgStatus;

    @Schema(name="入职日期")
    private Date hireDate;

    @Schema(name="生效日期")
    private Date effectStartDate;

    @Schema(name="失效日期")
    private Date effectEndDate;

    @Schema(name = "用户ids")
    @TableField(exist = false)
    private List<String> staffIds;

    @Schema(name = "角色ids")
    @TableField(exist = false)
    private List<String> roleIds;
}
