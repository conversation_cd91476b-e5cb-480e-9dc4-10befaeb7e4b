package cn.chinaunicom.sdsi.cloud.system.feign.fallback;

import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigQueryVO;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigVO;
import cn.chinaunicom.sdsi.cloud.system.feign.SysConfigFeignClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;

/**
 * feign调用
 */
@Slf4j
@Component
public class SysConfigFeignClientFallBack implements FallbackFactory<SysConfigFeignClient> {


    @Override
    public SysConfigFeignClient create(Throwable throwable) {
        return new SysConfigFeignClient() {
            @Override
            public List<SysConfigVO> selectConfigListByCode(@RequestBody SysConfigQueryVO sysConfigQueryVO) {
                log.error("{} UserCenterFeignClientFallback selectConfigListByCode feign异常了",sysConfigQueryVO.toString());
                log.error(throwable.getMessage());
                return null;
            }
        };
    }
}