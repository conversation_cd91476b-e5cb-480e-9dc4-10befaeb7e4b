package cn.chinaunicom.sdsi.cloud.system.log.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description:
 * @Author： Cyz
 * @Date: 2021/7/26 15:14
 */
@Data
public class SysLogQuery extends BaseQueryVO {

    @Schema(name = "员工名称")
    private String staffName;

    @Schema(name = "业务类型")
    private String busiType;

    @Schema(name = "操作类型")
    private String operType;

    @Schema(name = "开始时间")
    private String beginTime;

    @Schema(name="截至时间")
    private String endTime;

    @Schema(name = "业务ID")
    private String busiId;

    @Schema(name = "租户ID")
    private String tenantId;

    @Schema(name = "IP信息")
    private String userIp;

    @Schema(name = "操作状态")
    private String logStatus;

    @Schema(name = "登录名称")
    private String account;

    @Schema(name = "用户名称")
    private String accountName;

    @Schema(name = "排序的字段")
    private String orderByColumn;

    @Schema(name = "排序的规则")
    private String isAsc;

    @Schema(name = "浏览器类型")
    private String browser;

    @Schema(name = "操作系统")
    private String os;

    @Schema(name = "异常信息")
    private String exception;
}
