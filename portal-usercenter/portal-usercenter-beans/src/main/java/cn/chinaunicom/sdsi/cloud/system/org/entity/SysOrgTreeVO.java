package cn.chinaunicom.sdsi.cloud.system.org.entity;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 组织视图层对象 - 组织树
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Tag(name = "组织视图对象", description = "组织树使用")
public class SysOrgTreeVO implements Serializable {

    @Schema(hidden = true)
    private static final long serialVersionUID = 2L;

    @Schema(name = "组织ID")
    private String orgId;

    @Schema(name = "组织名称")
    private String orgName;

    @Schema(name = "组织父节点ID")
    private String parentId;

    @Schema(name = "是否父节点")
    private Boolean isParent;

}