package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员岗位展示对象
 *
 * <AUTHOR>
 */
@Tag(name = "人员岗位视图对象", description = "展示使用")
@Data
public class SysStaffOrgVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name="人员ID")
    private String staffId;

    @Schema(name="登录名")
    private String loginName;

    @Schema(name="姓名")
    private String staffName;

    @Schema(name="性别")
    private String sex;

    @Schema(name="手机号码")
    private String cellphone;

    @Schema(name="电子邮件")
    private String email;

    @Schema(name="人员状态:valid正常,invalid禁用")
    private String staffStatus;

    @Schema(name="岗位ID")
    private String staffOrgId;

    @Schema(name="岗位类型:F主岗，T兼职，J借调")
    private String staffOrgType;

    @Schema(name="岗位状态:valid正常,invalid禁用")
    private String staffOrgStatus;

    @Schema(name="组织ID")
    private String orgId;

    @Schema(name="组织名称")
    private String orgName;

    @Schema(name="详细的组织名称")
    private String allOrgName;

    @Schema(name="系统内人员类型")
    private String staffKind;

    @Schema(name="地址")
    private String address;

    @Schema(name = "租户ID")
    private String tenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name = "客户所属地区")
    private String staffArea;

    @Schema(name="创建时间-字符串")
    private String createDateStr;
}
