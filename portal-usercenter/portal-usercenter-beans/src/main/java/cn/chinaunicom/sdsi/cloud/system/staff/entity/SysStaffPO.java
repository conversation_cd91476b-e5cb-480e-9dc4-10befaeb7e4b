package cn.chinaunicom.sdsi.cloud.system.staff.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 系统人员表
 * </p>
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-01-17
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("sys_staff")
@Tag(name = "SysStaffPO对象", description = "系统人员表")
@KeySequence(value = "sys_staff_SEQ", clazz = String.class)
public class SysStaffPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @Schema(name = "人员ID")
    @TableId(value = "staff_id")
    private String staffId;

    @Schema(name = "人员编码")
    private String staffCode;

    @Schema(name = "姓名")
    private String staffName;

    @Schema(name = "主岗组织ID")
    private String orgId;


    @Schema(name="当前用户所在的公司ID")
    private String companyId;

    @Schema(name = "人员类型")
    private String staffType;

    @Schema(name = "状态：valid正常，invalid禁用")
    private String staffStatus;

    @Schema(name = "身份证号/工号")
    private String employeeCode;

    @Schema(name = "性别：male男，female女")
    private String sex;

    @Schema(name = "系统中人员类型：own内部人员；partner合作伙伴")
    private String staffKind;

    @Schema(name = "排序")
    private Integer staffSort;

    @Schema(name = "生日")
    private Date birthday;

    @Schema(name = "入职日期")
    private Date hireDate;

    @Schema(name = "生效日期（未使用）")
    private transient LocalDateTime effectStartDate;

    @Schema(name = "失效日期（未使用）")
    private transient LocalDateTime effectEndDate;

    @Schema(name = "邮编")
    private String postcode;

    @Schema(name = "邮箱")
    private String address;

    @Schema(name = "手机号码")
    private String cellphone;

    @Schema(name = "座机电话")
    private String telephone;

    @Schema(name = "电子邮件")
    private String email;

    @Schema(name = "登录名")
    private String loginName;

    @Schema(name = "密码")
    private String passwd;

    @Schema(name = "是否有兼岗，否：no，是：yes")
    private String multiJob;

    @Schema(name = "兼岗的组织id，多个用英文逗号分隔")
    private String multiOrgs;

    @Schema(name = "备用字段a")
    private String attra;

    @Schema(name = "备用字段a")
    private String attrb;

    @Schema(name = "备用字段a")
    private String attrc;

    @Schema(name = "人员头像")
    private String headImg;

    @Schema(name = "是否需要修改密码  0：否  1：是")
    private Integer userPwdFlag;

}
