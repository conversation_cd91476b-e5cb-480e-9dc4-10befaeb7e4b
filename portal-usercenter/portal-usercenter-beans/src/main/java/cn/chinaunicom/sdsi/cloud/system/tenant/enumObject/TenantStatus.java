package cn.chinaunicom.sdsi.cloud.system.tenant.enumObject;

/**
 * 租户管理功能 - 租户状态 tenantStatus 枚举类
 * <p><b>valid</b>-正常
 * <p><b>invalid</b>-冻结
 * <p><b>reject</b>-审批拒绝
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/22
 * -
 * @description valid-正常 invalid-冻结 reject-审批拒绝
 */
public enum TenantStatus {
    // 正常状态,此状态下数据为有效
    valid,
    // 冻结状态,此状态下数据为无效
    // 前台发起租户申请时,状态为冻结状态.拥有对应权限的账号也可在后台进行租户的冻结操作.
    invalid,
    // 审批拒绝状态,此状态下数据为无效
    // 前台发起租户申请时,审批人拒绝申请则为此状态
    reject
}