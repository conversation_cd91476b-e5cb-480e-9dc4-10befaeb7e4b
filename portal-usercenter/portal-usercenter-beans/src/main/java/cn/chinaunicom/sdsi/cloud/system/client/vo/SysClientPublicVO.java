package cn.chinaunicom.sdsi.cloud.system.client.vo;


//import cn.chinaunicom.sdsi.cloud.system.client.entity.DateTimeLongSerializer;

import cn.chinaunicom.sdsi.core.interfaces.Add;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@Tag(name="公共查询接口参数展示对象")
public class SysClientPublicVO implements Serializable {

    @Schema(name = "配置名称")
    @Length(max = 36)
    @NotBlank(message = "配置名称不能为空")
    private String configName;

    @Schema(name = "配置编码")
    @Length(max = 255)
    @NotBlank(message = "配置编码不能为空", groups = {Add.class})
    private String configCode;

    @Schema(name = "配置类型")
    @Length(max = 8)
    @NotBlank(message = "配置类型不能为空")
    private String configType;

    @Schema(name = "配置值")
    @NotBlank(message = "配置值不能为空", groups = {Add.class})
    private String configValue;

    @Schema(name = "客户端类型")
    @Length(max = 255)
    @NotBlank(message = "客户端类型不能为空")
    private String clientType;

    @Schema(name = "时间戳")
    private Long timeStamp;

}
