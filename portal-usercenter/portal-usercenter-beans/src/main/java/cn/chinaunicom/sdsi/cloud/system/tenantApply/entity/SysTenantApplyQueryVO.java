package cn.chinaunicom.sdsi.cloud.system.tenantApply.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 租户审核功能 - 租户申请单分页查询视图对象
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/20
 * -
 * @description 内容描述
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Tag(name = "租户审核功能 - 租户申请单分页查询视图对象", description = "租户审核")
public class SysTenantApplyQueryVO extends BaseQueryVO {

    private static final long serialVersionUID = 2L;

    @Schema(name = "租户申请单ID")
    private String tenantApplyId;

    @Schema(name = "租户申请单单号")
    private String tenantApplyNo;

    @Schema(name = "租户ID")
    private String tenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name = "租户管理员ID")
    private String tenantAdminId;

    @Schema(name = "租户域名")
    private String tenantDomain;

    @Schema(name = "申请单状态 - init 申请 ok 通过 reject 拒绝")
    private String tenantApplyStatus;
}
