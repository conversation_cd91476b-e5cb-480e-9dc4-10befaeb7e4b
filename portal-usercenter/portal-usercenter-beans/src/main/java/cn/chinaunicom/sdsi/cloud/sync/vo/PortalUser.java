package cn.chinaunicom.sdsi.cloud.sync.vo;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
/**
 * 门户组织实体
 * <AUTHOR> 2012-9-5
 */

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "")
@XmlRootElement(name = "dataEntity")
public class PortalUser implements Serializable {

	private static final long serialVersionUID = 1L;
	private String op;//操作类型1 新增 2 修改 3 删除
	private String uid;//门户全国目录中的唯一编码，包括9位、7位及统一邮件前缀等类型
	private String employeenumber;//HR员工编码（非正式用户无该信息）,
	private String cn;//中文姓名
	private String cumail;//统一邮件
	private String displayname;//显示名，一般等于CN字段值，如部门内有重名的情况，可以以特殊名称标明，例如：张明（大）
	private String ou;//部门编码
	private String orgfullname;//用户所属部门名称为长名称，包括公司、部门、处室信息，如中国联通总部管理信息系统部规划应用处
	private String telephonenumber;//用户办公电话
	private String mobile;//手机号码
	
	private String managername;//部门领导姓名
	private String cumanagernumber;//部门领导的员工编号
	private String cuorder;//三位数字，只定义部门内用户显示排序
	private String site;//省份简称
	private String isDel;//用户状态，0 正常 1删除
	private String cucompanynumber;//所属公司组织名称
	// 二级组织数据商旅用 2021-01-14
	private String cucompanynumberTravel;
	private String dataStatus;//数据验证后的结果
	private String batchId;//soa请求中的批次号
	private String requestIp;//客户端请求ip
	private String cuentrystatus;//用户状态：active,enable,disable,delete
	private String portalUid;//老门户 portal的实际uid，不一定为统一邮件前缀
	private String departmentnumber;//老门户用户同步时部门实际编码
	/*************云门户接口改造新增字段 2014-07-14 ↓↓↓↓*************/
	private String RESERVED_1;		//离职标志  云门户下发人员信息至B-SDM时必填
	private String RESERVED_2;		//联系地址
	private String RESERVED_3;		//渠道编码
	private String RESERVED_4;		//云门户人员编码 云门户系统下发时必填，需确保唯一
	private String RESERVED_5;		//是否虚拟人员
	private String RESERVED_6;		//主账号邮箱地址 云门户系统下发时必填
	//人员岗位信息实体
	private String HEADER_ID;		//头ID
	private String SITE_ID;			//内部ID 人员岗位ID
	private String SITE_TYPE;		//岗位类型 1代表主岗类；2代表借调类；3代表兼职类； 
	private String CHANGE_TYPE;		//变动类型
	private String DIST_CODE;		//分配编号
	private String CURR_FLAG;		//有效岗位标识  如果有效则为Y 
	private String SEGMENT1;		//公司段
	private String SEGMENT2;		//成本中心段
	private String SEGMENT3;		//专业段
	private String SEGMENT4;		//科目段
	private String SEGMENT5;		//往来段
	private String SEGMENT6;		//项目段
	private String SEGMENT7;		//客户段
	/*************云门户接口改造新增字段 2014-07-14 ↑↑↑↑*************/
	public PortalUser() {
		super();
	}
	public String getUid() {
		return uid;
	}
	public void setUid(String uid) {
		this.uid = uid;
	}
	public String getEmployeenumber() {
		return employeenumber;
	}
	public void setEmployeenumber(String employeenumber) {
		this.employeenumber = employeenumber;
	}
	public String getCn() {
		return cn;
	}
	public void setCn(String cn) {
		this.cn = cn;
	}
	public String getCumail() {
		return cumail;
	}
	public void setCumail(String cumail) {
		this.cumail = cumail;
	}
	public String getDisplayname() {
		return displayname;
	}
	public void setDisplayname(String displayname) {
		this.displayname = displayname;
	}
	public String getOu() {
		return ou;
	}
	public void setOu(String ou) {
		this.ou = ou;
	}
	public String getOrgfullname() {
		return orgfullname;
	}
	public void setOrgfullname(String orgfullname) {
		this.orgfullname = orgfullname;
	}
	public String getTelephonenumber() {
		return telephonenumber;
	}
	public void setTelephonenumber(String telephonenumber) {
		this.telephonenumber = telephonenumber;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getManagername() {
		return managername;
	}
	public void setManagername(String managername) {
		this.managername = managername;
	}
	public String getCumanagernumber() {
		return cumanagernumber;
	}
	public void setCumanagernumber(String cumanagernumber) {
		this.cumanagernumber = cumanagernumber;
	}
	public String getCucompanynumberTravel() {
		return cucompanynumberTravel;
	}
	public void setCucompanynumberTravel(String cucompanynumberTravel) {
		this.cucompanynumberTravel = cucompanynumberTravel;
	}
	public String getCuorder() {
		return cuorder;
	}
	public void setCuorder(String cuorder) {
		this.cuorder = cuorder;
	}
	public String getSite() {
		return site;
	}
	public void setSite(String site) {
		this.site = site;
	}
	public String getOp() {
		return op;
	}
	public void setOp(String op) {
		this.op = op;
	}
	public String getIsDel() {
		return isDel;
	}
	public void setIsDel(String isDel) {
		this.isDel = isDel;
	}
	public String getCucompanynumber() {
		return cucompanynumber;
	}
	public void setCucompanynumber(String cucompanynumber) {
		this.cucompanynumber = cucompanynumber;
	}
	public String getDataStatus() {
		return dataStatus;
	}
	public void setDataStatus(String dataStatus) {
		this.dataStatus = dataStatus;
	}
	public String getBatchId() {
		return batchId;
	}
	public void setBatchId(String batchId) {
		this.batchId = batchId;
	}
	public String getRequestIp() {
		return requestIp;
	}
	public void setRequestIp(String requestIp) {
		this.requestIp = requestIp;
	}
	public String getCuentrystatus() {
		return cuentrystatus;
	}
	public void setCuentrystatus(String cuentrystatus) {
		this.cuentrystatus = cuentrystatus;
	}
	public String getPortalUid() {
		return portalUid;
	}
	public void setPortalUid(String portalUid) {
		this.portalUid = portalUid;
	}
	public String getDepartmentnumber() {
		return departmentnumber;
	}
	public void setDepartmentnumber(String departmentnumber) {
		this.departmentnumber = departmentnumber;
	}
	public String getRESERVED_1() {
		return RESERVED_1;
	}
	public void setRESERVED_1(String reserved_1) {
		RESERVED_1 = reserved_1;
	}
	public String getRESERVED_2() {
		return RESERVED_2;
	}
	public void setRESERVED_2(String reserved_2) {
		RESERVED_2 = reserved_2;
	}
	public String getRESERVED_3() {
		return RESERVED_3;
	}
	public void setRESERVED_3(String reserved_3) {
		RESERVED_3 = reserved_3;
	}
	public String getRESERVED_4() {
		return RESERVED_4;
	}
	public void setRESERVED_4(String reserved_4) {
		RESERVED_4 = reserved_4;
	}
	public String getRESERVED_5() {
		return RESERVED_5;
	}
	public void setRESERVED_5(String reserved_5) {
		RESERVED_5 = reserved_5;
	}
	public String getRESERVED_6() {
		return RESERVED_6;
	}
	public void setRESERVED_6(String reserved_6) {
		RESERVED_6 = reserved_6;
	}
	public String getHEADER_ID() {
		return HEADER_ID;
	}
	public void setHEADER_ID(String header_id) {
		HEADER_ID = header_id;
	}
	public String getSITE_ID() {
		return SITE_ID;
	}
	public void setSITE_ID(String site_id) {
		SITE_ID = site_id;
	}
	public String getSITE_TYPE() {
		return SITE_TYPE;
	}
	public void setSITE_TYPE(String site_type) {
		SITE_TYPE = site_type;
	}
	public String getCHANGE_TYPE() {
		return CHANGE_TYPE;
	}
	public void setCHANGE_TYPE(String change_type) {
		CHANGE_TYPE = change_type;
	}
	public String getDIST_CODE() {
		return DIST_CODE;
	}
	public void setDIST_CODE(String dist_code) {
		DIST_CODE = dist_code;
	}
	public String getCURR_FLAG() {
		return CURR_FLAG;
	}
	public void setCURR_FLAG(String curr_flag) {
		CURR_FLAG = curr_flag;
	}
	public String getSEGMENT1() {
		return SEGMENT1;
	}
	public void setSEGMENT1(String segment1) {
		SEGMENT1 = segment1;
	}
	public String getSEGMENT2() {
		return SEGMENT2;
	}
	public void setSEGMENT2(String segment2) {
		SEGMENT2 = segment2;
	}
	public String getSEGMENT3() {
		return SEGMENT3;
	}
	public void setSEGMENT3(String segment3) {
		SEGMENT3 = segment3;
	}
	public String getSEGMENT4() {
		return SEGMENT4;
	}
	public void setSEGMENT4(String segment4) {
		SEGMENT4 = segment4;
	}
	public String getSEGMENT5() {
		return SEGMENT5;
	}
	public void setSEGMENT5(String segment5) {
		SEGMENT5 = segment5;
	}
	public String getSEGMENT6() {
		return SEGMENT6;
	}
	public void setSEGMENT6(String segment6) {
		SEGMENT6 = segment6;
	}
	public String getSEGMENT7() {
		return SEGMENT7;
	}
	public void setSEGMENT7(String segment7) {
		SEGMENT7 = segment7;
	}

	@Override
	public String toString() {
		return "PortalUser [op=" + op + ", uid=" + uid + ", employeenumber=" + employeenumber + ", cn=" + cn
				+ ", cumail=" + cumail + ", displayname=" + displayname + ", ou=" + ou + ", orgfullname=" + orgfullname
				+ ", telephonenumber=" + telephonenumber + ", mobile=" + mobile + ", managername=" + managername
				+ ", cumanagernumber=" + cumanagernumber + ", cuorder=" + cuorder + ", site=" + site + ", isDel="
				+ isDel + ", cucompanynumber=" + cucompanynumber + ", cucompanynumberTravel=" + cucompanynumberTravel
				+ ", dataStatus=" + dataStatus + ", batchId=" + batchId + ", requestIp=" + requestIp
				+ ", cuentrystatus=" + cuentrystatus + ", portalUid=" + portalUid + ", departmentnumber="
				+ departmentnumber + ", RESERVED_1=" + RESERVED_1 + ", RESERVED_2=" + RESERVED_2 + ", RESERVED_3="
				+ RESERVED_3 + ", RESERVED_4=" + RESERVED_4 + ", RESERVED_5=" + RESERVED_5 + ", RESERVED_6="
				+ RESERVED_6 + ", HEADER_ID=" + HEADER_ID + ", SITE_ID=" + SITE_ID + ", SITE_TYPE=" + SITE_TYPE
				+ ", CHANGE_TYPE=" + CHANGE_TYPE + ", DIST_CODE=" + DIST_CODE + ", CURR_FLAG=" + CURR_FLAG
				+ ", SEGMENT1=" + SEGMENT1 + ", SEGMENT2=" + SEGMENT2 + ", SEGMENT3=" + SEGMENT3 + ", SEGMENT4="
				+ SEGMENT4 + ", SEGMENT5=" + SEGMENT5 + ", SEGMENT6=" + SEGMENT6 + ", SEGMENT7=" + SEGMENT7 + "]";
	}

}
