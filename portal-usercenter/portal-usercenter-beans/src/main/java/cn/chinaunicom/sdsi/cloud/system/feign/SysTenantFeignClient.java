package cn.chinaunicom.sdsi.cloud.system.feign;

import cn.chinaunicom.sdsi.cloud.system.feign.fallback.SysTenantFeignClientFallback;
import cn.chinaunicom.sdsi.cloud.system.org.entity.SysOrgVO;
import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantVO;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 租户管理
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2022/1/6
 * -
 * @description 内容描述
 */
@FeignClient(value = "${cloud.service.usercenter.serviceId:cloud-usercenter}", fallbackFactory = SysTenantFeignClientFallback.class)
public interface SysTenantFeignClient {

    /**
     * 查询租户信息列表
     *
     * @return 查询到的租户信息列表
     * <AUTHOR>
     * -
     * @date 2022/1/6
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "查询租户信息列表")
    @GetMapping("/tenants/list")
    BaseResponse<List<SysTenantVO>> queryTenantList();

    @Operation(summary = "根据租户查询组织视图列表", description ="根据租户查询组织视图列表")
    @GetMapping("/orgs/findListByTenantId")
    BaseResponse<List<SysOrgVO>> findListByTenantId(@RequestParam(value = "tenantId") String tenantId);
}
