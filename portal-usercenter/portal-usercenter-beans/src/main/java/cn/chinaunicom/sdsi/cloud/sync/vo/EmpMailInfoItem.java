
package cn.chinaunicom.sdsi.cloud.sync.vo;

import lombok.ToString;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>Java class for EmpMailInfoItem complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="EmpMailInfoItem">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PRI_KEY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="BATCH_ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="HEADER_ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EMAIL_ID" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PINGYIN_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MAIN_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_EMAIL_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EMAIL_ADDRESS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DISPLAY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EFFECTIVE_START_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="EFFECTIVE_END_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="RESERVED_1" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_2" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_3" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_4" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_5" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_6" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_7" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_8" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_9" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_10" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_11" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_12" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_13" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_14" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RESERVED_15" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EmpMailInfoItem", propOrder = {
    "prikey",
    "batchid",
    "headerid",
    "emailid",
    "pingyinname",
    "mainflag",
    "createemailflag",
    "emailaddress",
    "display",
    "effectivestartdate",
    "effectiveenddate",
    "reserved1",
    "reserved2",
    "reserved3",
    "reserved4",
    "reserved5",
    "reserved6",
    "reserved7",
    "reserved8",
    "reserved9",
    "reserved10",
    "reserved11",
    "reserved12",
    "reserved13",
    "reserved14",
    "reserved15"
})
@ToString
public class EmpMailInfoItem {

    @XmlElement(name = "PRI_KEY", required = true, nillable = true)
    protected String prikey;
    @XmlElement(name = "BATCH_ID", required = true, nillable = true)
    protected String batchid;
    @XmlElement(name = "HEADER_ID", required = true, nillable = true)
    protected String headerid;
    @XmlElement(name = "EMAIL_ID", required = true, nillable = true)
    protected String emailid;
    @XmlElement(name = "PINGYIN_NAME", required = true, nillable = true)
    protected String pingyinname;
    @XmlElement(name = "MAIN_FLAG", required = true, nillable = true)
    protected String mainflag;
    @XmlElement(name = "CREATE_EMAIL_FLAG", required = true, nillable = true)
    protected String createemailflag;
    @XmlElement(name = "EMAIL_ADDRESS", required = true, nillable = true)
    protected String emailaddress;
    @XmlElement(name = "DISPLAY", required = true, nillable = true)
    protected String display;
    @XmlElement(name = "EFFECTIVE_START_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected String effectivestartdate;
    @XmlElement(name = "EFFECTIVE_END_DATE", required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected String effectiveenddate;
    @XmlElement(name = "RESERVED_1", required = true, nillable = true)
    protected String reserved1;
    @XmlElement(name = "RESERVED_2", required = true, nillable = true)
    protected String reserved2;
    @XmlElement(name = "RESERVED_3", required = true, nillable = true)
    protected String reserved3;
    @XmlElement(name = "RESERVED_4", required = true, nillable = true)
    protected String reserved4;
    @XmlElement(name = "RESERVED_5", required = true, nillable = true)
    protected String reserved5;
    @XmlElement(name = "RESERVED_6", required = true, nillable = true)
    protected String reserved6;
    @XmlElement(name = "RESERVED_7", required = true, nillable = true)
    protected String reserved7;
    @XmlElement(name = "RESERVED_8", required = true, nillable = true)
    protected String reserved8;
    @XmlElement(name = "RESERVED_9", required = true, nillable = true)
    protected String reserved9;
    @XmlElement(name = "RESERVED_10", required = true, nillable = true)
    protected String reserved10;
    @XmlElement(name = "RESERVED_11", required = true, nillable = true)
    protected String reserved11;
    @XmlElement(name = "RESERVED_12", required = true, nillable = true)
    protected String reserved12;
    @XmlElement(name = "RESERVED_13", required = true, nillable = true)
    protected String reserved13;
    @XmlElement(name = "RESERVED_14", required = true, nillable = true)
    protected String reserved14;
    @XmlElement(name = "RESERVED_15", required = true, nillable = true)
    protected String reserved15;

    /**
     * Gets the value of the prikey property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPRIKEY() {
        return prikey;
    }

    /**
     * Sets the value of the prikey property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPRIKEY(String value) {
        this.prikey = value;
    }

    /**
     * Gets the value of the batchid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBATCHID() {
        return batchid;
    }

    /**
     * Sets the value of the batchid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBATCHID(String value) {
        this.batchid = value;
    }

    /**
     * Gets the value of the headerid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHEADERID() {
        return headerid;
    }

    /**
     * Sets the value of the headerid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHEADERID(String value) {
        this.headerid = value;
    }

    /**
     * Gets the value of the emailid property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEMAILID() {
        return emailid;
    }

    /**
     * Sets the value of the emailid property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEMAILID(String value) {
        this.emailid = value;
    }

    /**
     * Gets the value of the pingyinname property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPINGYINNAME() {
        return pingyinname;
    }

    /**
     * Sets the value of the pingyinname property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPINGYINNAME(String value) {
        this.pingyinname = value;
    }

    /**
     * Gets the value of the mainflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMAINFLAG() {
        return mainflag;
    }

    /**
     * Sets the value of the mainflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMAINFLAG(String value) {
        this.mainflag = value;
    }

    /**
     * Gets the value of the createemailflag property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCREATEEMAILFLAG() {
        return createemailflag;
    }

    /**
     * Sets the value of the createemailflag property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCREATEEMAILFLAG(String value) {
        this.createemailflag = value;
    }

    /**
     * Gets the value of the emailaddress property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getEMAILADDRESS() {
        return emailaddress;
    }

    /**
     * Sets the value of the emailaddress property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEMAILADDRESS(String value) {
        this.emailaddress = value;
    }

    /**
     * Gets the value of the display property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDISPLAY() {
        return display;
    }

    /**
     * Sets the value of the display property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDISPLAY(String value) {
        this.display = value;
    }

    /**
     * Gets the value of the effectivestartdate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public String getEFFECTIVESTARTDATE() {
        return effectivestartdate;
    }

    /**
     * Sets the value of the effectivestartdate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEFFECTIVESTARTDATE(XMLGregorianCalendar value) {
        this.effectivestartdate = value.toString();
    }

    /**
     * Gets the value of the effectiveenddate property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public String getEFFECTIVEENDDATE() {
        return effectiveenddate;
    }

    /**
     * Sets the value of the effectiveenddate property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setEFFECTIVEENDDATE(XMLGregorianCalendar value) {
        this.effectiveenddate = value.toString();
    }

    /**
     * Gets the value of the reserved1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED1() {
        return reserved1;
    }

    /**
     * Sets the value of the reserved1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED1(String value) {
        this.reserved1 = value;
    }

    /**
     * Gets the value of the reserved2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED2() {
        return reserved2;
    }

    /**
     * Sets the value of the reserved2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED2(String value) {
        this.reserved2 = value;
    }

    /**
     * Gets the value of the reserved3 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED3() {
        return reserved3;
    }

    /**
     * Sets the value of the reserved3 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED3(String value) {
        this.reserved3 = value;
    }

    /**
     * Gets the value of the reserved4 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED4() {
        return reserved4;
    }

    /**
     * Sets the value of the reserved4 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED4(String value) {
        this.reserved4 = value;
    }

    /**
     * Gets the value of the reserved5 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED5() {
        return reserved5;
    }

    /**
     * Sets the value of the reserved5 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED5(String value) {
        this.reserved5 = value;
    }

    /**
     * Gets the value of the reserved6 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED6() {
        return reserved6;
    }

    /**
     * Sets the value of the reserved6 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED6(String value) {
        this.reserved6 = value;
    }

    /**
     * Gets the value of the reserved7 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED7() {
        return reserved7;
    }

    /**
     * Sets the value of the reserved7 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED7(String value) {
        this.reserved7 = value;
    }

    /**
     * Gets the value of the reserved8 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED8() {
        return reserved8;
    }

    /**
     * Sets the value of the reserved8 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED8(String value) {
        this.reserved8 = value;
    }

    /**
     * Gets the value of the reserved9 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED9() {
        return reserved9;
    }

    /**
     * Sets the value of the reserved9 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED9(String value) {
        this.reserved9 = value;
    }

    /**
     * Gets the value of the reserved10 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED10() {
        return reserved10;
    }

    /**
     * Sets the value of the reserved10 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED10(String value) {
        this.reserved10 = value;
    }

    /**
     * Gets the value of the reserved11 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED11() {
        return reserved11;
    }

    /**
     * Sets the value of the reserved11 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED11(String value) {
        this.reserved11 = value;
    }

    /**
     * Gets the value of the reserved12 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED12() {
        return reserved12;
    }

    /**
     * Sets the value of the reserved12 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED12(String value) {
        this.reserved12 = value;
    }

    /**
     * Gets the value of the reserved13 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED13() {
        return reserved13;
    }

    /**
     * Sets the value of the reserved13 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED13(String value) {
        this.reserved13 = value;
    }

    /**
     * Gets the value of the reserved14 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED14() {
        return reserved14;
    }

    /**
     * Sets the value of the reserved14 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED14(String value) {
        this.reserved14 = value;
    }

    /**
     * Gets the value of the reserved15 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRESERVED15() {
        return reserved15;
    }

    /**
     * Sets the value of the reserved15 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRESERVED15(String value) {
        this.reserved15 = value;
    }

}
