package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionMenuVO;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 人员视图对象（含主岗ID、所属组织名称）
 * 展现时使用
 *
 * <AUTHOR>
 */
@Data
@Tag(name = "人员视图对象", description = "展现/提交时使用")
public class SysStaffInfoVO implements Serializable {
    private static final long serialVersionUID = 2L;

    @Schema(name = "人员ID", hidden = true)
    private String staffId;

    @Schema(name="登录名")
    private String loginName;

    @Schema(name="姓名")
    private String staffName;

    @Schema(name="主岗组织ID")
    private String orgId;

    @Schema(name="当前用户所在的公司ID")
    private String companyId;

    @Schema(name="当前用户所在的地市公司ID")
    private String cityCompanyId;

    @Schema(name="人员编码")
    private String staffCode;

    @Schema(name="状态:valid正常,invalid禁用")
    private String staffStatus;

    @Schema(name="身份证号")
    private String employeeCode;

    @Schema(name="性别:male-男,female-女")
    private String sex;

    @Schema(name="人员类别:own内部人员,partner合作伙伴")
    private String staffKind;

    @Schema(name="排序")
    private Integer staffSort;

    @Schema(name="生日")
    private transient LocalDate birthday;

    @Schema(name="入职日期")
    private transient LocalDate hireDate;

    @Schema(name="邮编")
    private String postcode;

    @Schema(name="通信地址")
    private String address;

    @Schema(name="手机号码")
    private String cellphone;

    @Schema(name="座机号码")
    private String telephone;

    @Schema(name="电子邮件")
    private String email;

    @Schema(name = "主岗ID", hidden = true)
    private String staffOrgId;

    @Schema(name="主岗组织名称")
    private String orgName;

    @Schema(name="主岗组织行政区划层级")
    private String orgLevel;

    @Schema(name="岗位集合")
    private List<SysStaffOrgVO> staffOrgs;

    @Schema(name="角色集合")
    private List<SysRoleVO> roles;

    @Schema(name="权限集合")
    private List<SysPermissionVO> permissions;

    @Schema(name="当前用户的权限字集合")
    private List<String> permissionsStr;

    @Schema(name="当前用户可见的菜单集合")
    private List<SysPermissionMenuVO> menus;

    @Schema(name="岗位状态")
    private String staffOrgStatus;

    @Schema(name = "密码")
    private String passwd;

    @Schema(name = "备用字段a：冻结原因")
    private String attra;

    @Schema(name = "是否需要修改密码  0：否  1：是")
    private Integer userPwdFlag;

    @Schema(name = "租户id")
    private String tenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name="租户管理员id")
    private String tenantAdminId;

    @Schema(name="显示的登录名称")
    private String displayName;

    @Schema(name="租户有效期")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date effectiveDate;

    @Schema(name="租户是否删除")
    private String tenantDeleteFlag;

    @Schema(name="租户loginName")
    private String tenantLoginName;
}
