package cn.chinaunicom.sdsi.cloud.sync.vo;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 组织信息同步实体
 * <AUTHOR>
 * @date 2021-3-5
 * @version V1.0
 */
@Data
@Accessors(chain = true)
@Tag(name="组织信息同步实体", description="组织信息同步实体")
public class SBEIPEIPImportOrgInfoSrvRequestBody {
    @Schema(name = "记录唯一关键字")
    private String PRI_KEY;
    @Schema(name = "批次号")
    private String BATCH_ID;
    @Schema(name = "业务类型")
    private BigDecimal COMMANDTYPE;
    @Schema(name = "主数据公司或部门编码")
    private String MDM_ORG_CODE;
    @Schema(name = "HR公司或部门编码")
    private String HR_ORG_CODE;
    @Schema(name = "公司或部门名称")
    private String ORG_NAME;
    @Schema(name = "公司或部门显示名称")
    private String ORG_NAME_DISPLAY;
    @Schema(name = "组织类型")
    private String CUNC_ORG_CLASS;
    @Schema(name = "组织范围")
    private String CUNC_ORG_SCOPE;
    @Schema(name = "上级组织编号")
    private String PARENT_ORG_CODE;
    @Schema(name = "组织层级编码")
    private String OUCLASS_CODE;
    @Schema(name = "组织层级名称")
    private String OUCLASS_NAME;
    @Schema(name = "上级业务指导部门编码")
    private String PARENT_TRAIN_CODE;
    @Schema(name = "电子邮件")
    private String ORG_EMAIL;
    @Schema(name = "公司段")
    private String ORG_CODE;
    @Schema(name = "成本中心代码")
    private String FLEX_VALUE;
    @Schema(name = "专业段代码")
    private String SPECIALITY_CODE;
    @Schema(name = "组织排序号")
    private String ORG_SORT;
    @Schema(name = "生效日期")
    private String ORG_START_DATE;
    @Schema(name = "失效日期")
    private String ORG_END_DATE;
    @Schema(name = "组织管理者编号")
    private String MANAGER_CODE;
    @Schema(name = "公司或部门邮政编码")
    private String POSTALCODE;
    @Schema(name = "公司或部门办公电话")
    private String TELEPHONENUMBER;
    @Schema(name = "公司或部门传真号码")
    private String FACSIMILETELEPHONENUMBER;
    @Schema(name = "所属省份简码")
    private String SITE;
    @Schema(name = "所属组织编号")
    private String BIG_ORG_CODE;
    @Schema(name = "是否在通讯录中显示")
    private String HZ_YES_NO;
    @Schema(name = "国家（地区）")
    private String COUNTRY;
    @Schema(name = "地市")
    private String CITY;
    @Schema(name = "地址行")
    private String ADDRESS;
    @Schema(name = "部门/渠道级别")
    private String RESERVED_1;
    @Schema(name = "有效标记")
    private String RESERVED_2;
    @Schema(name = "部门/组织管理者名称")
    private String RESERVED_3;
    @Schema(name = "归属区县名称")
    private String RESERVED_4;
    @Schema(name = "归属区域名称")
    private String RESERVED_5;
    @Schema(name = "所属组织名称")
    private String RESERVED_6;
    @Schema(name = "MDM编码")
    private String RESERVED_7;
    @Schema(name = "预留字段8")
    private String RESERVED_8;
    @Schema(name = "预留字段9")
    private String RESERVED_9;
    @Schema(name = "预留字段10")
    private String RESERVED_10;
    @Schema(name = "预留字段11")
    private String RESERVED_11;
    @Schema(name = "预留字段12")
    private String RESERVED_12;
    @Schema(name = "预留字段13")
    private String RESERVED_13;
    @Schema(name = "预留字段14")
    private String RESERVED_14;
    @Schema(name = "预留字段15")
    private String RESERVED_15;
    @Schema(name = "制单人员工工号")
    private String EMPLOYEE_NUMBER;

}
