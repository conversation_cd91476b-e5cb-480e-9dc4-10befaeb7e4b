package cn.chinaunicom.sdsi.cloud.system.feign.fallback;

import cn.chinaunicom.sdsi.cloud.system.feign.CmsFeignClient;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import org.springframework.cloud.openfeign.FallbackFactory;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 对于 {@link CmsFeignClient} 接口的异常处理类
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2022/3/15
 * -
 * @description 内容描述
 */
@Slf4j
@Component
public class CmsFeignClientFallback implements FallbackFactory<CmsFeignClient> {
    @Override
    public CmsFeignClient create(Throwable throwable) {
        return new CmsFeignClient() {
            @Override
            public BaseResponse<String> initTenantPrograma(String tenantId) {
                log.error("SysProgramaTypeFeignClient initTenantPrograma feign异常了");
                return null;
            }

            @Override
            public BaseResponse addEsDoc(Map<String, Object> map) {
                log.error("{} SysProgramaTypeFeignClientFallback addEsDoc feign异常了");
                log.error(throwable.getMessage());
                return null;
            }

            @Override
            public BaseResponse getEsDocById(String id) {
                log.error("{} SysProgramaTypeFeignClientFallback termQuery feign异常了");
                log.error(throwable.getMessage());
                return null;
            }

            @Override
            public BaseResponse uploadOSSFile() {
                log.error("{} SysProgramaTypeFeignClientFallback uploadOSSFile feign异常了");
                log.error(throwable.getMessage());
                return null;
            }
        };
    }

}
