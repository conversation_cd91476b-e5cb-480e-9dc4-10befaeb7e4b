package cn.chinaunicom.sdsi.cloud.system.feign.fallback;

import cn.chinaunicom.sdsi.cloud.system.feign.AppCenterFeignClient;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2022/4/13 15:33
 */
@Slf4j
@Component
public class AppCenterFeignClientFallback implements FallbackFactory<AppCenterFeignClient> {
    @Override
    public AppCenterFeignClient create(Throwable throwable) {
        return new AppCenterFeignClient() {
            @Override
            public BaseResponse<Integer> appTotal() {
                log.error(throwable.toString());
                log.error("AppCenterFeignClient appTotal feign异常了");
                return null;
            }
        };
    }
}
