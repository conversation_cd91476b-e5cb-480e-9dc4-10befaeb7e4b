package cn.chinaunicom.sdsi.cloud.system.client.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 配置客户端功能 - 客户端配置数据库映射对象
 *
 *
 * <AUTHOR>
 * @since 2022-03-16
 */

@Tag(name="配置客户端功能 - 客户端配置数据库映射对象", description="配置客户端")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_public_config")
public class SysClientPO extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "主键")
    @TableId(value = "config_id")
    private String configId;

    @Schema(name = "配置名称")
    private String configName;

    @Schema(name = "配置编码")
    private String configCode;

    @Schema(name = "配置类型")
    private String configType;

    @Schema(name = "配置值")
    private String configValue;

    @Schema(name = "客户端类型")
    private String clientType;

    @Schema(name = "配置描述")
    private String configDescribe;


    @Schema(name = "时间戳")
    private Long timeStamp;


    @Schema(name = "租户ID")
    private String tenantId;
}

