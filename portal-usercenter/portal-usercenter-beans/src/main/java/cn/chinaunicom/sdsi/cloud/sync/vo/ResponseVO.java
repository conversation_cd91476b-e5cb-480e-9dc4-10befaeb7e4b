package cn.chinaunicom.sdsi.cloud.sync.vo;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 回应实体类
 * @file cn.chinaunicom.mall.sync.vo.ResponseVO
 * @description 回应实体类
 * @author: yaowang
 * @date: 2021-1-29
 * @version: V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Data
@Accessors(chain = true)
@Tag(name="回应实体类", description="回应实体类")
public class ResponseVO implements Serializable {

    private static final long serialVersionUID = 1L;
//
//    @Schema(name = "应答标识")
//    private String flag;
//
//    @Schema(name = "应答消息")
//    private String message;

    private String SERVICE_MESSAGE;
    private String SERVICE_FLAG="1";
    /**
     * 系统编码
     */
    private String SYS_CODE="SdunStaffHome";
    private String PRI_KEY;

}
