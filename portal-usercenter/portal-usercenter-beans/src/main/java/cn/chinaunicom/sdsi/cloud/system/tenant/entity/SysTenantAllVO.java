package cn.chinaunicom.sdsi.cloud.system.tenant.entity;

import cn.chinaunicom.sdsi.core.interfaces.Add;
import cn.chinaunicom.sdsi.core.interfaces.Edit;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 用于页面展示数据
 *
 * <AUTHOR>
 */
@Data
@Tag(name="租户展示对象")
public class SysTenantAllVO implements Serializable {

    @Schema(hidden = true)
    private static final long serialVersionUID = 2L;

    @Schema(name = "租户ID")
    @NotNull(message = "租户ID不能为空", groups = {Edit.class})
    private String tenantId;

    @Schema(name = "租户名称")
    @Length(max = 128)
    @NotBlank(message = "租户名称不能为空", groups = {Add.class})
    private String tenantName;

    @Schema(name = "租户登录名")
    @Length(max = 20)
    @NotBlank(message = "租户登录名不能为空", groups = {Add.class})
    private String tenantLoginName;

    @Schema(name = "租户管理员ID")
    private String tenantAdminId;

    @Schema(name = "租户创建人")
    @Length(max = 36)
    private String tenantCreateBy;

    @Schema(name = "租户域名")
    @Length(max = 128)
    private String tenantDomain;

    @Schema(name = "业务ID")
    @Length(max = 128)
    private String businessId;

    @Schema(name = "最大用户数")
    private Integer maxStaff;

    @Schema(name = "有效截止时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date effectiveDate;

    @Schema(name = "状态 - valid-正常 invalid-冻结 reject-审批拒绝")
    @Length(max = 16)
    private String tenantStatus;

    @Schema(name = "组织ID")
    @NotNull(message = "组织id不能为空", groups = {Edit.class})
    private String orgId;

    @Schema(name = "组织编码")
    @Length(max = 64)
    @NotBlank(message = "组织编码不能为空", groups = {Add.class})
    private String code;

    @Schema(name = "组织名称")
    @NotBlank(message = "组织名称不能为空", groups = {Add.class})
    @Length(max = 255)
    private String orgName;

    @Schema(name = "组织简称")
    @Length(max = 64)
    private String shortName;

    @Schema(name = "组织全称")
    @Length(max = 255)
    private String fullName;

    @Schema(name = "组织父节点ID")
    @NotNull(message = "上级组织id不能为空", groups = {Add.class})
    private String parentId;

    @Schema(name = "组织父节点名称")
    private String parentName;

    @Schema(name = "组织父节点编码")
    @Length(max = 64)
    private String parentCode;

    @Schema(name = "组织类型-编码", allowableValues = "org,street,dept,area,grid,other")
    @Length(max = 8)
    @Pattern(regexp = "org|dept|street|area|grid|other", flags = {Pattern.Flag.CASE_INSENSITIVE},
            message = "组织类型只能为下列值：org,dept,street,area,grid,other")
    @NotNull(message = "组织类型不能为空", groups = {Add.class})
    private String orgType;

    @Schema(name = "组织状态-编码", allowableValues = "valid,invalid")
    @Length(max = 16)
    @Pattern(regexp = "valid|invalid", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "组织状态只能为 valid 或 invalid")
    private String orgStatus;

    @Schema(name = "组织种类-编码", allowableValues = "own,partner")
    @Length(max = 16)
    @Pattern(regexp = "own|partner", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "组织种类只能为 own 或 partner")
    @NotNull(message = "组织种类不能为空", groups = {Add.class})
    private String kind;

    @Schema(name = "排序")
    @Max(value = 1000000, message = "组织排序不得超过1000000")
    private Integer orgSort;

    @Schema(name = "组织机构编码")
    private String staffOid;

    @Schema(name = "人员顺序")
    private Integer staffSort;

    @Schema(name = "备用字段a")
    private Integer attra;

    @Schema(name = "是否有下级节点")
    private Boolean isParent;

    @Schema(name = "人员ID",hidden = true)
    private String staffId;

    @NotBlank(message = "登录密码必须输入",groups = {Add.class})
    @Schema(name="密码")
    private String passwd;

    @Schema(name="姓名")
    @NotBlank(message = "姓名不能为空", groups = {Add.class})
    @Length(max = 30)
    private String staffName;

    @Schema(name="人员编码")
    @Length(max = 240)
    private String staffCode;

    @Schema(name="状态:valid正常,invalid禁用")
    @Pattern(regexp = "valid|invalid", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "人员状态只能为 valid 或 invalid")
    @Length(max = 16)
    private String staffStatus;

    @Schema(name="身份证号")
    @Length(max = 64)
    private String employeeCode;

    @Schema(name="性别:M男,F女")
    @Length(max = 1)
    @Pattern(regexp = "M|W", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "性别只能为M或W")
    private String sex;

    @Schema(name="人员类别:own内部人员,partner合作伙伴")
    @Pattern(regexp = "own|partner", flags = {Pattern.Flag.CASE_INSENSITIVE}, message = "人员类型只能为 own 或 partner")
    private String staffKind;

    @Schema(name="生日")
    private Date birthday;

    @Schema(name="入职日期")
    private Date hireDate;

    @Schema(name="邮编")
    @Length(max = 32)
    private String postcode;

    @Schema(name="邮箱")
    @Length(max = 255)
    private String address;

    @Schema(name="手机号码")
    @Length(max = 64)
    private String cellphone;

    @Schema(name="座机号码")
    @Length(max = 64)
    private String telephone;

    @Schema(name="电子邮件")
    @Email
    @Length(max = 128)
    private String email;

    @Schema(name = "主岗ID",hidden = true)
    private String staffOrgId;

    @Schema(name="用户身份")
    private String userType;

    @Schema(name = "前台位置")
    private String tenantHtmlPath;
    @Schema(name = "管理员登录名")
    private String displayName;
    @Schema(name = "用户数")
    private String sumCust;
    @Schema(name = "登陆账号")
    private String loginName;
    @Schema(name = "备注")
    private String description;
    @Schema(name = "坐席数量")
    private String agentNum;

    @Schema(name = "保存类型 - add 新增 settled 入驻")
    private String tenantSaveType;

}
