package cn.chinaunicom.sdsi.cloud.system.staff.vo;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * transfer组件元素对象，角色分配时使用
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/5/19
 */
@Data
@Tag(name = "transfer组件元素对象", description = "展现/提交时使用")
public class SysTransferVO {
    @Schema(name="主键")
    private String key;
    @Schema(name="标题")
    private String title;
    @Schema(name="描述")
    private String description;
    @Schema(name="是否已选")
    private Boolean chosen;
}
