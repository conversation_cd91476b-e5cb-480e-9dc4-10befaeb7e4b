package cn.chinaunicom.sdsi.cloud.system.org.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Pattern;

/**
 * 组织视图层对象 - 查询条件
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Tag(name = "组织查询条件视图对象", description = "传递查询条件时使用")
public class SysOrgQueryVO extends BaseQueryVO {
    @Schema(hidden = true)
    private static final long serialVersionUID = 1L;

    @Schema(name = "组织ID")
    private String orgId;

    @Schema(name = "组织编码")
    private String code;

    @Schema(name = "组织名称")
    private String orgName;

    @Schema(name = "组织父节点ID")
    private String parentId;

    @Schema(name = "组织父节点编码")
    private String parentCode;

    @Schema(name = "组织类型")
    @Pattern(regexp = "org|dept|street|area|grid|other|all",
            flags = {Pattern.Flag.CASE_INSENSITIVE},
            message = "组织类型只能为下列值之一：org,dept,street,area,grid,other,all")
    private String orgType;

    /**
     * 电话
     */
    private String phone;

    @Schema(name = "租户Id")
    private String tenantId;

}