package cn.chinaunicom.sdsi.cloud.system.org.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 组织机构表 持久层对象
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@TableName("sys_org")
@KeySequence(value = "SYS_ORG_SEQ", clazz = String.class)
public class SysOrgPO extends BaseEntity {

    private static final long serialVersionUID = 2L;

    /**
     * 主键
     */
    @TableId(value = "org_id")
    private String orgId;

    /**
     * 编码
     */
    private String code;

    /**
     * 门户接口编码（未使用）
     */
    private String hrCode;

    /**
     * 公司或部门名称
     */
    private String orgName;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 包含上级组织相关的全称
     */
    private String fullName;

    /**
     * 直属上级ID
     */
    private String parentId;

    /**
     * 上级编码
     */
    private String parentCode;

    /**
     * 组织类别：org-组织，dept-部门，group-用户组
     */
    private String orgType;

    /**
     * 组织范围（未使用）
     */
    private String orgScope;

    /**
     * 级别（未使用）
     */
    private Integer grade;

    /**
     * 状态，valid-有效，invalid-无效
     */
    private String orgStatus;

    /**
     * 组织类型，own、partner合作伙伴；
     */
    private String kind;

    /**
     * 邮箱（未使用）
     */
    private String email;

    /**
     * 省份编码（未使用）
     */
    private String provinceCode;

    /**
     * 排序
     */
    private Integer orgSort;

    /**
     * 邮编
     */
    private String postcode;

    /**
     * 电话
     */
    private String phone;

    /**
     * 传真
     */
    private String fax;

    /**
     * 生效日期（未使用）
     */
    private transient LocalDateTime orgStartDate;

    /**
     * 失效日期（未使用）
     */
    private transient LocalDateTime orgEndDate;

    /**
     * 备用字段a
     */
    private String attra;

    /**
     * 备用字段a
     */
    private String attrb;

    /**
     * 备用字段a
     */
    private String attrc;

    /**
     * 行政区划层级 0:省 1:市 2:区县 3:街道
     */
    private String orgLevel;
}
