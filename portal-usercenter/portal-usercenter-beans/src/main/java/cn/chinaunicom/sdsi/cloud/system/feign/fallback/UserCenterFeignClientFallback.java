package cn.chinaunicom.sdsi.cloud.system.feign.fallback;

import cn.chinaunicom.sdsi.cloud.system.constant.UserCenterConstant;
import cn.chinaunicom.sdsi.cloud.system.feign.UserCenterFeignClient;
import cn.chinaunicom.sdsi.cloud.system.log.entity.SysLogPO;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffInfoVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffVO;
import cn.chinaunicom.sdsi.cloud.system.staffext.entity.SysStaffExtPO;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@Component
public class UserCenterFeignClientFallback implements FallbackFactory<UserCenterFeignClient> {



    @Override
    public UserCenterFeignClient create(Throwable throwable) {
        return new UserCenterFeignClient() {
            @Override
            public BaseResponse<SysStaffInfoVO> findUserInfo(String username) {
                log.error("UserCenterFeignClientFallback findUserInfo feign异常了");
                log.error(throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            /**
             * 登录用户根据用户名获取用户信息、权限字信息、角色信息
             *
             * @param  sysStaffVO
             * @return List<FileStatVO>
             */
            @Override
            public SysStaffInfoVO info( SysStaffVO sysStaffVO) {
                log.error("{} UserCenterFeignClientFallback info feign异常了",sysStaffVO.toString());
                log.error(throwable.getMessage());
                return null;
            }

            @Override
            public BaseResponse<List<SysStaffInfoVO>>  findInfoListByCellPhone(String cellPhone) {
                log.error("{} UserCenterFeignClientFallback info feign异常了",cellPhone);
                log.error(throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<String> getStaffOrgId(String staffId, String orgId) {
                log.error("UserCenterFeignClientFallback getStaffOrgId feign异常了");
                log.error(throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<SysStaffInfoVO> getUserVoByMobile(String username) {
                log.error("UserCenterFeignClientFallback getUserVoByMobile feign异常了");
                log.error(throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<SysStaffInfoVO> getUserVoByMobileAndTenant(String username, String tenantId) {
                log.error("UserCenterFeignClientFallback getUserVoByMobileAndTenant feign异常了");
                log.error(throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            /**
             * 查询用户列表信息
             * @param sysStaffOrgQuery 参数实体
             * @return BaseResponse<List<EshopPortaluserVO>>
             * <AUTHOR>
             * @date 2021-3-1
             */
            @Override
            public BaseResponse<List<SysStaffOrgVO>> getStaffList(
                SysStaffOrgQueryVO sysStaffOrgQuery) {
                log.info("UserCenterFeignClientFallback getStaffList feign异常了:参数--{}", sysStaffOrgQuery);
                BaseResponse baseResponse = new BaseResponse();
                baseResponse.setSuccess(Boolean.FALSE);
                baseResponse.setCode(ResponseEnum.FAIL.getCode());
                baseResponse.setMessage(ResponseEnum.FAIL.getMsg());
                baseResponse.setData(UserCenterConstant.FEIGN_FLAG_FALSE);
                return baseResponse;
            }

            @Override
            public BaseResponse<List<SysRoleVO>> getRoleByInfo(SysStaffOrgQueryVO vo) {
                log.error("UserCenterFeignClientFallback getRoleByInfo feign异常了");
                log.error("getRoleByInfo feign 调用失败原因{}",throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<List<SysPermissionVO>> getMenuByRoleId(String roleId) {
                log.error("UserCenterFeignClientFallback getMenuByRoleId feign异常了");
                log.error("getMenuByRoleId feign 调用失败原因{}",throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<String> saveLoginRecord(SysLogPO sysLogPO) {
                log.error("{} UserCenterFeignClientFallback saveLoginRecord feign异常了", sysLogPO.getAccount());
                log.error(throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<SysStaffExtVO> findInfoVoByDingUserId(String userId) {
                log.error("UserCenterFeignClientFallback findInfoVoByDingUserId feign异常了");
                log.error("findInfoVoByDingUserId feign 调用失败原因{}",throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());            }

            @Override
            public BaseResponse<Integer> updateDingUserId(SysStaffExtPO sysStaffExtPO) {
                log.error("UserCenterFeignClientFallback updateDingUserId feign异常了");
                log.error("updateDingUserId feign 调用失败原因{}",throwable.getMessage());
                return new BaseResponse(ResponseEnum.SYSTEM_SERVICE_ERROR.getCode(), throwable.getMessage());
            }

            @Override
            public BaseResponse<SysStaffInfoVO> findByUnionId(String unionid) {
                log.error("UserCenterFeignClientFallback findByUnionId feign异常了");
                log.error("findByUnionId feign 调用失败原因{}",throwable.getMessage());
                return null;
            }

            @Override
            public BaseResponse<List<String>> selectDingUserIdByQueryVO(@Valid SysStaffOrgQueryVO queryVO) {
                return null;
            }

            @Override
            public BaseResponse<List<SysStaffExtVO>> selectTenantDingUsers(String tenantId) {
                log.error("UserCenterFeignClientFallback selectTenantDingUsers feign异常了");
                log.error("selectTenantDingUsers feign 调用失败原因{}",throwable.getMessage());
                return null;
            }

        };
    }
}
