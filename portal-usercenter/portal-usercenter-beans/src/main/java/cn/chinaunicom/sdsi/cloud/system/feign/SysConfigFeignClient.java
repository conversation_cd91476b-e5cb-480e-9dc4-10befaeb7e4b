package cn.chinaunicom.sdsi.cloud.system.feign;

import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigQueryVO;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigVO;
import cn.chinaunicom.sdsi.cloud.system.feign.fallback.SysConfigFeignClientFallBack;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import java.util.List;


/**
 * 提供统一业务feign调用服务
 */
@FeignClient(value = "${cloud.service.cloudauth.serviceId:cloud-usercenter}",fallback = SysConfigFeignClientFallBack.class)
public interface SysConfigFeignClient {

    @Operation(summary = "获取添加公告状态")
    @PostMapping("/configs/selectConfigListByCode")
    List<SysConfigVO> selectConfigListByCode(@RequestBody SysConfigQueryVO sysConfigQueryVO);
}
