package cn.chinaunicom.sdsi.cloud.system.staffext.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-14
 * @version: V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Data
@Tag(name="SysStaffExtVO对象", description="SysStaffExtVO对象")
public class SysStaffExtVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "主键")
    @TableId(value = "staff_ext_id")
    private String staffExtId;

    @Schema(name = "用户侧头像 ")
    private String agentFace;

    @Schema(name = "用户侧名称 ")
    private String agentNickname;

    @Schema(name = "最大接待量")
    private Integer chatCount;

    @Schema(name = " 技能组，多选，逗号分隔  ")
    private String skillId;

    @Schema(name = " staffId ")
    private String staffId;

    @Schema(name = "用户名称")
    private String staffName;

    @Schema(name = "用户code")
    private String staffCode;

    @Schema(name = "租户id")
    private String tenantId;

    @Schema(name = "租户名称")
    private String tenantName;

    @Schema(name = "钉钉用户主键")
    private String dingUserId;

    @Schema(name = "登录名称")
    private String loginName;

    @Schema(name = "手机号码")
    private String cellphone;

    @Schema(name = "用户微信全局唯一标识")
    private String unionId;

    @Schema(name = "移动端登录设备唯一标识")
    private String registrationId;

    @Schema(name = "微信公众号用户openId")
    private String wechatOpenId;
}


