<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

    <!-- 系统入驻功能 - 系统入驻信息DAO层 -->
    <mapper namespace="cn.chinaunicom.sdsi.cloud.system.oauthClient.dao.OauthClientInfoMapper">

    <!-- 全部字段 -->
    <sql id="ALL_FIELD">
        client_id,
        tenant_id,
        client_app,
        client_key,
        client_secret,
        client_pass,
        client_unit,
        client_contact,
        client_tel,
        client_email,
        client_url,
        client_redirect,
        client_status,
        client_expiration,
        client_description,
        client_token_validity,
        client_refresh_validity,
        client_remark,
        create_by,
        create_date,
        update_by,
        update_date,
        delete_flag,
        versions,
        attra,
        attrb,
        attrc,
        attrd,
        attre
    </sql>

    <!-- 根据分页信息查询系统入驻信息列表 -->
    <select id="findByPageOauthClientInfo" resultType="cn.chinaunicom.sdsi.cloud.system.oauthClient.vo.OauthClientInfoVO">
        SELECT
        <include refid="ALL_FIELD"/>
        FROM oauth_client_info
        WHERE delete_flag = 'normal'
        <if test="query.clientApp != null and query.clientApp != ''">
            AND client_app LIKE #{query.clientApp}
        </if>
        <if test="query.clientUnit != null and query.clientUnit != ''">
            AND client_unit LIKE #{query.clientUnit}
        </if>
        <if test="query.clientContact != null and query.clientContact != ''">
            AND client_contact LIKE #{query.clientContact}
        </if>
        <if test="query.clientTel != null and query.clientTel != ''">
            AND client_tel LIKE #{query.clientTel}
        </if>
    </select>

    <!-- 物理删除系统入驻信息 -->
    <delete id="deleteOauthClientInfo">
        DELETE
        FROM oauth_client_info
        WHERE client_id = #{clientId}
    </delete>
</mapper>