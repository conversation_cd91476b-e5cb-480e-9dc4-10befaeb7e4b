<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.cloud.system.role.dao.SysRolePermissionMapper">
    <!-- 根据角色ID查询权限树 -->
    <select id="selectPermissionTreeByRoleId" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionTreeVO"
            parameterType="java.lang.String">
        SELECT
            sp.permission_id,
            sp.permission_name,
            sp.parent_id,
            case when count(sps.permission_id) > 0 then 1 else 0 end AS "isParent"
        FROM
            sys_role_permission srp
                JOIN sys_permission sp ON srp.permission_id = sp.permission_id
                AND sp.delete_flag = 'normal'
                AND sp.permission_status = 'valid'
                LEFT JOIN sys_permission sps ON sp.permission_id = sps.parent_id
                AND sps.delete_flag = 'normal'
                AND sps.permission_status = 'valid'
        WHERE
            srp.role_id = #{roleId}
        GROUP BY
            sp.permission_id,
            sp.permission_name,
            sp.parent_id
    </select>

    <!-- 根据时间同步角色权限 -->
    <select id="selectRolePermissionList" parameterType="cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO"
            resultType="cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePermissionPO">
        select srp.*
        from sys_role_permission srp
        left join sys_role sr on srp.role_id = sr.role_id
        where sr.delete_flag = 'normal'
        and sr.tenant_id = #{tenantId}
        <if test="startTime != '' and startTime != null">
            and  date_format(srp.create_date, '%Y-%m-%d' ) >= #{startTime}
        </if>
        <if test="endTime != '' and endTime != null">
            and  date_format(srp.create_date, '%Y-%m-%d' ) &lt;= #{endTime}
        </if>
        order by srp.create_date desc
    </select>

    <!-- 根据角色id查询角色权限 -->
    <select id="selectRolePermListByRoleId" parameterType="java.lang.String"
            resultType="cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePermissionPO">
        select srp.*
        from sys_role_permission srp
        where
         srp.role_id = #{roleId}
    </select>
</mapper>
