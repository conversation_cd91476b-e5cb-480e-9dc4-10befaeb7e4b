<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.cloud.system.tenantSubsystem.dao.SysTenantSubsystemMapper">
    <!-- 根据code查询租户信息 -->
    <select id="selectBySubsystemCode"
            resultType="cn.chinaunicom.sdsi.cloud.system.tenantSubsystem.vo.SysTenantSubsystemVO"
            parameterType="java.lang.String">
        SELECT
            st.tenant_id,
            st.tenant_name,
            st.tenant_login_name,
            ste.tenant_extend_id,
            ste.company_name,
            ste.contact_person,
            ste.contact_telephone,
            ste.company_max_count,
            ste.description,
            ste.agent_num,
            oci.client_key subsystemCode
        FROM
            oauth_client_info oci
            LEFT JOIN app_center ac ON oci.client_id = ac.client_id
            LEFT JOIN app_center_tenant act ON act.app_center_id = ac.id
            LEFT JOIN sys_tenant st ON act.tenant_id = st.tenant_id
            AND st.delete_flag = 'normal'
            LEFT JOIN sys_tenant_extend ste ON st.tenant_id = ste.tenant_id
            AND ste.delete_flag = 'normal'
        WHERE
            oci.delete_flag = 'normal'
            AND oci.client_status = 'valid'
            AND ac.is_delete = 0
            AND ac.status = 1
            AND st.tenant_id is not null
            AND oci.client_key = #{subsystemCode}
    </select>
</mapper>
