<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.cloud.system.permission.dao.SysPermissionMapper">
    <sql id="Base_Column_List">
        sys_permission.permission_id,
        sys_permission.code,
        sys_permission.permission_name,
        sys_permission.uri,
        sys_permission.permission_type,
        sys_permission.open_type,
        sys_permission.parent_id,
        sys_permission.permission_status,
        sys_permission.icon,
        sys_permission.check_code,
        sys_permission.description,
        sys_permission.permission_sort,
        sys_permission.permission_scope,
        sys_permission.permission_visible,
        sys_permission.permission_frame,
        sys_permission.terminal,
        sys_permission.tenant_id
    </sql>
    <sql id="Base_Column_Lists">
        sp.permission_id,
        sp.code,
        sp.permission_name,
        sp.uri,
        sp.permission_type,
        sp.open_type,
        sp.parent_id,
        sp.permission_status,
        sp.icon,
        sp.check_code,
        sp.description,
        sp.permission_sort,
        sp.permission_scope,
        sp.permission_visible,
        sp.permission_frame,
        sp.terminal,
        sp.tenant_id
    </sql>
    <sql id="OrderSql">
        order by
        case when sys_permission.permission_sort is null then 1 else 0 end,
        sys_permission.permission_sort asc,
        sys_permission.permission_id
    </sql>
    <sql id="OrderSqls">
        order by
          case when sp.permission_sort is null then 1 else 0 end,
          sp.permission_sort asc,
          sp.permission_id
    </sql>
    <select id="findListByStaffOrgId" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO">
        select
        <include refid="Base_Column_List"/>
        from sys_permission
        where delete_flag = 'normal' and (code != '1' or code is null )
        <if test="staffOrgId != null">
            <!-- 当包含岗位来查询时，查询此岗位对应的‘有效’的权限 -->
            and permission_status='valid'
            <!-- 从关联表中查询和岗位id相关的权限id -->
            and permission_id in (select srp.permission_id from
            sys_role_permission srp, sys_staff_role ssr
            where
            srp.role_id=ssr.role_id
            and ssr.staff_org_id =
            #{staffOrgId,jdbcType=BIGINT})
        </if>
        <include refid="OrderSql"></include>
    </select>
    <select id="findListByPermission" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO"
        parameterType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO">
        select
        <include refid="Base_Column_List"/>
        ,st.tenant_name
        from sys_permission
        left join
        sys_tenant st on st.tenant_id = sys_permission.tenant_id and st.delete_flag = 'normal' and st.tenant_status = 'valid'
        where sys_permission.delete_flag = 'normal'
        <if test="vo.staffOrgId != null">
            <!-- 当包含岗位来查询时，查询此岗位对应的‘有效’的权限 -->
            and permission_status='valid'
            <!-- 从关联表中查询和岗位id相关的权限id -->
            and permission_id in (select srp.permission_id from
            sys_role_permission srp, sys_staff_role ssr
            where
            srp.role_id=ssr.role_id
            and ssr.staff_org_id =
            #{vo.staffOrgId,jdbcType=BIGINT})
        </if>
        <if test="vo.permissionScope != null and vo.permissionScope != ''">
            and sys_permission.permission_scope =
            #{vo.permissionScope,jdbcType=VARCHAR}
        </if>
        <if test="vo.permissionName != null and vo.permissionName != ''">
            and permission_name like
            concat(concat('%', #{vo.permissionName}), '%')
        </if>
        <if test="vo.permissionStatus != null and vo.permissionStatus != ''">
            and permission_status = #{vo.permissionStatus}
        </if>
        <if test="vo.tenantId != null and vo.tenantId != ''">
            AND sys_permission.tenant_id = #{vo.tenantId}
        </if>
        <include refid="OrderSql" />
    </select>
    <select id="findMenuByStaffOrgId"
            resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionMenuVO">
        select
        sys_permission.permission_id "menuId",
        sys_permission.permission_name "name",
        sys_permission.uri "path",
        sys_permission.parent_id,
        sys_permission.permission_type "menuType",
        sys_permission.open_type "openType",
        sys_permission.icon,
        sys_permission.permission_sort "displayOrder"
        ,(case when count( sps.permission_id ) > 0 then 1 else 0 end) AS is_parent
        from sys_permission
        left join sys_permission sps
        on sys_permission.permission_id = sps.parent_id and sps.delete_flag='normal' and sps.permission_status='valid'
        where
        sys_permission.delete_flag = 'normal'
        and sys_permission.permission_status='valid'
        and sys_permission.permission_type='menu'
        <if test="staffOrgId != null">
            and sys_permission.permission_id in
            (select
            srp.permission_id
            from
            sys_role_permission srp, sys_staff_role ssr, sys_role sr
            where
            srp.role_id=ssr.role_id and ssr.role_id = sr.role_id
            and sr.role_status='valid' and sr.delete_flag='normal'
            and ssr.staff_org_id = #{staffOrgId,jdbcType=BIGINT})
        </if>
        <if test="parentId != null">
            and sys_permission.parent_id = #{parentId,jdbcType=BIGINT}
        </if>
        group by
        <include refid="Base_Column_List"/>
        <include refid="OrderSql" />
    </select>
    <select id="findTreeChildren"
            resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionTreeVO"
            parameterType="java.lang.String">
        select
        permission_id, permission_name, parent_id, code,
        CASE WHEN (SELECT count(sp.permission_id) FROM sys_permission sp WHERE sp.parent_id
        = sys_permission.permission_id and sp.delete_flag='normal') >0 THEN 1 ELSE 0 END is_parent
        from
        sys_permission
        where sys_permission.parent_id = #{permissionId,jdbcType=BIGINT} and sys_permission.delete_flag='normal'
        <include refid="OrderSql"></include>
    </select>
    <select id="findListByRoleId" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO">
        select
        <include refid="Base_Column_List"/>
        from sys_permission
        where delete_flag = 'normal' and
        permission_status='valid'
        <if test="roleId != null">
            and permission_id in (select srp.permission_id from
            sys_role_permission srp
            where srp.role_id = #{roleId,jdbcType=BIGINT})
        </if>
        <include refid="OrderSql"></include>
    </select>
    <select id="findPermissionById" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO">
        select
        (select parent.permission_name from sys_permission parent where parent.permission_id=sys_permission.parent_id)
        "parent_name",
        (select parent.code from sys_permission parent where parent.permission_id=sys_permission.parent_id)
        "parent_code",
        <include refid="Base_Column_List"/>
        ,st.tenant_name
        from sys_permission
        left join
        sys_tenant st on st.tenant_id = sys_permission.tenant_id and st.delete_flag = 'normal' and st.tenant_status = 'valid'
        where permission_id=#{id}
    </select>
    <select id="findListByDistribute" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO"
      parameterType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO">
        select
        <include refid="Base_Column_Lists"/>
        from sys_role_tenant srt
        left join sys_role_permission srp on srt.role_id = srp.role_id
        left join sys_permission sp on srp.permission_id = sp.permission_id and sp.delete_flag = 'normal'
        <where>
            <if test="vo.tenantId != null and vo.tenantId != ''">
                AND srt.tenant_id = #{vo.tenantId}
            </if>
        </where>
        <include refid="OrderSqls" />
    </select>

    <!-- 根据时间同步系统权限 -->
    <select id="selectPermissionList" parameterType="cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO"
            resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionPO">
        select *
        from sys_permission
        where delete_flag = 'normal'
        and tenant_id = #{tenantId}
        <if test="startTime != '' and startTime != null">
            and  date_format(update_date, '%Y-%m-%d' ) >= #{startTime}
        </if>
        <if test="endTime != '' and endTime != null">
            and  date_format(update_date, '%Y-%m-%d' ) &lt;= #{endTime}
        </if>
        order by update_date desc
    </select>

   <!--  根据id查询系统权限  -->
    <select id="findPremById" resultType="cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionPO"
    parameterType="java.lang.String">
        select
            permission_id,
            code,
            permission_name,
            uri,
            open_type,
            permission_type,
            parent_id,
            grade,
            permission_status,
            icon,
            check_code,
            description,
            permission_sort,
            create_date,
            create_by,
            update_date,
            update_by,
            delete_flag,
            versions,
            permission_scope,
            tenant_id,
            permission_visible,
            permission_frame,
            terminal
        from sys_permission
        where
            permission_id = #{permissionId}
    </select>
</mapper>
