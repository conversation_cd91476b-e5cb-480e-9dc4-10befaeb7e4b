<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.cloud.system.client.dao.ClientSysMapper">

    <select id="findSysClientByVo" resultType="cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientVO">
        SELECT
        s.config_id,
        s.config_name,
        s.config_code,
        s.config_type,
        dict.dict_label as configTypeName,
        s.config_value,
        s.client_type,
        dict1.dict_label as clientTypeName,
        s.config_describe,
        s.time_stamp
        FROM
        sys_public_config s
        left join sys_dict_data dict on s.config_type = dict.dict_value and dict.dict_code = 'client_config_type' and dict.delete_flag = 'normal'
        left join sys_dict_data dict1 on s.client_type = dict1.dict_value and dict1.dict_code = 'client_type' and dict1.delete_flag = 'normal'
        WHERE
        s.delete_flag = 'normal'
        <if test="vo.configName != null and vo.configName !=''">
            AND s.config_name like concat(concat('%',#{vo.configName}),'%')
        </if>
        <if test="vo.configCodes!= null and vo.configCodes.size > 0 ">
          AND s.config_code in
             <foreach collection="vo.configCodes" close=")" open="(" item="configCode" separator=",">
                   #{configCode}
              </foreach>
        </if>
        <if test="vo.configType != null and vo.configType != ''">
            AND s.config_type like concat(concat('%',#{vo.configType}),'%')
        </if>
        <if test="vo.tenantId != null and vo.tenantId != '' ">
            AND s.tenant_id = #{vo.tenantId}
        </if>
        <if test="vo.clientType != null and vo.clientType != '' ">
            AND s.client_type = #{vo.clientType}
        </if>
        order by s.create_date desc
    </select>

    <select id="findClientSys" resultType="cn.chinaunicom.sdsi.cloud.system.client.entity.SysClientPO">
        SELECT
        c.config_name,
        c.config_code,
        c.config_type,
        c.config_value,
        c.client_type,
        c.time_stamp
        FROM
        sys_public_config c
        WHERE
        c.delete_flag = 'normal'
        <if test="configCode != null and configCode != ''">
            AND c.config_code like concat(concat('%',#{configCode}),'%')
        </if>
        <if test="configType != null and configType != ''">
            AND c.config_type like concat(concat('%',#{configType}),'%')
        </if>
    </select>

    <select id="selectClientByInfo" resultType="cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientPublicVO">
        SELECT
        c.config_name,
        c.config_code,
        c.config_type,
        c.config_value,
        c.client_type,
        c.time_stamp
        FROM
        sys_public_config c
        WHERE
        c.delete_flag = 'normal'
        <if test="sysClientQueryVO.configCodes!= null and sysClientQueryVO.configCodes.size > 0 ">
            AND c.config_code in
            <foreach collection="sysClientQueryVO.configCodes" close=")" open="(" item="configCode" separator=",">
                #{configCode}
            </foreach>
        </if>
        <if test="sysClientQueryVO.clientType != null and sysClientQueryVO.clientType != ''">
            AND c.client_type like concat(concat('%',#{sysClientQueryVO.clientType}),'%')
        </if>
        <if test="sysClientQueryVO.tenantId != null and sysClientQueryVO.tenantId != ''">
            and c.tenant_id = #{sysClientQueryVO.tenantId}
        </if>
    </select>
</mapper>
