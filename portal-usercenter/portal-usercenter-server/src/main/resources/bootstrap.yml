spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
#        server-addr: 172.16.11.56:8848
        server-addr: ${NACOS_SERVER_ENDPOINT:172.16.11.56:8848}
        namespace: ${NACOS_SERVER_NAMESPACE:sanquan_majian}
        group: DEFAULT_GROUP
        cluster-name: DEFAULT
        username: ${NACOS_SERVER_USERNAME:nacos}
        password: ${NACOS_SERVER_PASSWORD:ghN<PERSON><PERSON>@2022}
      config:
        server-addr: ${NACOS_SERVER_ENDPOINT:172.16.11.56:8848}
        namespace: ${NACOS_SERVER_NAMESPACE:sanquan_majian}
        prefix: usercenter
        file-extension: yaml
        group: DEFAULT_GROUP
        cluster-name: DEFAULT
        username: ${NACOS_SERVER_USERNAME:nacos}
        password: ${NACOS_SERVER_PASSWORD:gh<PERSON><PERSON>s@2022}
management:
  endpoints:
    # [false] 关闭默认端点访问权限(同时关闭普罗米修斯监控) [true] 开启端点暴露,有一定安全隐患
    enabled-by-default: false
