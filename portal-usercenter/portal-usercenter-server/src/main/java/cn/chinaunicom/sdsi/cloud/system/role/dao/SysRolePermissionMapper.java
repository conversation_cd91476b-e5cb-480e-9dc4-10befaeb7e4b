package cn.chinaunicom.sdsi.cloud.system.role.dao;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionTreeVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePermissionPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色-权限关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2019-01-16
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Mapper
public interface SysRolePermissionMapper extends BaseMapper<SysRolePermissionPO> {

    /**
     * 根据角色id查询其具备的权限集合
     *
     * @param roleId 角色id
     * @return 权限树节点集合
     */
    List<SysPermissionTreeVO> selectPermissionTreeByRoleId(final String roleId);

    /**
     * 根据时间查询租户下角色权限数据
     *
     * @param vo 查询条件
     * @return 角色权限集合
     */
    List<SysRolePermissionPO> selectRolePermissionList(SysTransDateVO vo);

    /**
     * 根据角色id查询角色权限
     *
     * @param roleId 查询条件
     * @return 角色权限集合
     */
    List<SysRolePermissionPO> selectRolePermListByRoleId(@Param("roleId") String roleId);
}
