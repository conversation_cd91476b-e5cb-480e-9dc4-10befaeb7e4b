package cn.chinaunicom.sdsi.cloud.system.client.service;

import cn.chinaunicom.sdsi.cloud.system.client.entity.SysClientPO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientPublicVO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientQueryVO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
public interface ClientSysService extends IService<SysClientPO> {

    /**
     * 根据主键查询客户端配置
     *
     *
     * @param configId 主键
     * @return 查询到的系统配置对象
     * @throws ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     *
     * @date 2022/3/16
     * -
     * @version 1.0
     * @description 内容描述
     */
    SysClientVO findConfigSys(String configId) throws ServiceErrorException;


    /**
     * 新增系统配置
     *
     * @param sysClientVO 即将要新增的客户端配置对象
     * @return 新增成功后返回的数据的主键值
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * -
     * @date 2022/3/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    String addSysClient(SysClientVO sysClientVO) throws ServiceErrorException;

    /**
     * 删除系统配置
     * 逻辑删除,逻辑删除位 DELETE_FLAG 字段.
     *
     * @param configId 即将要删除的系统配置的主键
     * @return 删除的数据条数
     * -
     * @date 2022/3/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    int deleteSysClientByConfigId(String configId);

    /**
     * 修改系统配置
     *
     * @param sysClientVO 即将要修改的系统配置对象,方法会修改数据库中 configId 唯一对应的数据,修改的字段为 vo 中与表映射对象同名的字段
     * @return 修改的数据条数
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * -
     * @date 2022/3/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    Integer updateSysClient(SysClientVO sysClientVO) throws ServiceErrorException;

    /**
     * 根据查询条件查询系统参数分页数据
     *
     * @param sysClientQueryVO 查询条件实体
     * @return 系统配置的分页对象
     */
    IPage<SysClientVO> findSysClientPage(SysClientQueryVO sysClientQueryVO);


    /**
     * 获取客户端信息
     * @param sysClientQueryVO
     * @return
     */
    List<SysClientPublicVO> selectClientListByInfo(SysClientQueryVO sysClientQueryVO);
}
