package cn.chinaunicom.sdsi.cloud.system.tenantApply.controller;

import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantVO;
import cn.chinaunicom.sdsi.cloud.system.tenant.service.SysTenantService;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.cloud.system.tenantApply.entity.SysTenantApplyQueryVO;
import cn.chinaunicom.sdsi.cloud.system.tenantApply.entity.SysTenantApplyVO;
import cn.chinaunicom.sdsi.cloud.system.tenantApply.service.SysTenantApplyService;
import cn.chinaunicom.sdsi.core.annotation.OperateLog;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;

import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 租户审核功能 - 租户申请单控制层接口
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/20
 * -
 * @description 内容描述
 */
@Tag(name = "租户审核功能 - 租户申请单控制层接口", description = "租户审核")
@Slf4j
@RestController
@RequestMapping("/tenantApply")
public class SysTenantApplyController extends BaseController {

    /**
     * 租户审核功能 - 租户申请单服务层接口
     */
    @Resource
    private SysTenantApplyService sysTenantApplyService;

    /**
     * 租户管理功能 - 租户信息服务层接口
     */
    @Resource
    private SysTenantService sysTenantService;

    /**
     * 新增租户申请单
     *
     * @param vo 即将要新增的租户申请单对象
     * @return 新增成功后返回的数据的主键值
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "新增租户申请单", description ="租户审核")
    @OperateLog(value = "新增租户申请单")
    @PostMapping
    public BaseResponse<SysTenantApplyVO> addSysTenantApply(@Valid @RequestBody SysTenantApplyVO vo) {
        String tenantApplyId = sysTenantApplyService.addSysTenantApply(vo);
        return ok(sysTenantApplyService.findOne(tenantApplyId));
    }

    /**
     * 删除租户申请单
     *
     * @param id 即将要删除的数据的主键
     * @return 删除成功的数据条数
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "删除租户申请单", description ="租户审核")
    @OperateLog(value = "删除租户申请单")
    @Parameters({
        @Parameter(name = "id", description ="SysTenantApply的主键", in = ParameterIn.PATH, required = true)
    })
    @DeleteMapping("/{id}")
    public BaseResponse<Integer> deleteSysTenantApply(@PathVariable("id") String id) {
        return ok(sysTenantApplyService.removeById(id) ? 1 : 0);
    }

    /**
     * 修改租户申请单
     *
     * @param vo 即将要修改的租户申请单对象,方法会修改数据库中 tenantApplyId 唯一对应的数据,修改的字段为 vo 中与表映射对象同名的字段
     * @return 修改成功的数据条数
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "修改租户申请单", description ="租户审核")
    @OperateLog(value = "修改租户申请单")
    @PutMapping
    public BaseResponse<Integer> updateSysTenantApply(@Valid @RequestBody SysTenantApplyVO vo) {
        try {
            return ok(sysTenantApplyService.updateSysTenantApply(vo));
        } catch (ServiceErrorException e) {
            return new BaseResponse<Integer>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 根据主键查询单个租户申请单详情
     *
     * @param id 主键
     * @return 查询到的租户申请单对象
     * @throws ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "查询单条数据", description ="查询单条数据")
    @GetMapping("/{id}")
    public BaseResponse<SysTenantApplyVO> findSysTenantApply(@PathVariable("id") String id) {
        try {
            return ok(sysTenantApplyService.findOne(id));
        } catch (ServiceErrorException e) {
            return new BaseResponse<SysTenantApplyVO>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 根据分页信息查询租户申请单列表
     *
     * @param query 分页查询视图对象
     * @return 携带分页信息的租户申请单列表
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "分页查询")
    @GetMapping
    public BasePageResponse<SysTenantApplyVO> findSysTenantApplyPage(@Valid SysTenantApplyQueryVO query) {
        return pageOk(sysTenantApplyService.findSysTenantApplyPage(query));
    }

    /**
     * 审批租户申请单
     *
     * @param vo 根据主键查询对应数据,修改字段
     * @return 审核成功的数据数量
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "审核租户申请单", description ="租户审核")
    @OperateLog(value = "审核租户申请单")
    @PostMapping("audit")
    public BaseResponse<Integer> audit(@Valid @RequestBody SysTenantVO vo) {
        try {
            // 从业务控制层区分功能接口,但此处暂时调用租户api接口
            return ok(sysTenantService.updateSysTenant(vo));
        } catch (ServiceErrorException e) {
            return new BaseResponse<Integer>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 根据租户申请单单号查询租户申请单对象
     *
     * @param tenantApplyNo 租户申请单单号
     * @return 查询到的租户申请单对象
     * <AUTHOR>
     * -
     * @date 2021/12/20
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "根据申请单号查询单条数据", description ="根据申请单号查询单条数据")
    @GetMapping("/findSysTenantApplyInfo/{tenantApplyNo}")
    public BaseResponse<SysTenantApplyVO> findSysTenantApplyInfo(@PathVariable("tenantApplyNo") String tenantApplyNo) {
        return ok(sysTenantApplyService.findOneByTenantApplyNo(tenantApplyNo));
    }
}
