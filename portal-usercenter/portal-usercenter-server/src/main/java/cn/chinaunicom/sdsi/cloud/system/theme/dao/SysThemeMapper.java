package cn.chinaunicom.sdsi.cloud.system.theme.dao;

import cn.chinaunicom.sdsi.cloud.system.theme.entity.SysThemePO;
import cn.chinaunicom.sdsi.cloud.system.theme.vo.SysEnabledThemeVO;
import cn.chinaunicom.sdsi.cloud.system.theme.vo.SysThemeQueryVO;
import cn.chinaunicom.sdsi.cloud.system.theme.vo.SysThemeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>文件名称: SysThemeMapper </p>
 * <p>描述: 主题配置访问层 </p>
 * <p>创建时间: 2022/4/12 16:35</p>
 *
 * <AUTHOR>
 */
@Mapper
public interface SysThemeMapper extends BaseMapper<SysThemePO> {

  IPage<SysThemeVO> selectList(@Param("page") IPage page, @Param("vo") SysThemeQueryVO sysThemeQueryVO);

  SysThemeVO get(@Param("vo") SysThemeQueryVO sysThemeQueryVO);

  SysEnabledThemeVO getEnableTheme(@Param("vo") SysThemeQueryVO sysThemeQueryVO);
}
