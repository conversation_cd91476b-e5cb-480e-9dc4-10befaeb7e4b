package cn.chinaunicom.sdsi.cloud.system.test;


import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.Principal;

/**
 * 获取登录用户信息接口
 * Created by macro on 2020/6/19.
 */
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UnifastContext unifastContext;

    @GetMapping("/user")
    public MallUser user() {
        final MallUser user = unifastContext.getUser();
        return user;
    }

    @PreAuthorize("hasAuthority('ADMIN')")
    @GetMapping("/current")
    public Principal currentUser(Principal principal) {
        return principal;
    }

    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    @GetMapping("/test")
    public String test(Principal principal) {
        return "hasAuthority('TEST'):" + principal.getName();
    }


    @PreAuthorize("hasRole('TEST')")
    @GetMapping("/role")
    public String role(Principal principal) {
        return "hasAuthority('TEST'):" + principal.getName();
    }


    @PreAuthorize("hasRole('ROLE_TEST')")
    @GetMapping("/roletest")
    public Principal roleTest(Principal principal) {
        return principal;
    }
}
