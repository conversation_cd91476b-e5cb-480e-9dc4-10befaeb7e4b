package cn.chinaunicom.sdsi.cloud.system.config.service;

import cn.chinaunicom.sdsi.cloud.system.config.entity.SysConfigPO;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigQueryVO;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 系统配置参数表，保存全局的参数、设置等 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-14
 */
public interface ISysConfigService extends IService<SysConfigPO> {

    /**
     * 新增系统配置
     *
     * @param sysConfigVO 系统参数实体
     * @return 主键
     */
    String addSysConfig(SysConfigVO sysConfigVO);

    /**
     * 编辑系统配置
     *
     * @param sysConfigVO 系统参数实体
     * @return 影响条数
     */
    Integer updateSysConfig(SysConfigVO sysConfigVO);

    /**
     * 根据主键，查询 系统参数
     *
     * @param configId 主键ID
     * @return 系统参数实体
     */
    SysConfigVO findSysConfig(final String configId);

    /**
     * 根据参数id、岗位id查询某条参数配置
     *
     * @param configId 配置id
     * @param staffOrgId 岗位id
     * @return 系统参数实体
     */
    SysConfigVO findSysConfig(final String configId, final String staffOrgId);

    /**
     * 根据查询条件查询系统参数分页数据
     *
     * @param sysConfigQueryVO 查询条件实体
     * @return 系统配置的分页对象
     */
    IPage<SysConfigVO> findSysConfigPage(SysConfigQueryVO sysConfigQueryVO);

    /**
     * 根据配置code、岗位id，查询系统参数
     *
     * @param code 配置code
     * @param staffOrgId 岗位id
     * @return 系统参数实体
     */
    SysConfigVO findConfigByCode(final String code, final String staffOrgId);

    /**
     * <AUTHOR>
     * @Description
     * @Date 13:52 2021/7/27
     * @param configId 配置id
     * @return 是否被逻辑删除
     **/
    Boolean deleteSysConfigByConfigId(String configId);

    /**
     * <AUTHOR>
     * @Description
     * @Date 16:24 2021/7/28
     * @param sysConfigQueryVO
     * @return IPage<SysConfigQueryVO>
     **/
    IPage<SysConfigVO> selectConfigByCode(SysConfigQueryVO sysConfigQueryVO);

    /**
     * 查询默认主题配置
     *
     * @return 系统参数实体
     */
    String colorMode();
}
