package cn.chinaunicom.sdsi.cloud.system.staff.controller;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffOrgService;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgSaveVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import cn.chinaunicom.sdsi.core.annotation.OperateLog;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.core.interfaces.Delete;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 人员-组织关联表（岗位表） 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-01-17
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Tag(name = "人员岗位管理接口")
@Slf4j
@RestController
@RequestMapping("staffOrgs")
public class SysStaffOrgController extends BaseController {
    @Autowired
    private SysStaffOrgService sysStaffOrgService;
    @Autowired
    public UnifastContext unifastContext;
    /**
     * 新增岗位
     * 新增人员岗位信息，根据人员ID和岗位ID创建岗位信息
     *
     * @param saveVO
     * @return
     */
    @Operation(summary = "新增岗位", description ="新增人员岗位信息，根据人员ID和岗位ID创建岗位信息")
    @PostMapping
    // @PreAuthorize("hasAnyAuthority('sys:staff:org')")
    @OperateLog("新增岗位")
    public BaseResponse<String> addStaffOrg(@Valid @RequestBody SysStaffOrgSaveVO saveVO) {
        saveVO.setStaffOrgId(null);
        return ok(sysStaffOrgService.saveStaffOrg(saveVO));
    }

    /**
     * 新增岗位-角色信息
     * 新增人员岗位信息，并且根据选择的角色将岗位进行绑定
     *
     * @param saveVO
     * @return
     */
    @Operation(summary = "新增岗位", description ="新增人员岗位信息，根据人员ID和岗位ID创建岗位信息")
    @PostMapping("/addStaffOrgAndRole")
    @OperateLog("新增岗位")
    public BaseResponse<String> addStaffOrgAndRole(@Valid @RequestBody SysStaffOrgSaveVO saveVO) {
        return ok(sysStaffOrgService.addStaffOrgAndRole(saveVO));
    }

    /**
     * 删除岗位
     * 根据岗位ID物理删除记录
     *
     * @param saveVO
     * @return
     */
    @Operation(summary = "删除岗位", description ="根据岗位ID删除岗位")
    @Parameter(name = "staffOrgId", description ="岗位id", required = true)
    @PostMapping("/delete")
    // @PreAuthorize("hasAnyAuthority('sys:staff:org')")
    @OperateLog("删除岗位")
    public BaseResponse<Integer> delStaffOrg(@Validated(value = Delete.class) @RequestBody SysStaffOrgSaveVO saveVO) {
        return ok(sysStaffOrgService.deleteStaffOrg(saveVO.getStaffOrgId()));
    }

    /**
     * 修改人员岗位信息
     * 根据岗位ID修改岗位基本信息
     *
     * @param saveVO
     * @return
     */
    @Operation(summary = "修改人员岗位信息", description ="根据岗位ID修改岗位")
    @PostMapping("/edit")
    @PreAuthorize("hasAnyAuthority('sys:staff:org')")
    @OperateLog("修改岗位")
    public BaseResponse<String> updateStaffOrg(@Valid @RequestBody SysStaffOrgSaveVO saveVO) {
        return ok(sysStaffOrgService.saveStaffOrg(saveVO));
    }

    /**
     * 根据岗位ID查询岗位详细信息
     * 包括人员名称、组织名称等信息
     *
     * @param staffOrgId
     * @return
     */
    @Operation(summary = "查询岗位信息", description ="根据岗位ID查询岗位详细信息")
    @GetMapping(value = "/{staffOrgId}")
    public BaseResponse<SysStaffOrgVO> findStaffOrgByStaffOrgId(@PathVariable("staffOrgId") String staffOrgId) {
        return ok(sysStaffOrgService.findStaffOrgByStaffOrgId(staffOrgId));
    }

    /**
     * 根据人员id查询其岗位信息列表
     * 包括人员名称、组织名称等信息
     *
     * @param staffId
     * @return
     */
    @Operation(summary = "根据人员id查询岗位列表", description ="根据人员id查询其岗位信息列表")
    @GetMapping("/by/{staffId}")
    public BaseResponse<List<SysStaffOrgVO>> findStaffOrgByStaffId(@PathVariable("staffId") String staffId) {
        SysStaffOrgQueryVO queryVO = new SysStaffOrgQueryVO();
        queryVO.setStaffId(staffId);
        return ok(sysStaffOrgService.findStaffOrgs(queryVO));
    }

    /**
     * 查询组织下的钉钉userId
     *
     * @param queryVO
     * @return
     */
    @Operation(summary = "查询组织下的钉钉userId", description ="查询组织下的钉钉userId")
    @PostMapping("/selectDingUserIdByQueryVO")
    public BaseResponse<List<String>> selectDingUserIdByQueryVO(@RequestBody SysStaffOrgQueryVO queryVO) {
        return ok(sysStaffOrgService.selectDingUserIdByQueryVO(queryVO));
    }

    /**
     * 查询租户下未绑定钉钉的用户信息
     *
     * @param tenantId
     * @return
     */
    @Operation(summary = "查询租户下未绑定钉钉的用户信息", description ="查询租户下未绑定钉钉的用户信息")
    @GetMapping("/selectTenantDingUsers")
    public BaseResponse<List<SysStaffExtVO>> selectTenantDingUsers(@RequestParam(value = "tenantId") String tenantId) {
        return ok(sysStaffOrgService.selectTenantDingUsers(tenantId));
    }

    /**
     * 根据条件查询人员数据，分页展示
     * 包括人员名称、组织名称等信息，默认只查询组织和人员未删除的数据
     *
     * @param queryVO
     * @return
     */
    @Operation(summary = "分页查询人员岗位列表", description ="根据条件查询人员数据，分页展示")
    @GetMapping
    public BasePageResponse<SysStaffOrgVO> searchStaffList(@Valid SysStaffOrgQueryVO queryVO) {
//        log.info("=====================searchStaffList===================");
        IPage<SysStaffOrgVO> findStaffPag=sysStaffOrgService.findStaffPage(queryVO);

        findStaffPag.getRecords().forEach(rec->{
            rec.setCellphone(StringUtils.overlay(rec.getCellphone(), "****", 3, 7));
        });

        return pageOk(findStaffPag);
    }

    /**
     * 根据岗位ID修改岗位状态
     *
     * @param staffOrgId
     * @param staffOrgStatus
     * @return
     */
    @Operation(summary = "修改人员岗位状态", description ="根据岗位ID修改岗位状态")
    @Parameters({
            @Parameter(name = "staffOrgId", description ="岗位id", required = true),
            @Parameter(name = "staffOrgStatus", description ="岗位状态", required = true)})
    @PostMapping(value = "/{staffOrgId}/staffOrgStatus/{staffOrgStatus}")
    @PreAuthorize("hasAnyAuthority('sys:staff:org')")
    @OperateLog("修改人员岗位状态")
    public BaseResponse<Integer> changeStaffOrgStatus(@PathVariable("staffOrgId") String staffOrgId,
                                                    @PathVariable("staffOrgStatus") String staffOrgStatus) {
        MallUser current = unifastContext.getUser();
        if (current.getStaffOrgId().equals(staffOrgId)) {
            throw new ServiceErrorException("该用户受保护，不可禁用");
        }
        return ok(sysStaffOrgService.updateStatusByStaffOrgId(staffOrgId, staffOrgStatus));
    }


}
