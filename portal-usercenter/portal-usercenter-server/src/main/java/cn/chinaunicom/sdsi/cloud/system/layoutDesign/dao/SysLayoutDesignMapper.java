package cn.chinaunicom.sdsi.cloud.system.layoutDesign.dao;

import cn.chinaunicom.sdsi.cloud.system.layoutDesign.entity.SysLayoutDesignPO;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.vo.SysLayoutDesignQueryVO;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.vo.SysLayoutDesignVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>文件名称: SysLayoutDesignMapper </p>
 * <p>描述: 布局设计 </p>
 * <p>创建时间: 2022/4/25 16:00</p>
 *
 * <AUTHOR>
 */
@Mapper
public interface SysLayoutDesignMapper extends BaseMapper<SysLayoutDesignPO> {

  IPage<SysLayoutDesignVO> selectList(@Param("page") IPage page, @Param("vo") SysLayoutDesignQueryVO queryVO);

  SysLayoutDesignVO get(@Param("vo") SysLayoutDesignQueryVO queryVO);

  SysLayoutDesignVO getEnableLayoutDesign(@Param("vo") SysLayoutDesignQueryVO queryVO);
}
