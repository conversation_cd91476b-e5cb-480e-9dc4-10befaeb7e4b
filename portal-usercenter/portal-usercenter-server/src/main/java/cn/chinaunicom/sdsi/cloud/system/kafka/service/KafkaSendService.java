//package cn.chinaunicom.sdsi.cloud.system.kafka.service;
//
//import java.util.Map;
//
///**
// * kafka消息推送
// *
// * <AUTHOR>
// * -
// * @version 1.0
// * @date 2021/12/21
// * -
// * @description 内容描述
// */
//public interface KafkaSendService {
//
//    /**
//     * 调用 spring 封装的 Kafka 接口,为业务服务提供消息发送能力
//     *
//     * @param topic     kafka主题标识
//     * @param partition kafka分区数量
//     * @param key       区分消息逻辑含义的标识,如 create/update 等
//     * @param value     消息主体
//     * <AUTHOR>
//     * -
//     * @date 2021/12/14
//     * -
//     * @version 1.0
//     * @description 内容描述
//     */
//    Map sendKafka(String topic, Integer partition, String key, String value);
//
//    /**
//     * 公用消息队列发送
//     *
//     * @param key     操作类型
//     * @param orgTopic 主题
//     * @param tanentId  租户id
//     * @param obj    发送数据
//     * <AUTHOR>
//     * -
//     * @date 2021/12/21
//     * -
//     * @version 1.0
//     * @description 内容描述
//     */
//     void sendKafkaMessage(String key, String orgTopic, String tanentId, Object obj);
//    }
