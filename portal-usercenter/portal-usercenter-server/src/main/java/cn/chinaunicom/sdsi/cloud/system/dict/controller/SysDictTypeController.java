package cn.chinaunicom.sdsi.cloud.system.dict.controller;

import cn.chinaunicom.sdsi.cloud.system.dict.service.SysDictTypeService;
import cn.chinaunicom.sdsi.cloud.system.dict.vo.SysDictQueryVO;
import cn.chinaunicom.sdsi.cloud.system.dict.vo.SysDictTypeVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.core.interfaces.Add;
import cn.chinaunicom.sdsi.core.interfaces.Edit;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.groups.Default;

/**
 * 数据字典功能 - 字典类型(主字典)控制层接口
 * 字典类型为若干数据字典的集合.查询某个具体字典值时,还需要关联查询当前字典类型下的对应 key 的字典数据值.
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/14
 * -
 * @description 内容描述
 */
@Tag(name = "数据字典功能 - 字典类型(主字典)控制层接口", description = "字典管理")
@RestController
@RequestMapping("/dict/type")
public class SysDictTypeController extends BaseController {

    /**
     * 字典类型(主字典)服务层接口
     */
    @Resource
    private SysDictTypeService sysDictTypeService;

    /**
     * 新增字典类型
     *
     * @param vo 即将要新增的字典类型对象
     * @return HTTP响应结果 data:新增成功后返回的数据的主键值
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "新增字典类型")
    @PostMapping("/add")
    public BaseResponse<String> addSysDictType(@Validated(value = {Add.class, Default.class}) @RequestBody SysDictTypeVO vo) {
        try {
            return ok(this.sysDictTypeService.addSysDictType(vo));
        } catch (ServiceErrorException e) {
            return new BaseResponse<String>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 删除字典类型
     * 逻辑删除,逻辑删除位 DELETE_FLAG 字段.
     *
     * @param vo 即将要删除的字典类型对象
     * @return HTTP响应结果 data:删除的数据条数
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "删除字典类型")
    @PostMapping("/delete")
    public BaseResponse<Integer> deleteSysDictType(@RequestBody SysDictTypeVO vo) {
        try {
            return ok(this.sysDictTypeService.deleteSysDictType(vo.getDictId()));
        } catch (ServiceErrorException e) {
            return new BaseResponse<Integer>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 修改字典类型
     *
     * @param vo 即将要修改的字典类型对象
     * @return HTTP响应结果 data:修改的数据条数
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "修改字典类型")
    @PostMapping("/edit")
    public BaseResponse<Integer> updateSysDictType(@Validated(value = {Edit.class, Default.class}) @RequestBody SysDictTypeVO vo) {
        try {
            return ok(this.sysDictTypeService.updateSysDictType(vo));
        } catch (ServiceErrorException e) {
            return new BaseResponse<Integer>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 根据主键查询单个字典类型详情
     *
     * @param dictId 主键
     * @return HTTP响应结果 data:查询到的字典类型对象
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "根据主键查询单个字典类型详情")
    @GetMapping("/details/{dictId}")
    public BaseResponse<SysDictTypeVO> findSysDictType(@PathVariable String dictId, HttpServletResponse httpServletResponse) {
        try {
            if(dictId.indexOf(".")>0){
                httpServletResponse.setStatus(403);
            }
            return ok(this.sysDictTypeService.findDictTypeById(dictId));
        } catch (ServiceErrorException e) {
            httpServletResponse.setStatus(403);
            return new BaseResponse<SysDictTypeVO>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 根据分页信息查询字典类型列表
     * 支持根据 dictName 字典类型名称进行模糊匹配查询
     *
     * @param queryVO 分页查询视图对象
     * @return HTTP响应结果 data:携带分页信息的字典类型列表
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "根据分页信息查询字典类型列表")
    @GetMapping
    public BasePageResponse<SysDictTypeVO> findPage(SysDictQueryVO queryVO) {
        return pageOk(this.sysDictTypeService.findDictTypePage(queryVO));
    }
}
