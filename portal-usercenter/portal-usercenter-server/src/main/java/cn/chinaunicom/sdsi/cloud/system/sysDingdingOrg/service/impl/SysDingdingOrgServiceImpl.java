package cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.dao.SysDingdingOrgMapper;
import cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.entity.SysDingdingOrgPO;
import cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.service.SysDingdingOrgService;
import cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.vo.SysDingdingOrgVo;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 * 门户组织与钉钉组织关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2022-02-25
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Service
public class SysDingdingOrgServiceImpl extends ServiceImpl<SysDingdingOrgMapper, SysDingdingOrgPO> implements SysDingdingOrgService {

    @Resource
    private UnifastContext unifastContext;

    @Override
    public int addDingdingOrg(SysDingdingOrgPO sysDingdingOrgPO) {
        SysDingdingOrgVo sysDingdingOrgVo = new SysDingdingOrgVo();
        sysDingdingOrgVo.setDingDeptId(sysDingdingOrgPO.getDingDeptId());
        int count = this.baseMapper.checkDingOrgHasBind(sysDingdingOrgVo);
        if(count > 0){
            throw new ServiceErrorException("该钉钉组织已被绑定", HttpStatus.BAD_REQUEST);

        }
        sysDingdingOrgVo.setSysDeptId(sysDingdingOrgPO.getSysDeptId());
        SysDingdingOrgVo vo = this.baseMapper.selectDingdingOrg(sysDingdingOrgVo);
        if(vo != null){
            throw new ServiceErrorException("该系统组织已绑定钉钉组织", HttpStatus.BAD_REQUEST);
        }
        this.baseMapper.selectDingdingOrg(sysDingdingOrgVo);
        sysDingdingOrgPO.setId(IdUtil.randomUUID());
        sysDingdingOrgPO.setCreateDate(new Date());
        MallUser user = unifastContext.getUser();
        if (null != user) {
            sysDingdingOrgPO.setCreateBy(user.getStaffId());
        }
        return this.baseMapper.insert(sysDingdingOrgPO);
    }

    @Override
    public SysDingdingOrgVo selectDingdingOrg(SysDingdingOrgVo sysDingdingOrgVo) {
        return this.baseMapper.selectDingdingOrg(sysDingdingOrgVo);
    }

    @Override
    public int updateDingdingOrg(SysDingdingOrgPO sysDingdingOrgPO) {
        //查询是否已经被绑定
        SysDingdingOrgVo sysDingdingOrgVo = new SysDingdingOrgVo();
        BeanUtils.copyProperties(sysDingdingOrgPO, sysDingdingOrgVo);
        int count = this.baseMapper.checkDingOrgHasBind(sysDingdingOrgVo);
        if(count > 0){
            throw new ServiceErrorException("该钉钉组织已被绑定", HttpStatus.BAD_REQUEST);
        }
        return this.baseMapper.updateById(sysDingdingOrgPO);
    }

    @Override
    public int deleteDingdingOrgById(String id) {
        return this.baseMapper.deleteById(id);
    }
}
