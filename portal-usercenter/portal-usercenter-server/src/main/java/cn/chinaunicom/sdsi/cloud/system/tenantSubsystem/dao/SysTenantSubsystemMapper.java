/*
 * Copyright (c) 2020, SDCNCSI. All rights reserved.
 */
package cn.chinaunicom.sdsi.cloud.system.tenantSubsystem.dao;

import cn.chinaunicom.sdsi.cloud.system.tenantSubsystem.vo.SysTenantSubsystemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户与子系统关联表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022-01-10
 * @version: V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Mapper
public interface SysTenantSubsystemMapper {

    /***
     * 根据code查询租户信息
     * @param subsystemCode
     * @return SysTenantSubsystemVO
     * **/
    List<SysTenantSubsystemVO> selectBySubsystemCode(@Param("subsystemCode") String subsystemCode);

}
