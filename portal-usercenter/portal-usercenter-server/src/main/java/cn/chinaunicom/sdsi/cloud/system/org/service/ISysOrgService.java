package cn.chinaunicom.sdsi.cloud.system.org.service;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.org.entity.*;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 组织管理功能 - 组织服务层接口
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2019-01-15
 * -
 * @description 内容描述
 */
public interface ISysOrgService extends IService<SysOrgPO> {

    /**
     * 新增组织，判断组织code是否重复
     *
     * @param sysOrg 组织视图对象
     * @return 组织id
     */
    String saveSysOrg(SysOrgVO sysOrg);

    String saveSysOrgPO(SysOrgPO sysOrg);

    /**
     * 新增租户时 新增根组织信息
     *
     * @param sysOrg 组织视图对象
     * @return 组织id
     */
    String saveSysOrgByAddTenant(SysOrgVO sysOrg);

    /**
     * 逻辑删除组织，如果组织下有子组织，或组织下有人员，则不允许删除
     *
     * @param orgId 组织id
     * @return 影响条数
     */
    Integer deleteOrgById(final String orgId);

    /**
     * 更新组织
     *
     * @param sysOrg 组织视图对象
     * @return 影响条数
     */
    Integer updateSysOrg(SysOrgVO sysOrg);

    /**
     * 根据组织id，查询下一级的组织树节点
     *
     * @param orgId 组织id
     * @return 组织树对象集合
     */
    List<SysOrgTreeVO> findTreeChildren(final String orgId);

    /**
     * 根据id查询组织详情
     *
     * @param orgId 组织id
     * @return 组织视图对象
     */
    SysOrgVO findById(final String orgId);

//    SysOrgPO findSysOrgPOById(final String orgId);

    /**
     * 根据查询条件（上级组织id），查询下一级组织 及 该组织下的人员，供人员树使用
     *
     * @param queryVO 查询条件对象
     * @return 组织视图对象（返回的数据同时包含组织、人员，将人员信息放入组织对象对应的属性中）
     */
    List<SysOrgVO> selectOrgAndStaffForTree(final SysOrgStaffTreeQueryVO queryVO);

    /**
     * 根据查询条件，查询组织分页列表
     *
     * @param queryVO 查询条件对象
     * @return 组织视图分页对象
     */
    IPage<SysOrgVO> findPageByParam(final SysOrgQueryVO queryVO);

    /**
     * 根据查询条件，查询组织视图列表，不分页
     *
     * @param queryVO 查询条件对象
     * @return 组织视图对象集合
     */
    List<SysOrgVO> findListByParam(final SysOrgQueryVO queryVO);

    /**
     * 根据租户查询组织视图列表，不分页
     *
     * @param tenantId 租户Id
     * @return 组织视图对象集合
     */
    List<SysOrgVO> findListByTenantId(String tenantId);

    /**
     * 根据组织id，查询上级组织信息
     *
     * @param orgId 组织id
     * @return 组织视图对象
     */
    SysOrgVO findParentOrg(final String orgId);

    /**
     * 根据组织id，查询所有下级节点
     *
     * @param orgId orgId
     * @return List<SysOrgTreeVO>
     */
    List<SysOrgTreeVO> findAllNodeChildren(String orgId);

    /**
     * 根据组织id，查询所有下级节点
     *
     * @param queryVO 标签说明
     * @return List<SysOrgTreeVO>
     */
    List<SysOrgVO> findAllOrgChildren(SysOrgTreeQueryVO queryVO);

    /**
     * 根据组织id，查询用于树节点的详情
     *
     * @param orgId 组织id
     * @return 树节点视图对象
     */
    SysOrgTreeVO findTreeVoById(final String orgId);

    /**
     * 向上或向下递归查询组织节点
     *
     * @param sysOrgVO 标签说明
     * @return List<SysOrgVO>
     * <AUTHOR>
     * -
     * @date 2022/1/11
     * -
     * @version 1.0
     * @description 内容描述
     */
    List<SysOrgVO> queryOrgUpAndDownList(SysOrgVO sysOrgVO);

    /**
     * 根据租户id查询当前租户的组织树的根节点<br/>
     * 判断组织根节点的条件为 <b>parent_id[上级id]<b/> 与 <b>parent_code[上级编码]</b> is null
     *
     * @param sysOrgVO 根据 <b>sysOrgVO.tenantId[租户id]</b> 字段,过滤租户
     * @return 查询到的组织树根节点对象
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2022/1/11
     * -
     * @version 1.0
     * @description 内容描述
     */
    SysOrgVO findRootNodeByTenantId(SysOrgVO sysOrgVO) throws ServiceErrorException;

    /**
     * 根据时间查询租户下组织数据
     *
     * @param vo 查询条件
     * @return 组织集合
     */
    List<SysOrgPO> selectOrgList(SysTransDateVO vo);

    /**
     * 获取地市级数据，包含地市级的上级.
     */
    List<SysOrgVO> getCityData(SysOrgVO sysOrgVO);
}
