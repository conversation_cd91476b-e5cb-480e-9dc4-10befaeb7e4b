package cn.chinaunicom.sdsi.cloud.system.config.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.system.config.dao.SysConfigMapper;
import cn.chinaunicom.sdsi.cloud.system.config.dao.SysPersonalConfigMapper;
import cn.chinaunicom.sdsi.cloud.system.config.entity.SysPersonalConfigPO;
import cn.chinaunicom.sdsi.cloud.system.config.service.SysPersonalConfigService;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigVO;
import cn.chinaunicom.sdsi.cloud.system.constant.UserCenterConstant;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum.PersonalConfigTypeEnum;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum.SystemThemeModeEnum;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 个人配置表，对应sys_confg，如果有允许用户自定义的配置，存到此表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-14
 */
@Service
public class SysPersonalConfigServiceImpl extends ServiceImpl<SysPersonalConfigMapper, SysPersonalConfigPO>
        implements SysPersonalConfigService {

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    public UnifastContext unifastContext;

    @Override
    public int deleteByPersonaLId(String personalId) {
        return this.baseMapper.deleteByPersonaLId(personalId);
    }

    @Override
    public int updateConfigStatus(SysPersonalConfigPO po) {
        if(po.getConfigStatus() == 0){
           //只允许一个启动
            this.baseMapper.updateConfigDisable(po);
        }
        return this.baseMapper.updateConfigStatus(po);
    }

    @Override
    public int updateTenantStatus(SysPersonalConfigPO po) {
        // 先批量改成未使用状态，然后将选择的配置设置成启用
        if(po.getTenantConfigStatus() == 0){
            //只允许一个启动
            this.baseMapper.updateTenantConfigDisable(po);
        }
        return this.baseMapper.updateTenantConfigStatus(po);
    }

    @Override
    public int updateOrgStatus(SysPersonalConfigPO po) {
        if(po.getOrgConfigStatus() == 0){
            //只允许一个启动
            this.baseMapper.updateOrgConfigDisable(po);
        }
        return this.baseMapper.updateOrgConfigStatus(po);
    }

    @Override
    public SysConfigVO findConfigInfo(SysPersonalConfigPO po) {
        SysConfigVO vo = this.baseMapper.selectPersionConfigByInfo(po);
        if(vo == null){
            //查询租户配置
            //查询当前租户
            MallUser user = unifastContext.getUser();
            po.setTenantId(user.getTenantId());
            vo  = this.baseMapper.selectPersionConfigByTenantId(po);
            if(vo == null){
                vo = sysConfigMapper.findConfigByCode(po.getConfigCode(),null);
            }
        } else {
            SysConfigVO gol = sysConfigMapper.findConfigByCode(po.getConfigCode(),null);
            if("true".equals(gol.getAttra())){
                vo.setConfigValue(gol.getConfigValue());
            }
            vo.setAttra(gol.getAttra());
            vo.setAttrb(gol.getAttrb());
        }
        return vo;
    }

    @Override
    public List<SysPersonalConfigPO> getAllLayoutList(SysPersonalConfigPO po) {
        po.setConfigCode(PersonalConfigTypeEnum.PORTAL_HOME.getKey());
        po.setOrgId(unifastContext.getUser().getOrgId());
        po.setStaffOrgId(unifastContext.getUser().getStaffOrgId());
        if (StringUtils.isBlank(po.getTenantId())) {
            //查询当前租户
            MallUser user = unifastContext.getUser();
            if (Objects.nonNull(user)) {
                po.setTenantId(user.getTenantId());
            }
        }
        return this.baseMapper.getAllLayoutList(po);
    }

    @Override
    public List<SysPersonalConfigPO> selectConfigByTenantId(SysPersonalConfigPO po) {
        //查询当前租户
        MallUser user = unifastContext.getUser();
        po.setTenantId(user.getTenantId());
        List<String> tenantQueryScope = Lists.newArrayList();
        tenantQueryScope.add(PersonalConfigTypeEnum.TENANT_TITLE.getKey());
        tenantQueryScope.add(PersonalConfigTypeEnum.TENANT_LOGO.getKey());
        if (StringUtils.isNotBlank(po.getConfigCode())) {
            tenantQueryScope.add(po.getConfigCode());
        }
        po.setTenantQueryScope(tenantQueryScope);
        return this.baseMapper.selectConfigByTenantId(po);
    }

    @Override
    public List<SysPersonalConfigPO> selectGeneralConfigForTenant(SysPersonalConfigPO po) {
        if (StringUtils.isBlank(po.getTenantId())) {
            //查询当前租户
            MallUser user = unifastContext.getUser();
            if (Objects.nonNull(user)) {
                po.setTenantId(user.getTenantId());
            }
        }
        return this.baseMapper.selectGeneralConfigForTenant(po);
    }

    @Override
    public int updateConfigInfo(SysPersonalConfigPO po) {
        return this.baseMapper.updateConfigInfo(po);
    }

    @Override
    public int updatePersonalThemeMode(SysPersonalConfigPO po) {
        return this.baseMapper.updatePersonalThemeMode(po);
    }

    @Override
    public List<SysPersonalConfigPO> selectConfigForTenant(SysPersonalConfigPO po) {
        List<String> tenantQueryScope = Lists.newArrayList();
        tenantQueryScope.add(PersonalConfigTypeEnum.TENANT_TITLE.getKey());
        tenantQueryScope.add(PersonalConfigTypeEnum.TENANT_LOGO.getKey());
        po.setTenantQueryScope(tenantQueryScope);
        if (StringUtils.isNotBlank(po.getConfigCode())) {
            tenantQueryScope.add(po.getConfigCode());
        }
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
            unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            po.setTenantId(tenantId);
        }
        return this.baseMapper.selectConfigForTenant(po);
    }

    @Override
    public SysConfigVO getPersonalThemeConfig(SysPersonalConfigPO po) {
        // 查询个人布局模式的配置，如果没有则新建 - (系统默认-》自定义-》组织-》租户)
        SysPersonalConfigPO themeModePo = new SysPersonalConfigPO();
        themeModePo.setStaffOrgId(unifastContext.getUser().getStaffOrgId());
        themeModePo.setTenantId(unifastContext.getUser().getTenantId());
        themeModePo.setConfigCode(UserCenterConstant.PERSONAL_THEME_MODE);
        SysConfigVO themeModeConfig = this.baseMapper.selectPersonalLayoutConfigByInfo(themeModePo);
        SysConfigVO layoutConfig = null;
        // 查询个人的布局数据
        SysPersonalConfigPO query = new SysPersonalConfigPO();
        query.setConfigCode(PersonalConfigTypeEnum.PORTAL_HOME.getKey());
        query.setOrgId(unifastContext.getUser().getOrgId());
        query.setStaffOrgId(unifastContext.getUser().getStaffOrgId());
        query.setTenantId(unifastContext.getUser().getTenantId());
        if (Objects.isNull(themeModeConfig)) {
            // 1.先查询个人配置的布局数据
            query.setConfigScope(SystemThemeModeEnum.CUSTOM.getKey());
            layoutConfig = this.baseMapper.getOneLayout(query);
            if (Objects.isNull(layoutConfig)) {
                // 2.如果没有个人配置则查询组织配置，查询当前组织
                query.setConfigScope(SystemThemeModeEnum.ORG_CONFIG.getKey());
                layoutConfig = this.baseMapper.getOneLayout(query);
                if (Objects.isNull(layoutConfig)) {
                    // 3.如果没有组织配置则查询租户配置，查询当前租户
                    query.setConfigScope(SystemThemeModeEnum.TENANT_CONFIG.getKey());
                    layoutConfig = this.baseMapper.getOneLayout(query);
                    if (Objects.isNull(layoutConfig)) {
                        // 系统默认配置
                        layoutConfig = sysConfigMapper.findConfigByCode(po.getConfigCode(),null);
                        layoutConfig.setThemeMode(SystemThemeModeEnum.SYSTEM_DEFAULT.getKey());
                    } else {
                        layoutConfig.setThemeMode(SystemThemeModeEnum.TENANT_CONFIG.getKey());
                    }
                } else {
                    layoutConfig.setThemeMode(SystemThemeModeEnum.ORG_CONFIG.getKey());
                }
            } else {
                layoutConfig.setThemeMode(SystemThemeModeEnum.CUSTOM.getKey());
            }
            if (SystemThemeModeEnum.SYSTEM_DEFAULT.getKey().equals(layoutConfig.getThemeMode())) {
                themeModePo.setPersonalValue(SystemThemeModeEnum.SYSTEM_DEFAULT.getKey());
            } else if (SystemThemeModeEnum.CUSTOM.getKey().equals(layoutConfig.getThemeMode())) {
                themeModePo.setPersonalValue(SystemThemeModeEnum.CUSTOM.getKey());
            } else if (SystemThemeModeEnum.ORG_CONFIG.getKey().equals(layoutConfig.getThemeMode())) {
                themeModePo.setPersonalValue(SystemThemeModeEnum.ORG_CONFIG.getKey());
            } else if (SystemThemeModeEnum.TENANT_CONFIG.getKey().equals(layoutConfig.getThemeMode())) {
                themeModePo.setPersonalValue(SystemThemeModeEnum.TENANT_CONFIG.getKey());
            }
            themeModePo.setConfigId(UserCenterConstant.PERSONAL_THEME_MODE);
            themeModePo.setOrgId(unifastContext.getUser().getOrgId());
            this.save(themeModePo);
            layoutConfig.setThemeMode(themeModePo.getPersonalValue());
            return layoutConfig;
        } else {
            String themeMode = themeModeConfig.getConfigValue();
            if (SystemThemeModeEnum.SYSTEM_DEFAULT.getKey().equals(themeMode)) {
                layoutConfig = sysConfigMapper.findConfigByCode(po.getConfigCode(),null);
            } else {
                query.setConfigScope(themeMode);
                layoutConfig = this.baseMapper.getOneLayout(query);
            }
            if (Objects.isNull(layoutConfig)) {
                layoutConfig = new SysConfigVO();
            }
            layoutConfig.setThemeMode(themeModeConfig.getConfigValue());
            return layoutConfig;
        }
    }
}
