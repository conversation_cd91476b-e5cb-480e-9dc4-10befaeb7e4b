package cn.chinaunicom.sdsi.cloud.system.client.controller;


import cn.chinaunicom.sdsi.cloud.system.client.service.ClientSysService;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientQueryVO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientVO;
import cn.chinaunicom.sdsi.core.annotation.OperateLog;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.core.interfaces.Add;
import cn.chinaunicom.sdsi.core.interfaces.Edit;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.groups.Default;


/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Tag(name = "客户端配置功能 - 客户端配置控制层接口", description = "客户端配置")
@RestController
@RequestMapping("/client")
public class ClientSysController extends BaseController {

    /**
     * 客户端配置服务层接口
     */
    @Resource
    ClientSysService clientSysService;

    /**
     * 根据主键查询单个客户端配置详情
     * @param configId 主键
     * @return 查询到的客户端配置对象
     * @throws ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     *
     * @date 2022/3/16
     *
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "查询客户端配置", description ="查询configId的客户端设置")
    @GetMapping("/{configId}")
    public BaseResponse<SysClientVO> findConfigInfo(@PathVariable("configId")  String configId) {
        return ok(clientSysService.findConfigSys(configId));
    }

    /**
     * 新增客户端配置
     *
     * @param sysClientVO 即将要新增的系统配置对象
     * @return 新增成功后返回的数据的主键值
     * -
     * @date 2022/3/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "新增客户端配置")
    @OperateLog(value = "新增客户端配置")
    @PostMapping
    public BaseResponse<String> addSysClient(@Validated(value = {Add.class, Default.class}) @RequestBody SysClientVO sysClientVO) {
        try {
            return ok(clientSysService.addSysClient(sysClientVO));
        } catch (ServiceErrorException e) {
            return new BaseResponse<String>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 删除系统配置
     * 逻辑删除,逻辑删除位 DELETE_FLAG 字段.
     *
     * @param configId 即将要删除的系统配置的主键
     * @return 删除的数据条数
     * -
     * @date 2022/3/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "删除客户端配置")
    @OperateLog(value = "删除客户端配置")
    @GetMapping("/delete/{configId}")
    public BaseResponse<Integer> deleteSysClient(@PathVariable("configId") String configId) {
         return ok(clientSysService.deleteSysClientByConfigId(configId));
    }

    /**
     * 修改系统配置
     *
     * @param sysClientVO 即将要修改的系统配置对象,方法会修改数据库中 configId 唯一对应的数据,修改的字段为 vo 中与表映射对象同名的字段
     * @return 修改的数据条数
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * -
     * @date 2022/3/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Operation(summary = "修改客户端配置")
    @OperateLog(value = "修改客户端配置")
    @PostMapping("/edit")
    public BaseResponse<Integer> updateSysClient(@Validated({Edit.class, Default.class}) @RequestBody SysClientVO sysClientVO) {
        try {
            return ok(clientSysService.updateSysClient(sysClientVO));
        } catch (ServiceErrorException e) {
            return new BaseResponse<Integer>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 分页查询系统设置记录
     * @param sysClientQueryVO
     * @return
     */
    @Operation(summary = "分页查询系统设置记录")
    @PostMapping("/page")
    public BasePageResponse<SysClientVO> findSysClientPage(@Valid @RequestBody SysClientQueryVO sysClientQueryVO) {
        return pageOk(clientSysService.findSysClientPage(sysClientQueryVO));
    }


}
