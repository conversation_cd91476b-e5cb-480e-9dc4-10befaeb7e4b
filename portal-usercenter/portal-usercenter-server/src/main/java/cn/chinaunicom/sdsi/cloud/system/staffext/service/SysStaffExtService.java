/*
 * Copyright (c) 2020, SDCNCSI. All rights reserved.
 */
package cn.chinaunicom.sdsi.cloud.system.staffext.service;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.staffext.entity.SysStaffExtPO;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 用户扩展表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-17
 * @version: V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
public interface SysStaffExtService extends IService<SysStaffExtPO> {

     /**
      * 分页查询数据
      *
      * @param
      * @return
      */
     IPage<SysStaffExtVO> findSysStaffExtPage(SysStaffExtQueryVO query);

     /**
      * 根据组织或者租户并集查询
      *
      * @param
      * @return
      */
     List<SysStaffExtVO> findStaffExtByOrgOrTenant(SysStaffExtQueryVO query);

     /**
      * 查询单条数据
      *
      * @param
      * @return
      */
     SysStaffExtVO findOne(String id);

     /**
      * 新增数据
      *
      * @param
      * @return
      */
     String addSysStaffExt(SysStaffExtVO entity);

     /**
      * 修改数据
      *
      * @param
      * @return
      */
     int updateSysStaffExt(SysStaffExtVO entity);
     /**
      * 根据StaffId修改数据
      *
      * @param
      * @return
      */
     public int saveOrUpdateByStaffId(SysStaffExtVO vo);
     /**
      * 获取staffExt信息
      * @param staffId
      * @return
      */
     SysStaffExtVO findInfoVoByStaffId(final String staffId);

     /**
      * 检查昵称是否重复
      * @param agentNickName
      * @param tenantId
      * @return
      */
     int checkAgentNickname(String agentNickName, String tenantId);

     /**
      * 更新拓展表状态
      * @param staffExtId
      * @param deleteFlag
      * @return
      */
     int updateStatusByStaffExtId(String staffExtId, String deleteFlag);

     /**
      * 根据人员id查询人员信息视图对象
      *
      * @param userId 钉钉用户人员id
      * @return 人员信息视图对象
      */
     SysStaffExtVO findInfoVoByDingUserId(String userId);

     /**
      * 根据时间查询租户下用户拓展数据
      *
      * @param vo 查询条件
      * @return 用户集合
      */
     List<SysStaffExtPO> selectStaffExtList(SysTransDateVO vo);

     /**
      * 根据staffId更新用户dingUserId
      *
      * @param sysStaffExtPO
      * @return
      */
     int updateDingUserId(SysStaffExtPO sysStaffExtPO);
}
