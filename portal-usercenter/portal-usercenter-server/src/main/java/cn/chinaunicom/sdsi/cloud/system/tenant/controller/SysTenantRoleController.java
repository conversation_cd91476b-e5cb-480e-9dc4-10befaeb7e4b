package cn.chinaunicom.sdsi.cloud.system.tenant.controller;

import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleTenantVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import cn.chinaunicom.sdsi.cloud.system.role.service.SysRoleTenantService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>项目名称: unified-portal-mysql </p>
 * <p>文件名称: SysTenantRoleController </p>
 * <p>描述: 租户-角色 </p>
 * <p>创建时间: 2021/11/29 13:59</p>
 *
 * <AUTHOR>
 */
@Tag(name = "租户-角色接口")
@Slf4j
@RestController
@RequestMapping("/tenantRole")
public class SysTenantRoleController extends BaseController {
  @Resource
  private SysRoleTenantService sysRoleTenantService;

  @Operation(summary = "查看某个租户权限", description ="查看某个租户权限")
  @GetMapping(value = "/get")
  public BaseResponse<SysRoleVO> getHadRolesAndNoRoles(SysRoleTenantVO sysRoleTenantVO) {
    return ok(sysRoleTenantService.getHadRolesAndNoRoles(sysRoleTenantVO.getTenantId()));
  }

  @Operation(summary = "修改租户角色", description ="修改租户角色")
  @PostMapping(value = "/update/{tenantId}/{roleId}")
  public BaseResponse<Integer> updateRoleTenantByTenantId(@PathVariable("tenantId") String tenantId,
      @PathVariable("roleId") String roleId) {
    return ok(sysRoleTenantService.updateRoleTenantByTenantId(tenantId, roleId));
  }

  @Operation(summary = "删除租户权限", description ="删除租户权限")
  @PostMapping(value = "/delete/{tenantId}/{roleId}")
  public BaseResponse<Integer> deleteRoleTenantByTenantId(@PathVariable("tenantId") String tenantId,
      @PathVariable("roleId") String roleId) {
    return ok(sysRoleTenantService.deleteRoleTenant(tenantId, roleId));
  }
}