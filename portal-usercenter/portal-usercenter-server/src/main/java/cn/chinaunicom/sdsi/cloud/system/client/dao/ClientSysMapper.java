package cn.chinaunicom.sdsi.cloud.system.client.dao;

import cn.chinaunicom.sdsi.cloud.system.client.entity.SysClientPO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientPublicVO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientQueryVO;
import cn.chinaunicom.sdsi.cloud.system.client.vo.SysClientVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ClientSysMapper extends BaseMapper<SysClientPO> {

    /**
     * 查询系统配置的分页列表
     *
     * @param page             分页参数对象
     * @param sysClientQueryVO 查询条件参数对象
     * @return 客户端参数视图实体分页集合
     */
    IPage<SysClientVO> findSysClientByVo(@Param("page") IPage<SysClientVO> page, @Param("vo") SysClientQueryVO sysClientQueryVO);

    List<SysClientPublicVO> selectClientByInfo(@Param("sysClientQueryVO") SysClientQueryVO sysClientQueryVO);
}
