package cn.chinaunicom.sdsi.cloud.system.dict.dao;

import cn.chinaunicom.sdsi.cloud.system.dict.entity.SysDictDataPO;
import cn.chinaunicom.sdsi.cloud.system.dict.vo.SysDictDataVO;
import cn.chinaunicom.sdsi.cloud.system.dict.vo.SysDictDateVO;
import cn.chinaunicom.sdsi.cloud.system.dict.vo.SysDictQueryVO;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据字典功能 - 字典数据(子字典)DAO层
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/14
 * -
 * @description 内容描述
 */
@Mapper
public interface SysDictDataMapper extends BaseMapper<SysDictDataPO> {

    /**
     * 根据分页信息查询字典数据列表
     * 支持根据 dictCode 字典值精准匹配查询,参数为空时不进行过滤
     * 支持根据 dictLabel 字典标签模糊匹配查询,参数为空时不进行过滤
     *      *
     * @param queryVO 分页查询视图对象
     * @return 携带分页信息的字典数据列表
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    IPage<SysDictDataVO> selectDictDataPage(@Param("page") IPage<SysDictDataVO> page, @Param("query") SysDictQueryVO queryVO);

    /**
     * 根据条件查询字典数据列表
     * 支持根据 startTime 开始时间和 endTime 结束时间范围匹配 UPDATE_DATE 字段进行查询
     *
     * @param queryVO 数据同步查询视图对象
     * @return 查询到的字典数据列表
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    List<SysDictDataPO> selectDictDataList(@Param("query") SysDictDateVO queryVO);

    /**
     * 根据主键 dictDataId 查询字典数据项
     * 查询无任何过滤条件
     *
     * @param id 主键 dictDataId
     * @return 查询到的字典数据对象
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    SysDictDataPO selectDictDataById(@Param("id") String id);
}
