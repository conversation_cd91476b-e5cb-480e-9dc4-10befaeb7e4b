package cn.chinaunicom.sdsi.cloud.system.staff.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.system.config.service.SysConfigService;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigVO;
import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum.SysDataValid;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum.SystemStaffKindEnum;
import cn.chinaunicom.sdsi.cloud.system.feign.WoSmsFeignClient;
import cn.chinaunicom.sdsi.cloud.system.role.dao.SysRoleMapper;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePO;
import cn.chinaunicom.sdsi.cloud.system.staff.dao.SysStaffMapper;
import cn.chinaunicom.sdsi.cloud.system.staff.dao.SysStaffOrgMapper;
import cn.chinaunicom.sdsi.cloud.system.staff.dao.SysStaffRoleMapper;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffOrgPO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffPO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffRolePO;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffOrgService;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffRoleService;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffService;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.*;
import cn.chinaunicom.sdsi.cloud.system.staffext.entity.SysStaffExtPO;
import cn.chinaunicom.sdsi.cloud.system.staffext.service.SysStaffExtService;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantVO;
import cn.chinaunicom.sdsi.cloud.system.tenant.service.SysTenantService;
import cn.chinaunicom.sdsi.component.recorder.UniRecorder;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.enums.OperationEnum;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.chinaunicom.sdsi.security.util.PasswordChecker;
import cn.chinaunicom.sdsi.security.util.Sm2Encryptor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 系统人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-01-17
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Slf4j
@Service
public class SysStaffServiceImpl extends ServiceImpl<SysStaffMapper, SysStaffPO> implements SysStaffService {

    public static final String WEAK_PIN_MESSAGE = "密码不符合规范，请修改为高强度密码";
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    private SysStaffOrgMapper sysStaffOrgMapper;
    @Resource
    private SysConfigService sysConfigService;
    @Resource
    private SysStaffRoleService sysStaffRoleService;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysStaffRoleMapper sysStaffRoleMapper;
    @Resource
    private UnifastContext unifastContext;
    @Autowired
    private WoSmsFeignClient woSmsFeignClient;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private UniRecorder uniRecorder;
    @Autowired
    private SysTenantService sysTenantService;
//    @Autowired
//    private KafkaSendService kafkaSendService;
    @Autowired
    private SysStaffOrgService sysStaffOrgService;
    @Autowired
    public SysStaffExtService sysStaffExtService;

    @Value("${default_passwd_for_reset}")
    private String configCode;
    @Resource
    private Sm2Encryptor sm2Encryptor;
    @Value("${unifast.security.private-key:}")
    private String privateKey;
    @Value("${unifast.security.public-key:}")
    private String publicKey;
    private static final String REDISKEY_PREFIX_RESETPWD = "ResetPWDSmsCode:";

//    @Value("${spring.kafka.consumer.staff-topics}")
//    private String staffTopic;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addByVo(final SysStaffVO staffVO) {
        //登录账号重复校验
        if (null != checkLoginName(staffVO.getLoginName(), staffVO.getTenantId())) {
            throw new ServiceErrorException("操作失败,账号已存在！");
        }
        //手机号重复校验
        if (!StringUtils.isBlank(staffVO.getCellphone())) {
            if (checkCellphone(staffVO.getCellphone(), staffVO.getTenantId())) {
                throw new BusinessException("该手机号已被占用！");
            }
        }
        //邮箱重复校验
        if (!StringUtils.isBlank(staffVO.getEmail())) {
            if (checkEmail(staffVO.getEmail(), staffVO.getTenantId())) {
                throw new BusinessException("该邮箱已被占用！");
            }
        }
        SysStaffPO po = new SysStaffPO();
        BeanUtils.copyProperties(staffVO, po);
        po.setStaffStatus(UnifastConstants.STR_VALID);
        if (StringUtils.isEmpty(po.getPasswd())) {
            //查询配置
            SysConfigVO sysConfigVO = sysConfigService.findConfigByCode(configCode, null);
            if (sysConfigVO != null) {
                po.setPasswd(sysConfigVO.getConfigValue());
            }
        }
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String newPwd = encoder.encode(po.getPasswd());
        po.setPasswd(newPwd);
        if (staffVO.getUserPwdFlag() != null) {
            po.setUserPwdFlag(staffVO.getUserPwdFlag());
        } else {
            po.setUserPwdFlag(1);
        }
        if (null != unifastContext.getUser()) {
            if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                    unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
                String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
                po.setTenantId(tenantId);
            }
        }
        // 校验当前新增人员所属租户的用户数量
        SysTenantVO sysTenantVO = sysTenantService.findOne(po.getTenantId());
        if (Objects.nonNull(sysTenantVO)) {
            int count = this.count(new QueryWrapper<SysStaffPO>().lambda()
                    .eq(SysStaffPO::getTenantId, po.getTenantId()));
            if (count > sysTenantVO.getMaxStaff()) {
                throw new BusinessException("当前租户下用户数量已超过最大数量，禁止添加！");
            }
        }
        this.baseMapper.insert(po);
        //添加用户，推送kafka
        sendStaffKafkaMessage(OperationEnum.CREATE.getCode(),po.getStaffId());
        // 设置人员主岗信息：人员ID、组织ID、默认主岗
        SysStaffOrgPO staffOrgPo = new SysStaffOrgPO();
        staffOrgPo.setStaffId(po.getStaffId());
        staffOrgPo.setOrgId(po.getOrgId());
        staffOrgPo.setStaffOrgType(UnifastEnum.StaffOrgType.FULLTIME.value());
        staffOrgPo.setStaffOrgStatus(SysDataValid.VALID.value());
        sysStaffOrgMapper.insert(staffOrgPo);
        //添加人员岗位，推送kafka
        sysStaffOrgService.sendStaffOrgKafkaMessage(OperationEnum.CREATE.getCode(),staffOrgPo.getStaffOrgId());
        // 设置人员角色
        SysRolePO defaultRole;
        if (SystemStaffKindEnum.CUSTOMER.getKey().equals(staffVO.getStaffKind())) {
            defaultRole = sysRoleMapper.selectOne(new QueryWrapper<SysRolePO>().lambda()
                .eq(SysRolePO::getRoleName, "客户")
                .eq(SysRolePO::getTenantId, UnifastConstants.DEFAULT_TENANT_ID));
        } else {
            defaultRole = sysRoleMapper.selectOne(new QueryWrapper<SysRolePO>().lambda()
                .eq(SysRolePO::getRoleId, "default_role")
                .eq(SysRolePO::getTenantId, UnifastConstants.DEFAULT_TENANT_ID));
        }
        if (Objects.nonNull(defaultRole)) {
            SysStaffRolePO sysStaffRolePO = new SysStaffRolePO();
            sysStaffRolePO.setRoleId(defaultRole.getRoleId());
            sysStaffRolePO.setStaffOrgId(staffOrgPo.getStaffOrgId());
            int num = sysStaffRoleMapper.insert(sysStaffRolePO);
            //添加默认人员角色，推送kafka
            sysStaffRoleService.sendStaffRoleKafkaMessage(OperationEnum.CREATE.getCode(),
                    sysStaffRolePO.getStaffOrgId(),po.getTenantId());
        }
        // 保存人员扩展信息
        SysStaffExtVO sysStaffExtVO = new SysStaffExtVO();
        sysStaffExtVO.setStaffId(po.getStaffId());
        sysStaffExtService.addSysStaffExt(sysStaffExtVO);
        //插入操作日志--新增
        MallUser user = unifastContext.getUser();
        if (null != user) {
            uniRecorder.record(user.getStaffId(), "addStaff", po.getStaffId(), user.getTenantId(), OperationEnum.CREATE.getCode(),
                    null, po);
        }
        return po.getStaffId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteById(final String staffId) {
        checkStaffId(staffId);
        this.sysStaffOrgMapper.delete(new QueryWrapper<SysStaffOrgPO>().lambda().eq(SysStaffOrgPO::getStaffId,
                staffId));
        this.sysStaffExtService.getBaseMapper()
            .delete(new QueryWrapper<SysStaffExtPO>().lambda().eq(SysStaffExtPO::getStaffId,
                staffId));
        sysStaffRoleService.deleteStaffRoleByStaffId(staffId);
        SysStaffInfoVO vo = this.baseMapper.findInfoVoById(staffId);
        int num = this.baseMapper.deleteById(staffId);
        //删除用户，推送kafka数据，从表数据需要自己删除
        sendStaffKafkaMessage(OperationEnum.DELETE.getCode(),staffId);
        MallUser user = unifastContext.getUser();
        //插入操作日志--删除
        if(user!=null){
            uniRecorder.record(user.getStaffId(), "deleteStaff", vo.getStaffId(), user.getTenantId(), OperationEnum.DELETE.getCode(),
                    vo, null);
        }

        return num;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer updateByVo(final SysStaffVO staffVO) {
        final String staffId = staffVO.getStaffId();
        checkStaffId(staffId);
//        if (!StringUtils.isBlank(staffVO.getCellphone())) {
//            if (checkUserCellphone(staffVO.getCellphone(), staffVO.getStaffId(), staffVO.getTenantId())) {
//                throw new BusinessException("该手机号已被占用！");
//            }
//        }
//        if (!StringUtils.isBlank(staffVO.getEmail())) {
//            if (checkUserEmail(staffVO.getEmail(), staffVO.getStaffId(), staffVO.getTenantId())) {
//                throw new BusinessException("该邮箱已被占用！");
//            }
//        }
        SysStaffInfoVO oldVo = this.baseMapper.findInfoVoById(staffId);
        SysStaffPO po = new SysStaffPO();
        BeanUtils.copyProperties(staffVO, po);
        int num = this.baseMapper.updateById(po);
        if (StringUtils.isNotBlank(staffVO.getOrgId())) {
            SysStaffOrgPO staffOrgPO = new SysStaffOrgPO();
            staffOrgPO.setOrgId(staffVO.getOrgId());
            this.sysStaffOrgMapper.update(staffOrgPO,
                new UpdateWrapper<SysStaffOrgPO>().lambda().eq(SysStaffOrgPO::getStaffId, staffId).eq(
                    SysStaffOrgPO::getStaffOrgType, UnifastEnum.StaffOrgType.FULLTIME.value()));
            //查询主岗
            SysStaffOrgPO ssp = this.sysStaffOrgMapper.selectOne(new QueryWrapper<SysStaffOrgPO>().lambda()
                    .eq(SysStaffOrgPO::getStaffId, staffId)
                    .eq(SysStaffOrgPO::getStaffOrgType,UnifastEnum.StaffOrgType.FULLTIME.value()));
            //更改人员岗位，推送kafka
            if(ssp!=null){
                sysStaffOrgService.sendStaffOrgKafkaMessage(OperationEnum.UPDATE.getCode(),ssp.getStaffOrgId());        }
            }

        //编辑用户，推送kafka数据
        sendStaffKafkaMessage(OperationEnum.UPDATE.getCode(),staffId);
        SysStaffInfoVO newVo = this.baseMapper.findInfoVoById(staffId);
        MallUser user = unifastContext.getUser();
        if(user!=null){
            uniRecorder.record(user.getStaffId(), "updateStaff", oldVo.getStaffId(), user.getTenantId(), OperationEnum.UPDATE.getCode(),
                    oldVo, newVo);
        }

        return num;
    }

    /**
     * 清除登录账号缓存
     *
     * @param loginName
     */
    @Override
    @CacheEvict(value = "SysStaffServiceImpl.selectByLoginName.#{7200}", allEntries = true)
    public void cleanCache(String loginName) {
        //do nothing
    }

    @Override
    public SysStaffInfoVO findUserInfo(String loginName) {
        SysStaffInfoVO sv = this.baseMapper.findInfoVoByLoginName(loginName);
        return sv;
    }

    @Override
    public List<SysStaffInfoVO> getUserInfoByMobile(String mobile) {
        return this.baseMapper.getUserInfoByMobile(mobile);
    }

    @Override
    public List<SysStaffInfoVO> findInfoListByCellPhone(String cellPhone) {
        return this.baseMapper.findInfoListByCellPhone(cellPhone);
    }
    @Override
    public SysStaffInfoVO getUserInfoByMobileAndTenant(String mobile, String tenantId) {
        return this.baseMapper.getUserInfoByMobileAndTenant(mobile, tenantId);
    }

    @Override
    public int updateUserPwd(SysStaffPdVO vo, SysStaffPO po) {
        String msg = "更改密码失败";
        if (StringUtils.isBlank(vo.getOldPassword())) {
            throw new BusinessException(msg = "旧密码不能为空");
        }
        if (StringUtils.isBlank(vo.getNewPassword())) {
            throw new BusinessException(msg = "新密码不能为空");
        }
        String newPassword = vo.getNewPassword();
        try {
            //获取旧密码

            final String oldPassword = sm2Encryptor.decryptString(privateKey, vo.getOldPassword());
            //检测旧密码是否正确
            if (!passwordEncoder.matches(oldPassword, po.getPasswd())) {
                throw new BusinessException(msg = "旧密码不正确");
            }
            //获取新密码
            newPassword = sm2Encryptor.decryptString(privateKey, vo.getNewPassword());
            final PasswordChecker.LEVEL level = PasswordChecker.checkPassStrength(newPassword);
            if (level.getScore() < PasswordChecker.LEVEL.MEDIUM.getScore()) {
                throw new BusinessException(msg = "输入的新密码是弱口令，请重新设置新密码！");
            }
            if (oldPassword.equals(newPassword)) {
                throw new BusinessException(msg = "新密码不能与当前密码相同，请重新输入");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(msg);
        }
        SysStaffInfoVO oldVo = this.baseMapper.findInfoVoById(unifastContext.getUser().getStaffId());
        int count = updatePasswdById(unifastContext.getUser().getStaffId(), newPassword);
        SysStaffInfoVO newVo = this.baseMapper.findInfoVoById(unifastContext.getUser().getStaffId());
        MallUser user = unifastContext.getUser();
        uniRecorder.record(user.getStaffId(), "updateStaff", oldVo.getStaffId(), user.getTenantId(), OperationEnum.UPDATE.getCode(),
                oldVo, newVo);
        return count;
    }

    @Override
    public String getVerificationCode(SysStaffVO vo) {
        SysStaffPO sysStaffPO = new SysStaffPO();
        if ("1".equals(vo.getGetVerificationCodeType())) {
            // 1：忘记密码时的获取验证码
            sysStaffPO = this.baseMapper.selectOne(new QueryWrapper<SysStaffPO>().lambda()
                    .eq(SysStaffPO::getLoginName, vo.getLoginName())
                    .eq(SysStaffPO::getCellphone, vo.getCellphone()));
            if (Objects.isNull(sysStaffPO)) {
                throw new BusinessException("当前用户登录名与手机号不匹配，请重新输入！");
            }
        } else if ("2".equals(vo.getGetVerificationCodeType())) {
            // 2：个人中心绑定手机号时获取的验证码
            sysStaffPO = this.baseMapper.selectOne(new QueryWrapper<SysStaffPO>().lambda()
                    .eq(SysStaffPO::getCellphone, vo.getCellphone()));
            if (Objects.nonNull(sysStaffPO)) {
                throw new BusinessException("当前手机号已绑定用户" + sysStaffPO.getStaffName() + "，请重新输入！");
            } else {
                sysStaffPO = new SysStaffPO();
                sysStaffPO.setCellphone(vo.getCellphone());
            }
        }
        String verificationCode = "";
        if (StringUtils.isNotBlank(sysStaffPO.getCellphone())) {
            String key = "1@bVS46ElU@" + sysStaffPO.getCellphone();
            Sm2Encryptor tool = new Sm2Encryptor();
            try {
                final String authkey = tool.encryptString(key, publicKey);
                BaseResponse<JSONObject> res = woSmsFeignClient.sendSms(authkey, "", "");
                if (res != null) {
                    JSONObject jsonObject = res.getData();
                    if ("发送成功".equals(jsonObject.getString("resultmsg"))) {
                        verificationCode = redisUtils.get(REDISKEY_PREFIX_RESETPWD + vo.getCellphone()).toString();
                    } else {
                        throw new BusinessException("获取验证码失败！" + jsonObject.getString("resultmsg"));
                    }
                } else {
                    throw new BusinessException("短信服务异常！");
                }
            } catch (InvalidCipherTextException e) {
                e.printStackTrace();
            }
        }
        return verificationCode;
    }

    /**
     * <b>根据微信全局唯一标识查询用户信息条数</b><br>
     * <b>描述: </b>
     * 根据微信全局唯一标识查询用户信息条数
     *
     * <AUTHOR>
     * @date 2022-04-13
     *
     * @param unionId 用户微信全局唯一标识
     * @return {@link Integer} 查询到的用户信息条数
     */
    public Integer findNumByUnionId(String unionId) {
        Assert.notNull(unionId, "unionId is null!");
        List<SysStaffInfoVO> sysStaffInfoVOs = baseMapper.findByUnionId(unionId, null);
        return sysStaffInfoVOs == null ? 0 : sysStaffInfoVOs.size();
    }

    /**
     * 根据微信全局唯一标识查询用户信息详情
     *
     * @param unionId 用户微信全局唯一标识
     * @param tenantId 租户id
     * @return 查询到的用户信息条数
     * <AUTHOR>
     * -
     * @date 2022/2/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Override
    public SysStaffInfoVO findByUnionId(String unionId, String tenantId) {
        Assert.notNull(unionId, "unionId is null!");
        List<SysStaffInfoVO> sysStaffInfoVO = baseMapper.findByUnionId(unionId, tenantId);
        if (sysStaffInfoVO == null || sysStaffInfoVO.size() == 0) {
            throw new BusinessException("未查询到unionId绑定的对应账号");
        }
        return sysStaffInfoVO.get(0);
    }

    /**
     * <b>微信绑定发送短信验证码</b><br>
     * <b>描述: </b>
     * 微信绑定发送短信验证码
     *
     * <AUTHOR>
     * @date 2022-04-13
     *
     * @param mobile 接收验证码的手机号
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     */
    @Override
    public void getCaptchaByWechatBind(String mobile) throws BusinessException {
        Assert.notNull(mobile, "mobile is null!");
        try {
            mobile = sm2Encryptor.decryptString(privateKey, mobile);
        } catch (InvalidCipherTextException e) {
            throw new BusinessException("手机号解密失败，请确认参数合法性！");
        }
        Integer count = this.baseMapper.selectCount(new QueryWrapper<SysStaffPO>().lambda()
                .eq(SysStaffPO::getDeleteFlag, "normal")
                .eq(SysStaffPO::getCellphone, mobile));
        if (count == 0) {
            throw new BusinessException("当前手机号没查询到匹配的用户，请重新输入！");
        }
        String key = "3@bVS46ElU@" + mobile;
        try {
            Sm2Encryptor tool = new Sm2Encryptor();
            final String authkey = tool.encryptString(key, publicKey);
            BaseResponse<JSONObject> res = woSmsFeignClient.sendSms(authkey, "", "");
            if (res != null) {
                JSONObject jsonObject = res.getData();
                if (!"发送成功".equals(jsonObject.getString("resultmsg"))) {
                    throw new BusinessException("获取验证码失败！" + jsonObject.getString("resultmsg"));
                }
            } else {
                throw new BusinessException("短信服务异常！");
            }
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
        }
    }

    /**
     * <b>微信公众号 - 根据用户手机号绑定微信unionid</b><br>
     * <b>描述: </b>
     * 微信公众号 - 根据用户手机号绑定微信unionid
     *
     * <AUTHOR>
     * @date 2022-04-08
     *
     * @param mobile 绑定手机号
     * @param smscode 短信验证码
     * @param unionid 用户微信全局唯一标识
     * @param openid 用户微信公众号id
     * @param tenantId 租户id
     * @return {@link List<SysStaffVO>} 如果手机号对应多个租户下的用户时,返回这些用户列表
     */
    @Override
    public List<SysStaffVO> wechatBind(String mobile, String smscode, String unionid, String openid, String tenantId) throws BusinessException {
        Assert.notNull(mobile, "cellphone is null!");
        Assert.notNull(smscode, "smscode is null!");
        Assert.notNull(unionid, "unionid is null!");
        // 校验手机号合法性
        try {
            mobile = sm2Encryptor.decryptString(privateKey, mobile);
        } catch (InvalidCipherTextException e) {
            throw new BusinessException("手机号解密失败，请确认参数合法性！");
        }

        // 校验验证码合法性
        String redisKey = woSmsFeignClient.getSmsTypeRedisKeyPrefixMap("3") + mobile;
        String smsCodeInRedis = (String) redisUtils.get(redisKey);
        if (smsCodeInRedis == null || !smsCodeInRedis.equals(smscode)) {
            throw new BusinessException("短信验证码不正确！");
        }

        // 手机号有效性校验
        SysStaffQueryVO staffQueryVO = new SysStaffQueryVO();
        staffQueryVO.setDeleteFlag("normal");
        staffQueryVO.setCellphone(mobile);
        staffQueryVO.setTenantId(tenantId);
        List<SysStaffVO> sysStaffVOs = this.baseMapper.selectStaffAndStaffExtList(staffQueryVO);
        if (sysStaffVOs == null || sysStaffVOs.size() == 0) {
            // 如果手机号对应一个租户
            throw new BusinessException("当前手机号没查询到匹配的用户，请重新输入！");
        } else if (sysStaffVOs.size() > 1) {
            return sysStaffVOs;
        }

        // 更新unionid
        SysStaffExtVO vo = new SysStaffExtVO();
        vo.setStaffId(sysStaffVOs.get(0).getStaffId());
        vo.setUnionId(unionid);
        if (StringUtils.isNotBlank(openid)) vo.setWechatOpenId(openid);
        sysStaffExtService.saveOrUpdateByStaffId(vo);
        return sysStaffVOs;
    }

    /**
     * <b>微信小程序 - 根据用户手机号绑定微信unionid</b><br>
     * <b>描述: </b>
     * 微信小程序 - 根据用户手机号绑定微信unionid
     *
     * <AUTHOR>
     * @date 2022-04-06
     *
     * @param mobile 绑定手机号
     * @param unionid 用户微信全局唯一标识
     * @param tenantId 租户id
     * @return {@link List<SysStaffVO>} 如果手机号对应多个租户下的用户时,返回这些用户列表
     */
    @Override
    public List<SysStaffVO> wechatBindMp(String mobile, String unionid, String tenantId) throws BusinessException {
        Assert.notNull(mobile, "cellphone is null!");
        Assert.notNull(unionid, "unionid is null!");

        // 手机号有效性校验
        SysStaffQueryVO staffQueryVO = new SysStaffQueryVO();
        staffQueryVO.setDeleteFlag("normal");
        staffQueryVO.setCellphone(mobile);
        staffQueryVO.setTenantId(tenantId);
        List<SysStaffVO> sysStaffVOs = this.baseMapper.selectStaffAndStaffExtList(staffQueryVO);
        if (sysStaffVOs == null || sysStaffVOs.size() == 0) {
            // 如果手机号对应一个租户
            throw new BusinessException("当前手机号没查询到匹配的用户，请重新输入！");
        } else if (sysStaffVOs.size() > 1) {
            return sysStaffVOs;
        }

        // 更新unionid
        SysStaffExtVO vo = new SysStaffExtVO();
        vo.setStaffId(sysStaffVOs.get(0).getStaffId());
        vo.setUnionId(unionid);
        sysStaffExtService.saveOrUpdateByStaffId(vo);
        return sysStaffVOs;
    }

    @Override
    public Integer forgottenPassword(SysStaffVO vo) {
        String msg = "更改密码失败";
        SysStaffPO pwdEntity = new SysStaffPO();
        SysStaffInfoVO old = this.findUserInfo(vo.getLoginName());
        String verificationCode = redisUtils.get(REDISKEY_PREFIX_RESETPWD + vo.getCellphone()).toString();
        if (!vo.getVerificationCode().equals(verificationCode)) {
            throw new BusinessException(msg = "验证码错误，请重新输入");
        } else {
            if (StringUtils.isBlank(vo.getNewPassWord())) {
                throw new BusinessException(msg = "密码不能为空！");
            } else {
                try {
                    SysStaffPO sysStaffPO = this.baseMapper.selectOne(new QueryWrapper<SysStaffPO>().lambda()
                            .eq(SysStaffPO::getLoginName, vo.getLoginName())
                            .eq(SysStaffPO::getCellphone, vo.getCellphone()));
                    // 解密前端输入的新密码-SM2
                    String newPassword = sm2Encryptor.decryptString(privateKey, vo.getNewPassWord());
                    final PasswordChecker.LEVEL level = PasswordChecker.checkPassStrength(newPassword);
                    if (level.getScore() < PasswordChecker.LEVEL.MEDIUM.getScore()) {
                        throw new BusinessException(msg = "输入的新密码是弱口令，请重新设置新密码！");
                    }
                    // 比较新密码和旧密码
                    if (passwordEncoder.matches(newPassword, sysStaffPO.getPasswd())) {
                        throw new BusinessException(msg = "新密码不能与当前密码相同，请重新输入！");
                    }
                    // Bcrypt加密后的新密码，实际要存入到数据库的
                    BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
                    pwdEntity.setStaffId(sysStaffPO.getStaffId());
                    pwdEntity.setPasswd(encoder.encode(newPassword));
                } catch (Exception e) {
                    log.error("更改密码失败：{" + e.getMessage() + "}");
                    throw new BusinessException(msg);
                }
            }
        }
        int num = this.baseMapper.updateById(pwdEntity);
        //编辑用户密码，推送kafka数据
        sendStaffKafkaMessage(OperationEnum.UPDATE.getCode(),old.getStaffId());
        //插入操作日志--修改
        SysStaffInfoVO newPo = this.findUserInfo(vo.getLoginName());
        uniRecorder.record(pwdEntity.getStaffId(), newPo.getStaffKind(), pwdEntity.getStaffId(), pwdEntity.getTenantId(), OperationEnum.UPDATE.getCode(),
                old, newPo);
        return num;
    }

    /**
     * saas登录使用
     *
     * @param sysStaffVO
     * @return
     */
    @Override
    public SysStaffVO checkLoginName4SaaS(SysStaffVO sysStaffVO) {
        return this.baseMapper.checkLoginName4SaaS(sysStaffVO);
    }

    @Override
    public List<SysStaffPO> selectStaffList(SysTransDateVO vo) {
        return this.baseMapper.selectStaffList(vo);
    }

    @Override
    public IPage<SysStaffOrgVO> getCustomerPageList(SysStaffOrgQueryVO queryVO) {
        IPage<SysStaffOrgVO> page = QueryVoToPageUtil.toPage(queryVO);
        return this.baseMapper.getCustomerPageList(page, queryVO);
    }

    @Override
    public List<SysStaffVO> getAllUser() {
        return this.baseMapper.findAllUser();
    }


    /**
     * 更改当前激活的岗位，刷新缓存
     *
     * @param staffOrgId
     * @param loginName
     * @return SysStaffVO
     * <AUTHOR>
     * @date 2018-09-29
     */
    @Override
    @CachePut(value = "SysStaffServiceImpl.selectByLoginName.#{7200}", key = "#loginName")
    public SysStaffVO changStation(final String staffOrgId, String loginName) {
        SysStaffVO vo = this.selectByLoginName(loginName);
        vo.setStaffOrgId(staffOrgId);
        return vo;
    }

    /**
     * 根据账号查询人员信息
     * 添加缓存信息
     *
     * @param loginName
     * @return SysStaffVO
     */
    @Override
    @Cacheable(value = "SysStaffServiceImpl.selectByLoginName.#{7200}", key = "#loginName")
    public SysStaffVO selectByLoginName(String loginName) {
        SysStaffVO sysStaffVO = baseMapper.findVoByLoginName(loginName);
        if (sysStaffVO == null) {
            throw new ServiceErrorException("未找到账号");
        }
        return sysStaffVO;
    }
    /**
     * 根据登录名，岗位类型，部门code查询人员详情
     *
     * @param sysStaffVOParams
     * @return 人员视图对象
     */
    @Override
    public SysStaffVO select4sysStaffSync(SysStaffVO sysStaffVOParams) {

        List<SysStaffVO> sysStaffVOList =  baseMapper.select4sysStaffSync(sysStaffVOParams);
        if (sysStaffVOList == null || sysStaffVOList.size() == 0) {
           return null;
        }else{
            return   sysStaffVOList.get(0);
        }
    }

    @Override
    public SysStaffInfoVO findInfoVoById(final String staffId) {
        SysStaffInfoVO sysStaffInfoVO= this.baseMapper.findInfoVoById(staffId);
        sysStaffInfoVO.setCellphone(org.apache.commons.lang.StringUtils.overlay(sysStaffInfoVO.getCellphone(), "****", 3, 7));
        return sysStaffInfoVO;
    }

    @Override
    public SysStaffVO checkLoginName(final String loginName, String tenantId) {
        SysStaffVO sysStaffVO = new SysStaffVO();
        if (null != unifastContext.getUser()) {
            // 如果用户登录了
            if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                    unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
                // 如果是普通的租户
                tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
                sysStaffVO.setTenantId(tenantId);
            } else {
                // 如果是system租户
                sysStaffVO.setTenantId(tenantId);
            }
        } else {
            // 如果未登录,则根据入参租户id进行校验
            sysStaffVO.setTenantId(tenantId);
        }
        sysStaffVO.setLoginName(loginName);
        return baseMapper.findVo(sysStaffVO);
    }

    @Override
    public Boolean checkCellphone(String cellphone, String tenantId) {
        if (tenantId == null) {
            return this.count(
                    new QueryWrapper<SysStaffPO>().lambda().eq(SysStaffPO::getCellphone, cellphone)) > 0;
        } else {
            return this.count(
                    new QueryWrapper<SysStaffPO>().lambda().eq(SysStaffPO::getCellphone, cellphone)
                            .eq(SysStaffPO::getTenantId, tenantId)) > 0;
        }
    }

    public Boolean checkUserCellphone(String cellphone, String staffId, String tenantId) {
        return this.count(new QueryWrapper<SysStaffPO>().lambda().eq(SysStaffPO::getCellphone, cellphone).ne(SysStaffPO::getStaffId, staffId).eq(SysStaffPO::getTenantId, tenantId)) > 0;
    }

    public Boolean checkEmail(String email, String tenantId) {
        return this.count(new QueryWrapper<SysStaffPO>().lambda().eq(SysStaffPO::getEmail, email)
                .eq(SysStaffPO::getTenantId, tenantId)) > 0;
    }

    public Boolean checkUserEmail(String email, String staffId, String tenantId) {
        return this.count(new QueryWrapper<SysStaffPO>().lambda().eq(SysStaffPO::getEmail, email)
                .ne(SysStaffPO::getStaffId, staffId).eq(SysStaffPO::getTenantId, tenantId)) > 0;
    }

    @Override
    public Integer updatePasswdById(final String staffId, final String passwd) {
        int score = PasswordChecker.checkPassStrength(passwd).getScore();
        if (score < PasswordChecker.LEVEL.STRONG.getScore()) {
            throw new ServiceErrorException(WEAK_PIN_MESSAGE);
        }
        SysStaffPO po = new SysStaffPO();
        po.setStaffId(staffId);
        po.setPasswd(passwordEncoder.encode(passwd));
        po.setUserPwdFlag(0);
        SysStaffInfoVO oldVo = this.baseMapper.findInfoVoById(staffId);
        int count = this.baseMapper.updateById(po);
        SysStaffInfoVO newVo = this.baseMapper.findInfoVoById(staffId);
        MallUser user = unifastContext.getUser();
        //编辑用户，推送kafka数据
        sendStaffKafkaMessage(OperationEnum.UPDATE.getCode(),staffId);
        if(user!=null){
            uniRecorder.record(user.getStaffId(), "updateStaff", oldVo.getStaffId(), user.getTenantId(), OperationEnum.UPDATE.getCode(),
                    oldVo, newVo);
        }

        return count;
    }

    @Override
    public String resetPasswd(String staffId, String newPassWord) {
//        SysConfigVO config =
//                this.sysConfigService.findConfigByCode(UnifastEnum.SystemConfigCode.CODE_DEFAULT_PWD.value(),
//                        null);
//        if (null == config) {
//            throw new ServiceErrorException("未查询到系统默认密码");
//        }
        updatePasswdById(staffId, newPassWord);
        return "success";
    }

    /**
     * 校验参数中的staffId
     *
     * @param staffId
     */
    private void checkStaffId(final String staffId) {
        if (null == staffId) {
            throw new ServiceErrorException("staffId不能为空", HttpStatus.BAD_REQUEST);
        }
        int count = this.count(new QueryWrapper<SysStaffPO>().lambda().eq(SysStaffPO::getStaffId, staffId));
        if (count == 0) {
            throw new ServiceErrorException(UnifastConstants.ERROR_NOT_FOUND, HttpStatus.NOT_FOUND);
        }
    }

    /**
     * 添加用户kafka消息
     * */
    private void sendStaffKafkaMessage(String key,String staffId){
        SysStaffPO vo =  this.baseMapper.findStaffPoById(staffId);
        if (null != vo && StringUtils.isNotEmpty(vo.getTenantId())) {
       //     kafkaSendService.sendKafkaMessage(key,staffTopic,vo.getTenantId(),vo);
        }
    }
}
