package cn.chinaunicom.sdsi.cloud.system.staff.service;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffPO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffInfoVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffPdVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 系统人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2019-01-17
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
public interface SysStaffService extends IService<SysStaffPO> {

    /**
     * 新增人员
     *
     * @param staffVO 人员视图对象
     * @return 主键
     */
    String addByVo(final SysStaffVO staffVO);

    /**
     * 根据id 删除人员，同时删除岗位、删除岗位角色关联数据
     *
     * @param staffId
     * @return
     */
    Integer deleteById(final String staffId);

    /**
     * 编辑人员，密码不为空时则更新密码
     *
     * @param staffVO 人员视图对象
     * @return 影响条数
     */
    Integer updateByVo(final SysStaffVO staffVO);

    /**
     * 变更岗位
     *
     * @param staffOrgId 切换至岗位id
     * @param loginName 登录名
     * @return 人员视图对象
     */
    SysStaffVO changStation(String staffOrgId, String loginName);

    /**
     * 根据登录名查询人员详情
     *
     * @param loginName 登录名
     * @return 人员视图对象
     */
    SysStaffVO selectByLoginName(String loginName);

    /**
     * 根据登录名，岗位类型，部门code查询人员详情
     *
     * @param sysStaffVOParams
     * @return 人员视图对象
     */
    SysStaffVO select4sysStaffSync(SysStaffVO sysStaffVOParams);

    /**
     * 根据人员id查询人员信息
     *
     * @param staffId 人员id
     * @return 人员信息对象
     */
    SysStaffInfoVO findInfoVoById(final String staffId);

    /**
     * 判断登录名是否存在，存在则返回人员视图，不存在返回空的SysStaffVO对象
     *
     * @param loginName 登录名
     * @return 人员视图对象
     */
    SysStaffVO checkLoginName(final String loginName, String tenantId);

    /**
     * 判断手机是否存在，存在则返回true
     *
     * @param cellphone 手机号
     * @return boolean
     */
    Boolean checkCellphone(final String cellphone, String tenantId);

    /**
     * 修改密码
     *
     * @param staffId 人员id
     * @param passwd 新密码（未加密）
     * @return 影响条数
     */
    Integer updatePasswdById(final String staffId, final String passwd);

    /**
     * 重置密码，默认密码从sys_config中获取
     *
     * @param staffId 人员id
     * @return 返回默认密码
     */
    String resetPasswd(String staffId, String newPassWord);

    /**
     * 清除登录账号缓存
     *
     * @param loginName
     */
    void cleanCache(String loginName);

    /**
     * 查看用户对象信息
     *
     * @param loginName
     */
    SysStaffInfoVO findUserInfo(String loginName);

    /**
     * 根据手机号码获取用户信息
     *
     * @param mobile 手机号
     */
    List<SysStaffInfoVO> getUserInfoByMobile(String mobile);


    /**
     *
     * @param cellPhone 手机号
     * @return
     */
    List<SysStaffInfoVO> findInfoListByCellPhone(String cellPhone);

    /**
     * 根据手机号码、租户获取用户信息
     *
     * @param mobile 手机号
     * @param tenantId 租户，唯一键
     */
    SysStaffInfoVO getUserInfoByMobileAndTenant(String mobile, String tenantId);

    /**
     * 修改密码
     *
     * @param vo 修改参数
     * @param po 当前用户
     */
    int updateUserPwd(SysStaffPdVO vo,SysStaffPO po);

    /**
     * <b>根据微信全局唯一标识查询用户信息条数</b><br>
     * <b>描述: </b>
     * 根据微信全局唯一标识查询用户信息条数
     *
     * <AUTHOR>
     * @date 2022-04-13
     *
     * @param unionId 用户微信全局唯一标识
     * @return {@link Integer} 查询到的用户信息条数
     */
    Integer findNumByUnionId(String unionId);

    /**
     * 根据微信全局唯一标识查询用户信息详情
     *
     * @param unionId 用户微信全局唯一标识
     * @param tenantId 租户id
     * @return 查询到的用户信息详情
     * <AUTHOR>
     * -
     * @date 2022/2/17
     * -
     * @version 1.0
     * @description 内容描述
     */
    SysStaffInfoVO findByUnionId(String unionId, String tenantId);

    /**
     * 用户重置密码发送验证码
     *
     * @param vo 标签说明
     * @return 标签说明
     * <AUTHOR>
     * -
     * @date 2022/1/12
     * -
     * @version 1.0
     * @description 内容描述
     */
    String getVerificationCode(SysStaffVO vo);

    /**
     * <b>微信绑定发送短信验证码</b><br>
     * <b>描述: </b>
     * 微信绑定发送短信验证码
     *
     * <AUTHOR>
     * @date 2022-04-13
     *
     * @param mobile 接收验证码的手机号
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     */
    void getCaptchaByWechatBind(String mobile);

    /**
     * <b>根据用户手机号绑定微信unionid</b><br>
     * <b>描述: </b>微信公众号 - 根据用户手机号绑定微信unionid
     *
     * <AUTHOR>
     * @date 2022-04-06
     *
     * @param mobile 绑定手机号
     * @param smscode 短信验证码
     * @param unionid 用户微信全局唯一标识
     * @param openid 用户微信公众号id
     * @param tenantId 租户id
     * @return {@link List<SysStaffVO>} 如果手机号对应多个租户下的用户时,返回这些用户列表
     */
    List<SysStaffVO> wechatBind(String mobile, String smscode, String unionid, String openid, String tenantId);

    /**
     * <b>微信小程序 - 根据用户手机号绑定微信unionid</b><br>
     * <b>描述: </b>
     * 微信小程序 - 根据用户手机号绑定微信unionid
     *
     * <AUTHOR>
     * @date 2022-04-06
     *
     * @param mobile 绑定手机号
     * @param unionid 用户微信全局唯一标识
     * @param tenantId 租户id
     * @return {@link List<SysStaffVO>} 如果手机号对应多个租户下的用户时,返回这些用户列表
     */
    List<SysStaffVO> wechatBindMp(String mobile, String unionid, String tenantId);

    /**
     * 未登录时根据手机号、验证码重置密码.
     */
    Integer forgottenPassword(SysStaffVO vo);

    /**
     * saas登录使用
     * @return
     */
    SysStaffVO checkLoginName4SaaS(SysStaffVO sysStaffVO);

    /**
     * 根据时间查询租户下用户数据
     *
     * @param vo 查询条件
     * @return 用户集合
     */
    List<SysStaffPO> selectStaffList(SysTransDateVO vo);

    /**
     * 根据查询条件查询客户分页信息
     *
     * @param queryVO 分页参数
     * @return 客户对象
     */
    IPage<SysStaffOrgVO> getCustomerPageList(final SysStaffOrgQueryVO queryVO);

    /**
     * 查询所有用户
     * @return
     */
    List<SysStaffVO> getAllUser();
}
