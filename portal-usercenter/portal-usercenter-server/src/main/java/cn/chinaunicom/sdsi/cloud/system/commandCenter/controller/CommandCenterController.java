package cn.chinaunicom.sdsi.cloud.system.commandCenter.controller;

import cn.chinaunicom.sdsi.cloud.system.commandCenter.service.CommandCenterService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>文件名称: CommandCenterController </p>
 * <p>描述: 指挥中心-接口 </p>
 * <p>创建时间: 2022/3/23 14:23</p>
 *
 * <AUTHOR>
 */
@Tag(name = "指挥中心控制层接口")
@RestController
@RequestMapping("/commandCenter")
public class CommandCenterController extends BaseController {
  @Resource
  private CommandCenterService commandCenterService;

  @Operation(summary = "获取服务状态", description ="获取服务实态")
  @GetMapping(value = "getBasicServicesHealthStatus")
  public BaseResponse<Map<String, Object>> getServiceHealthStatus() {
    return ok(commandCenterService.getBasicServicesHealthStatus());
  }

  @Operation(summary = "获取全部服务状态", description ="获取全部服务状态")
  @GetMapping(value = "getBasicServicesHealthStatusList")
  public BaseResponse<Map<String, Object>> getAllBasicServicesHealthStatus() {
    return ok(commandCenterService.getAllBasicServicesHealthStatus());
  }

  @Operation(summary = "获取数据库状态", description ="获取数据库读写状态")
  @GetMapping(value = "getDatabaseStatus")
  public BaseResponse<Map<String, Object>> getDatabaseStatus() {
    return ok(commandCenterService.getDatabaseStatus());
  }

  @Operation(summary = "获取Redis状态", description ="获取Redis读写状态")
  @GetMapping(value = "getRedisStatus")
  public BaseResponse<Map<String, Object>> getRedisStatus() {
    return ok(commandCenterService.getRedisStatus());
  }

  @Operation(summary = "获取Kafka状态", description ="获取Kafka状态")
  @GetMapping(value = "getKafkaStatus")
  public BaseResponse<Map<String, Object>> getKafkaStatus() {
    return ok(commandCenterService.getKafkaStatus());
  }

  @Operation(summary = "获取Elasticsearch状态", description ="获取Elasticsearch状态")
  @GetMapping(value = "getElasticsearchStatus")
  public BaseResponse<Map<String, Object>> getElasticsearchStatus() {
    return ok(commandCenterService.getElasticsearchStatus());
  }

  @Operation(summary = "获取对象存储状态", description ="获取对象存储状态")
  @GetMapping(value = "getOSSStatus")
  public BaseResponse<Map<String, Object>> getOSSStatus() {
    return ok(commandCenterService.getOSSStatus());
  }
}