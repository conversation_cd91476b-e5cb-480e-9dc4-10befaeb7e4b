package cn.chinaunicom.sdsi.cloud.system.staff.service;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffOrgPO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgSaveVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 人员-组织关联表（岗位表） 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2019-01-17
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
public interface SysStaffOrgService extends IService<SysStaffOrgPO> {
    SysStaffOrgPO select4sysStaffSync(String staffId,String staffOrgType);

    /**
     * 新增岗位
     *
     * @param saveVO 岗位表单对象
     * @return 岗位id
     */
    String saveStaffOrg(final SysStaffOrgSaveVO saveVO);

    /**
     * 新增岗位-角色信息
     *
     * @param saveVO 岗位表单对象
     * @return 岗位id
     */
    String addStaffOrgAndRole(final SysStaffOrgSaveVO saveVO);

    /**
     * 删除岗位
     *
     * @param staffOrgId 岗位id
     * @return 影响条数
     */
    Integer deleteStaffOrg(final String staffOrgId);

    /**
     * 根据岗位id查询岗位视图
     *
     * @param staffOrgId 岗位id
     * @return 岗位视图
     */
    SysStaffOrgVO findStaffOrgByStaffOrgId(final String staffOrgId);

    /**
     * 根据查询条件，查询岗位列表
     *
     * @param queryVO
     * @return
     */
    List<SysStaffOrgVO> findStaffOrgs(final SysStaffOrgQueryVO queryVO);

    /**
     * 根据查询条件查询岗位分页信息
     *
     * @param queryVO 分页参数
     * @return 岗位视图分页对象
     */
    IPage<SysStaffOrgVO> findStaffPage(final SysStaffOrgQueryVO queryVO);

    /**
     * 修改岗位状态
     *
     * @param staffOrgId 岗位id
     * @param staffOrgStatus 状态值 ：valid或invalid
     * @return 影响条数
     */
    Integer updateStatusByStaffOrgId(final String staffOrgId, final String staffOrgStatus);

    /**
     * 添加用户岗位kafka消息
     *
     * @param staffOrgId 岗位id
     * @param key 操作类型
     */
     void sendStaffOrgKafkaMessage(String key,String staffOrgId);

    /**
     * 根据时间查询租户下用户组织数据
     *
     * @param vo 查询条件
     * @return 用户组织集合
     */
    List<SysStaffOrgPO> selectStaffOrgList(SysTransDateVO vo);

    /**
     * 查询组织下的钉钉userId
     *
     * @param vo 查询条件
     * @return 钉钉id集合
     */
    List<String> selectDingUserIdByQueryVO(SysStaffOrgQueryVO vo);

    /**
     * 查询租户下未绑定钉钉的用户信息
     *
     * @param tenantId 租户id
     * @return 人员信息视图对象
     */
    List<SysStaffExtVO> selectTenantDingUsers(final @Param("tenantId") String tenantId);
}
