package cn.chinaunicom.sdsi.cloud.system.permission.service.impl;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum;
//import cn.chinaunicom.sdsi.cloud.system.kafka.service.KafkaSendService;
//import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum.SysOrgType;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.constant.Constant;
import cn.chinaunicom.sdsi.framework.enums.OperationEnum;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.cloud.system.permission.dao.SysPermissionMapper;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionMenuVO;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionPO;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionTreeVO;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO;
import cn.chinaunicom.sdsi.cloud.system.permission.service.ISysPermissionService;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePermissionPO;
import cn.chinaunicom.sdsi.cloud.system.role.service.ISysRolePermissionService;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;

/**
 * <p>
 * 系统权限表（菜单、操作等） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-15
 */
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermissionPO>
        implements ISysPermissionService {

    public static final int PERMISSION_CODE_MAX_LENGTH = 512;
    @Autowired
    private ISysRolePermissionService sysRolePermissionService;
    @Autowired
    public UnifastContext unifastContext;

    private static final String PERMISSION_ID_NULL = "permissionId不能为空";


//    @Autowired
//    private KafkaSendService kafkaSendService;

    @Override
    public String addByVo(final SysPermissionVO vo) {
        SysPermissionPO po = new SysPermissionPO();
        vo.setPermissionId(null);
        BeanUtils.copyProperties(vo, po);

        SysPermissionPO parent = this.baseMapper.selectById(vo.getParentId());
        if (parent == null && !"tenant".equals(vo.getSaveType()) && !Constant.ZERO.equals(vo.getParentId())) {
            throw new ServiceErrorException("上级节点不存在");
        }
        if (StringUtils.isNotBlank(po.getCode()) && po.getCode().length() > PERMISSION_CODE_MAX_LENGTH) {
            throw new ServiceErrorException("路径长度超出范围");
        }
        if (StringUtils.isNotBlank(po.getCheckCode())) {
            Integer checkCodeCount = this.baseMapper.selectCount(
                new QueryWrapper<SysPermissionPO>().lambda()
                    .eq(SysPermissionPO::getCheckCode, po.getCheckCode()));
            if (checkCodeCount > 0 && !"tenant".equals(vo.getSaveType())) {
                throw new ServiceErrorException("权校验字符已存在，请重新输入校验字符");
            }
        }
        if (null != unifastContext.getUser()) {
            if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                    unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
                String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
                po.setTenantId(tenantId);
            }
        }
        this.save(po);
        //新增权限，推送kafka数据
        sendPremKafkaMessage(OperationEnum.CREATE.getCode(),po.getPermissionId());
        return po.getPermissionId();
    }

    @Override
    public Integer updateByVo(final SysPermissionVO vo) {
        if (null == vo.getPermissionId()) {
            throw new ServiceErrorException(PERMISSION_ID_NULL, HttpStatus.BAD_REQUEST);
        }
        SysPermissionPO po = new SysPermissionPO();
        BeanUtils.copyProperties(vo, po);

        SysPermissionPO parent = this.baseMapper.selectById(vo.getParentId());
        if (parent == null && !Constant.ZERO.equals(vo.getParentId())) {
            throw new ServiceErrorException("上级节点不存在");
        }
        if (StringUtils.isNotBlank(po.getCheckCode())) {
            Integer checkCodeCount = this.baseMapper.selectCount(
                new QueryWrapper<SysPermissionPO>().lambda()
                    .eq(SysPermissionPO::getCheckCode, po.getCheckCode())
                    .ne(SysPermissionPO::getPermissionId, po.getPermissionId()));
            if (checkCodeCount > 0 && !"tenant".equals(vo.getSaveType())) {
                throw new ServiceErrorException("权校验字符已存在，请重新输入");
            }
        }

        //如果状态是无效，则级联修改所有下级节点为'无效'
        if (po.getPermissionStatus().equals(UnifastEnum.SysDataValid.INVALID.value())) {
            SysPermissionPO update = new SysPermissionPO();
            update.setPermissionStatus(UnifastEnum.SysDataValid.INVALID.value());
            this.baseMapper.update(update,
                    new UpdateWrapper<SysPermissionPO>().lambda()
                            .likeRight(SysPermissionPO::getCode, po.getCode() + "/"));
        }
        int num = this.baseMapper.updateById(po);
        //更改权限，推送kafka
        sendPremKafkaMessage(OperationEnum.UPDATE.getCode(),po.getPermissionId());
        return num;
    }

    @Override
    public SysPermissionVO findById(final String permissionId) {
        if (null == permissionId) {
            throw new ServiceErrorException(PERMISSION_ID_NULL, HttpStatus.BAD_REQUEST);
        }
        SysPermissionVO permission = this.baseMapper.findPermissionById(permissionId);
        if (null == permission) {
            throw new ServiceErrorException(UnifastConstants.ERROR_NOT_FOUND, HttpStatus.NOT_FOUND);
        }
        return permission;
    }

    @Override
    public Integer deleteById(final String permissionId) {
        if (null == permissionId) {
            throw new ServiceErrorException(PERMISSION_ID_NULL, HttpStatus.BAD_REQUEST);
        }
        int count = this.count(new QueryWrapper<SysPermissionPO>().lambda().eq(SysPermissionPO::getParentId,
                permissionId));
        if (count > 0) {
            throw new ServiceErrorException("该权限有子节点，不可删除");
        }
        // 删除角色权限关联表中 该权限的相关数据
        this.sysRolePermissionService.remove(new QueryWrapper<SysRolePermissionPO>()
                .lambda().eq(SysRolePermissionPO::getPermissionId, permissionId));
        int num = this.baseMapper.deleteById(permissionId);
        //删除权限，推送kafka，需要将角色权限一并删除
        sendPremKafkaMessage(OperationEnum.DELETE.getCode(),permissionId);
        return num;
    }

    @Override
    public List<SysPermissionVO> findListByStaffOrgId(final String staffOrgId) {
        return this.baseMapper.findListByStaffOrgId(staffOrgId);
    }

    @Override
    public List<SysPermissionVO> findListByPermission(SysPermissionVO vo) {
       // vo.setStaffOrgId(unifastContext.getUser().getStaffOrgId());
        return this.baseMapper.findListByPermission(vo);
    }

    @Override
    public List<SysPermissionVO> findAllMenu(SysPermissionVO vo) {
        // 先查询本租户的权限列表
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
            unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            vo.setTenantId(tenantId);
        }
        List<SysPermissionVO> permissionVOList = this.baseMapper.findListByPermission(vo);
        // 如果不是system租户，需要查询分配给该租户的权限字(解决新租户用户组无权限字问题)
        if (!UnifastConstants.DEFAULT_TENANT_ID.equals(
            unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID))) {
            List<SysPermissionVO> distributeList = this.baseMapper.findListByDistribute(vo);
            if (CollectionUtil.isNotEmpty(distributeList)) {
                permissionVOList.addAll(distributeList);
            }
            permissionVOList = permissionVOList.stream().distinct().collect(Collectors.toList());
        }
        return permissionVOList;
    }

    @Override
    public List<SysPermissionMenuVO> findMyMenu(final String staffOrgId, final String parentId) {
        return this.baseMapper.findMenuByStaffOrgId(staffOrgId, parentId);
    }

    @Override
    public List<SysPermissionTreeVO> findChildren(final String permissionId) {
        return this.baseMapper.findTreeChildren(permissionId);
    }

    @Override
    public List<SysPermissionVO> findListByRoleId(final String roleId) {
        Assert.notNull(roleId, "roleId不能为空");
        return this.baseMapper.findListByRoleId(roleId);
    }

    @Override
    public List<SysPermissionPO> selectPermissionList(SysTransDateVO vo) {
        return this.baseMapper.selectPermissionList(vo);
    }

    /**
     * 添加权限消息
     * */
    private void sendPremKafkaMessage(String key,String premId){
        SysPermissionPO po = this.baseMapper.findPremById(premId);
        if (null != po && StringUtils.isNotEmpty(po.getTenantId())) {
           // kafkaSendService.sendKafkaMessage(key,premissionTopic,po.getTenantId(),po);
        }
    }
}
