package cn.chinaunicom.sdsi.cloud.system.commandCenter.service.impl;

import cn.chinaunicom.sdsi.cloud.system.commandCenter.service.CommandCenterService;
import cn.chinaunicom.sdsi.cloud.system.feign.CmsFeignClient;
import cn.chinaunicom.sdsi.cloud.system.log.entity.SysLogPO;
import cn.chinaunicom.sdsi.cloud.system.log.service.SysLogInfoService;
import cn.chinaunicom.sdsi.framework.constant.Constant;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.CollectionUtil;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import com.alibaba.nacos.api.PropertyKeyConst;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Maps;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import javax.annotation.Resource;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.util.StopWatch.TaskInfo;

/**
 * <p>文件名称: CommandCenterServiceImpl </p>
 * <p>描述: 指挥中心-接口实现 </p>
 * <p>创建时间: 2022/3/23 14:26</p>
 *
 * <AUTHOR>
 */
@Service
public class CommandCenterServiceImpl implements CommandCenterService {
  @Resource
  private SysLogInfoService sysLogInfoService;

  @Resource
  private RedisUtils redisUtils;

  @Resource
  @Qualifier("kafkaTemplateMaster")
  private KafkaTemplate kafkaTemplate;

  @Resource
  private CmsFeignClient cmsFeignClient;

  // nacos服务地址
  @Value("${spring.cloud.nacos.config.server-addr}")
  private String nacosServerAddr;

  // nacos命名空间
  @Value("${spring.cloud.nacos.config.namespace}")
  private String nacosNamespace;

  // nacos组名
  @Value("${spring.cloud.nacos.config.group}")
  private String nacosGroup;

  // nacos组名
  @Value("${spring.cloud.nacos.discovery.cluster-name}")
  private String nacosClusterName;

  @Value("${unifast.basic-applications.auth:cloud-auth}")
  public String APPLICATION_AUTH;
  @Value("${unifast.basic-applications.gateway:cloud-gateway}")
  public String APPLICATION_GATEWAY;
  @Value("${unifast.basic-applications.cms:cloud-cms}")
  public String APPLICATION_CMS;
  @Value("${unifast.basic-applications.usercenter:cloud-usercenter}")
  public String APPLICATION_USERCENTER;
  @Value("${unifast.basic-applications.exchange:cloud-exchange}")
  public String APPLICATION_EXCHANGE;

  // nacos用户名
  @Value("${spring.cloud.nacos.config.username}")
  private String nacosName;

  // nacos用户名
  @Value("${spring.cloud.nacos.config.password}")
  private String nacosPassword;

  // kafka topic
  private final String kafkaTopic = "kafka_command_module_test_topic";

  private final String write = "write";

  private final String read = "read";

  @Override
  public Map<String, Object> getBasicServicesHealthStatus() {
    try {
        Map<String, Object> basicServicesStatus = Maps.newHashMap();
        Properties properties = System.getProperties();
        properties.setProperty(PropertyKeyConst.SERVER_ADDR, nacosServerAddr);
        properties.setProperty(PropertyKeyConst.NAMESPACE, nacosNamespace);
        properties.setProperty("group", nacosGroup);
        // 配置用户名：
        properties.put(PropertyKeyConst.USERNAME, nacosName);
        // 配置密码：
        properties.put(PropertyKeyConst.PASSWORD, nacosPassword);
        NamingService naming = NamingFactory.createNamingService(properties);
        StopWatch basicServicesSw = new StopWatch();
        // 分别获取四个基础服务的健康状况
        basicServicesSw.start(Constant.APPLICATION_AUTH);
        List<Instance> authInstanceList = naming.getAllInstances(APPLICATION_AUTH);
        if (CollectionUtil.isNotEmpty(authInstanceList)) {
          basicServicesStatus.put(Constant.APPLICATION_AUTH, authInstanceList.get(0).isHealthy());
        } else {
          basicServicesStatus.put(Constant.APPLICATION_AUTH, false);
        }
        basicServicesSw.stop();

        basicServicesSw.start(Constant.APPLICATION_GATEWAY);
        List<Instance> gatewayInstanceList = naming.getAllInstances(APPLICATION_GATEWAY);
        if (CollectionUtil.isNotEmpty(gatewayInstanceList)) {
          basicServicesStatus.put(Constant.APPLICATION_GATEWAY, gatewayInstanceList.get(0).isHealthy());
        } else {
          basicServicesStatus.put(Constant.APPLICATION_GATEWAY, false);
        }
        basicServicesSw.stop();

        basicServicesSw.start(Constant.APPLICATION_CMS);
        List<Instance> cmsInstanceList = naming.getAllInstances(APPLICATION_CMS);
        if (CollectionUtil.isNotEmpty(cmsInstanceList)) {
          basicServicesStatus.put(Constant.APPLICATION_CMS, cmsInstanceList.get(0).isHealthy());
        } else {
          basicServicesStatus.put(Constant.APPLICATION_CMS, false);
        }
        basicServicesSw.stop();

        basicServicesSw.start(Constant.APPLICATION_USERCENTER);
        List<Instance> usercenterInstanceList = naming.getAllInstances(APPLICATION_USERCENTER);
        if (CollectionUtil.isNotEmpty(usercenterInstanceList)) {
          basicServicesStatus.put(Constant.APPLICATION_USERCENTER, usercenterInstanceList.get(0).isHealthy());
        } else {
          basicServicesStatus.put(Constant.APPLICATION_USERCENTER, false);
        }
        basicServicesSw.stop();

        basicServicesSw.start(Constant.APPLICATION_EXCHANGE);
        List<Instance> exchangeInstanceList = naming.getAllInstances(APPLICATION_EXCHANGE);
        if (CollectionUtil.isNotEmpty(exchangeInstanceList)) {
          basicServicesStatus.put(Constant.APPLICATION_EXCHANGE, exchangeInstanceList.get(0).isHealthy());
        } else {
          basicServicesStatus.put(Constant.APPLICATION_EXCHANGE, false);
        }
        basicServicesSw.stop();

        TaskInfo[] taskInfos = basicServicesSw.getTaskInfo();
        basicServicesStatus.put(Constant.APPLICATION_AUTH + "Time",
            taskInfos.length > 0 ? taskInfos[0].getTimeMillis() : "0");
        basicServicesStatus.put(Constant.APPLICATION_GATEWAY + "Time",
            taskInfos.length >= 2 ? taskInfos[1].getTimeMillis() : "0");
        basicServicesStatus.put(Constant.APPLICATION_CMS + "Time",
            taskInfos.length >= 3 ? taskInfos[2].getTimeMillis() : "0");
        basicServicesStatus.put(Constant.APPLICATION_USERCENTER + "Time",
            taskInfos.length >= 4 ? taskInfos[3].getTimeMillis() : "0");
        basicServicesStatus.put(Constant.APPLICATION_EXCHANGE + "Time",
            taskInfos.length >= 5 ? taskInfos[4].getTimeMillis() : "0");
        basicServicesStatus.put("totalTime", basicServicesSw.getTotalTimeMillis());
        return basicServicesStatus;
      } catch (NacosException nacosException) {
        throw new BusinessException("获取单个服务健康状态失败");
      }
  }

  @Override
  public Map<String, Object> getAllBasicServicesHealthStatus() {
    try {
      Map<String, Object> basicServicesStatus = Maps.newHashMap();
      Properties properties = System.getProperties();
      properties.setProperty(PropertyKeyConst.SERVER_ADDR, nacosServerAddr);
      properties.setProperty(PropertyKeyConst.NAMESPACE, nacosNamespace);
      properties.setProperty("group", nacosGroup);
      properties.setProperty(PropertyKeyConst.CLUSTER_NAME, nacosClusterName);
      // 配置用户名：
      properties.put(PropertyKeyConst.USERNAME, nacosName);
      // 配置密码：
      properties.put(PropertyKeyConst.PASSWORD, nacosPassword);
      NamingService naming = NamingFactory.createNamingService(properties);
      List<Instance> authInstanceList = naming.getAllInstances(APPLICATION_AUTH);
      List<Instance> gatewayInstanceList = naming.getAllInstances(APPLICATION_GATEWAY);
      List<Instance> cmsInstanceList = naming.getAllInstances(APPLICATION_CMS);
      List<Instance> usercenterInstanceList = naming.getAllInstances(APPLICATION_USERCENTER);
      List<Instance> exchangeInstanceList = naming.getAllInstances(APPLICATION_EXCHANGE);
      basicServicesStatus.put(Constant.APPLICATION_AUTH, authInstanceList);
      basicServicesStatus.put(Constant.APPLICATION_GATEWAY, gatewayInstanceList);
      basicServicesStatus.put(Constant.APPLICATION_CMS, cmsInstanceList);
      basicServicesStatus.put(Constant.APPLICATION_USERCENTER, usercenterInstanceList);
      basicServicesStatus.put(Constant.APPLICATION_EXCHANGE, exchangeInstanceList);
      return basicServicesStatus;
    } catch (NacosException nacosException) {
      throw new BusinessException("获取全部服务健康状态失败");
    }
  }

  @Override
  public Map<String, Object> getDatabaseStatus() {
    Map<String, Object> databaseStatus = Maps.newHashMap();
    StopWatch databaseSw = new StopWatch();
    // 测试数据库写
    databaseSw.start(write);
    try {
      SysLogPO writeLog = new SysLogPO();
      writeLog.setException("测试数据库写");
      int writeCount = sysLogInfoService.getBaseMapper().insert(writeLog);
      if (writeCount == 1) {
        databaseStatus.put(write, true);
        sysLogInfoService.getBaseMapper()
            .delete(new QueryWrapper<SysLogPO>().lambda().eq(SysLogPO::getException, "测试数据库读写"));
      } else {
        databaseStatus.put(write, false);
      }
    } catch (Exception e) {
      databaseStatus.put(write, false);
      throw new BusinessException("测试数据库读写失败！");
    }
    databaseSw.stop();
    // 测试数据库读
    databaseSw.start(read);
    int readyResult = sysLogInfoService.getBaseMapper().selectCount(new QueryWrapper<>());
    if (readyResult >= 0) {
      databaseStatus.put(read, true);
    } else {
      databaseStatus.put(read, false);
    }
    databaseSw.stop();
    TaskInfo[] taskInfos = databaseSw.getTaskInfo();
    databaseStatus.put(write + "Time", taskInfos.length > 0 ? taskInfos[0].getTimeMillis() : "0");
    databaseStatus.put(read + "Time", taskInfos.length >= 2 ? taskInfos[1].getTimeMillis() : "0");
    return databaseStatus;
  }

  @Override
  public Map<String, Object> getRedisStatus() {
    Map<String, Object> redisStatus = Maps.newHashMap();
    StopWatch redisSw = new StopWatch();
    // 测试redis写
    // redis key
    String redisKey = "testRedis";
    redisSw.start(write);
    boolean writeRedisResult = redisUtils.set(redisKey, write);
    if (writeRedisResult) {
      redisStatus.put(write, true);
    } else {
      redisStatus.put(write, false);
    }
    redisSw.stop();
    // 测试redis读
    redisSw.start(read);
    String readStr = (String) redisUtils.get(redisKey);
    if (StringUtils.isNotBlank(readStr)) {
      redisStatus.put(read, true);
    } else {
      redisStatus.put(read, false);
    }
    redisSw.stop();
    TaskInfo[] taskInfos = redisSw.getTaskInfo();
    redisStatus.put(write + "Time", taskInfos.length > 0 ? taskInfos[0].getTimeMillis() : "0");
    redisStatus.put(read + "Time", taskInfos.length >= 2 ? taskInfos[1].getTimeMillis() : "0");
    return redisStatus;
  }

  @Override
  public Map<String, Object> getKafkaStatus() {
    Map<String, Object> kafkaStatus = Maps.newHashMap();
    StopWatch kafkaSw = new StopWatch();
    kafkaSw.start(write);
    try {
      kafkaTemplate.send(kafkaTopic, "指挥舱测试Kafka生产");
      kafkaStatus.put(write, true);
    } catch (Exception e) {
      kafkaStatus.put(write, false);
    }
    kafkaSw.stop();
    kafkaSw.start(read);
    try {
      ConsumerRecords<String, String> records = this.buildKafkaConsumer().poll(Duration.ofSeconds(1));
      List<String> messages = new ArrayList<>(records.count());
      for (ConsumerRecord<String, String> record : records.records(kafkaTopic)) {
        String message = record.value();
        messages.add(message);
      }
      kafkaStatus.put(read, true);
    } catch (Exception e) {
      kafkaStatus.put(read, false);
    }
    kafkaSw.stop();
    TaskInfo[] taskInfos = kafkaSw.getTaskInfo();
    kafkaStatus.put(write + "Time", taskInfos.length > 0 ? taskInfos[0].getTimeMillis() : "0");
    kafkaStatus.put(read + "Time", taskInfos.length >= 2 ? taskInfos[1].getTimeMillis() : "0");
    return kafkaStatus;
  }

  @Override
  public Map<String, Object> getElasticsearchStatus() {
    String esDocId = "commandmoduletestes";
    Map<String, Object> esStatus = Maps.newHashMap();
    StopWatch esSw = new StopWatch();
    esSw.start(write);
    Map<String, Object> map = Maps.newHashMap();
    map.put("id", esDocId);
    map.put("title", "指挥舱测试Es");
    map.put("noticeContent", "指挥舱测试Es插入文档是否健康");
    BaseResponse writeRes = cmsFeignClient.addEsDoc(map);
    if (writeRes.isSuccess()) {
      esStatus.put(write, true);
    } else {
      esStatus.put(write, false);
    }
    esSw.stop();
    esSw.start(read);
    BaseResponse readRes = cmsFeignClient.getEsDocById(esDocId);
    if (readRes.isSuccess()) {
      esStatus.put(read, true);
    } else {
      esStatus.put(read, false);
    }
    esSw.stop();
    TaskInfo[] taskInfos = esSw.getTaskInfo();
    esStatus.put(write + "Time", taskInfos.length > 0 ? taskInfos[0].getTimeMillis() : "0");
    esStatus.put(read + "Time", taskInfos.length >= 2 ? taskInfos[1].getTimeMillis() : "0");
    return esStatus;
  }

  @Override
  public Map<String, Object> getOSSStatus() {
    Map<String, Object> ossStatus = Maps.newHashMap();
    StopWatch ossSw = new StopWatch();
    ossSw.start(write);
    BaseResponse res = cmsFeignClient.uploadOSSFile();
    if (res.isSuccess()) {
      ossStatus.put(write, true);
    } else {
      ossStatus.put(write, false);
    }
    ossSw.stop();
    ossSw.start(read);
    if (res.isSuccess() && StringUtils.isNotBlank((String) res.getData())) {
      ossStatus.put(read, true);
    } else {
      ossStatus.put(read, false);
    }
    ossSw.stop();
    TaskInfo[] taskInfos = ossSw.getTaskInfo();
    ossStatus.put(write + "Time", taskInfos.length > 0 ? taskInfos[0].getTimeMillis() : "0");
    ossStatus.put(read + "Time", taskInfos.length >= 2 ? taskInfos[1].getTimeMillis() : "0");
    return ossStatus;
  }

  private KafkaConsumer<String, String> buildKafkaConsumer() {
    Properties props = new Properties();
    //设置Kafka服务器地址
//    props.put("bootstrap.servers", kafkaBootstrapServers);
//    //设置消费组
//    props.put("group.id", kafkaGroupId);
    //设置数据key的反序列化处理类
    props.put("key.deserializer", StringDeserializer.class.getName());
    //设置数据value的反序列化处理类
    props.put("value.deserializer", StringDeserializer.class.getName());
    props.put("enable.auto.commit", "true");
    props.put("auto.commit.interval.ms", "1000");
    props.put("session.timeout.ms", "30000");
    KafkaConsumer<String, String> kafkaConsumer = new KafkaConsumer<>(props);
    kafkaConsumer.subscribe(Arrays.asList(kafkaTopic));
    return kafkaConsumer;
  }
}