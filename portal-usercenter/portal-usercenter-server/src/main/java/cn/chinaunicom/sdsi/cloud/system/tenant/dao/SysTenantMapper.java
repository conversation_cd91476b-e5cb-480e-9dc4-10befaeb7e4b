/*
 * Copyright (c) 2020, SDCNCSI. All rights reserved.
 */
package cn.chinaunicom.sdsi.cloud.system.tenant.dao;

import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantPO;
import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantQueryVO;
import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2020-02-12
 * @version: V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Mapper
public interface SysTenantMapper extends BaseMapper<SysTenantPO> {
    List<SysTenantVO> queryTenantList(@Param("vo") SysTenantQueryVO sysTenantQueryVO);

    /**
     * 查询租户分页数据
     * @param page
     * @param sysTenantQueryVO
     * @return
     */
    IPage<SysTenantVO> findSysTenantByVo(@Param("page") IPage page, @Param("vo") SysTenantQueryVO sysTenantQueryVO);
    /***
     * 根据businessid查询租户信息
     * @param businessId
     * @return SysTenantPO
     * **/
    SysTenantPO selectTenantByBunessId(String businessId);

    /**
     * 根据租户登录名验证 其唯一性
     * @param tenantLoginName
     * @return
     */
    SysTenantVO findVoByTenantLoginName(final @Param("tenantLoginName") String tenantLoginName);

    /**
     * 根据租户名 验证其唯一性
     * @param tenantName
     * @return
     */
    SysTenantVO findVoByTenantName(final @Param("tenantName") String tenantName);

    /**
     * 查询租户分页数据
     * @param sysTenantQueryVO
     * @param sysTenantQueryVO
     * @return
     */
    SysTenantVO findSysTenantByVo(@Param("vo") SysTenantQueryVO sysTenantQueryVO);
    /**
     * 查询租户分页数据
     * @param sysTenantQueryVO
     * @param sysTenantQueryVO
     * @return
     */
    SysTenantVO findSysTenantByVoLoginUse(@Param("vo") SysTenantQueryVO sysTenantQueryVO);
}
