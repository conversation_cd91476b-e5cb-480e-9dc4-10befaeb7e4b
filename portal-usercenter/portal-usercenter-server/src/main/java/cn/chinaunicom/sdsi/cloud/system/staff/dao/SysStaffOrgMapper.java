package cn.chinaunicom.sdsi.cloud.system.staff.dao;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffOrgPO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.staffext.vo.SysStaffExtVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 人员-组织关联表（岗位表） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-01-17
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Mapper
public interface SysStaffOrgMapper extends BaseMapper<SysStaffOrgPO> {

    /**
     * 根据岗位id查询人岗信息
     *
     * @param staffOrgId 岗位id
     * @return 人岗视图对象
     */
    SysStaffOrgVO findVoByStaffOrgId(final @Param("staffOrgId") String staffOrgId);

    /**
     * 条件查询人岗视图列表，不分页
     *
     * @param vo 查询条件对象
     * @return 人岗视图对象集合
     */
    List<SysStaffOrgVO> selectStaffsByQueryVO(@Param("vo") SysStaffOrgQueryVO vo);

    /**
     * 分页查询人岗视图对象
     *
     * @param page    分页条件
     * @param queryVO 查询条件
     * @return 人岗视图分页对象
     */
    IPage<SysStaffOrgVO> selectStaffsByQueryVO(
            @Param("page") IPage<SysStaffOrgVO> page,
            @Param("vo") SysStaffOrgQueryVO queryVO);

    /**
     * 根据时间查询租户下用户组织数据
     *
     * @param vo 查询条件
     * @return 用户组织集合
     */
    List<SysStaffOrgPO> selectStaffOrgList(SysTransDateVO vo);

    /**
     * 查询组织下的钉钉userId
     *
     * @param vo 查询条件
     * @return 钉钉id集合
     */
    List<String> selectDingUserIdByQueryVO(@Param("vo") SysStaffOrgQueryVO vo);

    /**
     * 查询租户下未绑定钉钉的用户信息
     *
     * @param tenantId 租户id
     * @return 人员信息视图对象
     */
    List<SysStaffExtVO> selectTenantDingUsers(final @Param("tenantId") String tenantId);


    List<SysStaffOrgPO> select4sysStaffSync(final @Param("staffId") String staffId,final @Param("staffOrgType") String staffOrgType);
}
