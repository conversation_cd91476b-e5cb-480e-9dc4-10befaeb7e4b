package cn.chinaunicom.sdsi.cloud.system.transfer.controller;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.org.entity.SysOrgPO;
import cn.chinaunicom.sdsi.cloud.system.org.service.ISysOrgService;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionPO;
import cn.chinaunicom.sdsi.cloud.system.permission.service.ISysPermissionService;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePermissionPO;
import cn.chinaunicom.sdsi.cloud.system.role.service.ISysRolePermissionService;
import cn.chinaunicom.sdsi.cloud.system.role.service.ISysRoleService;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffOrgPO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffPO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffRolePO;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffOrgService;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffRoleService;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffService;
import cn.chinaunicom.sdsi.cloud.system.staffext.entity.SysStaffExtPO;
import cn.chinaunicom.sdsi.cloud.system.staffext.service.SysStaffExtService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 系统数据同步
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-10
 */
@Tag(name = "系统数据同步", description = "系统数据同步")
@RestController
@RequestMapping("/v1.0/transfer/sys")
public class SysTransferController extends BaseController {

    @Autowired
    private ISysOrgService sysOrgService;

    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private ISysRolePermissionService sysRolePermissionService;

    @Autowired
    private ISysPermissionService sysPermissionService;

    @Autowired
    private SysStaffService sysStaffService;

    @Autowired
    private SysStaffOrgService sysStaffOrgService;

    @Autowired
    private SysStaffRoleService sysStaffRoleService;

    @Autowired
    private SysStaffExtService sysStaffExtService;

    @Schema(name="同步系统组织")
    @PostMapping("/orgList")
    public BaseResponse<List<SysOrgPO>> selectOrgList(@RequestBody SysTransDateVO vo) {
        return ok(this.sysOrgService.selectOrgList(vo));
    }

    @Schema(name="同步系统角色")
    @PostMapping("/roleList")
    public BaseResponse<List<SysRolePO>> selectRoleList(@RequestBody SysTransDateVO vo) {
        return ok(this.sysRoleService.selectRoleList(vo));
    }

    @Schema(name="同步系统角色权限")
    @PostMapping("/rolePermissionList")
    public BaseResponse<List<SysRolePermissionPO>> selectRolePermissionList(
            @RequestBody SysTransDateVO vo) {
        return ok(this.sysRolePermissionService.selectRolePermissionList(vo));
    }

    @Schema(name="同步系统权限")
    @PostMapping("/permissionList")
    public BaseResponse<List<SysPermissionPO>> selectPermissionList(
            @RequestBody SysTransDateVO vo) {
        return ok(this.sysPermissionService.selectPermissionList(vo));
    }

    @Schema(name="同步系统用户")
    @PostMapping("/staffList")
    public BaseResponse<List<SysStaffPO>> selectStaffList(
            @RequestBody SysTransDateVO vo) {
        return ok(this.sysStaffService.selectStaffList(vo));
    }

    @Schema(name="同步系统用户岗位")
    @PostMapping("/staffOrgList")
    public BaseResponse<List<SysStaffOrgPO>> selectStaffOrgList(
            @RequestBody SysTransDateVO vo) {
        return ok(this.sysStaffOrgService.selectStaffOrgList(vo));
    }

    @Schema(name="同步系统用户角色")
    @PostMapping("/staffRoleList")
    public BaseResponse<List<SysStaffRolePO>> selectStaffRoleList(
            @RequestBody SysTransDateVO vo) {
        return ok(this.sysStaffRoleService.selectStaffRoleList(vo));
    }

    @Schema(name="同步系统用户拓展")
    @PostMapping("/staffExtList")
    public BaseResponse<List<SysStaffExtPO>> selectStaffExtList(
            @RequestBody SysTransDateVO vo) {
        return ok(this.sysStaffExtService.selectStaffExtList(vo));
    }
}
