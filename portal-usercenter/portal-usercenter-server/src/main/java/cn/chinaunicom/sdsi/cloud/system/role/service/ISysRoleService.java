package cn.chinaunicom.sdsi.cloud.system.role.service;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRolePermissionVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleQueryVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import cn.chinaunicom.sdsi.cloud.system.role.service.impl.SysRoleServiceImpl;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 系统角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 * @since 2019-01-16
 */
public interface ISysRoleService extends IService<SysRolePO> {

    /**
     * 新增角色
     *
     * @param vo 角色视图对象
     * @return 主键id
     */
    String addByVo(final SysRoleVO vo);

    /**
     * 删除角色，当角色被分配时不允许删除
     *
     * @param roleId 角色id
     * @return 影响条数
     */
    Integer deleteById(final String roleId);

    /**
     * 编辑角色
     *
     * @param vo 角色视图对象
     * @return 影响条数
     */
    Integer updateByVo(final SysRoleVO vo);

    /**
     * 根据角色id查询详情
     *
     * @param roleId 角色id
     * @return 角色视图对象
     */
    SysRoleVO getVoById(final String roleId);

    /**
     * 条件查询分页角色列表
     *
     * @param queryVO 查询条件对象
     * @return 角色视图分页对象
     */
    IPage<SysRoleVO> findPageByQueryVo(final SysRoleQueryVO queryVO);

    /**
     * 条件查询角色列表，不分页
     *
     * @param queryVO 条件对象
     * @return 角色视图列表
     */
    List<SysRoleVO> findListByQueryVo(final SysRoleQueryVO queryVO);

    /**
     * 固定查询角色列表
     * 根据入参视图对象中的变量直接使用 SQL 查询,不判断当前登录用户信息
     *
     * @param queryVO 入参视图对象
     * @return 查询到的角色列表
     * <AUTHOR>
     * -
     * @date 2021/12/21
     * -
     * @version 1.0
     * @description 使用 {@link SysRoleServiceImpl} findListByQueryVo 方法时,因请求发起为前台未登录状态,
     * 无法获取 unifastContext 上下文环境,未避免改动影响旧代码与逻辑混淆,所以编写此方法.
     */
    List<SysRoleVO> findListByQueryVoNotLogin(final SysRoleQueryVO queryVO);

    /**
     * 角色授权
     *
     * @param rolePermVO
     * @return
     */
    Integer updateRolePerms(final SysRolePermissionVO rolePermVO);

    /**
     * 更新角色状态
     *
     * @param roleId
     * @param roleStatus
     * @return
     */
    Integer updateRoleStatus(final String roleId, final String roleStatus);

    /**
     * 判断角色名是否存在，排除roleId记录
     *
     * @param roleName 角色名
     * @param roleId   排除的roleId
     * @return 存在则返回true，否则false
     */
    Boolean checkByRoleName(final String roleName, final String roleId, final String tenantId);

    /**
     * 根据角色id查询其权限集合
     *
     * @param roleId 角色id
     * @return 角色权限视图对象
     */
    SysRolePermissionVO findPermissionsByRoleId(final String roleId);

    /**
     * 根据用户组ID查询权限树
     *
     * @param orgId 用户组id
     * @return 角色权限视图对象
     */
    SysRolePermissionVO findPermissionsByOrgId(final String orgId);

    /**
     * 查看某个角色权限
     *
     * @param sysRoleVO 角色参数
     * @return 查看某个角色权限
     */
    SysRolePermissionVO get(SysRolePO sysRolePO);

    /**
     * 角色kafka消息队列
     *
     * @param key 操作类型
     * @param roleId 角色id
     *
     */
    void sendRoleKafkaMessage(String key,String roleId);

    /**
     * 根据时间查询租户下角色数据
     *
     * @param vo 查询条件
     * @return 角色集合
     */
    List<SysRolePO> selectRoleList(SysTransDateVO vo);
}
