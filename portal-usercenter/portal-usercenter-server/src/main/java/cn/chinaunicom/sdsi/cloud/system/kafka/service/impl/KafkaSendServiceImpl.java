//package cn.chinaunicom.sdsi.cloud.system.kafka.service.impl;
//
//import cn.chinaunicom.sdsi.cloud.system.dict.entity.SysDictDataPO;
//import cn.chinaunicom.sdsi.cloud.system.dict.service.SysDictDataService;
//import cn.chinaunicom.sdsi.cloud.system.dict.vo.SysDictDateVO;
//import cn.chinaunicom.sdsi.cloud.system.kafka.service.KafkaSendService;
//import cn.chinaunicom.sdsi.framework.exception.BusinessException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.kafka.clients.producer.ProducerRecord;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.kafka.core.KafkaProducerException;
//import org.springframework.kafka.core.KafkaSendCallback;
//import org.springframework.kafka.core.KafkaTemplate;
//import org.springframework.stereotype.Service;
//import org.springframework.util.concurrent.ListenableFuture;
//
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 数据字典功能 - 字典类型(主字典)服务层实现
// *
// * <AUTHOR>
// * -
// * @version 1.0
// * @date 2021/12/14
// * -
// * @description 内容描述
// */
//@Slf4j
//@Service
//public class KafkaSendServiceImpl implements KafkaSendService {
//
//    /**
//     * kafka 工具类
//     */
//    @Resource
//    @Qualifier("kafkaTemplateMaster")
//    private KafkaTemplate kafkaTemplate;
//
//    @Autowired
//    private SysDictDataService sysDictDataService;
//
//    /**
//     * 调用 spring 封装的 Kafka 接口,为业务服务提供消息发送能力
//     *
//     * @param topic     kafka主题标识
//     * @param partition kafka分区数量
//     * @param key       区分消息逻辑含义的标识,如 create/update 等
//     * @param value     消息主体
//     * <AUTHOR>
//     * -
//     * @date 2021/12/14
//     * -
//     * @version 1.0
//     * @description 内容描述
//     */
//    @Override
//    public Map sendKafka(String topic, Integer partition, String key, String value) {
//        Map<String, Object> map = new HashMap<>();
//        try {
//            log.info("kafka的消息={}", value);
//            ListenableFuture listenableFuture = kafkaTemplate.send(topic, partition, key, value);
//            listenableFuture.addCallback(new KafkaSendCallback() {
//                @Override
//                public void onFailure(KafkaProducerException e) {
//                    // 异步发送失败
//                    ProducerRecord<Object, Object> failedProducerRecord = e.getFailedProducerRecord();
//                    log.info(failedProducerRecord.topic());
//                    log.info(failedProducerRecord.key().toString());
//                    log.info(failedProducerRecord.value().toString());
//                    log.error("kafa消息发送失败");
//                    e.printStackTrace();
//                }
//
//                @Override
//                public void onSuccess(Object o) {
//                    // 异步发送成功
//                    log.info(o.toString());
//                }
//            });
//            log.info("发送kafka成功.");
//            map.put("kafka", "发送kafka成功!!");
//            map.put("success", true);
//        } catch (Exception e) {
//            log.error("发送kafka失败", e);
//            map.put("kafka", "发送kafka失败!!");
//            map.put("success", false);
//        }
//        return map;
//    }
//
//    /**
//     * 发送消息
//     * key 类型
//     * id 业务主键
//     */
//    public void sendKafkaMessage(String key, String orgTopic, String tanentId, Object obj) {
//        try {
//            //获取详情数据
//            ObjectMapper objectMapper = new ObjectMapper();
//            String json = objectMapper.writeValueAsString(obj);
//            if (obj != null && StringUtils.isNotEmpty(tanentId)) {
//                //通过字典数据获取topic与分区
//                SysDictDateVO queryVO = new SysDictDateVO();
//                String tp = orgTopic + "_" + tanentId;
//                queryVO.setDictLabel(tp);
//                List<SysDictDataPO> list = sysDictDataService.selectDictDataList(queryVO);
//                int partition = 1;
//                if (list != null && list.size() > 0) {
//                    partition = list.get(0).getDictValue() == null ? 1 : Integer.valueOf(list.get(0).getDictValue());
//                    for (int i = 0; i < partition; i++) {
//                        Map map = sendKafka(tp, i, key, json);
//                        Boolean kafkaFlag = (Boolean) map.get("success");
//                        if (!kafkaFlag) {
//                            //抛出异常
//                            throw new BusinessException(String.valueOf(map.get("kafka")));
//                        }
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            throw new BusinessException("json转换失败！");
//        }
//    }
//}
