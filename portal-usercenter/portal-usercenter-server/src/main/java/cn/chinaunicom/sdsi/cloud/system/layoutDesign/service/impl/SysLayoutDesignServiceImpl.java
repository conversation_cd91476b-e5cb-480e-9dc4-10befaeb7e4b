package cn.chinaunicom.sdsi.cloud.system.layoutDesign.service.impl;

import cn.chinaunicom.sdsi.cloud.system.layoutDesign.dao.SysLayoutDesignMapper;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.entity.SysLayoutDesignPO;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.entity.SysLayoutDesignTenantPO;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.service.SysLayoutDesignService;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.service.SysLayoutDesignTenantService;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.vo.SysLayoutDesignQueryVO;
import cn.chinaunicom.sdsi.cloud.system.layoutDesign.vo.SysLayoutDesignVO;
import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantPO;
import cn.chinaunicom.sdsi.cloud.system.tenant.service.SysTenantService;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.utils.CollectionUtil;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

/**
 * <p>文件名称: SysLayoutDesignServiceImpl </p>
 * <p>描述: 布局设计 </p>
 * <p>创建时间: 2022/4/25 16:02</p>
 *
 * <AUTHOR>
 */
@Service
public class SysLayoutDesignServiceImpl extends ServiceImpl<SysLayoutDesignMapper, SysLayoutDesignPO> implements
    SysLayoutDesignService {
  @Resource
  private UnifastContext unifastContext;
  @Resource
  private SysTenantService tenantService;
  @Resource
  private SysLayoutDesignTenantService layoutDesignTenantService;
  private final String ENABLED = "1";
  private final String NOT_ENABLED = "0";
  private final String DEFAULT_LAYOUT_DESIGN = "default";

  @Override
  public IPage<SysLayoutDesignVO> getPage(SysLayoutDesignQueryVO queryVO) {
    if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
        unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
      String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
      queryVO.setTenantId(tenantId);
    }
    IPage pageParam = QueryVoToPageUtil.toPage(queryVO);
    IPage<SysLayoutDesignVO> pageResult = this.baseMapper.selectList(pageParam, queryVO);
    pageResult.getRecords().forEach(layoutDesignVO -> {
      // 根据租户Id获取启用状态
      List<SysLayoutDesignTenantPO> layoutDesignTenantPOList = layoutDesignTenantService.getBaseMapper().selectList(
          new QueryWrapper<SysLayoutDesignTenantPO>().lambda()
              .eq(SysLayoutDesignTenantPO::getLayoutDesignId, layoutDesignVO.getId())
              .eq(SysLayoutDesignTenantPO::getTenantId, unifastContext.getUser().getTenantId()));
      // 如果有启用的设置为启用，否则设置为未启用
      if (CollectionUtil.isNotEmpty(layoutDesignTenantPOList)) {
        layoutDesignVO.setEnabledStatus(
            layoutDesignTenantPOList.get(0).getEnabledStatus().equals(NOT_ENABLED) ? NOT_ENABLED
                : ENABLED);
      } else {
        layoutDesignVO.setEnabledStatus(NOT_ENABLED);
      }
    });
    return pageResult;
  }

  @Override
  public SysLayoutDesignVO get(SysLayoutDesignQueryVO queryVO) {
    SysLayoutDesignVO vo = this.baseMapper.get(queryVO);
    if (StringUtils.isNotBlank(vo.getLayoutDesignValue())) {
      JSONObject jsonObject = JSONUtil.parseObj(vo.getLayoutDesignValue());
      vo.setComponentId(jsonObject.getStr("id"));
      vo.setComponentTemplateJson(jsonObject.get("templateJson", JSONObject.class));
      vo.setComponent(jsonObject.get("component", JSONArray.class));
    }
    return vo;
  }

  @Override
  public SysLayoutDesignVO getEnableLayoutDesign(SysLayoutDesignQueryVO queryVO) {
    if (StringUtils.isBlank(queryVO.getTenantId())) {
      if (unifastContext != null) {
        queryVO.setTenantId(unifastContext.getUser().getTenantId());
      }
    }
    SysLayoutDesignVO layoutDesignVO = this.baseMapper.getEnableLayoutDesign(queryVO);
    if (Objects.isNull(layoutDesignVO)) {
      SysLayoutDesignQueryVO defaultQuery = new SysLayoutDesignQueryVO();
      defaultQuery.setLayoutDesignCode(DEFAULT_LAYOUT_DESIGN);
      layoutDesignVO = this.baseMapper.getEnableLayoutDesign(defaultQuery);
    }
    if (StringUtils.isNotBlank(layoutDesignVO.getLayoutDesignValue())) {
      JSONObject jsonObject = JSONUtil.parseObj(layoutDesignVO.getLayoutDesignValue());
      layoutDesignVO.setComponent(jsonObject.get("component", JSONArray.class));
    }
    return layoutDesignVO;
  }

  @Override
  public String add(SysLayoutDesignVO vo) {
    vo.setId(null);
    SysLayoutDesignPO po = new SysLayoutDesignPO();
    BeanUtils.copyProperties(vo, po);
    handleEnabled(vo);
    this.baseMapper.insert(po);
    return po.getId();
  }

  @Override
  public int update(SysLayoutDesignVO vo) {
    // 根据主键判断数据有效性
    Integer count = this.baseMapper.selectCount(new QueryWrapper<SysLayoutDesignPO>().lambda()
        .eq(SysLayoutDesignPO::getId, vo.getId()));
    if (count == 0) {
      throw new ServiceErrorException("未查询到此条主题数据数据", HttpStatus.INTERNAL_SERVER_ERROR);
    }
    SysLayoutDesignPO po = new SysLayoutDesignPO();
    BeanUtils.copyProperties(vo, po);
    handleEnabled(vo);
    return this.baseMapper.updateById(po);
  }

  @Override
  public int delete(String id) {
    List<SysLayoutDesignTenantPO> layoutDesignTenantPOList = layoutDesignTenantService.getBaseMapper()
        .selectList(new QueryWrapper<SysLayoutDesignTenantPO>().lambda()
            .eq(SysLayoutDesignTenantPO::getLayoutDesignId, id)
            .eq(SysLayoutDesignTenantPO::getEnabledStatus, ENABLED)
            .isNotNull(SysLayoutDesignTenantPO::getTenantId));
    // 如果没有现有的关联数据
    if (CollectionUtil.isNotEmpty(layoutDesignTenantPOList)) {
      // 查出正在使用该主题的租户Id
      List<String> tenantIds = layoutDesignTenantPOList.stream().map(SysLayoutDesignTenantPO::getTenantId)
          .collect(Collectors.toList());
      List<String> tenantName = tenantService.getBaseMapper().selectList(new QueryWrapper<SysTenantPO>().lambda()
              .in(SysTenantPO::getTenantId, tenantIds)).stream().map(SysTenantPO::getTenantName)
          .collect(Collectors.toList());
      throw new BusinessException("当前该主题正在被以下租户使用：" + tenantName + "禁止删除！");
    }
    return this.baseMapper.deleteById(id);
  }

  public void handleEnabled(SysLayoutDesignVO vo) {
    String enabledStatus = vo.getEnabledStatus();
    try {
      // 如果启用状态不为空并且是开启状态
      if (StringUtils.isNotBlank(enabledStatus) && enabledStatus.equals(ENABLED)) {
        // 查询当前是否有已启用的主题
        List<SysLayoutDesignTenantPO> enabledLayoutDesignList = layoutDesignTenantService.getBaseMapper()
            .selectList(new QueryWrapper<SysLayoutDesignTenantPO>().lambda()
                .eq(SysLayoutDesignTenantPO::getTenantId, unifastContext.getUser().getTenantId()));
        if (CollectionUtil.isNotEmpty(enabledLayoutDesignList)) {
          // 将启用的删除掉
          layoutDesignTenantService.getBaseMapper().deleteBatchIds(
              enabledLayoutDesignList.stream().map(SysLayoutDesignTenantPO::getId).collect(Collectors.toList()));
        }
        // 新增一条启用的
        SysLayoutDesignTenantPO sysLayoutDesignTenantPO = new SysLayoutDesignTenantPO();
        sysLayoutDesignTenantPO.setLayoutDesignId(vo.getId());
        sysLayoutDesignTenantPO.setEnabledStatus(vo.getEnabledStatus());
        layoutDesignTenantService.getBaseMapper().insert(sysLayoutDesignTenantPO);
      } else if ((StringUtils.isNotBlank(enabledStatus) && enabledStatus.equals(NOT_ENABLED))){
        List<SysLayoutDesignTenantPO> layoutDesignTenantPOList = layoutDesignTenantService.getBaseMapper()
            .selectList(new QueryWrapper<SysLayoutDesignTenantPO>().lambda()
                .eq(SysLayoutDesignTenantPO::getLayoutDesignId, vo.getId())
                .eq(SysLayoutDesignTenantPO::getTenantId, unifastContext.getUser().getTenantId()));
        // 如果没有现有的关联数据
        SysLayoutDesignTenantPO sysLayoutDesignTenantPO = new SysLayoutDesignTenantPO();
        if (CollectionUtil.isNotEmpty(layoutDesignTenantPOList)) {
          sysLayoutDesignTenantPO.setId(layoutDesignTenantPOList.get(0).getId());
          sysLayoutDesignTenantPO.setEnabledStatus(vo.getEnabledStatus());
          layoutDesignTenantService.getBaseMapper().updateById(sysLayoutDesignTenantPO);
        } else {
          sysLayoutDesignTenantPO.setLayoutDesignId(vo.getId());
          sysLayoutDesignTenantPO.setEnabledStatus(vo.getEnabledStatus());
          layoutDesignTenantService.getBaseMapper().insert(sysLayoutDesignTenantPO);
        }
      }
    } catch (Exception e) {
      throw new BusinessException(ResponseEnum.FAIL, "布局启用失败");
    }
  }
}