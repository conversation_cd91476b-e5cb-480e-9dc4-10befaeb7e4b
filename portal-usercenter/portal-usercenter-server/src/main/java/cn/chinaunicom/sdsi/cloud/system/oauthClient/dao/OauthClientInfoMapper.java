package cn.chinaunicom.sdsi.cloud.system.oauthClient.dao;

import cn.chinaunicom.sdsi.cloud.system.oauthClient.entity.OauthClientInfoPO;
import cn.chinaunicom.sdsi.cloud.system.oauthClient.vo.OauthClientInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统入驻功能 - 系统入驻信息DAO层
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/24
 * -
 * @description 内容描述
 */
@Mapper
public interface OauthClientInfoMapper extends BaseMapper<OauthClientInfoPO> {

    /**
     * 根据分页信息查询系统入驻信息列表
     * 支持根据 clientApp 系统入驻名进行模糊匹配查询
     *
     * @param page 分页信息
     * @param oauthClientInfoVO 查询视图对象
     * @return 查询到的携带分页信息的系统入驻信息列表
     * <AUTHOR>
     * -
     * @date 2021/12/24
     * -
     * @version 1.0
     * @description 内容描述
     */
    IPage<OauthClientInfoVO> findByPageOauthClientInfo(@Param("page") IPage<OauthClientInfoVO> page, @Param("query") OauthClientInfoVO oauthClientInfoVO);

    /**
     * 物理删除系统入驻信息
     *
     * @param clientId 即将要删除的系统入驻信息的主键
     * @return 成功删除的数据条数
     * <AUTHOR>
     * -
     * @date 2021/12/28
     * -
     * @version 1.0
     * @description 内容描述
     */
    int deleteOauthClientInfo(String clientId);
}
