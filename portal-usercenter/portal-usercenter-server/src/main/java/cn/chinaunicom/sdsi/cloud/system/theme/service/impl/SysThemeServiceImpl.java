package cn.chinaunicom.sdsi.cloud.system.theme.service.impl;

import cn.chinaunicom.sdsi.cloud.system.tenant.entity.SysTenantPO;
import cn.chinaunicom.sdsi.cloud.system.tenant.service.SysTenantService;
import cn.chinaunicom.sdsi.cloud.system.theme.entity.SysThemeTenantPO;
import cn.chinaunicom.sdsi.cloud.system.theme.service.SysThemeService;
import cn.chinaunicom.sdsi.cloud.system.theme.dao.SysThemeMapper;
import cn.chinaunicom.sdsi.cloud.system.theme.entity.SysThemePO;
import cn.chinaunicom.sdsi.cloud.system.theme.service.SysThemeTenantService;
import cn.chinaunicom.sdsi.cloud.system.theme.vo.SysEnabledThemeVO;
import cn.chinaunicom.sdsi.cloud.system.theme.vo.SysThemeQueryVO;
import cn.chinaunicom.sdsi.cloud.system.theme.vo.SysThemeVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.utils.CollectionUtil;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>文件名称: SysThemeServiceImpl </p>
 * <p>描述: 主题配置服务实现 </p>
 * <p>创建时间: 2022/4/12 16:35</p>
 *
 * <AUTHOR>
 */
@Service
public class SysThemeServiceImpl extends ServiceImpl<SysThemeMapper, SysThemePO> implements
    SysThemeService {
  @Resource
  private UnifastContext unifastContext;
  @Resource
  private SysThemeTenantService themeTenantService;
  @Resource
  private SysTenantService tenantService;
  private final String ENABLED = "1";
  private final String NOT_ENABLED = "0";
  private final String DEFAULT_THEME = "default";

  @Override
  public IPage<SysThemeVO> getPage(SysThemeQueryVO sysThemeQueryVO) {
    IPage pageParam = QueryVoToPageUtil.toPage(sysThemeQueryVO);
    IPage<SysThemeVO> pageResult = this.baseMapper.selectList(pageParam, sysThemeQueryVO);
    pageResult.getRecords().forEach(sysThemeVO -> {
      // 根据租户Id获取启用状态
      List<SysThemeTenantPO> themeTenantPOList = themeTenantService.getBaseMapper().selectList(
          new QueryWrapper<SysThemeTenantPO>().lambda()
              .eq(SysThemeTenantPO::getThemeId, sysThemeVO.getId())
              .eq(SysThemeTenantPO::getTenantId, unifastContext.getUser().getTenantId()));
      // 如果有启用的设置为启用，否则设置为未启用
      if (CollectionUtil.isNotEmpty(themeTenantPOList)) {
        sysThemeVO.setEnabledStatus(
            themeTenantPOList.get(0).getEnabledStatus().equals(NOT_ENABLED) ? NOT_ENABLED
                : ENABLED);
      } else {
        sysThemeVO.setEnabledStatus(NOT_ENABLED);
      }
    });
    return pageResult;
  }

  @Override
  public SysThemeVO get(SysThemeQueryVO sysThemeQueryVO) {
    return this.baseMapper.get(sysThemeQueryVO);
  }

  @Override
  public SysEnabledThemeVO getEnableTheme(SysThemeQueryVO sysThemeQueryVO) {
    if (StringUtils.isBlank(sysThemeQueryVO.getTenantId())) {
      if (unifastContext != null) {
        sysThemeQueryVO.setTenantId(unifastContext.getUser().getTenantId());
      }
    }
    SysEnabledThemeVO tenantTheme = this.baseMapper.getEnableTheme(sysThemeQueryVO);
    if (Objects.isNull(tenantTheme)) {
      SysThemeQueryVO defaultQuery = new SysThemeQueryVO();
      defaultQuery.setThemeCode(DEFAULT_THEME);
      tenantTheme = this.baseMapper.getEnableTheme(defaultQuery);
    }
    return tenantTheme;
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public String add(SysThemeVO vo) {
    vo.setId(null);
    SysThemePO po = new SysThemePO();
    BeanUtils.copyProperties(vo, po);
    handleEnabled(vo);
    this.baseMapper.insert(po);
    return po.getId();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public int update(SysThemeVO vo) {
    // 根据主键判断数据有效性
    Integer count = this.baseMapper.selectCount(new QueryWrapper<SysThemePO>().lambda()
        .eq(SysThemePO::getId, vo.getId()));
    if (count == 0) {
      throw new ServiceErrorException("未查询到此条主题数据数据", HttpStatus.INTERNAL_SERVER_ERROR);
    }
    SysThemePO po = new SysThemePO();
    BeanUtils.copyProperties(vo, po);
    this.handleEnabled(vo);
    return this.baseMapper.updateById(po);
  }

  @Override
  public int delete(String id) {
    List<SysThemeTenantPO> themeTenantPOList = themeTenantService.getBaseMapper()
        .selectList(new QueryWrapper<SysThemeTenantPO>().lambda()
            .eq(SysThemeTenantPO::getThemeId, id)
            .eq(SysThemeTenantPO::getEnabledStatus, ENABLED)
            .isNotNull(SysThemeTenantPO::getTenantId));
    // 如果没有现有的关联数据
    if (CollectionUtil.isNotEmpty(themeTenantPOList)) {
      // 查出正在使用该主题的租户Id
      List<String> tenantIds = themeTenantPOList.stream().map(SysThemeTenantPO::getTenantId)
          .collect(Collectors.toList());
      List<String> tenantName = tenantService.getBaseMapper().selectList(new QueryWrapper<SysTenantPO>().lambda()
          .in(SysTenantPO::getTenantId, tenantIds)).stream().map(SysTenantPO::getTenantName)
          .collect(Collectors.toList());
      throw new BusinessException("当前该主题正在被以下租户使用：" + tenantName + "禁止删除！");
    }
    return this.baseMapper.deleteById(id);
  }

  public void handleEnabled(SysThemeVO vo) {
    String enabledStatus = vo.getEnabledStatus();
    try {
      // 如果启用状态不为空并且是开启状态
      if (StringUtils.isNotBlank(enabledStatus) && enabledStatus.equals(ENABLED)) {
        // 查询当前是否有已启用的主题
        List<SysThemeTenantPO> enabledThemeList = themeTenantService.getBaseMapper()
            .selectList(new QueryWrapper<SysThemeTenantPO>().lambda()
                .eq(SysThemeTenantPO::getTenantId, unifastContext.getUser().getTenantId()));
        if (CollectionUtil.isNotEmpty(enabledThemeList)) {
          // 将启用的删除掉
          themeTenantService.getBaseMapper().deleteBatchIds(
              enabledThemeList.stream().map(SysThemeTenantPO::getId).collect(Collectors.toList()));
        }
        // 新增一条启用的
        SysThemeTenantPO sysThemeTenantPO = new SysThemeTenantPO();
        sysThemeTenantPO.setThemeId(vo.getId());
        sysThemeTenantPO.setEnabledStatus(vo.getEnabledStatus());
        themeTenantService.getBaseMapper().insert(sysThemeTenantPO);
      } else if ((StringUtils.isNotBlank(enabledStatus) && enabledStatus.equals(NOT_ENABLED))){
        List<SysThemeTenantPO> themeTenantPOList = themeTenantService.getBaseMapper()
            .selectList(new QueryWrapper<SysThemeTenantPO>().lambda()
                .eq(SysThemeTenantPO::getThemeId, vo.getId())
                .eq(SysThemeTenantPO::getTenantId, unifastContext.getUser().getTenantId()));
        // 如果没有现有的关联数据
        SysThemeTenantPO sysThemeTenantPO = new SysThemeTenantPO();
        if (CollectionUtil.isNotEmpty(themeTenantPOList)) {
          sysThemeTenantPO.setId(themeTenantPOList.get(0).getId());
          sysThemeTenantPO.setEnabledStatus(vo.getEnabledStatus());
          themeTenantService.getBaseMapper().updateById(sysThemeTenantPO);
        } else {
          sysThemeTenantPO.setThemeId(vo.getId());
          sysThemeTenantPO.setEnabledStatus(vo.getEnabledStatus());
          themeTenantService.getBaseMapper().insert(sysThemeTenantPO);
        }
      }
    } catch (Exception e) {
      throw new BusinessException(ResponseEnum.FAIL, "主题启用失败");
    }
  }
}