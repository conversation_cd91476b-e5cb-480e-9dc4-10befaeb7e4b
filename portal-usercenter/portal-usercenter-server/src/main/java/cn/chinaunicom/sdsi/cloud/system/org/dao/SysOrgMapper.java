package cn.chinaunicom.sdsi.cloud.system.org.dao;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.org.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 组织机构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-15
 */
@Mapper
public interface SysOrgMapper extends BaseMapper<SysOrgPO> {

    /**
     * 查询组织详情（带parent信息）
     *
     * @param orgId 组织id
     * @return SysOrgVO
     */
    SysOrgVO findById(String orgId);

    /**
     * 查询组织详情（含删除状态）
     *
     * @param orgId 组织id
     * @return SysOrgVO
     */
    SysOrgPO findInfoById(String orgId);

    /**
     * 查询组织详情（带parent信息）
     *
     * @param vo 组织查询视图
     * @return SysOrgVO
     */
    SysOrgVO findOne(@Param("vo") SysOrgQueryVO vo);

    /**
     * 根据参数，查询组织树列表，不分页
     *
     * @param param 查询条件对象
     * @return 组织树节点对象集合
     */
    List<SysOrgTreeVO> findTreeNode(SysOrgQueryVO param);

    /**
     * 根据参数，查询组织+人员的树节点信息
     *
     * @param queryVO 查询条件对象
     * @return 组织树节点对象集合（人员信息也放到组织对象中）
     */
    List<SysOrgVO> selectOrgAndStaffForTree(SysOrgStaffTreeQueryVO queryVO);

    /**
     * 根据查询条件，查询组织分页列表
     *
     * @param page    分页条件对象
     * @param queryVo 查询条件对象
     * @return 组织视图分页对象
     */
    IPage<SysOrgVO> findListByParam(@Param("page") IPage<SysOrgVO> page, @Param("vo") SysOrgQueryVO queryVo);

    /**
     * 根据查询条件，查询组织视图集合，不分页
     *
     * @param queryVo 查询条件对象
     * @return 组织视图对象集合
     */
    List<SysOrgVO> findListByParam(@Param("vo") SysOrgQueryVO queryVo);

    /**
     * 根据组织id，查询其上级组织信息
     *
     * @param orgId 组织id
     * @return 组织视图对象
     */
    SysOrgVO selectParentByKey(@Param("orgId") String orgId);

    /**
     * 查询指定id下的所有下级节点
     *
     * @param orgId 组织id
     * @return 组织视图节点对象集合
     */
    List<SysOrgTreeVO> findAllNodeChildren(@Param("orgId") String orgId);

    /**
     * 查询指定id下的所有下级节点
     *
     * @param queryVO SysOrgTreeQueryVO
     * @return 组织视图对象集合
     */
    List<SysOrgVO> findAllOrgChildren(@Param("query") SysOrgTreeQueryVO queryVO);

    /**
     * 查询指定id下的所有下级节点(只包含下级)
     *
     * @param queryVO SysOrgTreeQueryVO
     * @return 组织视图对象集合
     */
    List<SysOrgVO> findOrgChildren(@Param("query") SysOrgVO queryVO);

    /**
     * 根据时间查询租户下组织数据
     *
     * @param vo 查询条件
     * @return 组织集合
     */
    List<SysOrgPO> selectOrgList(SysTransDateVO vo);

    /**
     * 根据租户id查询当前租户的组织树的根节点
     * 判断组织根节点的条件为 parent_id[上级id] 与 parent_code[上级编码] is null
     *
     * @param sysOrgVO 根据 sysOrgVO.tenantId[租户id] 字段,过滤租户
     * @return 查询到的组织树根节点对象
     * <AUTHOR>
     * -
     * @date 2022/1/11
     * -
     * @version 1.0
     * @description 内容描述
     */
    SysOrgVO findRootNodeByTenantId(@Param("query") SysOrgVO sysOrgVO);

    /**
     * 获取地市级数据，包含地市级的上级.
     */
    List<SysOrgVO> getCityData(@Param("query") SysOrgVO sysOrgVO);
}
