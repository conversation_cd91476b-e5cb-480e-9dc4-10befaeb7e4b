package cn.chinaunicom.sdsi.cloud.system.config.service.impl;

import cn.chinaunicom.sdsi.cloud.system.config.dao.SysConfigMapper;
import cn.chinaunicom.sdsi.cloud.system.config.entity.SysConfigPO;
import cn.chinaunicom.sdsi.cloud.system.config.service.SysConfigService;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigQueryVO;
import cn.chinaunicom.sdsi.cloud.system.config.vo.SysConfigVO;
import cn.chinaunicom.sdsi.cloud.system.dict.entity.SysDictDataPO;
import cn.chinaunicom.sdsi.cloud.system.dict.entity.SysDictTypePO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.enums.OperationEnum;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 系统配置功能 - 系统配置服务层实现
 *
 * <AUTHOR>
 * -
 * @version 1.0
 * @date 2021/12/16
 * -
 * @description 内容描述
 */
@Slf4j
@Service
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfigPO> implements SysConfigService {

    /**
     * 新增系统配置
     *
     * @param sysConfigVO 即将要新增的系统配置对象
     * @return 新增成功后返回的数据的主键值
     * @throws ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2021/12/16
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Override
    public String addSysConfig(SysConfigVO sysConfigVO) throws ServiceErrorException {
        // 主键重复校验
        int num = this.count(new QueryWrapper<SysConfigPO>().lambda()
                .eq(SysConfigPO::getConfigCode, sysConfigVO.getConfigCode()));
        if (num > 0) {
            log.error("系统参数名称{}已存在,不能重复添加", sysConfigVO.getConfigCode());
            throw new ServiceErrorException("系统参数编码已存在,不能重复添加");
        }
        sysConfigVO.setConfigId(null);
        SysConfigPO po = new SysConfigPO();
        BeanUtils.copyProperties(sysConfigVO, po);
        this.baseMapper.insert(po);
        return po.getConfigId();
    }

    /**
     * 删除系统配置
     * 逻辑删除,逻辑删除位 DELETE_FLAG 字段.
     *
     * @param configId 即将要删除的系统配置的主键
     * @return 删除的数据条数
     * <AUTHOR>
     * -
     * @date 2021/12/16
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Override
    public int deleteSysConfigByConfigId(String configId) {
        // 根据主键判断数据是否存在
        SysConfigPO po = this.baseMapper.selectById(configId);
        if (po == null) {
            return 0;
        }
        // 删除,并返回删除行数
        int num = this.baseMapper.deleteById(configId);
        return num;
    }

    /**
     * 修改系统配置
     *
     * @param sysConfigVO 即将要修改的系统配置对象,方法会修改数据库中 configId 唯一对应的数据,修改的字段为 vo 中与表映射对象同名的字段
     * @return 修改的数据条数
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2021/12/16
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Override
    public Integer updateSysConfig(SysConfigVO sysConfigVO) throws ServiceErrorException {
        int num = this.count(new QueryWrapper<SysConfigPO>().lambda()
                .eq(SysConfigPO::getConfigCode, sysConfigVO.getConfigCode())
                .ne(SysConfigPO::getConfigId, sysConfigVO.getConfigId()));
        if (num > 0) {
            throw new ServiceErrorException("系统参数编码已存在");
        }
        SysConfigPO po = new SysConfigPO();
        BeanUtils.copyProperties(sysConfigVO, po);
        return this.baseMapper.updateById(po);
    }

    /**
     * 根据主键查询单个系统配置详情
     *
     * @param configId 主键
     * @return 查询到的系统配置对象
     * @throws ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     * <AUTHOR>
     * -
     * @date 2021/12/16
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Override
    public SysConfigVO findSysConfig(String configId) throws ServiceErrorException {
        SysConfigPO po = this.baseMapper.selectById(configId);
        if (null == po) {
            throw new ServiceErrorException("没有查询到 ["+ configId +"] 对应的数据", HttpStatus.NOT_FOUND);
        }
        SysConfigVO vo = new SysConfigVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /**
     * 根据分页信息查询系统配置列表
     * 支持根据 dictName 字典类型名称进行模糊匹配查询
     *
     * @param sysConfigQueryVO 分页查询视图对象
     * @return 携带分页信息的字典类型列表
     * <AUTHOR>
     * -
     * @date 2021/12/14
     * -
     * @version 1.0
     * @description 内容描述
     */
    @Override
    public IPage<SysConfigVO> findSysConfigPage(SysConfigQueryVO sysConfigQueryVO) {
        IPage<SysConfigVO> page = QueryVoToPageUtil.toPage(sysConfigQueryVO);
        return this.baseMapper.findSysConfigByVo(page, sysConfigQueryVO);
    }

    @Override
    public SysConfigVO findSysConfig(String configId, String staffOrgId) {
        SysConfigQueryVO vo = new SysConfigQueryVO();
        vo.setStaffOrgId(staffOrgId);
        vo.setConfigId(configId);
        List<SysConfigVO> list = this.baseMapper.findSysConfigByVo(vo);
        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public SysConfigVO findConfigByCode(String code, String staffOrgId) {
        return this.baseMapper.findConfigByCode(code, staffOrgId);
    }

    @Override
    public IPage<SysConfigVO> selectConfigByCode(SysConfigQueryVO sysConfigQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(sysConfigQueryVO);
        return this.baseMapper.selectConfigByCode(page, sysConfigQueryVO);
    }
    /**
     * 获取配置信息
     * @param sysConfigQueryVO
     * @return
     */
    @Override
    public List<SysConfigVO> selectConfigListByCode(SysConfigQueryVO sysConfigQueryVO){
        return this.baseMapper.selectConfigByCode(sysConfigQueryVO);
    }

    @Override
    public String colorMode() {
        String colorMode = "colorful";
        SysConfigVO vo = this.baseMapper.findConfigByCode("colorMode", null);
        if (vo != null && StringUtils.isNotEmpty(vo.getConfigCode())) {
            colorMode = vo.getConfigValue();
        }
        return colorMode;
    }

}
