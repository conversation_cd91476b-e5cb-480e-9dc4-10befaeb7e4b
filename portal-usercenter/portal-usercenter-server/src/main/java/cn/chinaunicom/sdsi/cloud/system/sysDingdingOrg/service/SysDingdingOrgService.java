package cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.service;

import cn.chinaunicom.sdsi.cloud.system.entity.SysTransDateVO;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffRolePO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffRoleQueryVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysTransferVO;
import cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.entity.SysDingdingOrgPO;
import cn.chinaunicom.sdsi.cloud.system.sysDingdingOrg.vo.SysDingdingOrgVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 门户组织与钉钉组织关联表 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2022-02-25
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
public interface SysDingdingOrgService extends IService<SysDingdingOrgPO> {


    /**
     * 插入关联关系
     *
     * @param sysDingdingOrgPO 关联表实体
     * @return
     */
    int addDingdingOrg(SysDingdingOrgPO sysDingdingOrgPO);

    /**
     * 查询关联数据
     *
     * @param sysDingdingOrgVo 关联表实体
     * @return
     */
    SysDingdingOrgVo selectDingdingOrg(SysDingdingOrgVo sysDingdingOrgVo);

    /**
     * 编辑关联关系
     *
     * @param sysDingdingOrgPO 关联表实体
     * @return
     */
    int updateDingdingOrg(SysDingdingOrgPO sysDingdingOrgPO);

    /**
     * 删除关联关系
     *
     * @param id 主键
     * @return
     */
    int deleteDingdingOrgById(String id);
}