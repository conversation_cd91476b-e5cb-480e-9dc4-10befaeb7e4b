package cn.chinaunicom.sdsi.cloud.system.application.service.impl;

import cn.chinaunicom.sdsi.cloud.system.application.ApplicationPO;
import cn.chinaunicom.sdsi.cloud.system.application.ApplicationQueryVO;
import cn.chinaunicom.sdsi.cloud.system.application.ApplicationVO;
import cn.chinaunicom.sdsi.cloud.system.application.dao.ApplicationMapper;
import cn.chinaunicom.sdsi.cloud.system.application.service.IApplicationService;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @return
 * @create 2020-10-31 14:17
 */
@Service
public class ApplicationServiceImpl extends ServiceImpl<ApplicationMapper, ApplicationPO>
        implements IApplicationService {

    @Autowired
    private ApplicationMapper applicationMapper;
    @Autowired
    public UnifastContext unifastContext;

    private final String APP_TYPE_DING = "ding";

    @Override
    public IPage<ApplicationVO> findPageByQueryVo(ApplicationQueryVO queryVO) {
        IPage<ApplicationQueryVO> page = QueryVoToPageUtil.toPage(queryVO);
        String tenantId= (String) unifastContext.getCustomerParamsByKey("tenantId");
        if(!UnifastConstants.DEFAULT_TENANT_ID.equals(tenantId)) {
            queryVO.setTenantId(tenantId);
        }
        return this.baseMapper.findListByQueryVo(page, queryVO);
    }
    @Override
    public List<ApplicationPO> findApplicationByListVo(ApplicationPO applicationPO) {
        //        String tenantId= (String) unifastContext.getCustomerParamsByKey("tenantId");
//        if(!UnifastConstants.DEFAULT_TENANT_ID.equals(tenantId)) {
//            applicationPO.setTenantId(tenantId);
//        }
        return this.baseMapper.findApplicationByListVo(applicationPO);
    }
    @Override
    public List<ApplicationPO> dingDingfindApplicationByListVo(ApplicationPO applicationPO) {
        return this.baseMapper.findApplicationByListVo(applicationPO);
    }
    @Override
    public List<ApplicationPO> dingDingFindApplicationByListVo(ApplicationPO applicationPO) {
        return this.baseMapper.findApplicationByListVo(applicationPO);
    }

    @Override
    public ApplicationVO findById(final String id) {
        ApplicationPO po = applicationMapper.queryById(id);
        if (null == po) {
            throw new ServiceErrorException(UnifastConstants.ERROR_NOT_FOUND, HttpStatus.NOT_FOUND);
        }
        ApplicationVO vo = new ApplicationVO();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    @Override
    public Integer updateByVo(final ApplicationVO vo) {
        if (StringUtils.isBlank(vo.getId())) {
            throw new ServiceErrorException("id不能为空", HttpStatus.BAD_REQUEST);
        }
        ApplicationPO po = new ApplicationPO();
        BeanUtils.copyProperties(vo, po);
        return this.baseMapper.updateById(po);
    }

    @Override
    public String addByVo(ApplicationVO vo) {
        ApplicationPO po = new ApplicationPO();
        BeanUtils.copyProperties(vo, po);
        this.save(po);
        return po.getId();
    }

    @Override
    public ApplicationVO getVoByCropIdAndApplicationId(String cropId, String applicationId) {
        ApplicationVO vo = new ApplicationVO();
        vo.setCropId(cropId);
        vo.setApplicationId(applicationId);
        vo.setAppType(APP_TYPE_DING);
        ApplicationPO applicationPO = this.baseMapper.findApplicationByApplication(vo);
        if (null == applicationPO) {
            throw new ServiceErrorException("应用配置信息未找到，无法使用该应用，请联系管理员", HttpStatus.BAD_REQUEST);
        }
        ApplicationVO applicationVO = new ApplicationVO();
        BeanUtils.copyProperties(applicationPO, applicationVO);
        return applicationVO;
    }
    @Override
    public ApplicationPO selectOneByTenantIdAndApplicationId(String tenantId,String applicationId){
        return baseMapper.selectOneByTenantIdAndApplicationId(tenantId,applicationId);
    }
    @Override
    public void updateTenantAppDeletedStatus(ApplicationPO applicationPO){
        baseMapper.updateTenantAppDeletedStatus(applicationPO);
    }

    @Override
    public ApplicationPO findApplicationByApplication(ApplicationVO applicationVO) {
        applicationVO.setAppType(APP_TYPE_DING);
        return this.baseMapper.findApplicationByApplication(applicationVO);
    }

    @Override
    public Boolean findApplicationByApplicationId(ApplicationVO applicationVO) {
        applicationVO.setTenantId(unifastContext.getUser().getTenantId());
        ApplicationPO applicationPO = this.baseMapper.findApplicationByApplicationId(applicationVO);
        if (null != applicationPO) {
            throw new ServiceErrorException("应用主键已经存在", HttpStatus.BAD_REQUEST);
        }
        return true;
    }
}
