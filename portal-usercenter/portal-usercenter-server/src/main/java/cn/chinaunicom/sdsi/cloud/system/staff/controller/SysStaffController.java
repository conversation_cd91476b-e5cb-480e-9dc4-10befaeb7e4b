package cn.chinaunicom.sdsi.cloud.system.staff.controller;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.system.config.properties.UniBootProperties;
import cn.chinaunicom.sdsi.cloud.system.enums.UnifastEnum;
import cn.chinaunicom.sdsi.cloud.system.org.entity.SysOrgVO;
import cn.chinaunicom.sdsi.cloud.system.org.service.ISysOrgService;
import cn.chinaunicom.sdsi.cloud.system.permission.entity.SysPermissionVO;
import cn.chinaunicom.sdsi.cloud.system.permission.service.ISysPermissionService;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import cn.chinaunicom.sdsi.cloud.system.staff.entity.SysStaffPO;
import cn.chinaunicom.sdsi.cloud.system.staff.excel.SysStaffUploadData;
import cn.chinaunicom.sdsi.cloud.system.staff.excel.SysStaffUploadDataListener;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffOrgService;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffRoleService;
import cn.chinaunicom.sdsi.cloud.system.staff.service.SysStaffService;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.*;
import cn.chinaunicom.sdsi.core.annotation.OperateLog;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.core.interfaces.Add;
import cn.chinaunicom.sdsi.core.interfaces.Delete;
import cn.chinaunicom.sdsi.core.interfaces.Edit;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.alibaba.excel.EasyExcel;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.BindException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.groups.Default;
import java.io.IOException;
import java.util.List;

/**
 * <b>用户管理功能 - 用户控制层接口</b><br>
 * <b>描述: </b>
 * 用户管理功能 - 用户控制层接口
 *
 * <AUTHOR>
 * @date 2022-04-06
 */
@Slf4j
@Tag(name = "用户控制层接口")
@RestController
@RequestMapping("/staffs")
public class SysStaffController extends BaseController {

    /**
     * 【注入对象】 项目级配置上下文对象
     */
    @Resource
    private UniBootProperties uniBootProperties;

    /**
     * 【注入对象】 联通集成组件上下文对象
     */
    @Resource
    public UnifastContext unifastContext;

    /**
     * 【注入对象】 用户服务层接口
     */
    @Resource
    private SysStaffService sysStaffService;

    /**
     * 【注入对象】 用户组织关系服务层接口
     */
    @Resource
    private SysStaffOrgService sysStaffOrgService;

    /**
     * 【注入对象】 用户角色关系服务层接口
     */
    @Resource
    private SysStaffRoleService sysStaffRoleService;

    /**
     * 【注入对象】 权限服务层接口
     */
    @Resource
    private ISysPermissionService sysPermissionService;

    /**
     * 【注入对象】 组织服务层接口
     */
    @Resource
    private ISysOrgService iSysOrgService;

    /**
     * 新增人员信息
     * 1.检验登录账号是否存在，如果存在抛出业务异常ServiceErrorException
     * 2.保存人员表，并返回人员ID到实体对象
     * 3.根据staffID和orgId新增人员主岗信息
     *
     * @param staffVO 标签说明
     * @return 标签说明
     */
    @Operation(summary = "新增人员", description ="新增人员数据")
    @PostMapping(produces = "application/json")
//    @PreAuthorize("hasAnyAuthority('sys:staff:add')")
    @OperateLog("新增人员")
    public BaseResponse<String> addStaff(@Validated(value = {Add.class, Default.class}) @RequestBody SysStaffVO staffVO) {
        return ok(sysStaffService.addByVo(staffVO));
    }

    /**
     * 删除人员信息
     * 1.更新人员表删除标志
     * 2.更新岗位表状态标志
     * 3.删除岗位角色表映射关系
     *
     * @param staffVO 标签说明
     * @return 标签说明
     */
    @Operation(summary = "删除人员", description ="根据人员ID删除人员信息，失效岗位，删除岗位角色映射关系")
    @Parameter(name = "人员ID", description = "staffId", required = true, in = ParameterIn.PATH)
    @PostMapping("/delete")
    //@PreAuthorize("hasAnyAuthority('sys:staff:delete')")
    @OperateLog("删除人员")
    public BaseResponse<Integer> deleteStaff(@RequestBody @Validated(value = Delete.class) SysStaffVO staffVO) {
        if (uniBootProperties.getProtectStaffIds().contains(staffVO.getStaffId())) {
            throw new BusinessException("该用户不可删除");
        }
        return ok(sysStaffService.deleteById(staffVO.getStaffId()));
    }

    /**
     * 修改人员信息
     * 1.把非null的数据更新到人员表
     * 2.如果主岗变化，先失效旧岗位再新增主岗
     *
     * @param staffVO 标签说明
     * @return 标签说明
     */
    @Operation(summary = "修改人员", description ="修改个人基本信息")
    @Parameter(name = "人员ID", description = "staffId", required = true, in = ParameterIn.PATH)
    @PostMapping(value = "edit")
//    @PreAuthorize("hasAnyAuthority('sys:staff:edit')")
    @OperateLog("编辑人员")
    public BaseResponse<Integer> updateStaff(@RequestBody
                                             @Validated(value = {Edit.class, Default.class}) SysStaffVO staffVO) {
        if (uniBootProperties.getProtectStaffIds().contains(staffVO.getStaffId())) {
            if (UnifastEnum.SysDataValid.INVALID.value().equals(staffVO.getStaffOrgStatus())) {
                throw new BusinessException("该用户不可禁用");
            }
        }
        return ok(sysStaffService.updateByVo(staffVO));
    }

    /**
     * 根据人员id查询人员详细信息
     * 仅展示人员表基本详细
     *
     * @param staffId 标签说明
     * @return 标签说明
     * @throws BindException 异常
     */
    @Operation(summary = "查询个人信息", description ="根据人员id查询人员详细信息")
    @Parameters({
            @Parameter(name = "人员ID", description = "staffId", required = true, in = ParameterIn.PATH)
    })
    @GetMapping(value = "/getUser/{staffId}")
    public BaseResponse<SysStaffInfoVO> findStaffById(@PathVariable(value = "staffId") String staffId) throws BindException {
        try {
//            log.error("getUser=============="+staffId);
            //人员信息

            return ok(sysStaffService.findInfoVoById(staffId));
        } catch (ServiceErrorException e) {
            throw new BindException("error","error");
            // [anchor] 添加一个锚点,便于后续回滚
            // return new BaseResponse<SysStaffInfoVO>(ResponseEnum.STATUS_CODE_102.getCode(), e.getMessage(), null);
        }
    }

    /**
     * 根据人员id查询人员详细信息
     * 仅展示人员表基本详细
     *
     * @param staffId    标签说明
     * @param staffOrgId 标签说明
     * @return 标签说明
     */
    @Operation(summary = "查询个人详细信息", description ="根据人员id查询人员详细信息，包括其角色、岗位等")
    @Parameters({
            @Parameter(name = "人员ID", description = "staffId", required = true, in = ParameterIn.PATH),
            @Parameter(name = "岗位ID", description = "staffOrgId", required = true, in = ParameterIn.PATH)
    })
    @GetMapping(value = "/{staffId}/{staffOrgId}")
    public BaseResponse<SysStaffInfoVO> findStaffInfo(@PathVariable String staffId, @PathVariable String staffOrgId) {
        //人员信息
        SysStaffInfoVO sv = sysStaffService.findInfoVoById(staffId);
        //查询岗位信息
        SysStaffOrgQueryVO queryVO = new SysStaffOrgQueryVO();
        queryVO.setStaffId(staffId);
        List<SysStaffOrgVO> staffOrgs = sysStaffOrgService.findStaffOrgs(queryVO);
        //查询角色信息
        List<SysRoleVO> roles = sysStaffRoleService.findHadRoles(staffOrgId);
        //查询权限集合
        List<SysPermissionVO> permissions = sysPermissionService.findListByStaffOrgId(staffOrgId);
        //集合都放在人员实体中
        sv.setStaffOrgs(staffOrgs);
        sv.setRoles(roles);
        sv.setPermissions(permissions);
        return ok(sv);
    }

    /**
     * 验证登录账号是否存在
     * 返回true表示账号已存在
     *
     * @param loginName 标签说明
     * @param tenantId  标签说明
     * @return 标签说明
     */
    @Operation(summary = "验证登录账号唯一性", description ="验证登录账号，如果存在，返回人员实体")
    @Parameter(name = "loginName", description ="人员账号", required = true, in = ParameterIn.PATH)
    @GetMapping("/checkLoginName/{loginName}/{tenantId}")
    public BaseResponse<SysStaffVO> checkLoginName(@PathVariable("loginName") String loginName, @PathVariable("tenantId") String tenantId) {
        SysStaffVO vo = sysStaffService.checkLoginName(loginName, tenantId);
        return ok(null == vo ? new SysStaffVO() : vo);
    }

    /**
     * 新增验证手机号是否存在
     * 返回true表示手机号已存在
     *
     * @param cellphone 标签说明
     * @param tenantId  标签说明
     * @return 标签说明
     */
    @Operation(summary = "新增验证手机号唯一性", description ="新增验证手机号，如果存在，返回true")
    @Parameter(name = "cellphone", description ="手机号", required = true, in = ParameterIn.PATH)
    @GetMapping("/checkCellphone/{cellphone}/{tenantId}")
    public boolean checkCellphone(@PathVariable("cellphone") String cellphone, @PathVariable(value = "tenantId") String tenantId) {
        return sysStaffService.checkCellphone(cellphone, tenantId);
    }

    /**
     * 新增验证手机号是否存在
     * 返回true表示手机号已存在
     *
     * @param cellphone 标签说明
     * @return 标签说明
     */
    @Operation(summary = "新增验证手机号唯一性", description ="新增验证手机号，如果存在，返回true")
    @Parameter(name = "cellphone", description ="手机号", required = true, in = ParameterIn.PATH)
    @GetMapping("/checkCellphone/{cellphone}")
    public boolean checkCellphone(@PathVariable("cellphone") String cellphone) {
        return sysStaffService.checkCellphone(cellphone, null);
    }

    /**
     * 修改密码
     *
     * @param vo SysStaffPdVO
     * @return Integer
     */
    @Operation(summary = "修改密码")
    @PostMapping(value = "/change")
    @PreAuthorize("isAuthenticated()")
    @OperateLog("修改密码")
    public BaseResponse<Integer> updatePasswd(@RequestBody @Valid SysStaffPdVO vo) {
        MallUser user = unifastContext.getUser();
        final SysStaffPO po = this.sysStaffService.getById(user.getStaffId());
        int count = sysStaffService.updateUserPwd(vo, po);
        if (count > 0) {
            ok(count);
        }
        return notOk();
    }

    /**
     * 重置密码
     * 重置的密码来自系统配置表
     *
     * @param sysStaffVO 标签说明
     * @return 标签说明
     */
    @Operation(summary = "重置密码")
    @OperateLog("管理员重置密码")
    @PostMapping("/reset")
    @PreAuthorize("hasAnyAuthority('system:user:resetPwd')")
    public BaseResponse<String> resetPasswd(@RequestBody @Validated(value = Edit.class) SysStaffVO sysStaffVO) {
        return ok(sysStaffService.resetPasswd(sysStaffVO.getStaffId(), sysStaffVO.getNewPassWord()));
    }
//    /**
//     * 重置密码
//     * 重置的密码来自系统配置表
//     *
//     * @param sysStaffVO 标签说明
//     * @return 标签说明
//     */
//    @Operation(summary = "重置密码")
//    @OperateLog("管理员重置密码")
//    @PostMapping("/reset")
//    @PreAuthorize("hasAnyAuthority('system:user:resetPwd')")
//    public BaseResponse<String> resetPasswd(@RequestBody @Validated(value = Edit.class) SysStaffVO sysStaffVO) {
////        System.out.println(gettelend("15508653490"));
//        List<SysStaffVO> list = sysStaffService.getAllUser();
//        int i=0;
//        int count=list.size();
//        for (SysStaffVO vo :list) {
//            if(StringUtils.isNotEmpty(vo.getCellphone())&&vo.getCellphone().length()>=11&&!"sysman".equals(vo.getLoginName())){
//
//                try {
//                    System.out.println("共计"+count+"条======正在修改第======="+(i+1)+"条");
//                    sysStaffService.resetPasswd(vo.getStaffId(), "Sdokjdn@"+gettelend(vo.getCellphone())+"!@#2022");
//                    i+=1;
//                } catch (Exception e) {
//                    System.out.println("============="+vo.getStaffName()+"修改密码失败");
//                    System.out.println("共计"+count+"条=======修改成功======"+i+"条");
//                    throw new RuntimeException(e);
//                }
//                System.out.println("共计"+count+"条=======修改成功======"+i+"条");
//
//            }
//
//        }
////        JSONArray synchJson = new JSONArray(list);
////        System.out.println("========================resetPasswd=================================="+synchJson);
//
////        sysStaffService
////        return ok(sysStaffService.resetPasswd(sysStaffVO.getStaffId(), sysStaffVO.getNewPassWord()));
//        return ok("success");
//    }

    private String gettelend(String tel){
        return tel.substring(tel.length()-6);
    }

    /**
     * 用户重置密码发送验证码
     *
     * @param vo 标签说明
     * @return 标签说明
     * <AUTHOR>
     */
    @Operation(summary = "获取手机短信验证码", description ="获取手机短信验证码")
    @PostMapping("/getVerificationCode")
    public BaseResponse<String> getVerificationCode(@RequestBody SysStaffVO vo) {
        return ok(sysStaffService.getVerificationCode(vo));
    }

    /**
     * 标签说明
     *
     * @param vo 标签说明
     * @return 标签说明
     * <AUTHOR>
     */
    @Operation(summary = "未登录时根据手机号、验证码重置密码", description ="未登录时根据手机号、验证码重置密码")
    @PostMapping("/forgottenPassword")
    public BaseResponse<Integer> forgottenPassword(@RequestBody SysStaffVO vo) {
        return ok(sysStaffService.forgottenPassword(vo));
    }

    /**
     * <b>根据微信全局唯一标识查询用户信息条数</b><br>
     * <b>描述: </b>
     * 根据微信全局唯一标识查询用户信息条数
     *
     * <AUTHOR>
     * @date 2022-04-13
     *
     * @param unionId 用户微信全局唯一标识
     * @return {@link Integer} 查询到的用户信息条数
     */
    @Operation(summary = "根据微信全局唯一标识查询用户信息条数", description ="根据微信全局唯一标识查询用户信息条数")
    @PostMapping("/findNumByUnionId")
    public Integer findNumByUnionId(@RequestParam(value = "unionId") String unionId) {
        return sysStaffService.findNumByUnionId(unionId);
    }

    /**
     * <b>微信绑定发送短信验证码</b><br>
     * <b>描述: </b>
     * 微信绑定发送短信验证码
     *
     * <AUTHOR>
     * @date 2022-04-13
     *
     * @param mobile 接收验证码的手机号
     * @exception ServiceErrorException 服务接口调用过程中,内部逻辑错误,如重复性校验失败\关键性参数缺失等
     */
    @Operation(summary = "微信绑定发送短信验证码", description ="微信绑定发送短信验证码")
    @PostMapping("/getCaptchaByWechatBind")
    public BaseResponse<String> getCaptchaByWechatBind(@RequestParam(value = "mobile") String mobile) {
        sysStaffService.getCaptchaByWechatBind(mobile);
        return ok("success");
    }

    /**
     * <b>根据用户手机号绑定微信unionid</b><br>
     * <b>描述: </b>微信公众号 - 根据用户手机号绑定微信unionid
     *
     * <AUTHOR>
     * @date 2022-04-06
     *
     * @param mobile 绑定手机号
     * @param smscode 短信验证码
     * @param unionid 用户微信全局唯一标识
     * @param openid 用户微信公众号id
     * @param tenantId 租户id
     * @return {@link BaseResponse<List<SysStaffVO>>} 如果手机号对应多个租户下的用户时,返回这些用户列表
     */
    @Operation(summary = "根据用户手机号绑定微信unionid", description ="微信公众号 - 根据用户手机号绑定微信unionid")
    @PostMapping("/wechatBind")
    public BaseResponse<List<SysStaffVO>> wechatBind(@RequestParam(value = "mobile") String mobile,
                                           @RequestParam(value = "smscode") String smscode,
                                           @RequestParam(value = "unionid") String unionid,
                                           @RequestParam(value = "openid", required = false) String openid,
                                           @RequestParam(value = "tenantId", required = false) String tenantId) {
        List<SysStaffVO> sysStaffVOs = sysStaffService.wechatBind(mobile, smscode, unionid, openid, tenantId);
        return new BaseResponse(sysStaffVOs);
    }

    /**
     * <b>微信小程序 - 根据用户手机号绑定微信unionid</b><br>
     * <b>描述: </b>
     * 微信小程序 - 根据用户手机号绑定微信unionid
     *
     * <AUTHOR>
     * @date 2022-04-06
     *
     * @param mobile 绑定手机号
     * @param unionid 用户微信全局唯一标识
     * @param tenantId 租户id
     * @return {@link BaseResponse<List<SysStaffVO>>} 如果手机号对应多个租户下的用户时,返回这些用户列表
     */
    @Operation(summary = "微信小程序 - 根据用户手机号绑定微信unionid", description ="微信小程序 - 根据用户手机号绑定微信unionid")
    @PostMapping("/wechatBindMp")
    public BaseResponse<List<SysStaffVO>> wechatBindMp(@RequestParam(value = "mobile") String mobile,
                                             @RequestParam(value = "unionid") String unionid,
                                             @RequestParam(value = "tenantId", required = false) String tenantId) {
        List<SysStaffVO> sysStaffVOs = sysStaffService.wechatBindMp(mobile, unionid, tenantId);
        return new BaseResponse(sysStaffVOs);
    }

    /**
     * <b>用户数据导入</b><br>
     * <b>描述: </b>
     * 用户数据导入
     *
     * <AUTHOR>
     * @date 2022-04-06
     *
     * @param multipartFile 上传的文件
     * @return {@link BaseResponse<String>} 调用是否成功
     */
    @Operation(summary = "用户导入文件上传", description ="上传导入用户数据的模板excel文件")
    @Parameters({
            @Parameter(name = "multipartFile", description ="文件", required = true),
            @Parameter(name = "appCode", description ="应用标识", required = true, in = ParameterIn.QUERY)
    })
    @PostMapping(value = "/upload", headers = "content-type=multipart/form-data")
    public BaseResponse<String> upload(@RequestParam(value = "multipartFile") MultipartFile multipartFile) {
        try {
            SysOrgVO rootNode = iSysOrgService.findRootNodeByTenantId(SysOrgVO.builder()
                    .tenantId(UnifastConstants.DEFAULT_TENANT_ID)
                    .build());
            EasyExcel.read(multipartFile.getInputStream(), SysStaffUploadData.class, new SysStaffUploadDataListener(sysStaffService, rootNode)).sheet().doRead();
        } catch (IOException e) {
            e.printStackTrace();
            return notOk();
        }
        return ok("");
    }

    @Operation(summary = "分页查询客户列表", description ="根据条件查询客户人员数据，分页展示")
    @GetMapping("/getCustomerPageList")
    public BasePageResponse<SysStaffOrgVO> getCustomerPageList(SysStaffOrgQueryVO queryVO) {
        return pageOk(sysStaffService.getCustomerPageList(queryVO));
    }
}
