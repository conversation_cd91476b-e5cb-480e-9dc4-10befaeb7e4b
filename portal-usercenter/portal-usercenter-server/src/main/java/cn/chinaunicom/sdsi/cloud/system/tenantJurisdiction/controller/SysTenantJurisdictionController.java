package cn.chinaunicom.sdsi.cloud.system.tenantJurisdiction.controller;

import cn.chinaunicom.sdsi.cloud.system.tenantJurisdiction.entity.SysTenantJurisdictionQueryVO;
import cn.chinaunicom.sdsi.cloud.system.tenantJurisdiction.entity.SysTenantJurisdictionVO;
import cn.chinaunicom.sdsi.cloud.system.tenantJurisdiction.service.SysTenantJurisdictionService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>文件名称: SysTenantJurisdictionController </p>
 * <p>描述: 租户管辖关联接口 </p>
 * <p>创建时间: 2022/3/23 17:57</p>
 *
 * <AUTHOR>
 */
@Tag(name = "租户管辖管理接口")
@RestController
@RequestMapping("/tenantJurisdiction")
public class SysTenantJurisdictionController extends BaseController {
  @Resource
  private SysTenantJurisdictionService sysTenantJurisdictionService;

  @Operation(summary = "分页查询")
  @GetMapping
  public BasePageResponse<SysTenantJurisdictionVO> page(@Valid SysTenantJurisdictionQueryVO sysTenantQueryVO) {
    return pageOk(sysTenantJurisdictionService.page(sysTenantQueryVO));
  }

  /**
   * 获取被自己管辖的租户ID.
   */
  @Operation(summary = "获取被自己管辖的租户ID", description ="获取被自己管辖的租户ID")
  @PostMapping("/getJurisdictionTenantIds")
  public BaseResponse<List<String>> getJurisdictionTenantIds(@RequestBody SysTenantJurisdictionQueryVO queryVO) {
    return ok(sysTenantJurisdictionService.getJurisdictionTenantIds(queryVO));
  }

  /**
   * 获取被自己管辖的租户.
   */
  @Operation(summary = "获取被自己管辖的租户", description ="获取被自己管辖的租户")
  @PostMapping("/getJurisdictionTenantList")
  public BaseResponse<List<SysTenantJurisdictionVO>> getJurisdictionTenantList(
      @RequestBody SysTenantJurisdictionQueryVO queryVO) {
    return ok(sysTenantJurisdictionService.getJurisdictionTenantList(queryVO));
  }

  /**
   * 添加管辖租户.
   */
  @Operation(summary = "添加管辖租户", description ="添加管辖租户")
  @PostMapping("/addJurisdictionTenant")
  public BaseResponse<?> addJurisdictionTenant(@RequestBody SysTenantJurisdictionVO vo) {
    return ok(sysTenantJurisdictionService.addJurisdictionTenant(vo));
  }
}