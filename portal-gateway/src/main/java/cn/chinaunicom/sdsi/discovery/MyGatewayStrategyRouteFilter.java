//package cn.chinaunicom.sdsi.discovery;
//
//import cn.chinaunicom.sdsi.domain.Rule;
//import cn.chinaunicom.sdsi.domain.Rules;
//import org.springframework.beans.factory.annotation.Value;
//
//import javax.annotation.Resource;
//
///**
// * 适用于A/B Testing或者更根据某业务参数决定灰度路由路径。可以结合配置中心分别配置A/B两条路径，可以动态改变并通知
// * 当Header中传来的用户为张三，执行一条路由路径；为李四，执行另一条路由路径
// * <AUTHOR>
// */
//public class MyGatewayStrategyRouteFilter extends DefaultGatewayStrategyRouteFilter {
//
//    @Override
//    public int getOrder() {
//        return 10099;
//    }
//
//    @Resource
//    private Rules rules;
//
//    @Value("${b.route.version:}")
//    private String bRouteVersion;
//
//    // 自定义全链路条件命中
//    @Override
//    public String getRouteVersion() {
//        String user = strategyContextHolder.getHeader("discovery_user");
//        String org = strategyContextHolder.getHeader("discovery_org");
//        for (Rule route : rules.getRoute()) {
//            if (route.getU() != null && route.getU().equals(user)) {
//                return route.getRules();
//            } else {
//                if (route.getO() != null && route.getO().equals(org)) {
//                    return route.getRules();
//                }
//            }
//        }
//        if (bRouteVersion != null) {
//            return bRouteVersion;
//        }
//
//        return super.getRouteVersion();
//    }
//}