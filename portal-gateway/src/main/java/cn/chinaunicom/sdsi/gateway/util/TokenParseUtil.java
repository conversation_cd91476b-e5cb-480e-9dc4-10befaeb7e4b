package cn.chinaunicom.sdsi.gateway.util;

import cn.chinaunicom.sdsi.domain.TokenPayload;
import cn.hutool.json.JSONUtil;
import com.nimbusds.jose.JWSObject;

import java.text.ParseException;

/**
 * <AUTHOR>
 */
public class TokenParseUtil {

    /**
     * 解析token工具类
     * @param token token
     * @return token加密前的信息
     */
    public static TokenPayload tokenParse(String token){
        TokenPayload payloadDto = null;
        try {
            JWSObject jwsObject = JWSObject.parse(token);
            String payload = jwsObject.getPayload().toString();
            payloadDto = JSONUtil.toBean(payload, TokenPayload.class);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return payloadDto;
    }
}
