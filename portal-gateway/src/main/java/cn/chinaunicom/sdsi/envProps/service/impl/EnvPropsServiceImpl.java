package cn.chinaunicom.sdsi.envProps.service.impl;

import cn.chinaunicom.sdsi.envProps.service.EnvPropsService;
import cn.chinaunicom.sdsi.envProps.util.ConfigContextUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Iterator;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2022/4/6 16:11
 */
@Service
public class EnvPropsServiceImpl implements EnvPropsService {

    @Value("${unifast.allowEnv:false}")
    private Boolean allowEnv;
    @Override
    public HashMap<String, Object> getSpringConfig() {
        if (allowEnv) {
            ConfigurableApplicationContext ctx = ConfigContextUtil.getonfigurableApplicationContext();
            //获取环境变量
            ConfigurableEnvironment environment = ctx.getEnvironment();
            //获取属性源
            MutablePropertySources propertySources = environment.getPropertySources();
            Iterator<PropertySource<?>> propsIterator = propertySources.iterator();
            HashMap<String, Object> props = new HashMap<>();
            while (propsIterator.hasNext()) {
                PropertySource<?> propertySource = propsIterator.next();
                Object prop = propertySource.getSource();
                ObjectMapper jsonObjectMapper = new ObjectMapper();
                String propString = null;
                try {
                    propString = jsonObjectMapper.writeValueAsString(prop);
                } catch (JsonProcessingException e) {
                }
                JSONObject propObject = JSONUtil.parseObj(propString);
                props.put(propertySource.getName(), propObject);
            }
            return props;
        }else {
            return null;
        }
    }
}
