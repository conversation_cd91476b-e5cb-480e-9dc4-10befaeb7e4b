package cn.chinaunicom.sdsi.envProps.util;

import org.springframework.context.ConfigurableApplicationContext;

/**
 * @Description:ConfigurableApplicationContext上下文工具类
 * @Author： cuiyuanzhen
 * @Date: 2022/4/6 15:26
 */
public class ConfigContextUtil {

    private static ConfigurableApplicationContext configurableApplicationContext;

    /*
     * <AUTHOR>
     * @Description
     * @Date 15:41 2022/4/6
     * @param
     * @return org.springframework.context.ConfigurableApplicationContext
     **/
    public static ConfigurableApplicationContext getonfigurableApplicationContext() {
        return configurableApplicationContext;
    }

    /*
     * <AUTHOR>
     * @Description
     * @Date 15:41 2022/4/6
     * @param configurableApplicationContext
     * @return void
     **/
    public static void setConfigurableApplicationContext(ConfigurableApplicationContext configurableApplicationContext) {
        ConfigContextUtil.configurableApplicationContext = configurableApplicationContext;
    }
}
