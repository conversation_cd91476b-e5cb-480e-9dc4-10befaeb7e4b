# Unifast后端开发规范

## 全局

* 首先要遵循公司制定的软件开发规范
* 本文档在公司规范基础上，对Unifast后台代码规范进行补充

## Controller规范

* 全局
  * 所有功能型Controller均定义为`@RestController`

  * 所有功能型Controller均继承`cn.chinaunicom.sdsi.framework.base.BaseController`

  * 所有功能型接口，尽量贴合RESTful风格，Get请求做查询，Post请求做新增，Put请求做编辑，Delete请求做（逻辑）删除

* 方法命名
  * 查询方法，以find开头

  * 新增方法，以add开头

  * 删除方法（包括逻辑删除），以delete开头

  * 编辑方法，以update开头

  * 其他有具体含义的方法，以合理的英文动词开头，正例：`resetPassword`

* RequestMapping命名

  * __所有接口路径不能以 `/` 结尾__

  * 每个Controller类上要有全局的映射，精简、体现当前类的功能；正例：`/staffs`, `/orgs`，反例：`/sysStaffController`, `/getMenu`

  * 每个功能型Controller下的基础的增删改查，路径可为空，它将沿用Controller上定义的全局，通过不同的RequestMethod做相应的操作

  * 查询详情，或者根据某一个值查询数据，参数放到路径中，即`/{staffId}`

* 参数
  * 超过两个参数的方法，将参数封装为VO对象，详情参见Entity规范中关于VO的描述

  * 分页查询的对象，需要继承`cn.chinaunicom.sdsi.framework.base.BaseQueryVO`

  * VO对象参数前要加`@Valid`或`@Validated`注解，激活后端验证，针对新增、编辑方法，共用同一个VO对象，其验证规则不同，
  需要使用 `@Validated(value = {Add.class, Default.class})`或`@Validated(value = {Edit.class, Default.class})`

* 返回值

  * 如何获取返回值
 ~~~
  //如何获取到插入后的主键。
  //使用范围：数据层
  SysStaffPO staffPO = new SysStaffPO();
  BeanUtils.copyProperties(staffVO, staffPO);
  sysStaffOrgMapper.insert(staffPO );
  //获取ID,获取其他属性同理。
  staffPO .getId();
~~~
  * 统一用标准返回BaseController.ok(T)、pageOk(IPage<T>)、自定义new BaseResponse();
  
  * 业务处理正常，则Http状态码为200，其余可预判的非正常情况，用`异常返回 throw new BusinessException("message");`来抛出异常，平台已将此异常的Http状态码处理为406返回

  * 新增的方法，返回新插入数据的主键，即`ok<Integer>`（如果主键是字符，则为`ok<String>`）

  * 编辑、删除或相关对数据有影响的方法，非特殊要求，返回影响条数，即`ok<Integer>`

  * 列表查询的结果集，即接口返回定义为 `ok<BasePageResponse<E>>`，其中`E`为`List<E>`中的列表元素，即返回给前台的VO对象

  * 查询单个对象，或非分页的集合，则将返回值作为T，通过`ok<T>`返回

* 注解
  * 方法上的映射注解，采用`GetMapping`、`PostMapping`等，  不要用`RequestMapping`

  * __OperateLog__  
  系统日志是采取AOP机制实现，在需要进行日志记录的方法上，增加`@OperateLog("方法用途")`的注解，即可实现对该方法的日志记录。记录的数据由request对象、spring上下文获取；该方式取得的值有限，如果想记录更详细的内容，建议自行实现。

  * __Swagger__
    * Controller类必须写`@Api`注解，`tags`中简要写出此类的作用，`description`中可做详细描述，如：
        ```java
        @Tag(name = "系统日志接口", description = "本接口用于sys_log表的查询，数据由框架代码根据系统调用情况来进行记录")
        @RestController
        @RequestMapping("logs")
        public class SysLogController extends BaseController {}
        ```
    * 每个公共方法（接口）必须写`@ApiOperation`注解，`value`中简要说明接口作用，`notes`中详细描述，`tags`与类的`@Api`中的`tags`保持一致

    * 如果接口中包含基本数据类型的参数，必须写`@ApiImplicitParam`注解来说明参数的含义、参数数据类型、参数传输类型等，如：`@Parameter(name = "sysLogId", description ="日志ID", in = ParameterIn.PATH, required = true)`，多个参数，用如下方式定义：
        ```
        @Parameters({
                @Parameter(),
                @Parameter()
        })
        ``` 
      如果参数是VO实体，Swagger会读取实体中的相关注解，无需在方法上重复编写

* 注释

  * 类的注释模版：

  ```java
  /**
   * 系统日志接口
   *
   * @project: unifast-boot
   * @file: cn.chinaunicom.sdsi.systemn: 系统日志接口，用于sys_log表的查询
   * @author: wangwj
   * @date: 2018-09-10
   * @version: V1.0
   * @copyright: Copyright(c) 2018 Sdcncsi Co. Ltd. All rights reserved.
   */
  ```

  * 方法注释的模版：_由于写了swagger接口，对Controller方法上的注释不做硬性要求_

  ```java
   /**
    * 添加日志信息
    *
    * @param sysLog
    * @return
    * <AUTHOR> 2018-09-05
    */
  ```

## Service规范

* 所有Service必须定义接口

* 接口中方法必须详细注释，实现类中不做强制要求

* 接口方法命名参考Controller规范中的命名要求

* Service中方法的参数与返回值中使用的实体对象，均采用VO，不用PO

* 实现类以 接口名+Impl后缀命名，路径存放到接口文件所在包的impl子包下面

* 所有接口实现类继承`cn.chinaunicom.sdsi.framework.base.BaseService`

* 分页方法，统一返回`com.github.pagehelper.PageInfo<T>`

* 一个功能模块下的Service，允许调用其他模块的Service，不要直接用其他模块的Mapper

* 一个功能模块下的Service，不要做查询其他模块表的方法（允许返回存在一定关联关系的数据，但不能以其他模块的数据作为主体），更要极力避免变更其他模块的表，如果有此业务需要，请通过调用其他模块的Service的方式来实现

## DAO规范

* 通过MybatisGenerator生成的方法，请酌情删减，只保留用到的，比如`insert`和`insertSelective`，只保留`insert`即可，而`updateByPrimaryKey`和`updateByPrimaryKeySelective`，建议保留`updateByPrimaryKeySelective`

* 尽量对Mapper的接口进行复用，而不是每个Service方法都对应新建一个Mapper接口，比如对某个对象的编辑、启用/禁用、逻辑删除等操作，其实都是对记录的编辑，都调用Mapper中的`updateByPrimaryKeySelective`即可

* 查询方法，均用实体来封装结果集，不得使用Map，并且尽量不使用PO实体，而是针对实际情况，建立VO实体，只存放必要的数据

## Entity规范

* [Lombok](https://projectlombok.org/)
  * 建议所有实体，采用Lombok的@Data注解，节省getter、setter代码

* Validator
  * PO实体、用户表单提交的VO实体，必须根据业务要求、数据库表结构等因素，编写详细的验证注解，常用注解：

    |注解|作用|
    |----------|:-------------:|
    |@Null|引用为空|
    |@NotNull|引用不为空|
    |@AssertTrue|布尔值为真|
    |@AssertFalse|布尔值为假|
    |@NotEmpty|字符串引用和值都不是空|
    |@Length|验证字符串长度必须在指定范围内|
    |@Min|数字的最小值|
    |@Max|数字的最大值|
    |@DecimalMin|浮点数字的最小值|
    |@DecimalMax|浮点数字的最大值|
    |@Past|日期必须是过去|
    |@Future|日期必须是未来|
    |@Pattern|字符串必须匹配正则表达式|
    |@Size|验证集合内元素数量是否在指定范围内|
    |@Email|验证字符串是否是一个有效的电子邮箱|
    |@URL|字符串是否是一个有效的URL|
    |@Valid|递归验证引用|
* PO、VO？
    VO（View Object）：
    视图对象，用于展示层，它的作用是把某个指定页面（或组件）的所有数据封装起来。
    PO（Persistent Object）：
    持久化对象，它跟持久层（通常是关系型数据库）的数据结构形成一一对应的映射关系，那么，数据表中的每个字段（或若干个）就对应PO的一个（或若干个）属性。
    注意继承BaseEntity
* Swagger

  * 实体上编写`@ApiModel`

  * 属性上编写`@ApiModelProperty`
## feign调用
责任划分，谁的服务谁开放
fallback，必须写，返回值建议和调用方讨论，并且在返回值上加特定标记,标示是feign fallBack触发返回的。
A调用B，如果A需要接受B的异常，请不要使用BaseResponse或者BasePageResponse作为返回值，如果非得用则需要根据消息体判断是否异常
feign中的参数如果使用RequestParam则必须加上RequestParma("paramsName)
## 公共对象

* 系统级字典值枚举 `cn.chinaunicom.sdsi.framework.enums.UnifastEnum`

* Validator中用于标记为添加的接口 `cn.chinaunicom.sdsi.core.interfaces.Add`

* Validator中用于标记为编辑的接口 `cn.chinaunicom.sdsi.core.interfaces.Edit`

* 统一分页返回对象 `cn.chinaunicom.sdsi.core.response.BasePageVO<T>`

## Excel导出

* 如果不是模板导出，需要在导出时将所有的已知数据类型声明，提前做好数据计算，严禁使用公式
* 如果模板导出，需要严格校验模板的真伪。



## 2020.0.1升级说明

* 1、springboot版本升至2.6.6 、unifast版本升级至2.7.2-SNAPSHOT
* 2、springcloud升至 2020.0.1 GA 版
* 3、新增portal-parent 负责管理全局通用版本号和整体编译
* 4、cloud-core为微服务基础依赖，常用的依赖均已引入
* 5、原portal-storage中artifactId改为portal-cms 仓库名称未更改
* 6、原mutil-platform改为 exchange
* 7、springfox库因不兼容springboot新版已经全替换为springdoc 影响体现在swaager的注解上，具体参考下方
* 8、因循环依赖及bean覆盖swagger兼容问题导致的兼容性建议增加

~~~yaml
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

unifast:
  swagger-enabled: false
~~~


## springdoc 替换swaager说明
 SpringFox			SpringDoc
 @Api				@Tag
 @ApiIgnore			@Parameter(hidden = true)or@Operation(hidden = true)or@Hidden
 @ApiImplicitParam	@Parameter
 @ApiImplicitParams	@Parameters
 @ApiModel			@Schema
 @ApiModelProperty	@Schema
 @ApiOperation(value = "foo", notes = "bar")	@Operation(summary = "foo", description = "bar")
 @ApiParam	@Parameter
 @ApiResponse(code = 404, message = "foo")	ApiResponse(responseCode = "404", description = "foo")

详情参考 https://cloud.tencent.com/developer/article/1978526

## 附录

* [Swagger参考](https://www.jianshu.com/p/12f4394462d5)
