package cn.chinaunicom.sdsi.component.recorder;

import cn.chinaunicom.sdsi.component.recorder.config.UniRecorderProperties;
import cn.chinaunicom.sdsi.component.recorder.entity.SysLogOpertableColumnPO;
import cn.chinaunicom.sdsi.component.recorder.entity.SysLogOpertablePO;
import cn.chinaunicom.sdsi.component.recorder.entity.SysLogPO;
import cn.chinaunicom.sdsi.component.recorder.exception.UniRecorderException;
import cn.chinaunicom.sdsi.component.recorder.service.SysLogOpertableColumnService;
import cn.chinaunicom.sdsi.component.recorder.service.SysLogOpertableService;
import cn.chinaunicom.sdsi.component.recorder.service.SysLogService;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;

/**
 * 记录业务操作痕迹
 *
 * <AUTHOR>
 * @version V1.0
 * @date 2019-03-22
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Component
@Slf4j
public class UniRecorder {

    @Autowired
    private UniRecorderProperties uniRecorderProperties;
    @Autowired
    private SysLogService sysLogService;
    @Autowired
    private SysLogOpertableService sysLogOpertableService;
    @Autowired
    private SysLogOpertableColumnService sysLogOpertableColumnService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


    /**
     * 记录操作日志
     *
     * @param staffId  操作人
     * @param busiType 业务类型
     * @param operType 操作类型
     * @param old      旧对象
     * @param now      新对象
     * @param filter   过滤字段，将不被记录明细
     * @return UniRecorder
     */
    @Async
    public <T> Future<String> record(@NonNull String staffId, String busiType, String busiId,String tenantId, String operType, T old,
                                     T now, String... filter) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(busiType)) {
            throw new UniRecorderException("busiType不能为空");
        }
        String logId = UUID.randomUUID().toString();
        SysLogPO log = new SysLogPO();
        log.setLogId(logId);
        log.setStaffId(staffId);
        log.setBusiType(busiType);
        log.setBusiId(busiId);
        log.setTenantId(tenantId);
        log.setCreateTime(new Date());
        operType = checkOperType(operType, old, now);
        log.setOperType(operType);
        this.sysLogService.save(log);
        if (operType.equals(uniRecorderProperties.getCreateTypeName())) {
            if (now == null) {
                throw new UniRecorderException("添加时now对象不能为空");
            }
            this.saveOperTable(now, logId);
        } else if (operType.equals(uniRecorderProperties.getDeleteTypeName())) {
            if (old == null) {
                throw new UniRecorderException("删除时old对象不能为空");
            }
            this.saveOperTable(old, logId);
        } else if (operType.equals(uniRecorderProperties.getUpdateTypeName())) {
            if (now == null || old == null) {
                throw new UniRecorderException("编辑时new、old对象皆不能为空");
            }
            this.update(old, now, logId, filter);
        }
        return AsyncResult.forValue(logId);
    }

    private <T> String checkOperType(String operType, T old, T now) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(operType)) {
            if (old != null && now != null) {
                operType = uniRecorderProperties.getUpdateTypeName();
            } else if (old == null && now != null) {
                operType = uniRecorderProperties.getCreateTypeName();
            } else if (old != null) {
                operType = uniRecorderProperties.getDeleteTypeName();
            }
        }
        return operType;
    }

    /**
     * 记录日志业务表
     *
     * @param obj 操作的对象
     * @return 操作业务表ID
     */
    private <S> String saveOperTable(S obj, String logId) {
        assert logId != null;
        assert obj != null;
        SysLogOpertablePO po = new SysLogOpertablePO();
        po.setOpertabId(UUID.randomUUID().toString());
        po.setLogId(logId);
        TableName tableName = obj.getClass().getAnnotation(TableName.class);
        if (null == tableName) {
            po.setTableName(obj.getClass().getSimpleName());
        } else {
            po.setTableName(tableName.value());
        }
        Arrays.stream(obj.getClass().getDeclaredFields())
                .filter(t -> t.getAnnotation(TableId.class) != null).forEach((Field v) -> {
            String temp = po.getTableUniqueName();
            po.setTableUniqueName(org.apache.commons.lang3.StringUtils.isNotEmpty(temp) ?
                    (temp + "," + v.getAnnotation(TableId.class).value()) : v.getAnnotation(TableId.class).value());
            v.setAccessible(true);
            try {
                String tempValue = po.getTableUniqueValue();
                Object value = v.get(obj);
                po.setTableUniqueValue(org.apache.commons.lang3.StringUtils.isNoneEmpty(tempValue) ? (tempValue + "," + value) :
                        String.valueOf(value));
            } catch (IllegalAccessException e) {
                log.error("日志记录失败！", e);
                po.setTableUniqueValue("");
            }
        });
        this.sysLogOpertableService.save(po);
        return po.getOpertabId();
    }

    /**
     * 记录操作日志列明细表
     *
     * @param old   编辑前的对象
     * @param now   编辑后的对象
     * @param logId 操作日志表id
     * @return UniRecorder
     */
    private <V> UniRecorder update(@NonNull V old, @NonNull V now, String logId, String... filterWords) {
        String filterPropertyName = this.uniRecorderProperties.getFilterPropertyNames();
        Set<String> filter = new HashSet<>();
        if (null != filterPropertyName) {
            String[] names = filterPropertyName.split(",");
            if (names.length > 0) {
                filter.addAll(Arrays.asList(names));
            }
        }
        if (null != filterWords && filterWords.length > 0) {
            filter.addAll(Arrays.asList(filterWords));
        }
        String operTableId = this.saveOperTable(old, logId);
        if (old.getClass().isInstance(now)) {
            List<SysLogOpertableColumnPO> pos = getColumns(old, now, filter, operTableId);
            sysLogOpertableColumnService.saveBatch(pos);
        }
        return this;
    }

    private <V> List<SysLogOpertableColumnPO> getColumns(@NonNull V old, @NonNull V now, Set<String> filter,
                                                         String operTableId) {
        List<SysLogOpertableColumnPO> pos = new ArrayList<>();
        Field[] fs1 = old.getClass().getDeclaredFields();
        Field[] fs2 = old.getClass().getSuperclass().getDeclaredFields();
        Field[] fs = ArrayUtils.addAll(fs1, fs2);
        Arrays.stream(fs).filter(t -> !filter.contains(t.getName())).forEach(
                (Field f) -> {
                    f.setAccessible(true);
                    try {
                        Object v1 = f.get(old);
                        Object v2 = f.get(now);
                        if (!equals(v1, v2)) {
                            SysLogOpertableColumnPO po = new SysLogOpertableColumnPO();
                            po.setOpercolId(UUID.randomUUID().toString());
                            po.setOpertabId(operTableId);
                            po.setColumnName(StringUtils.camelToUnderline(f.getName()));
                            if (null != v1) {
                                doWithOld(v1, po);
                            }
                            if (null != v2) {
                                dealWithNew(v2, po);
                            }
                            po.setColumnDesc(f.getAnnotation(Schema.class) == null ? "" :
                                    f.getAnnotation(Schema.class).name());
                            pos.add(po);
                        }
                    } catch (IllegalAccessException e) {
                        log.error("保存操作记录时getColumns方法出现异常", e);
                    }
                }
        );
        return pos;
    }

    private void dealWithNew(Object v2, SysLogOpertableColumnPO po) {
        if (v2 instanceof Date) {
            po.setNewValue(sdf.format(v2));
        } else if (v2 instanceof byte[]) {
            String tmpUtf = new String((byte[]) v2, StandardCharsets.UTF_8);
            if (Arrays.equals((byte[]) v2, tmpUtf.getBytes(StandardCharsets.UTF_8))) {
                po.setNewValue(tmpUtf);
            } else {
                String tmpGbk = new String((byte[]) v2, Charset.forName("GBK"));
                po.setNewValue(tmpGbk);
            }
        } else {
            po.setNewValue(v2.toString());
        }
    }

    private void doWithOld(Object v1, SysLogOpertableColumnPO po) {
        if (v1 instanceof Date) {
            po.setOldValue(sdf.format(v1));
        } else if (v1 instanceof byte[]) {
            String tmpUtf = new String((byte[]) v1, StandardCharsets.UTF_8);
            if (Arrays.equals((byte[]) v1, tmpUtf.getBytes(StandardCharsets.UTF_8))) {
                po.setOldValue(tmpUtf);
            } else {
                String tmpGbk = new String((byte[]) v1, Charset.forName("GBK"));
                po.setOldValue(tmpGbk);
            }
        } else if (v1 instanceof LocalDateTime) {
            po.setOldValue(((LocalDateTime) v1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd " +
                    "HH:mm:ss")));
        } else {
            po.setOldValue(v1.toString());
        }
    }

    /**
     * 自定义两个对象的equals
     *
     * @param oldObject 旧值
     * @param newObject 新值
     * @return boolean
     */
    private boolean equals(Object oldObject, Object newObject) {
        if (oldObject == newObject) {
            return true;
        }
        if (oldObject != null && newObject == null) {
            return false;
        }
        if (oldObject == null) {
            return false;
        }
        if (oldObject instanceof byte[] || newObject instanceof byte[]) {
            return Arrays.equals((byte[]) oldObject, (byte[]) newObject);
        }
        return oldObject.equals(newObject);
    }
}
