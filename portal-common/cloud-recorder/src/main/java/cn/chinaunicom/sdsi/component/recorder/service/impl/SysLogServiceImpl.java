package cn.chinaunicom.sdsi.component.recorder.service.impl;

import cn.chinaunicom.sdsi.component.recorder.dao.SysLogMapper;
import cn.chinaunicom.sdsi.component.recorder.entity.SysLogPO;
import cn.chinaunicom.sdsi.component.recorder.service.SysLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 操作日志表 服务实现类
 *
 * <AUTHOR>
 * @date 2019-03-22
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Service
public class SysLogServiceImpl extends ServiceImpl<SysLogMapper, SysLogPO> implements SysLogService {

}
