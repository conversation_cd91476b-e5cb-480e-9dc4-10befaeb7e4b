package cn.chinaunicom.sdsi.component.recorder.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 操作日志表
 *
 * <AUTHOR>
 * @date 2019-03-22
 * @version V1.0
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Data
@Accessors(chain = true)
@TableName("t_sys_log")
@Tag(name = "TSysLog对象", description = "操作日志表")
public class SysLogPO {

    private static final long serialVersionUID = 1L;

    @Schema(name = "日志ID")
    @TableId(value = "log_id", type = IdType.INPUT)
    private String logId;

    @Schema(name = "人员IDt_sys_staff.staff_id")
    private String staffId;

    @Schema(name = "网格信息、综合业务、实有人口、特殊人群、重点青少年、经济社会组织管理、社会治安、矛盾纠纷排查、校园及周边安全、护路护线")
    private String busiType;

    @Schema(name = "add 新增 modify 修改 remove 删除")
    private String operType;

    @Schema(name = "操作时间")
    private Date createTime;

    @Schema(name = "业务主键id")
    private String busiId;

    @Schema(name = "租户id")
    private String tenantId;

}
