<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.chinaunicom.sdsi</groupId>
        <artifactId>portal-parent</artifactId>
        <version>2021.0.1-SNAPSHOT</version>
        <relativePath>../../portal-common/portal-parent</relativePath>
    </parent>


    <groupId>cn.chinaunicom.sdsi</groupId>
    <artifactId>cloud-recorder</artifactId>
    <version>${portal.cloud.version}</version>
    <description>操作痕迹记录组件</description>

    <dependencies>
        <dependency>
            <groupId>cn.chinaunicom.sdsi</groupId>
            <artifactId>cloud-core</artifactId>
            <version>${portal.cloud.version}</version>
        </dependency>
        <!-- 生成 spring configuration metadata json 配置元数据 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.6.0</version>
                <configuration>
                    <flattenMode>clean</flattenMode>
                    <pomElements>
                        <parent>flatten</parent>
                    </pomElements>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <distributionManagement>
        <repository>
            <id>central</id>
            <name>外网镜像处理</name>
            <url>http://technexus:2475/repository/seal_maven_snapshot/</url>
        </repository>
    </distributionManagement>
<!--    <repositories>-->
<!--        <repository>-->
<!--            <id>central</id>-->
<!--            <name>外网镜像处理</name>-->
<!--            <url>http://technexus:2475/repository/seal_maven_snapshot/</url>-->
<!--        </repository>-->
<!--    </repositories>-->
</project>
