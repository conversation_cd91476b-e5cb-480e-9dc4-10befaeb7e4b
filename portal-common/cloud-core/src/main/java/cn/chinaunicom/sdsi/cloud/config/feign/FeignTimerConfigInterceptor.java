package cn.chinaunicom.sdsi.cloud.config.feign;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * @auther 赵芳城
 * @时间 2021/4/27
 * @描述 为定时任务访问feign接口时自动生成token
 */
@Slf4j
public class FeignTimerConfigInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        HttpServletRequest httpServletRequest = this.getHttpServletRequest();
        if(null==httpServletRequest){
            return;
        }
        Boolean xxl= (Boolean) httpServletRequest.getAttribute("xxl");
        if(xxl){
            //临时获取方便从request取
            String tokenTemp= httpServletRequest.getAttribute("Authorization") == null ? "" : (String) httpServletRequest.getAttribute("Authorization");
            requestTemplate.header("Authorization",tokenTemp);
            log.debug("临时------------------"+tokenTemp);
            //正式需要通过下面方法获取
            //requestTemplate.header("Authorization", createTimerToken());
            //log.debug("requestTemplate中的token------------------"+requestTemplate.headers().toString());
        }
    }

    private HttpServletRequest getHttpServletRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * TO-DO
     * 实现根据固定用户xxlJob（在portal-user中新增）获取token机制
     * @return
     */
    private String createTimerToken() {
        // TO-DO 获取token
        String token="Bearer 33aa5e1a-bdbb-4d94-85b5-4d0209a57cc7";
        return token;
    }
}