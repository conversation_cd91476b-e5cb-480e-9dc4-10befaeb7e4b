package cn.chinaunicom.sdsi.cloud.aop.annotation;

import java.lang.annotation.*;

/**
 * <b>生成租户id注解</b><br>
 * <b>描述: </b>
 * 根据当前登录的用户信息,自动获取租户id,并装配至此注解对应的入参字段中<br><br>
 * 注解的使用方法:<br>
 * 1.请求入参是一个vo对象,则会自动遍历vo对象中的 <b>String tenantId</b> 字段,并进行写入;<br>
 * 2.请求入参直接是 <b>String tenantId</b> ,则会直接替换,注意直接使用 String 类型入参时,必须保证参数正确初始化,不能为 null,需要像下面这种声明默认值<br>
 * <i>@RequestParam("tenantId",</i><b>defaultValue=""<i></b>) String tenantId</i>;<br><br>
 * 租户id的生成规则:<br>
 * 1.如果用户登录了,system租户,传递了租户id使用传递的,没传递默认使用system;<br>
 * 2.如果用户登录了,普通的租户,强制获取用户所属的租户id;<br>
 * 3.如果未登录,则根据注解参数 <b>noLoginDefaultId</b> 赋值,未赋值时为空
 * <AUTHOR>
 * @date 2022-05-25
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CurrentTenantId {

    String name() default "";

    // 用户未登录时的默认租户id
    String noLoginDefaultId() default "";
}