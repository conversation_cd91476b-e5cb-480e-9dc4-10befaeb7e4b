package cn.chinaunicom.sdsi.platform.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class UserVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    /**
     * 工号
     */
    private String jobNum;

    /**
     * 姓名
     */
    private String name;

    /**
     * 所属地市
     */
    private String city;

    /**
     * 所属区县
     */
    private String county;

    /**
     * 省,地市
     */
    private String orgType;

    /**
     * 所属行业多个,分割
     */
    private String industry;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 逻辑删除
     */
    private String deleted="0";

    /**
     * 用户类型:  0 普通用户,1地市接口人,2客户经理
     */
    private String userType;

    /**
     * 角色
     */
    private List<String> roles;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 地市
     */
    private String cityId;

    /**
     * 区县
     */
    private String countyId;

    /**
     * 细分行业多个,分割
     */
    private String segments;

}
