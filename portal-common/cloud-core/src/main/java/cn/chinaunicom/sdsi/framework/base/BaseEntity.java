package cn.chinaunicom.sdsi.framework.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 实体基类
 *
 * @file: com.chinaunicom.sdsi.framework.base.BaseEntity
 * @description: 所有对应数据库的实体继承此类，统一处理主键、创建人、创建日期、逻辑删除等字段
 * @author: wangwj
 * @date: 2018-12-25
 * @version: V1.0
 * @copyright: Copyright(c) 2018 Sdcncsi Co. Ltd. All rights reserved.
 */
@Data
@Accessors(chain = true)
public class BaseEntity implements Serializable {

    public <T> Object convertVo(T vo){
        BeanUtils.copyProperties(this, vo);
        return vo;
    }

    /**

    // 将 TABLE_NAME 替换为对应表名
    // Oracle 数据库,增加基类包含的字段
    ALTER TABLE "TABLE_NAME" ADD ("CREATE_BY" VARCHAR2(36));
    ALTER TABLE "TABLE_NAME" ADD ("CREATE_DATE" TIMESTAMP(6));
    ALTER TABLE "TABLE_NAME" ADD ("UPDATE_BY" VARCHAR2(36));
    ALTER TABLE "TABLE_NAME" ADD ("UPDATE_DATE" TIMESTAMP(6));
    ALTER TABLE "TABLE_NAME" ADD ("DELETE_FLAG" VARCHAR2(16));
    ALTER TABLE "TABLE_NAME" ADD ("VERSIONS" NUMBER(11,0));
    ALTER TABLE "TABLE_NAME" ADD ("TENANT_ID" VARCHAR2(36));

    COMMENT ON COLUMN "TABLE_NAME"."CREATE_BY" IS '创建人';
    COMMENT ON COLUMN "TABLE_NAME"."CREATE_DATE" IS '创建时间';
    COMMENT ON COLUMN "TABLE_NAME"."UPDATE_BY" IS '编辑人';
    COMMENT ON COLUMN "TABLE_NAME"."UPDATE_DATE" IS '编辑时间';
    COMMENT ON COLUMN "TABLE_NAME"."DELETE_FLAG" IS '逻辑删除位';
    COMMENT ON COLUMN "TABLE_NAME"."VERSIONS" IS '乐观锁标记位';
    COMMENT ON COLUMN "TABLE_NAME"."TENANT_ID" IS '租户id';
	
    // 将 TABLE_NAME 替换为对应表名
    // MySql 数据库,增加基类包含的字段
	ALTER TABLE `TABLE_NAME` ADD COLUMN `create_by` VARCHAR(36) DEFAULT NULL COMMENT '创建人';
	ALTER TABLE `TABLE_NAME` ADD COLUMN `create_date` DATETIME DEFAULT NULL COMMENT '创建时间';
	ALTER TABLE `TABLE_NAME` ADD COLUMN `update_by` VARCHAR(36) DEFAULT NULL COMMENT '编辑人';
	ALTER TABLE `TABLE_NAME` ADD COLUMN `update_date` DATETIME DEFAULT NULL COMMENT '编辑时间';
	ALTER TABLE `TABLE_NAME` ADD COLUMN `delete_flag` VARCHAR(16) DEFAULT NULL COMMENT '逻辑删除位，normal表示正常，deleted表示删除';
	ALTER TABLE `TABLE_NAME` ADD COLUMN `versions` INT(11) DEFAULT '1' COMMENT '乐观锁标记位';
	ALTER TABLE `TABLE_NAME` ADD COLUMN `tenant_id` VARCHAR(36) DEFAULT NULL COMMENT '租户id';
    */

    /**
     *
     */
    private static final long serialVersionUID = -7920715128210690669L;

    /**
     * 租户标识
     */
    @Schema(name="租户id")
    @TableField(fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 创建人
     */
    @Schema(name="创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(name="创建时间")
    @TableField(fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;

    /**
     * 编辑人
     */
    @Schema(name="编辑人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 编辑时间
     */
    @Schema(name="编辑时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.TIMESTAMP)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateDate;

    /**
     * 乐观锁
     */
    @Version
    @Schema(name="乐观锁")
    private Long versions;

    /**
     * 逻辑删除，normal表示正常，deleted表示删除
     */
    @TableLogic(value = "normal", delval = "deleted")
    @Schema(name="逻辑删除，normal表示正常，deleted表示删除")
    private String deleteFlag = "normal";
}
