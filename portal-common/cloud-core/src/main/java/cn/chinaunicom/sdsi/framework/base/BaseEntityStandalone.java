package cn.chinaunicom.sdsi.framework.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.Version;

import java.io.Serializable;
import java.util.Date;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.ibatis.type.JdbcType;

/**
 * 实体基类
 *
 * @file: com.chinaunicom.sdsi.framework.base.BaseEntity
 * @description: 所有对应数据库的实体继承此类，统一处理主键、创建人、创建日期、逻辑删除等字段
 * @author: wangwj
 * @date: 2018-12-25
 * @version: V1.0
 * @copyright: Copyright(c) 2018 Sdcncsi Co. Ltd. All rights reserved.
 */
@Data
@Accessors(chain = true)
public class BaseEntityStandalone implements Serializable {

    /**
     * 创建人
     */
    @Schema(name="创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(name="创建时间")
    @TableField(fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    private Date createDate;

    /**
     * 编辑人
     */
    @Schema(name="编辑人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 编辑时间
     */
    @Schema(name="编辑时间")
    @TableField(fill = FieldFill.INSERT_UPDATE, jdbcType = JdbcType.TIMESTAMP)
    private Date updateDate;

    /**
     * 乐观锁
     */
    @Version
    @Schema(name="乐观锁")
    private Long versions;

    /**
     * 逻辑删除，normal表示正常，deleted表示删除
     */
    @TableLogic(value = "normal", delval = "deleted")
    @Schema(name="逻辑删除，normal表示正常，deleted表示删除")
    private String deleteFlag = "normal";
}
