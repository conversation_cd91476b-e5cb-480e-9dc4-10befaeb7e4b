package cn.chinaunicom.sdsi.framework.log.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 系统操作日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-01-15
 */
@Data
@Accessors(chain = true)
@Tag(name = "系统日志-视图对象-基本信息")
@TableName("sys_log")
public class SysLogPO {

    /**
     * 主键
     */
    @TableId(value = "log_id")
    @Schema(name="日志ID")
    private String logId;

    /**
     * 用户ID
     */
    @Schema(name="用户的ID")
    private String userId;

    /**
     * 用户帐号
     */
    @Schema(name="用户的账号")
    private String account;

    /**
     * IP信息
     */
    @Schema(name="用户的ip")
    private String userIp;

    /**
     * 被访问主机IP
     */
    @Schema(name="服务器端ip")
    private String hostIp;

    /**
     * 被访问主机名
     */
    @Schema(name="服务器端主机名")
    private String hostName;

    /**
     * 操作时间
     */
    @Schema(name="操作时间")
    private LocalDateTime logTime;

    /**
     * 操作时长
     */
    @Schema(name="耗时")
    private Integer costTime;

    /**
     * 访问路径
     */
    @Schema(name="访问路径")
    private String url;

    /**
     * 权限名
     */
    @Schema(name="权限名")
    private String permissionName;

    /**
     * 权限编码
     */
    @Schema(name="权限编码")
    private String permissionCode;

    /**
     * 参数
     */
    @Schema(name="参数")
    private String parameter;

    /**
     * 操作状态
     */
    @Schema(name="操作状态-编码")
    private String logStatus;

    /**
     * 错误日志
     */
    @Schema(name="异常信息")
    private String exception;

}
