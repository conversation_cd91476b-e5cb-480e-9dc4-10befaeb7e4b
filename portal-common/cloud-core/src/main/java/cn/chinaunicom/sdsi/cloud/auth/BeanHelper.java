package cn.chinaunicom.sdsi.cloud.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 类型转换
 *
 * <AUTHOR>
 * @date 2023年3月3日
 */
@Slf4j
public class BeanHelper {

    private BeanHelper() {
    }

    private static final Pattern PATTERN_FORMAT = Pattern.compile("[A-Z]+");

    /**
     * 对象复制
     *
     * <AUTHOR>
     * @Date 2024/6/5 8:51
     */
    public static <T> T copyProperties(Object source, T target) {
        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 对象转换
     *
     * <AUTHOR>
     * @Date 2024/6/5 8:52
     */
    public static <T> T transform(Object target, Class<T> clazz) {
        if (target == null) {
            return null;
        } else {
            String targetJSON = JSON.toJSONString(target);
            return JSON.parseObject(targetJSON, clazz);
        }
    }

    /**
     * 数组转换
     * <AUTHOR>
     * @Date 2024/6/5 8:52
     */
    public static <T> List<T> transform(List<?> target, Class<T> clazz) {
        if (target == null) {
            return null;
        } else {
            JSONArray jsonArray = new JSONArray();
            jsonArray.fluentAddAll(target);
            return transform(jsonArray, clazz);
        }
    }

    /**
     * 对象转map
     * <AUTHOR>
     * @Date 2024/6/5 8:52
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> transformMap(Object target) {
        if (target != null) {
            String targetJSON = JSON.toJSONString(target);
            return JSON.parseObject(targetJSON, Map.class);
        }
        return new HashMap<>();
    }

    /**
     * json array 转 List<T>
     *
     * @param array
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> transform(JSONArray array, Class<T> clazz) {
        return JSON.parseArray(array.toJSONString(), clazz);
    }

    /**
     * json array 转 List<T>
     *
     * @param array
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> List<T> transform(String array, Class<T> clazz) {
        return JSON.parseArray(array, clazz);
    }

    public static <T> String transString(Collection<T> array) {

        return JSON.toJSONString(array);
    }

    public static <T> String transString(T entity) {
        return JSON.toJSONString(entity);
    }

    public static <T> Map<String, Object> queryBean(T query) {
        Map<String, Object> params = new LinkedHashMap<>();
        Field[] fields = query.getClass().getDeclaredFields();
        for (Field field : fields) {
            Object value;
            try {
                field.setAccessible(true);
                value = field.get(query);
                if ((value != null && !"null".equals(value)) && !(value.toString().isEmpty())) {
                    params.put(fieldNameToDataBaseName(field.getName()), value);
                }
            } catch (Exception ignored) {
                log.error("");
            }
        }
        return params;
    }

    public static String fieldNameToDataBaseName(String fieldName) {
        StringBuilder field = new StringBuilder();

        String[] fieldPattern = fieldName.split("");
        for (String s : fieldPattern) {
            Matcher m = PATTERN_FORMAT.matcher(s);
            if (m.matches()) {
                field.append("_").append(s.toLowerCase());
            } else {
                field.append(s);
            }
        }
        return field.toString();
    }
}
