package cn.chinaunicom.sdsi.framework.response;

import com.baomidou.mybatisplus.core.metadata.IPage;


import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 分页返回对象
 *
 * @param <T>
 * <AUTHOR>
 */
@Accessors(chain = true)
@Tag(name="分页返回对象")
public class BasePageResponse<T> extends BaseResponse<BasePageVO<T>> {
    @Schema(hidden = true)
    private static final long serialVersionUID = 1L;

    public BasePageResponse(IPage<T> page, Integer draw, String error) {
        super(new BasePageVO<>(page, draw, error));
    }

    public BasePageResponse(IPage<T> page, Integer draw) {
        super(new BasePageVO<>(page, draw));
    }

    public BasePageResponse(IPage<T> page) {
        super(new BasePageVO<>(page));
    }

}
