package cn.chinaunicom.sdsi.cloud.aop.aop;

import cn.chinaunicom.sdsi.cloud.aop.annotation.CurrentTenantId;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.hutool.core.lang.Assert;
import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;

/**
 * <b>获取租户id切面实现类</b><br>
 * <b>描述: </b>
 * 自动获取当前登录用户的租户id的切面实现类
 * 
 * <AUTHOR>
 * @date 2022-05-25
 */
@Aspect
@Component
@Slf4j
@Data
public class CurrentTenantIdAspect {

    @Resource
    private UnifastContext unifastContext;

    @Pointcut("@annotation(cn.chinaunicom.sdsi.cloud.aop.annotation.CurrentTenantId)")
    public void pcMethod() {
    }

    @Around(value = "pcMethod() && @annotation(currentTenantId)")
    public Object TokenInject(ProceedingJoinPoint joinPoint, CurrentTenantId currentTenantId) throws Throwable {
        // 动态处理方法入参
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg != null) {
                Class<?> argClass = arg.getClass();
                if (argClass == String.class) {
                    Field field = argClass.getDeclaredField("value");
                    field.setAccessible(true);
                    String tenantId = this.getTenantId(arg.toString(), currentTenantId.noLoginDefaultId());
                    field.set(arg, tenantId == null ? null : tenantId.toCharArray());
                }
                Field[] declaredFields = argClass.getDeclaredFields();
                if (declaredFields.length > 0) {
                    for (Field field : declaredFields) {
                        if (field != null && (field.getType() == String.class) && field.getName().equals(UnifastConstants.TENANT_ID)) {
                            field.setAccessible(true);
                            String tenantId = this.getTenantId(field.get(arg) == null ? null : field.get(arg).toString(), currentTenantId.noLoginDefaultId());
                            field.set(arg, tenantId);
                        }
                    }
                }

            }
        }
        return joinPoint.proceed(args);
    }

    private String getTenantId(String tenantId, String noLoginDefaultId) {
        Assert.notNull(unifastContext, "when you used this annotation, the white list can not be configured.");
        // 获取当前用户的租户id
        MallUser user = unifastContext.getUser();
        if (user != null) {
            // 如果用户登录了
            if (UnifastConstants.USERT_TYPE_ADMIN.equals(unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
                // 如果是 system 租户,传递了默认使用传递的,没传递默认使用 system
                tenantId = StringUtils.isBlank(tenantId) ? UnifastConstants.DEFAULT_TENANT_ID : tenantId;
            } else {
                // 如果是普通的租户,强制获取用户所属的租户id
                tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            }
        } else {
            // 如果未登录,则根据注解参数赋值,未赋值时为空
            tenantId = StringUtils.isBlank(tenantId) ? null : (StringUtils.isBlank(noLoginDefaultId)) ? null : noLoginDefaultId;
        }
        return tenantId;
    }
}