package cn.chinaunicom.sdsi.framework.config.mybatis;

import com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * mybatis-plus功能配置类
 *
 * <AUTHOR>
 * @date 2019-01-02
 * @version V1.0
 */
@Configuration
public class MyBatisPlusConfiguration {

    /**
     * 乐观锁插件
     *
     * @return OptimisticLockerInterceptor
     * <AUTHOR>
     * @date 2019-01-07
     */
    @Bean
    public OptimisticLockerInterceptor optimisticLockerInterceptor() {
        return new OptimisticLockerInterceptor();
    }

    /**
     * 分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        return new PaginationInterceptor();
    }

    /**
     * 自定义批量插入 SQL 注入器
     */
    @Bean
    public InsertBatchSqlInjector insertBatchSqlInjector() {
        return new InsertBatchSqlInjector();
    }
}
