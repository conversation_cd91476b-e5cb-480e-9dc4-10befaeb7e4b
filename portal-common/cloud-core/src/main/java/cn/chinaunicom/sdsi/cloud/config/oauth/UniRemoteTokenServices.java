package cn.chinaunicom.sdsi.cloud.config.oauth;

import cn.chinaunicom.sdsi.cloud.converter.UserEntityConverter;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.security.oauth2.resource.ResourceServerProperties;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Primary;
import org.springframework.security.authentication.CredentialsExpiredException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 自定义资源服务器的ResourceServerTokenServices，第一次取值后，存入本服务的缓存，不再重复远程获取
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/11/26
 */
@Slf4j
@Primary
@Component
@ConditionalOnProperty(value = "unifast.cloud.resource.enabled", havingValue = "true", matchIfMissing = true)
public class UniRemoteTokenServices extends RemoteTokenServices {

    @Autowired
    private RedisUtils redisUtils;
    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 构造方法，设置必要参数
     *
     * @param properties ResourceServerProperties
     */
    public UniRemoteTokenServices(ResourceServerProperties properties) {
        super.setClientId(properties.getClientId());
        super.setClientSecret(properties.getClientSecret());
        super.setCheckTokenEndpointUrl(properties.getTokenInfoUri());
        final DefaultAccessTokenConverter tokenConverter = new DefaultAccessTokenConverter();
        tokenConverter.setUserTokenConverter(new UserEntityConverter());
        super.setAccessTokenConverter(tokenConverter);
    }

    /**
     * 覆盖loadAuthentication方法，增加缓存的读取和写入
     *
     * @param accessToken String
     * @return OAuth2Authentication
     * @throws AuthenticationException AuthenticationException
     * @throws InvalidTokenException   InvalidTokenException
     */
    @Override
    public OAuth2Authentication loadAuthentication(String accessToken) throws AuthenticationException,
            InvalidTokenException {
        Object object = redisUtils.get(accessToken);
        /**
         *  临时增加accesskey获取
         *  TO-DO 此方式只支持redis使用同一个服务方式。
         */
        boolean access = redisUtils.exists("access:"+accessToken);
        if (object != null&&access) {
            try {
                final UniOauth2Authentication uniOauth2Authentication = mapper.readValue(object.toString(),
                        UniOauth2Authentication.class);
                return uniOauth2Authentication.transfer();
            } catch (JsonProcessingException e) {
                e.printStackTrace();
                throw new InvalidTokenException("令牌转换失败");
            }
        }
        try {
            //读取本地配置文件中的认证中心配置，访问check_token方法,验证token并缓存
            final OAuth2Authentication authentication = super.loadAuthentication(accessToken);
            redisUtils.set(accessToken, mapper.writeValueAsString(new UniOauth2Authentication(authentication)), 3600L);
            return authentication;
        } catch (Exception ex) {
            throw new InvalidTokenException("令牌认证失败");
        }
    }
}
