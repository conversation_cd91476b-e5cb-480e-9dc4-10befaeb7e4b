package cn.chinaunicom.sdsi.cloud.generator.generatorcode.entity;

import cn.chinaunicom.sdsi.cloud.generator.table.entity.ColumnInfo;
import cn.chinaunicom.sdsi.cloud.generator.table.entity.TableInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2022/4/21 15:08
 */
@Data
public class GenCode implements Serializable {
    private static final long serialVersionUID = 469387959382714L;

    private String datasourceId;
    private String author;
    private Boolean enableSwagger;
    private String outPutDir;
    private String parentPackage;
    private String moduleName;
    private String tableName;
    private String tablePrefix;
    private String tableSuffix;
    private String entitySuperClass;
    private Boolean enableLombok;
    private String controllerSuperClass;
    private TableInfo tableInfo;
    private List<ColumnInfo> formColumnList;
    private List<ColumnInfo> tableColumnList;
    private List<ColumnInfo> entityColumnList;

}
