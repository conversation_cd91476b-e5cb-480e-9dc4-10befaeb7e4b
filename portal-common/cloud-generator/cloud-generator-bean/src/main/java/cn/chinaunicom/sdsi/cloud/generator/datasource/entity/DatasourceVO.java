package cn.chinaunicom.sdsi.cloud.generator.datasource.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2022/4/22 9:19
 */
public class DatasourceVO extends BaseQueryVO {
    /**
     * ID
     */
    private String id;

    /**
     * 主机
     */
    private String host;

    /**
     * 端口
     */
    private String port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 数据库
     */
    private String databaseName;

    /**
     * 驱动名称
     */
    private String driverName;

    /**
     * URL后缀
     */
    private String suffix;
}
