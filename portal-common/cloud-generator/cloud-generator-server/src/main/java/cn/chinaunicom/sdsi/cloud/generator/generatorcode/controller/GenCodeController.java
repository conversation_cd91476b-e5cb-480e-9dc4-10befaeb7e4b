package cn.chinaunicom.sdsi.cloud.generator.generatorcode.controller;

import cn.chinaunicom.sdsi.cloud.generator.generatorcode.entity.GenCode;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.service.GenCodeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2022/4/21 15:28
 */
@RestController
@RequestMapping("/genCode")
public class GenCodeController {

    @Autowired
    private GenCodeService genCodeService;

    @PostMapping("/genCode")
    public void genCode(@RequestBody GenCode genCode, HttpServletRequest request, HttpServletResponse response){
        genCodeService.genCode(genCode,request,response);
    }

}
