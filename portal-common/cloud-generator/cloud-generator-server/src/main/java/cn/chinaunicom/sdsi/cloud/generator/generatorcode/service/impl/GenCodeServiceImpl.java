package cn.chinaunicom.sdsi.cloud.generator.generatorcode.service.impl;

import cn.chinaunicom.sdsi.cloud.generator.datasource.entity.Datasource;
import cn.chinaunicom.sdsi.cloud.generator.datasource.service.DatasourceService;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.constant.FolderConstant;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.constant.TemplateConstant;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.entity.GenCode;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.service.GenCodeService;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.utils.CaseUtils;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.utils.FileUtils;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.utils.GenCodeUtil;
import cn.chinaunicom.sdsi.cloud.generator.generatorcode.utils.ZipFileUtils;
import cn.chinaunicom.sdsi.cloud.generator.table.entity.ColumnInfo;
import cn.chinaunicom.sdsi.cloud.generator.table.entity.PublicFields;
import cn.hutool.core.io.FileUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.apache.tools.zip.ZipOutputStream;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2022/4/21 15:31
 */
@Service
public class GenCodeServiceImpl implements GenCodeService {

    @Autowired
    private DatasourceService datasourceService;
    @Autowired
    private PublicFields publicFields;

    /*
     * <AUTHOR>
     * @Description 生成代码
     * @Date 19:21 2022/4/25
     * @param genCode
     * @param request
     * @param response
     * @return void
     **/
    @Override
    public void genCode(GenCode genCode, HttpServletRequest request, HttpServletResponse response) {
        Datasource datasource = datasourceService.findOne(genCode.getDatasourceId());
        String outPutDir = request.getSession().getServletContext().getRealPath("/") + File.separator + "WEB-INF" + File.separator + "upload" + File.separator + request.getSession().getId();
        genCode.setOutPutDir(outPutDir);
        restList(genCode);
        //生成代码
        GenCodeUtil.generatorCode(datasource, genCode);
        //处理文件
        handleFile(genCode);
        //打包下载代码
        downloadCode(request, response);
    }

    /*
     * <AUTHOR>
     * @Description 重置表属性
     * @Date 19:18 2022/4/25
     * @param genCode
     * @return void
     **/
    public void restList(GenCode genCode) {
        List<ColumnInfo> formColumnList = new ArrayList<>();
        List<ColumnInfo> tableColumnList = new ArrayList<>();
        List<ColumnInfo> entityColumnList = new ArrayList<>();
        for (ColumnInfo columnInfo : genCode.getTableInfo().getColumnList()) {
            if (!columnInfo.getTextType().equals("no")) {
                formColumnList.add(columnInfo);
            }
            if (columnInfo.getTableShow()) {
                tableColumnList.add(columnInfo);
            }
            if (!publicFields.getPublicFields().contains(columnInfo.getColumnName())) {
                entityColumnList.add(columnInfo);
            }
        }
        genCode.setTableColumnList(tableColumnList);
        genCode.setFormColumnList(formColumnList);
        genCode.setEntityColumnList(entityColumnList);
    }

    /*
     * <AUTHOR>
     * @Description 下载代码
     * @Date 19:21 2022/4/25
     * @param request
     * @param response
     * @return void
     **/
    public void downloadCode(HttpServletRequest request, HttpServletResponse response) {
        response.setContentType("APPLICATION/OCTET-STREAM");
        response.setHeader("Content-Disposition", "attachment; filename=src.zip");
        try {
            ZipFileUtils zip = new ZipFileUtils();
            ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
            System.out.println("路径---------------" + request.getSession().getServletContext().getRealPath("/") + File.separator + "WEB-INF" + File.separator + "upload" + File.separator + request.getSession().getId());
            String fileName = request.getSession().getServletContext().getRealPath("/") + File.separator + "WEB-INF" + File.separator + "upload" + File.separator + request.getSession().getId();
            File ff = new File(fileName);
            if (!ff.exists()) {
                ff.mkdirs();
            }
            zip.zip(ff, zos, "");
            zos.flush();
            zos.close();
            //删除目录
            FileUtils.DeleteFolder(fileName);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /*
     * <AUTHOR>
     * @Description 处理文件夹及文件
     * @Date 19:18 2022/4/25
     * @param genCode
     * @return void
     **/
    public void handleFile(GenCode genCode) {
        String servletPath = genCode.getOutPutDir();
        String packagePath = genCode.getParentPackage().replace(".", File.separator);
        String otherPath = servletPath + File.separator + packagePath + File.separator + genCode.getModuleName() + File.separator + FolderConstant.OTHER;
        String modulePath = getModulePath(genCode);
        String filePath = otherPath + File.separator + modulePath;

        handleVue(servletPath, modulePath, filePath);
        handleVO(servletPath, modulePath, filePath, genCode);

        FileUtils.deleteDirectory(otherPath);
    }

    /*
     * <AUTHOR>
     * @Description 移动vue、js至各自目录下
     * @Date 20:17 2022/4/27
     * @param servletPath
     * @param modulePath
     * @param filePath
     * @return void
     **/
    public void handleVue(String servletPath, String modulePath, String filePath) {
        String vuePath = filePath + File.separator + TemplateConstant.vueFileName;
        String jsPath = filePath + File.separator + TemplateConstant.jsFileName;
        File vueSrc = new File(vuePath);
        File jsSrc = new File(jsPath);

        String vueFolder = servletPath + File.separator + FolderConstant.VUE + File.separator + modulePath;
        String jsFolder = servletPath + File.separator + FolderConstant.JS + File.separator + modulePath;
        File vueDest = new File(vueFolder);
        if (!vueDest.exists()) {
            vueDest.mkdirs();
        }
        File jsDest = new File(jsFolder);
        if (!jsDest.exists()) {
            jsDest.mkdirs();
        }

        FileUtil.copyFile(vueSrc, vueDest);
        FileUtil.copyFile(jsSrc, jsDest);
    }

    /*
     * <AUTHOR>
     * @Description 移动VO至entity目录下
     * @Date 20:17 2022/4/27
     * @param servletPath
     * @param modulePath
     * @param filePath
     * @param genCode
     * @return void
     **/
    public void handleVO(String servletPath, String modulePath, String filePath, GenCode genCode) {
        String voSrcPath = filePath + File.separator + modulePath + TemplateConstant.voFileSUFFIX;
        File voSrc = new File(voSrcPath);

        String packagePath = genCode.getParentPackage().replace(".", File.separator);
        String voDestPath = servletPath + File.separator + packagePath + File.separator + genCode.getModuleName() + File.separator + FolderConstant.ENTITY;
        File voDest = new File(voDestPath);
        if (!voDest.exists()) {
            voDest.mkdirs();
        }

        FileUtil.copyFile(voSrc, voDest);
    }

    /*
     * <AUTHOR>
     * @Description 获取mybatisPlus生成的module名
     * @Date 20:16 2022/4/27
     * @param genCode
     * @return String
     **/
    public String getModulePath(GenCode genCode) {
        String tableName = genCode.getTableName();
        String modulePath = null;
        if (StringUtils.isNotBlank(genCode.getTablePrefix())) {
            int prefixLength = genCode.getTablePrefix().length();
            if (StringUtils.isNotBlank(genCode.getTableSuffix())) {
                int suffixLength = genCode.getTableSuffix().length();
                String clearPre = tableName.substring(prefixLength);
                String clearSuffix = clearPre.substring(0, clearPre.length() - suffixLength);
                String camel = CaseUtils.underlineToCamel(clearSuffix);
                modulePath = camel.substring(0, 1).toUpperCase() + camel.substring(1);
                return modulePath;
            } else {
                String clearPre = tableName.substring(prefixLength);
                String camel = CaseUtils.underlineToCamel(clearPre);
                modulePath = camel.substring(0, 1).toUpperCase() + camel.substring(1);
                return modulePath;
            }
        } else {
            if (StringUtils.isNotBlank(genCode.getTableSuffix())) {
                int suffixLength = genCode.getTableSuffix().length();
                String clearSuffix = tableName.substring(0, tableName.length() - suffixLength);
                String camel = CaseUtils.underlineToCamel(clearSuffix);
                modulePath = camel.substring(0, 1).toUpperCase() + camel.substring(1);
                return modulePath;
            } else {
                String camel = CaseUtils.underlineToCamel(tableName);
                modulePath = camel.substring(0, 1).toUpperCase() + camel.substring(1);
                return modulePath;
            }
        }
    }
}
