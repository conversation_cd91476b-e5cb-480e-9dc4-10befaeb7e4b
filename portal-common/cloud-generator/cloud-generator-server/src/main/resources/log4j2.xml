<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="true">

    <property name="logback.logdir" value="/log"/>
    <property name="logback.appname" value="cloud-generator"/> <!--APP_NAME修改为应用名称-->

    <contextName>${logback.appname}</contextName>

    <!--输出到控制台 ConsoleAppender-->
    <appender name="consoleLog" class="ch.qos.logback.core.ConsoleAppender">
        <!--展示格式 layout-->
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                <!-- 标准版 -->
                <!-- <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} ${MY_POD_IP} %contextName [%thread] %-5level %logger{50} - [ppTraceId: %X{PtxId}, ppSpanId: %X{PspanId}] - %msg%n</pattern> -->
                <!-- 简化版 -->
                <pattern>%highlight(%-5level) %gray([%thread]) %cyan(%logger{50}) - %msg%n</pattern>
            </pattern>
        </layout>
    </appender>

    <appender name="fileInfoLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${logback.logdir}/${logback.appname}-${MY_POD_IP}.log</File>
        <!--滚动策略，按照时间滚动 TimeBasedRollingPolicy-->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--文件路径,定义了日志的切分方式——把每一天的日志归档到一个文件中,以防止日志填满整个磁盘空间-->
            <FileNamePattern>${logback.logdir}/${logback.appname}-${MY_POD_IP}.%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--只保留最近3天的日志-->
            <maxHistory>90</maxHistory>
            <!--应用启动时删除超过时间范围的日志文件-->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
            <!--用来指定日志文件的上限大小，那么到了这个值，就会删除旧的日志-->
            <!--<totalSizeCap>1GB</totalSizeCap>-->
        </rollingPolicy>
        <!--日志输出编码格式化-->
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%d ${MY_POD_IP} [%thread] %-5level %logger{64} %line - [ppTraceId: %X{PtxId}, ppSpanId: %X{PspanId}] - %msg%n
            </pattern>
        </encoder>
    </appender>

    <!--指定最基础的日志输出级别-->
    <root level="DEBUG">
        <appender-ref ref="consoleLog"/>
        <!--appender将会添加到这个logger-->
        <appender-ref ref="fileInfoLog"/>
        <appender-ref ref="consoleLog"/>
    </root>

</configuration>
