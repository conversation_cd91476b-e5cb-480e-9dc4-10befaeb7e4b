spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        # 联通铭盛内网测试应用地址
#        server-addr: 172.16.4.103:8848
        server-addr: 192.168.27.42:18848
        # 命名空间
        namespace: saas-test
        # 个人命名空间 - 注意不要提交至远程仓库
#        namespace: cuiyuanzhen
      config:
        # 联通铭盛内网测试应用地址
#        server-addr: 172.16.4.103:8848
        server-addr: 192.168.27.42:18848
        # 命名空间
        namespace: saas-test
        # 个人命名空间 - 注意不要提交至远程仓库
#        namespace: cuiyuanzhen
        # nacos 配置项中的 Data Id 的文件名
        prefix: cloud-generator
        # nacos 配置项中的 Data Id 的扩展文件格式
        file-extension: yaml
        # nacos 配置项中的 Group
        #group: Dog
unifast:
  recorder:
    create-type-name: create
    update-type-name: update
    delete-type-name: delete
    filter-property-names: createBy,createDate,updateBy,updateDate,deleteFlag
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
