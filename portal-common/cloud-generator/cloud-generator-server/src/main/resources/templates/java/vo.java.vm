package ${package.Entity};

#if(${swagger})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
#if(${entityLombokModel})
import lombok.Data;
#if(${chainModel})
import lombok.experimental.Accessors;
#end
#end

##/**
## * <p>
## * $!{table.comment}
## * </p>
## * <AUTHOR>
## * @since ${date}
## */
/**
 * <p>
 * $!{genCode.tableInfo.comments}
 * </p>
 * <AUTHOR>
 * @since ${date}
 */
#if(${entityLombokModel})
@Data
  #if(${chainModel})
@Accessors(chain = true)
  #end
#end
#if(${swagger})
@ApiModel(value = "${entity}VO", description = "")
#end
#if(${superEntityClass})
public class ${entity}VO extends BaseQueryVO#if(${activeRecord})<${entity}>#end {
#elseif(${activeRecord})
public class ${entity} extends Model<${entity}> {
#elseif(${entitySerialVersionUID})
public class ${entity} implements Serializable {
#else
public class ${entity} {
#end

## ----------  BEGIN 字段循环遍历  ----------
###foreach($field in ${table.fields})
#foreach($field in ${genCode.entityColumnList})

#if(${field.keyFlag})
#set($keyPropertyName=${field.propertyName})
#end
###if("$!field.comment" != "")
##  #if(${swagger})
##    @ApiModelProperty("${field.comment}")
##  #else
##    /**
##     * ${field.comment}
##     */
##  #end
###end
#if("$!field.columnComment" != "")
  #if(${swagger})
    @ApiModelProperty("${field.columnComment}")
  #else
    /**
     * ${field.columnComment}
     */
  #end
#end
###if(${field.keyFlag})
#if(${field.columnKey}=="PRI")
## 主键
  #if(${field.keyIdentityFlag})
    @TableId(value = "${field.annotationColumnName}", type = IdType.AUTO)
  #elseif(!$null.isNull(${idType}) && "$!idType" != "")
    @TableId(value = "${field.annotationColumnName}", type = IdType.${idType})
  #elseif(${field.convert})
    @TableId("${field.annotationColumnName}")
  #end
## 普通字段
#elseif(${field.fill})
## -----   存在字段填充设置   -----
  #if(${field.convert})
    @TableField(value = "${field.annotationColumnName}", fill = FieldFill.${field.fill})
  #else
    @TableField(fill = FieldFill.${field.fill})
  #end
#elseif(${field.convert})
    @TableField("${field.annotationColumnName}")
#end
## 乐观锁注解
#if(${field.versionField})
    @Version
#end
## 逻辑删除注解
#if(${field.logicDeleteField})
    @TableLogic
#end
    private ${field.propertyType} ${field.propertyName};
#end
## ----------  END 字段循环遍历  ----------

#if(!${entityLombokModel})
###foreach($field in ${table.fields})
#foreach($field in ${genCode.entityColumnList})
  #if(${field.propertyType.equals("boolean")})
##    #set($getprefix="is")
    #set($getprefix="get")
  #else
    #set($getprefix="get")
  #end
    public ${field.propertyType} ${getprefix}${field.propertyName}() {
        return ${field.propertyName};
    }

  #if(${chainModel})
    public ${entity} set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
  #else
    public void set${field.propertyName}(${field.propertyType} ${field.propertyName}) {
  #end
        this.${field.propertyName} = ${field.propertyName};
  #if(${chainModel})
        return this;
  #end
    }
#end
## --foreach end---
#end
## --end of #if(!${entityLombokModel})--

#if(${entityColumnConstant})
  #foreach($field in ${table.fields})
    public static final String ${field.name.toUpperCase()} = "${field.name}";

  #end
#end
#if(${activeRecord})
    @Override
    public Serializable pkVal() {
  #if(${keyPropertyName})
        return this.${keyPropertyName};
  #else
        return null;
  #end
    }

#end
###if(!${entityLombokModel})
##    @Override
##    public String toString() {
##        return "${entity}{" +
##  #foreach($field in ${table.fields})
##    #if($!{foreach.index}==0)
##        "${field.propertyName}=" + ${field.propertyName} +
##    #else
##        ", ${field.propertyName}=" + ${field.propertyName} +
##    #end
##  #end
##        "}";
##    }
###end
}
