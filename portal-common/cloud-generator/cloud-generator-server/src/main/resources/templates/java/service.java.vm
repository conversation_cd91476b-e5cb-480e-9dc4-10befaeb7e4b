package ${package.Service};

import ${package.Entity}.${entity};
import ${superServiceClassPackage};
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
#set($lowerEntity = $table.entityName.substring(0,1).toLowerCase() + $table.entityName.substring(1,$table.entityName.length()))
/**
 * <p>
 * $!{table.comment} 服务类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.serviceName} : ${superServiceClass}<${entity}>
#else
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {

    // 分页查询
    IPage<${table.entityName}> findPage(${table.entityName}VO ${lowerEntity}VO);

    // 根据id查询
    ${table.entityName} findOne(String id);

    // 查询列表
    List<${table.entityName}> findList();

    // 新增
    int add(${table.entityName} ${lowerEntity});

    // 修改
    int update(${table.entityName} ${lowerEntity});

    // 删除
    int delete(String id);

}
#end
