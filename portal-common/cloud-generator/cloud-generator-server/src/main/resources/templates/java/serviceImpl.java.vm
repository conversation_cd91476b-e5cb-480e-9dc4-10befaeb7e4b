package ${package.ServiceImpl};

import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
#set($lowerEntity = $table.entityName.substring(0,1).toLowerCase() + $table.entityName.substring(1,$table.entityName.length()))
/**
 * <p>
 * $!{table.comment} 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ${date}
 */
@Service
#if(${kotlin})
open class ${table.serviceImplName} : ${superServiceImplClass}<${table.mapperName}, ${entity}>(), ${table.serviceName} {

}
#else
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param ${lowerEntity}
     * @return IPage<${table.entityName}>
     **/
    @Override
    public IPage<${table.entityName}> findPage(${table.entityName}VO ${lowerEntity}VO) {
        IPage page = QueryVoToPageUtil.toPage(${lowerEntity}VO);
        return baseMapper.selectPage(page, null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param id
     * @return ${table.entityName}
     **/
    @Override
    public ${table.entityName} findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @return List<${table.entityName}>
     **/
    @Override
    public List<${table.entityName}> findList() {
        return baseMapper.selectList(null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param ${lowerEntity}
     * @return int
     **/
    @Override
    public int add(${table.entityName} ${lowerEntity}) {
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
        unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
        String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
        ${lowerEntity}.setTenantId(tenantId);
        }
        return baseMapper.insert(${lowerEntity});
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param ${lowerEntity}
     * @return int
     **/
    @Override
    public int update(${table.entityName} ${lowerEntity}) {
        return baseMapper.updateById(${lowerEntity});
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
#end
