
#!/bin/sh

p=${1}
project="cloud-core core_service_usercenter unifast-auth mall-storage"

if [ -z $p ]; then
    sleep 1
    echo 1s 后开始进行编译...

    for i in $project;

    do  
        echo $i：编译中...

        build=(`cd $i && mvn clean install -Dmaven.test.skip=true`)

        echo $build

        echo $i：编译完成

        sleep 1
        echo $i：1s 后开始上传到 ************
        case "$i" in
            "core_service_usercenter")  `scp -P 9876 ./core_service_usercenter/core_service_usercenter_server/target/core-service-usercenter-server-1.0.0.jar root@************:/data/docker/unifast-cloud/usercenter/core-service-usercenter-server-1.0.0.jar `
            ;;
            "unifast-auth") `scp -P 9876 ./unifast-auth/target/cloud-auth.jar root@************:/data/docker/unifast-cloud/auth/cloud-auth.jar `
            ;;
            "mall-storage") `scp -P 9876 ./mall-storage/mall-storage-server/target/mall-storage-server-1.0.0-SNAPSHOT.jar root@************:/data/docker/unifast-cloud/cms/mall-storage-server-1.0.0-SNAPSHOT.jar `
            ;;
        esac

        sleep 1
        echo 1s 后开始编译下一个服务...
    done  
else 

    echo $p：编译中...

    build=(`cd $p && mvn clean install -Dmaven.test.skip=true`)

    echo $build

    echo $p：编译完成

fi


 sleep 20


echo 20s 之后关闭，你也可以手动关闭...