package cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商机周报-汇总版 王增廷
 * </p>
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class OppoReviewStaticsExcelVo implements Serializable {


    /**
     * 地市信息
     */
    private String city;

    /**
     * 政企中台，商机个数
     */
    private String oppoNum;

    /**
     * 政企中台，商机金额
     */
    private String oppoAmount;

    /**
     * 营销经理核查通过商机个数
     */
    private String oppoCheckTotal;
    /**
     * 营销经理核查通过商机金额
     */
    private String oppoCheckAmount;

    /**
     * 重点跟进商机个数
     */
    private String oppoZhongdianTotal;
    /**
     * 重点跟进商机金额
     */
    private String oppoZhongdianAmount;

    /**
     * 继续跟进商机个数
     */
    private String oppoContinueTotal;
    /**
     * 继续跟进商机金额
     */
    private String oppoContinueAmount;

    /**
     * 摸查详细需求商机个数
     */
    private String oppoExamineTotal;
    /**
     * 摸查详细需求商机金额
     */
    private String oppoExamineAmount;

    /**
     * 有效商机个数
     */
    private String oppoYouxiaoTotal;
    /**
     * 有效商机金额
     */
    private String oppoYouxiaoAmount;



}
