package cn.chinaunicom.sdsi.cloud.strategy.vo;

import cn.chinaunicom.sdsi.cloud.strategy.workFeedbackNumber.entity.TSanquanWorkFeedbackNumber;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
* 工单反馈表（潜在机会推送）
*
* <AUTHOR>
* @since  2024-04-16
*/
@Data
@Schema(description = "工单反馈表（潜在机会推送）")
public class TSanquanWorkFeedbackVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "工单id")
	private String id;

	@Schema(description = "摸排表id")
	private String interViewId;

	@Schema(description = "自然客户id")
	private String customerId;

	@Schema(description = "潜在计划id")
	private String opportunityId;

	@Schema(description = "自然客户名称")
	private String customerName;

	@Schema(description = "客户经理名称")
	private String customerManager;

	@Schema(description = "客户经理id")
	private String customerManagerId;

	@Schema(description = "产品id")
	private String productId;

	@Schema(description = "产品名称")
	private String productName;

	@Schema(description = "0执行中、1已查看(客户经理已读)、2关单、3待获取商机编号、4未读(客户经理未读)、-1待改派(已推送到地市接口人的)")
	private Integer status;

	@Schema(description = "商机编号")
	private String businessOpportunityId;

	@Schema(description = "账期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date account;

	@Schema(description = "反馈类型（已创建商机、客户无需求、客户有需求、客户需求已建成或在建、其他）")
	private String feedbackType;

	@Schema(description = "反馈内容")
	private String feedbackContent;

	@Schema(description = "反馈时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date feedbackDate;

	@Schema(description = "承建方")
	private String contractor;

	@Schema(description = "承建时间")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date contractorDate;

	@Schema(description = "异网类型（运营商/非运营商）")
	private String newworkType;

	@Schema(description = "备用字段1（备注）")
	private String attr1;

	@Schema(description = "备用字段2")
	private String attr2;

	@Schema(description = "关单时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date closeTime;

	@Schema(description = "项目规模（万元）")
	private String projectScale;

	/***
	 * 摸排信息
	 */
	@Schema(description = "是否完成摸排（0否，1是）")
	private String isVisit;
	@Schema(description = "未拜访理由")
	private String reason;
	@Schema(description = "摸排方式(电话,现场)")
	private String visitMode;
	@Schema(description = "现场售前支撑人员")
	private String supportPerson;
	private String supportPersonName;
	@Schema(description = "摸排时间")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date visitDate;
	@Schema(description = "现场照片")
	private String attachments;

	/**
	 * 联系人类别
	 */
	private String contactType;

	/**
	 * 联系人名称
	 */
	private String contactName;

	/**
	 * 摸排反馈
	 */
	private String interviewFeedback;


	/**
	 * 商机情况
	 */
	private String businessOpportunities;

	@Schema(description = "产品到期预警时间")
	private String issueMonth;

	@Schema(description = "匹配模式：产品选客户、客户选产品")
	private String matchModel;

	@Schema(description = "达产（0未达产、1达产）产品找客户的时候使用")
	private String meetProduction;

	@Schema(description = "营销方案（主推产品、主推+辅推产品）")
	private String marketingPlan;

	@Schema(description = "业务类型（未达产）")
	private String businessType;

	@Schema(description = "业务数量（未达产）")
	private String businessNumber;

	@Schema(description = "达产业务号码数据")
	private List<TSanquanWorkFeedbackNumber> dcForm;

	@Schema(description = "联网通信（辅推产品）")
	private List<TSanquanWorkFeedbackNumber> lwtxForm;

	@Schema(description = "算网数智（辅推产品）")
	private List<TSanquanWorkFeedbackNumber> swszForm;

	@Schema(description = "推送人")
	private String pushPerson;

	@Schema(description = "推送人工号")
	private String pushPersonId;

	@Schema(description = "处理人")
	private String currentPerson;

	@Schema(description = "处理人工号")
	private String currentPersonId;

	@Schema(description = "策略Id")
	private String strategyId;

	@Schema(description = "策略名称")
	private String strategyName;

	@Schema(description = "地市")
	private String cityCode;

	@Schema(description = "监控状态（0、正常，1、预警，2、催办，3、督办）")
	private String monitorStatus;

	@Schema(name = "推送时间")
	private Date pushTime;

	@Schema(name = "是否推送 0:未推送 1:已推送")
	private Long isPush;

	@Schema(name = "是否推送地市接口人 0:未推送 1:已推送")
	private Long isPushCity;

	@Schema(description = "支撑人退回原因")
	private String returnReason;

	@Schema(description = "工单创建人")
	private String createBy;

	@Schema(description = "行业")
	private String industry;

	/**
	 * 是否支撑（0、否，1、是）
	 */
	private String isBrace;

	/**
	 * 解决方案支撑人
	 */
	private String solutionSupporter;

	/**
	 * 支撑时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date supportTime;

	/**
	 * 支撑形式
	 */
	private String supportForms;

	/**
	 * 补充说明
	 */
	private String supplementaryExplanation;

	/**
	 * 待办code
	 */
	private String todoCode;

	/**
	 * 工单待办表Id
	 */
	private String todoOppoId;

	/**
	 * 推送类型
	 */
	private String pushType;

	/**
	 * 软件规模
	 */
	private String softwareScale;

	/**
	 * 毛利润
	 */
	private String grossProfit;

	/**************************************新增vo字段************************************************/
	/**
	 * 客户经理状态
	 */
	private String khStatus;
	/**
	 * 营销经理状态
	 */
	private String yxStatus;
	/**
	 * 策略创建人
	 */
	private String createUser;
	/**
	 * 策略创建人
	 */
	private String customerManagerIsIntention;//客户经理是否意向
	private String marketingManagerIsIntention;//营销经理是否意向
	private String khjlBusinessOpportunityNum;//客户经理商机编号
	private String yxjlBusinessOpportunityNum;//营销经理商机编号
	private String marketingManager;//营销经理
	private String marketingManagerId;//营销经理id




}
