package cn.chinaunicom.sdsi.cloud.strategy.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "策略匹配客户群")
public class TSanquanStrtegyCustomerQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "策略表id")
    private String id;

    @Schema(description = "策略配置名称")
    private String strategyName;

    @Schema(description = "策略配置id")
    private String strategyId;

    @Schema(description = "客户群id")
    private String customerGroupId;

    @Schema(description = "客户群名称")
    private String customerGroupName;

    @Schema(description = "客户群中客户数量")
    private String customerGroupNum;

    public String getStartDate() {
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate thirtyDaysAgo = today.minusDays(30); // 当前日期减去30天
        // 输出结果
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return thirtyDaysAgo.format(formatter);
    }
}
