package cn.chinaunicom.sdsi.cloud.leaderVisit.visitflow.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import cn.chinaunicom.sdsi.cloud.leaderVisit.visitflow.enums.VisitFlowOrderStatusEnum;
import cn.hutool.json.JSONObject;

import java.io.Serializable;

/**
 * 流程状态变更回调接口日志表
 *
 * <AUTHOR>
 */
@Data
@TableName("t_sanquan_flow_callback_log")
public class TSanquanFlowCallbackLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 流程实例ID
     */
    private String orderId;
    /**
     * 订单业务编码
     */
    private String orderBizCode;
    /**
     * 订单业务名称
     */
    private String orderBizName;

    /**
     * 订单创建者oa工号名称
     */
    private String orderOperatorName;
    
    /**
     * 订单创建者oa工号
     */
    private String orderOperatorId;
    
    /**
     * 订单创建者省份编码
     */
    private String orderProvinceCode;
    
    /**
     * 订单创建者地市编码
     */
    private String orderCityCode;
    
    /**
     * 流程状态(3:结束:1:在途)
     */
    private String orderStatus;
    /**
     * 流程状态名称
     */
    private String orderStatusName;
    
    /**
     * 任务ID
     */
    private String worksheetId;
    
    /**
     * 任务编码
     */
    private String taskCode;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 处理人编码
     */
    private String dealUserCode;
    
    /**
     * 处理人名称
     */
    private String dealUserName;
    
    /**
     * 处理状态编码
     */
    private String dealResultCode;
    
    /**
     * 报文体(JSON格式)
     */
    private String body;
    
    /**
     * 任务处理内容(JSON格式)
     */
    private String receiptBody;
    
    /**
     * 处理意见
     */
    private String suggestion;
    
    /**
     * 上游系统单号
     */
    private String originOrderId;
    
    /**
     * 高层拜访时间
     */
    private String visitTime;
    
    /**
     * 拜访客户
     */
    private String visitCustomer;
    
    /**
     * 拜访内容
     */
    private String visitContent;
    
    /**
     * 跟进方式
     */
    private String followMethod;
    
    /**
     * 跟进情况
     */
    private String followStatus;
    
    /**
     * 关联商机
     */
    private String relatedOpportunity;
    
    /**
     * 工作计划
     */
    private String workPlan;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 从JSONObject解析数据
     */
    public static TSanquanFlowCallbackLog parseFromJson(JSONObject jsonObject) {
        TSanquanFlowCallbackLog log = new TSanquanFlowCallbackLog();
        // 解析基础字段
        log.setOrderId(jsonObject.getStr("orderId"));
        log.setOrderOperatorName(jsonObject.getStr("orderOperatorName"));
        log.setOrderOperatorId(jsonObject.getStr("orderOperatorId"));
        log.setOrderProvinceCode(jsonObject.getStr("orderProvinceCode"));
        log.setOrderCityCode(jsonObject.getStr("orderCityCode"));
        //订单业务编码
        log.setOrderBizCode(jsonObject.getStr("bizCode"));
        // 订单状态
        log.setOrderStatus(jsonObject.getStr("orderStatus"));
        // 订单状态名称
        log.setOrderStatusName(VisitFlowOrderStatusEnum.getByCode(Integer.parseInt(jsonObject.getStr("orderStatus"))).getDesc());
        log.setWorksheetId(jsonObject.getStr("worksheetId"));
        log.setTaskCode(jsonObject.getStr("taskCode"));
        log.setTaskName(jsonObject.getStr("taskName"));
        log.setDealUserCode(jsonObject.getStr("dealUserCode"));
        log.setDealUserName(jsonObject.getStr("dealUserName"));
        log.setDealResultCode(jsonObject.getStr("dealResultCode"));
        log.setOriginOrderId(jsonObject.getStr("originOrderId"));
        // 解析body对象
        JSONObject bodyObj = jsonObject.getJSONObject("body");
        if (bodyObj != null) {
            log.setBody(bodyObj.toString());
            //订单业务名称
            log.setOrderBizName(bodyObj.getStr("bizName"));
            //高层拜访时间
            log.setVisitTime(bodyObj.getStr("dateField_ng7tSxik"));
            //拜访客户
            log.setVisitCustomer(bodyObj.getStr("custName"));
            //拜访内容
            log.setVisitContent(bodyObj.getStr("textField_aIo1Oewc"));
            //工作计划
            log.setWorkPlan(bodyObj.getStr("textareaField_2LqTPIki"));
            //跟进情况
            log.setFollowStatus(bodyObj.getStr("textareaField_uUBYx0MW"));
            //跟进方式
            log.setFollowMethod(bodyObj.getStr("radioField_F9Fv4Yym"));
            //关联商机
            log.setRelatedOpportunity(bodyObj.getStr("businessOpportunity"));
        }
        JSONObject receiptBodyObj = jsonObject.getJSONObject("receiptBody");
        if (receiptBodyObj != null) {
            log.setReceiptBody(receiptBodyObj.toString());
            log.setSuggestion(receiptBodyObj.getStr("suggestion"));
        }
        return log;
    }
}