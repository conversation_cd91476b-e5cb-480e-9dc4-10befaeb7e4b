package cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.vo;

import java.io.Serializable;
import java.math.BigDecimal;

import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import lombok.Data;

/**
 * <p>
 * 政企中台商机
 * </p>
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class ZqOppoPoolsVo implements Serializable {


    /**
     * 商机编号
     */
    private String oppoNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 自然客户id
     */
    private String natureCustId;

    /**
     * 自然客户名称
     */
    private String natureCustName;

    /**
     * 地市
     */
    private String city;

    /**
     * 省分行业
     */
    private String industry;

    /**
     * 名单制客户经理
     */
    private String managerName;

    /**
     * 名单制客户经理OA
     */
    private String managerOa;

    /**
     * 名单制客户经理电话
     */
    private String managerPhone;

    /**
     * 预计合同总金额(万元）
     */
    private BigDecimal contractAmount;

    /**
     * 客户需求简介
     */
    private String khxqjj;

    /**
     * 客户是否明确预算
     */
    private String khsfmqys;

    /**
     * 数据插入时间
     */
    private String dayId;
    /**
     * 营销经理处理时长
     */
    private String dealTimeM;

    // ===============================================ai处理数据

    /**
     * AI推荐产品
     */
    private String productAi;

    /**
     * 需求关键词
     */
    private String reqKeyword;
    /**
     * 商机打标类型（纯硬件或纯联网商机、软硬结合商机 、纯软件商机
     */
    private String markType;
    /**
     * 是否初筛通过(1是、0否)
     */
    private String firstAuditPass;


    /**
     * 状态(0:未处理 1：已处理)
     */
    private String status;

    /**
     * 更新时间
     */
    private String updateTime;

    // =============================================== 商机库数据

    /**
     * 商机评审表（商机库表）ID
     */
    private String reviewId;

    /**
     * 预计合同总金额(万元）（修改前）
     */
    private BigDecimal contractAmountPrevReview;

    /**
     * 预计合同总金额(万元）（修改后）
     */
    private BigDecimal contractAmountReview;

    /**
     * 商机落实信息
     */
    private String oppoLsInfo;

    /**
     * 是否有效商机(是、否)（0、否，1、是）
     */
    private String sfyxOppo;

    /**
     * 是否继续跟踪(是、否)（0、否，1、是）
     */
    private String sfjxTrack;

    /**
     * 商机跟进类型（重点跟踪/继续跟进/摸查需求）
     */
    private String sjgjlxM;

    /**
     * 商机跟进建议
     */
    private String sjgjjyM;

    /**
     * 填写人工号（营销经理）
     */
    private String createByM;

    /**
     * 填写时间（营销经理）
     */
    private String createTimeM;

    /**
     * 填写人姓名（营销经理）
     */
    private String createNameM;

    /**
     * 流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusM;
    /**
     * 运营-流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusYY;
    /**
     * 集成交付-流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusJC;
    /**
     * 产研-流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusCY;

    /**
     * 退回意见
     */
    private String backRemark;

    /**
     * 语音识别id
     */
    private String asrId;

    public String getFirstAuditPass() {
        if(StringUtils.isNotEmpty(firstAuditPass)){
            if("1".equals(firstAuditPass)){
                return "是";
            }else if("0".equals(firstAuditPass)){
                return "否";
            }
        }
        return firstAuditPass;
    }

    public String getCreateTimeM() {
        if(StringUtils.isNotEmpty(createTimeM) && createTimeM.length() > 20){
            createTimeM = createTimeM.substring(0,createTimeM.length()-2);
        }
        return createTimeM;
    }
}
