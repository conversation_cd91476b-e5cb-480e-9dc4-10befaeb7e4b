package cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 商机评审分析表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
public class OppoReviewAnalysis implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 商机编号
     */
    private String oppoNumber;
    /**
     * 预计合同总金额(万元）（修改前）
     */
    private BigDecimal contractAmountPrev;

    /**
     * 预计合同总金额(万元）（修改后）
     */
    private BigDecimal contractAmount;

    /**
     * 商机落实信息
     */
    private String oppoLsInfo;

    /**
     * 是否有效商机(是、否)（0、否，1、是）
     */
    private String sfyxOppo;

    /**
     * 是否继续跟踪(是、否)（0、否，1、是）
     */
    private String sfjxTrack;

    /**
     * 商机跟进类型（重点跟踪/继续跟进/摸查需求）
     */
    private String sjgjlxM;

    /**
     * 商机跟进建议
     */
    private String sjgjjyM;

    /**
     * 填写人工号（营销经理）
     */
    private String createByM;

    /**
     * 填写时间（营销经理）
     */
    private String createTimeM;

    /**
     * 填写人姓名（营销经理）
     */
    private String createNameM;

    /**
     * 流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusM;

    /**
     * 退回意见
     */
    private String backRemark;

    /**
     * 融自主交付机会(是、否)（0、否，1、是）
     */
    private String rzzjfjh;

    /**
     * 融联通云(是、否)（0、否，1、是）
     */
    private String rlty;

    /**
     * 融安全(是、否)（0、否，1、是）
     */
    private String rsafe;

    /**
     * 商机跟进类型（重点跟踪/继续跟进/摸查需求）
     */
    private String sjgjlxJc;

    /**
     * 商机跟进建议
     */
    private String sjgjjyJc;

    /**
     * 填写人工号（集成交付）
     */
    private String createByJc;

    /**
     * 填写时间（集成交付）
     */
    private String createTimeJc;

    /**
     * 填写人姓名（集成交付）
     */
    private String createNameJc;

    /**
     * 流程状态（0：未处理 1：已处理）（集成交付）
     */
    private String statusJc;

    /**
     * 融标品机会(是、否)（0、否，1、是）
     */
    private String rbpjh;

    /**
     * 云大物安等标品
     */
    private String ydwabp;

    /**
     * 融专线(是、否)（0、否，1、是）
     */
    private String rzx;

    /**
     * 融工作手机(是、否)（0、否，1、是）
     */
    private String rgzsj;

    /**
     * 融5G专网(是、否)（0、否，1、是）
     */
    private String r5gzw;

    /**
     * 是否可借助硬件类产品框架集采优势(是、否)
     */
    private String sfkjzyjlcpkjjcys;

    /**
     * 商机跟进类型（重点跟踪/继续跟进/摸查需求）
     */
    private String sjgjlxYy;

    /**
     * 商机跟进建议
     */
    private String sjgjjyYy;

    /**
     * 填写人工号（运营服务部）
     */
    private String createByYy;

    /**
     * 填写时间（运营服务部）
     */
    private String createTimeYy;

    /**
     * 填写人姓名（运营服务部）
     */
    private String createNameYy;

    /**
     * 流程状态（0：未处理 1：已处理）（运营服务部）
     */
    private String statusYy;

    /**
     * 融自研产品机会(是、否)（0、否，1、是）
     */
    private String rzycpjh;

    /**
     * 适配目录自研产品(山东自研产品目录中选择)
     */
    private String spmlzycp;

    /**
     * 适配非目录自研产品(填写)
     */
    private String spfmlzycp;

    /**
     * 融物联网(是、否)（0、否，1、是）
     */
    private String rwlw;

    /**
     * 融大数据(是、否)（0、否，1、是）
     */
    private String rdsj;

    /**
     * 融AI(是、否)（0、否，1、是）
     */
    private String rai;

    /**
     * 商机跟进类型（重点跟踪/继续跟进/摸查需求）
     */
    private String sjgjlxCy;

    /**
     * 商机跟进建议
     */
    private String sjgjjyCy;

    /**
     * 产品机会(是、否)（0、否，1、是）
     */
    private String cpjh;

    /**
     * 产品跟进建议
     */
    private String cpgjjy;

    /**
     * 填写人工号（产品研发部）
     */
    private String createByCy;

    /**
     * 填写时间（产品研发部）
     */
    private String createTimeCy;

    /**
     * 填写人姓名（产品研发部）
     */
    private String createNameCy;

    /**
     * 流程状态（0：未处理 1：已处理）（产研）
     */
    private String statusCy;

    /**
     * 商机跟进类型（最终）
     */
    private String sjgjlxResult;

    /**
     * 商机跟进建议(最终)
     */
    private String sjgjjyResult;

    /**
     * 是否有效商机(是、否或已移出)（0、否，1、是）
     */
    private String oppoStatus;

    /**
     * 数据插入时间
     */
    private String dayId;

    /**
     * 默认0锁
     */
    private Integer version;

    /**
     * 是否更新（0、否，1、是）
     */
    private Integer isUpdate;

    /**
     * 产品研发部更新之后是否处理是否更新（0、否，1、是）
     */
    private Integer isHandleCpyf;

    /**
     * 运营服务部更新之后是否处理是否更新（0、否，1、是）
     */
    private Integer isHandleYyfw;

    /**
     * 集成交付部更新之后是否处理是否更新（0、否，1、是）
     */
    private Integer isHandleJcjf;
}
