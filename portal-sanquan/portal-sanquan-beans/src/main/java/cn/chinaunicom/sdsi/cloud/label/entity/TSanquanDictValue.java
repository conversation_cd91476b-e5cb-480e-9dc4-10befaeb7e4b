package cn.chinaunicom.sdsi.cloud.label.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 字典管理-字典分类表
 *
 * @Author: 白志虎
 * @Date: 2024/5/11 13:40
 */
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_dist_vaule")
public class TSanquanDictValue extends BaseEntity {

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 字典健
     */
    private String dictKey;

    /**
     * 字典值
     */
    private String dictValue;


    /**
     * 排序
     */
    private String type_id;


    private String sort;


    private String delete_flag = "normal";


    private String ext1;

    private String ext2;

    private String ext3;

    private String ext4;

    private String ext5;


    private String dictName;


}
