package cn.chinaunicom.sdsi.cloud.menus.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@Schema(description = "标签管理VO")
public class MenuVO implements Serializable {

    @TableId
    private String id;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "图标")
    private String roles;


    @Schema(description = "上级id")
    private String parentId;

    @Schema(description = "角色多个 ,分割")
    private List<String> role;

    @Schema(description = "下级")
    private List<MenuVO> children;
}
