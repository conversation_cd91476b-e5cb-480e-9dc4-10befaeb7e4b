package cn.chinaunicom.sdsi.cloud.strategy.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import java.util.List;

/**
 * 摸排信息表
 *
 * <AUTHOR>
 * @since 2024-04-25
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_sanquan_strategy_config")
public class TSanquanStrtegyConfiguration extends BaseEntity {

    @TableId
    private String id;

    @Schema(description = "策略编号")
    private String strategyNo;

    @Schema(description = "策略名称")
    private String strategyName;

    @Schema(description = "执行频次")
    private String executeFrequencyType;

    @Schema(description = "执行频次")
    private String executeFrequency;

    @Schema(description = "匹配方式/客户找产品/产品找客户")
    private String matchModel;

    @Schema(description = "有效期起")
    private String validityStart;

    @Schema(description = "有效期至")
    private String validityEnd;

    @Schema(description = "执行日期")
    private String executeDate;
    @Schema(description = "周几执行")
    private String executeWeek;

    @Schema(description = "执行时间")
    private String executeTime;

    @Schema(description = "执行周期")
    private String executeCycle;

    @Schema(description = "执行范围区域")
    private String executeArea;

    @Schema(description = "执行人/ 地方政企/ 客户经理")
    private String excuteUser;

    @Schema(description = "创建人")
    private String createUser;


    @Schema(description = "是否有效")
    private String isEffective;

    @Schema(description = "是否可以转派")
    private String isReturnUser;
    @Schema(description = "描述")
    private String descriptionContent;
    @Schema(description = "状态")
    private String executeStatus;
    @Schema(description = "逻辑删除")
    private String delFlag;

    @Schema(description = "策略模型")
    private String strategyModel;

    @Schema(description = "所属行业")
    private String industry;

    @Schema(description = "营销模式")
    private String markingMode;

    @Schema(description = "产品选择方式")
    private String productSelType;

    @Schema(description = "所属行业")
    @Transient
    @TableField(exist = false)
    private List<String> industryList;

    @Schema(description = "自动审核 0否 1是")
    @Transient
    @TableField(exist = false)
    private String autoPass;

    @Schema(description = "定时任务ID")
    private String jobId;

    @Schema(description = "所属地市")
    private String city;

    @Schema(description = "所属区县")
    private String district;

    @Schema(description = "创建人登录名")
    private String loginName;

    @Schema(description = "区域类型 P:省  C:地市")
    private String areaType;

    @Schema(description = "触点（靶向营销、要客管家）")
    private String contactor;

    @Schema(description = "营销方案")
    private String marketingPlan;

    @Schema(description = "模型id")
    private String modelId;

    @Schema(description = "任务（0、不是，1、是）")
    private String isTask;

    @Schema(description = "任务Id")
    private String taskId;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "反馈时是否上传图片1:必传 0:非必传")
    private String isUploadFeedback;

    @Schema(description = "是否发送短信 0:否 1:是")
    private String isSms;

    @Schema(description = "是否推送营销经理 0:否 1:是")
    private String isPushMarketingManager;

    @Schema(description = "短信模版")
    private String smsTemplate;

    @Schema(description = "所属行业")
    private String belongIndustry;

    @Schema(description = "是否推送靶向 0:否 1:是")
    private String isPushBaxiang;

}
