package cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 地市接口人表
 *
 * <AUTHOR> 
 * @since  2024-05-30
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_prefecture_interface_person")
public class TSanquanPrefectureInterfacePerson extends BaseEntity {
	/**
	* id
	*/
	@TableId
	private String id;

	/**
	 * 地市接口人工号
	 */
	private String jobNumber;

	/**
	* 地市接口人名字
	*/
	private String name;

	@Schema(description = "是否审核人")
	private String type;

	/**
	* 地市接口人电话
	*/
	private String tel;

	/**
	* 所属行业
	*/
	private String industry;

	/**
	* 客户省分
	*/
	private String listCustomerProvince;

	/**
	* 所在地市
	*/
	private String listCustomerCity;

	/**
	* 所在区县
	*/
	private String listCustomerDistrict;

	/**
	* 名单制客户id
	*/
	private String rosterCustomerId;

	/**
	* 名单制客户名称
	*/
	private String rosterCustomerName;

	/**
	* 客户经理id
	*/
	private String customerManagerId;

	/**
	* 客户经理名称
	*/
	private String customerManagerName;

	/**
	* 省分行业
	*/
	private String provinceIndustry;

	/**
	* 客户等级
	*/
	private String listCustomerLevel;

	/**
	* 联系地址
	*/
	private String contactAddress;

	/**
	* 备用字段1
	*/
	private String attr1;

	/**
	* 备用字段2
	*/
	private String attr2;

	/**
	* 备用字段3
	*/
	private String attr3;

	/**
	 * 二级审核（地市审核）
	 */
	private String cityRole;
}