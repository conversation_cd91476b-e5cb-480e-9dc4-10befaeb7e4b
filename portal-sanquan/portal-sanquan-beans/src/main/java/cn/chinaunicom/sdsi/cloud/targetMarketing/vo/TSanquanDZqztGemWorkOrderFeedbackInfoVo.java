package cn.chinaunicom.sdsi.cloud.targetMarketing.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 靶向营销反馈表视图对象
 * @Author: duanzk
 * @Date: 2024-11-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "靶向营销反馈表视图对象", description = "靶向营销反馈表视图对象")
public class TSanquanDZqztGemWorkOrderFeedbackInfoVo implements Serializable {

    @Schema(name = "主键")
    private String ID;

    @Schema(name = "任务编码")
    private String workOrderCode;

    @Schema(name = "反馈触点id")
    private String touchId;

    @Schema(name = "客户中心拜访id")
    private String visitId;

    @Schema(name = "集客工号")
    private String userCode;

    @Schema(name = "反馈结果（1执行成功；0无法执行")
    private String visitResult;

    @Schema(name = "  反馈时间")
    private String backTime;

    @Schema(name = "是否继续拜访(1是，0否)")
    private String isContinueVisit;

    @Schema(name = "无法执行原因")
    private String noVisitReason;

    @Schema(name = "下次执行时间")
    private String nextTime;

    @Schema(name = "拜访主题")
    private String visitTheme;

    @Schema(name = "拜访形式")
    private String visitWay;

    @Schema(name = "拜访时间")
    private String visitTime;

    @Schema(name = "拜访地点")
    private String visitSite;

    @Schema(name = "访谈人姓名")
    private String visitPerson;

    @Schema(name = "访谈人联系方式")
    private String visitPhone;

    @Schema(name = "访谈内容")
    private String visitContent;

    @Schema(name = "拜访总结")
    private String visitSummarize;

    @Schema(name = "重点推荐产品")
    private String keyRecommendedProduct;

    @Schema(name = "是否有商机意向（1是，0否）")
    private String isBusiOppWilling;

    @Schema(name = "是否需要支撑（1是，0否）")
    private String isNeedSupport;

    @Schema(name = "客户中心商机id")
    private String busiOppt;

    @Schema(name = "商机类型")
    private String busiOppType;

    @Schema(name = "商机名称")
    private String busiOpptName;

    @Schema(name = "是否录入异网信息   0：否，1：是")
    private String isInsertDiff;

    @Schema(name = "运营商   1移动2电信")
    private String OPERATOR;

    @Schema(name = "业务类型 1移网，2固网，3创新，4其它")
    private String businessType;

    @Schema(name = "异网到期时间")
    private String endDate;

    @Schema(name = "异网业务信息描述")
    private String diffBusinessInfo;

    @Schema(name = "附件链接（多个用逗号隔开）")
    private String pictureUrls;

    @Schema(name = "客户意愿 计划不续约（选择后，出现“是否录入异网信息”选项）、暂")
    private String customerWillingness;

    @Schema(name = "是否完成续约   1是、0否")
    private String isRenewal;

    @Schema(name = "预计收回时间")
    private String estimatedRecoveryTime;

    @Schema(name = "是否催缴成功   1是、0否")
    private String isPaymentSuccess;

    @Schema(name = "是否销账   1是、0否")
    private String isWriteOff;

    @Schema(name = "是否续约成功（1是，0否）")
    private String isRenewalSuccess;

    @Schema(name = "续约内容")
    private String renewalContent;

    @Schema(name = "是否存在离网倾向（1是，0否）")
    private String isOffLineTendency;

    @Schema(name = "业务到期预警反馈-客户意愿（0计划不续约，1计划续约，2暂无计划")
    private String customerWillingnessForBusiness;

    @Schema(name = "业务到期预警反馈-是否完成续约（1是，0否）")
    private String isCompleteRenewalForBusiness;

    @Schema(name = "收入波动反馈-是否存在离网倾向（2未知，1存在，0不存在）")
    private String isOffLineTendencyForIncome;

    @Schema(name = "零产原因反馈")
    private String zeroProductionReason;

    @Schema(name = "下一步动作")
    private String nextStep;

    @Schema(name = "是否本楼客户（1是，0否）")
    private String isBuildingCustomer;

    @Schema(name = "是否修改客户入位信息（1是，0否）")
    private String isModifyCustomerCheckInfo;

    @Schema(name = "是否录入拜访记录（1是，0否）")
    private String isVisitId;

    @Schema(name = "是否有效（1有效，0无效）")
    private String isValid;

    @Schema(name = "无效原因")
    private String invalidReason;

    @Schema(name = "签到位置")
    private String signInPlace;

    @Schema(name = "签到开始时间")
    private String signInStartTime;

    @Schema(name = "签到结束时间")
    private String signInEndTime;

    @Schema(name = "反馈额外信息关联id")
    private String extraInfoLink;

    @Schema(name = "创建时间")
    private String gmtCreate;

    @Schema(name = "创建人ID")
    private String createUserId;

    @Schema(name = "创建人姓名")
    private String createUserName;

    @Schema(name = "修改时间")
    private String gmtModified;

    @Schema(name = "修改人姓名")
    private String updateUserName;

    @Schema(name = "修改人ID")
    private String updateUserId;

    @Schema(name = "是否删除")
    private String isDelete;

    @Schema(name = "删除时间")
    private String deleteTime;

    @Schema(name = "")
    private String monthId;

    @Schema(name = "")
    private String dayId;

    private String tenantId;

}
