package cn.chinaunicom.sdsi.cloud.label.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 标签管理-标签分类表
 * @Author: 王永凯
 * @Date: 2024/5/11 13:40
 */
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_label_classify")
public class TSanquanLabelClassify extends BaseEntity {

    /**
     *  主键
     */
    @TableId
    private String id;

    /**
     * 标签分类编号
     */
    private String code;

    /**
     * 标签分类名称
     */
    private String label;

    /**
     * 父级标签分类主键
     */
    private String parentId;

    /**
     * 父类标签分类编号
     */
    private String parentCode;

    /**
     * 父类标签分类名称
     */
    private String parentLabel;

}
