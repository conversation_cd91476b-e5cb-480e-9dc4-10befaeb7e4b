package cn.chinaunicom.sdsi.cloud.product.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.baomidou.mybatisplus.annotation.*;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 1
 *
 * <AUTHOR> 
 * @since  2024-04-19
 */

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_product_mark")
public class TSanquanProductMark extends BaseEntity {
	/**
	* 打标id
	*/
	@TableId
	private String id;

	/**
	* 产品id
	*/
	private String productId;

	/**
	* 产品名称
	*/
	private String productName;

	/**
	* 标签类型
	*/
	private String markType;

	/**
	* 标签类型id
	*/
	private String markTypeId;

	/**
	* 标签类型值
	*/
	private String markData;

	/**
	* 标签类型值id
	*/
	private String markDataId;

	/**
	* 其他
	*/
	private String other;

	/**
	 * 对应大宽表中的列
	 */
	private String bigColumn;

	/**
	 * 标签类型
	 */
	private String tagType;

}