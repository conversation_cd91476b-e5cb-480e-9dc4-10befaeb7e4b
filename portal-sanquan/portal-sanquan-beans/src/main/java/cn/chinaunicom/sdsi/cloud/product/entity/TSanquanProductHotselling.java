package cn.chinaunicom.sdsi.cloud.product.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 热销产品对象 t_sanquan_product_hotselling
 * @Author: hanruxiao
 * @Date: 2024-06-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "热销产品对象", description = "热销产品对象")
@TableName("t_sanquan_product_hotselling")
public class TSanquanProductHotselling extends BaseEntity {

    @Schema(name = "")
    @TableId
    private Long id;

    @Schema(name = "产品id")
    private String productId;

    @Schema(name = "行业")
    private String industry;

    @Schema(name = "排名")
    private String rank;

}
