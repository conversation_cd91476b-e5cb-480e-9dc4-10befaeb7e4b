package cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo;

import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商机周报-通报版 王增廷
 * </p>
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class OppoReviewCommonExcelVo implements Serializable {

    /**
     * 地市信息
     */
    private String city;

    /**
     * 政企中台，商机个数
     */
    private String oppoNum;

    /**
     * 政企中台，商机金额
     */
    private String oppoAmount;

    /**
     * 有效商机个数
     */
    private String oppoYouxiaoNum;
    /**
     * 有效商机金额
     */
    private String oppoYouxiaoAmount;

    /**
     * 有效商机数量占比
     */
    private String oppoYouxiaoNumRate;
    /**
     * 有效商机金额占比
     */
    private String oppoYouxiaoAmountRate;

}
