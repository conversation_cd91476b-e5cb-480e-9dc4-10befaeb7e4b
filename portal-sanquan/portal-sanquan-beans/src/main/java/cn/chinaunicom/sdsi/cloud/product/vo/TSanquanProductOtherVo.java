package cn.chinaunicom.sdsi.cloud.product.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 产品的其他信息
 * </p>
 * <AUTHOR>
 * @since 2024-10-21
 */
@Data
public class TSanquanProductOtherVo implements Serializable {


    private String id;

    /**
     * 产品ID
     */
    private String productId;

    /**
     * 标题、问题（标头）
     */
    private String otherTitle;

    /**
     * 详情、回答
     */
    private String otherDetails;

    /**
     * 标题类型（1、怎么讲解，2、怎么卖，3、产品功能，4、产品优势，5、常见问题）
     */
    private String titleType;


}
