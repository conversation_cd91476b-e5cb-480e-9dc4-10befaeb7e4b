package cn.chinaunicom.sdsi.cloud.customer.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户资料审核
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@Schema(description = "客户资料审核对象")
public class TSanquanCustomerInfoAuditRequestVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "审核表id")
    private String id;

    @Schema(description = "客户id（客户资料id）")
    private String[] customerIdList;

    @Schema(description = "是否通过（0-待审核，1-审核通过，2-审核不通过）")
    private String isPassed;

    @Schema(description = "审核原因")
    private String reason;

    @Schema(description = "备用字段1")
    private String attr1;
    
    @Schema(description = "备用字段2")
    private String attr2;

    @Schema(description = "备用字段3")
    private String attr3;
}
