package cn.chinaunicom.sdsi.cloud.dbcp.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 提醒任务表
 * </p>
 * <AUTHOR>
 * @since 2025-03-12
 */
@Data
//@ApiModel(value = "TSanquanRemindTaskVo", description = "")
public class TSanquanRemindTaskQueryVo extends BaseQueryVO {


      private String id;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 策略编号
     */
    private String superiorPolicyCode;

    /**
     * 提醒环节 mp:摸排 gz:跟踪 qy:签约
     */
    private String currentLink;

    /**
     * 短信内容
     */
    private String smsContent;

    /**
     * 任务状态: 0未执行、1已执行、2作废
     */
    private String taskStatus;
    private String taskStatus2;

    /**
     * 提醒任务计划开始时间
     */
    private String planWarnTime;

    /**
     * 提醒任务实际完成时间
     */
    private String realWarnTime;

    /**
     * 作废原因
     */
    private String remark;

    /**
     * 执行人手机号(客户经理、片区经理)
     */
    private String executorTel;

    /**
     * 执行人oa(客户经理、片区经理)
     */
    private String executorOa;

    private String createTime;

    private String createTimeYear;

    private String updateTime;

    /**
     * 提醒任务类型，1常规提醒，2超时提醒
     */
    private String smsType;
    /**
     * 执行时间字段（查询定时任务时使用）
     */
    private String queryTime;

}
