package cn.chinaunicom.sdsi.cloud.customerPicture.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
/**
 * 三全客户画像-客户拜访表
 *
 * <AUTHOR>
 * @since  2024-10-24
 */
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("t_sanquan_customer_profile_visit")
public class TSanquanCustomerProfileVisit implements Serializable {
  private static final long serialVersionUID = 1L;
  private String natureCustId;
  private String natureCustName;
  private String visitNumber;
  private String visitYear;
  private Date createTime;


}
