package cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("t_sanquan_zq_leader_visit")
public class ZqLeaderVisit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 拜访id
     */
    private String visitId;

    /**
     * 地市
     */
    private String city;

    /**
     * 签到地点
     */
    private String signInPlace;

    /**
     * 拜访方式： 现场、电话
     */
    private String visitType;

    /**
     * 拜访时间
     */
    private String visitTime;

    /**
     * 拜访客户ID
     */
    private String natureCustId;

    /**
     * 拜访客户名称
     */
    private String natureCustName;

    /**
     * 拜访总结
     */
    private String visitSummary;

    /**
     * 访谈id
     */
    private String interviewId;

    /**
     * 访谈内容
     */
    private String interviewContent;

    /**
     * 客户经理
     */
    private String managerName;

    /**
     * 客户经理OA
     */
    private String managerOa;

    /**
     * 公司领导
     */
    private String compLeader;

    /**
     * 公司领导OA
     */
    private String compLeaderOa;

    /**
     * 数据插入时间
     */
    private String dayId;

    /**
     * 客户经理电话
     */
    private String managerTelphone;
}
