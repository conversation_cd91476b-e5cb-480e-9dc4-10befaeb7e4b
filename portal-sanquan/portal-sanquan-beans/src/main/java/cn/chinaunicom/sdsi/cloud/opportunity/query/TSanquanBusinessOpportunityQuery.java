package cn.chinaunicom.sdsi.cloud.opportunity.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商机编号表查询条件
 *
 * <AUTHOR>
 * @since  2024-05-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "商机编号表查询")
public class TSanquanBusinessOpportunityQuery  extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户工号")
    private String creatorLogin;

    @Schema(description = "商机名称")
    private String oppoName;

    @Schema(description = "商机编号")
    private String oppoNumber;

    @Schema(description = "类型：0行业经理，1客户经理")
    private String status;

}
