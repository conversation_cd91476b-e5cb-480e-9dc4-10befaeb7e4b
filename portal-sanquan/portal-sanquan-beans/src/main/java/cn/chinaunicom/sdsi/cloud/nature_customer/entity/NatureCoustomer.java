package cn.chinaunicom.sdsi.cloud.nature_customer.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 摸排信息表
 *
 * <AUTHOR>
 * @since 2024-04-25
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_sanquan_d_roster_nature_customer_info")
public class NatureCoustomer implements Serializable {
    /**
     * 摸排表id
     */
    @Schema(description = "名单制客户ID")
    @TableId
    private String rosterCustomerId;
    @Schema(description = "名单客户名称")
    private String rosterCustomerName;
    @Schema(description = "核心要客")
    private String keyCoreCustomer;
    @Schema(description = "名单客户等级")
    private String listCustomerLevel;
    @Schema(description = "名单客户省分")
    private String listCustomerProvince;
    @Schema(description = "名单客户地市")
    private String listCustomerCity;
    @Schema(description = "名单客户区县")
    private String listCustomerDistrict;
    @Schema(description = "名单客户网格名称")
    private String listCustomerGridName;
    @Schema(description = "总部行业")
    private String headquartersIndustry;
    @Schema(description = "省分行业")
    private String provinceIndustry;
    @Schema(description = "首席客户经理A角")
    private String chiefCustomerManagerA;
    @Schema(description = "名单客户名称")
    private String chiefCustomerManagerAOaId;
    @Schema(description = "首席客户经理A角手机号")
    private String chiefCustomerManagerAMobile;
    @Schema(description = "服务客户经理B角")
    private String serviceCustomerManagerB;
    @Schema(description = "服务客户经理B角OA工号")
    private String serviceCustomerManagerBOaId;
    @Schema(description = "首席客户经理B角手机号")
    private String chiefCustomerManagerBMobile;
    @Schema(description = "挂帅领导")
    private String leadExecutive;
    @Schema(description = "挂帅领导OA工号")
    private String leadExecutiveOaId;
    @Schema(description = "挂帅领导级别")
    private String leadExecutiveLevel;
    @Schema(description = "数科接口人")
    private String dataScienceContact;
    @Schema(description = "数科接口人OA工号")
    private String dataScienceContactOaId;
    @Schema(description = "自然客户名称")
    private String naturalCustomerName;
    @Schema(description = "自然客户ID")
    private String naturalCustomerId;
    @Schema(description = "统一社会信用代码")
    private String unifiedSocialCreditCode;
    @Schema(description = "组织机构代码")
    private String organizationCode;
    @Schema(description = "归集关系维护工号")
    private String collectionRelationshipMaintenanceNo;
    @Schema(description = "国民经济行业")
    private String nationalEconomicIndustry;
    @Schema(description = "总部两级行业")
    private String headquartersTwoLevelIndustry;
    @Schema(description = "集团处室")
    private String groupOffice;
    @Schema(description = "是否有组织关系")
    private String hasOrganizationalRelationship;
    @Schema(description = "客户层级")
    private String customerLevel;
    @Schema(description = "在网情况")
    private String networkStatus;
    @Schema(description = "自然客户是否有客户经理")
    private String naturalCustomerHasManager;
    @Schema(description = "客户经理姓名")
    private String customerManagerName;
    @Schema(description = "客户经理工号")
    private String customerManagerId;
    @Schema(description = "客情录入总数量")
    private String totalCustomerInformationEntries;
    @Schema(description = "关键人数量")
    private String keyPersonCount;
    @Schema(description = "其中SVIP关键人数量")
    private String svipKeyPersonCount;
    @Schema(description = "拜访记录数量")
    private String visitRecordCount;
    @Schema(description = "员工数记录数量")
    private String employeeCountRecord;
    @Schema(description = "IT投资预算记录数量")
    private String itInvestmentBudgetRecordCount;
    @Schema(description = "市场份额数据量")
    private String marketShareDataVolume;
    @Schema(description = "异网业务信息数量")
    private String otherNetworkBusinessInfoCount;
    @Schema(description = "办公地址数量")
    private String officeAddressCount;
    @Schema(description = "战略合作信息数量")
    private String strategicCooperationInfoCount;
    @Schema(description = "客户规划录入量")
    private String customerPlanningEntries;
    @Schema(description = "关键决策流程录入量")
    private String keyDecisionProcessEntries;
    @Schema(description = "公司/组织编号")
    private String orgCode;
    @Schema(description = "统一信用代码")
    private String tydm;
    @Schema(description = "注册编码")
    private String enrollCode;
    @Schema(description = "地址")
    private String custAddress;
    @Schema(description = "法定代表人")
    private String legalRepresentative;
    @Schema(description = "注册资金（万元）")
    private String enrollFund;
    @Schema(description = "注册货币类型")
    private String enrollCurrency;
    @Schema(description = "公司/组织类型")
    private String orgType;
    @Schema(description = "经营地址")
    private String jydz;
    @Schema(description = "联系方式")
    private String telephone;
    @Schema(description = "null")
    private String eeCount;
    @Schema(description = "私有标志")
    private String privateFlag;
}
