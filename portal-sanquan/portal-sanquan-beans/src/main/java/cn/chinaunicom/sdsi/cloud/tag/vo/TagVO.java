package cn.chinaunicom.sdsi.cloud.tag.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.Date;


@Data
@Schema(description = "标签管理VO")
public class TagVO implements Serializable {

    @Schema(description = "主键")
    private String id;

    @Schema(description = "标签名字")
    private String tagName;

    @Schema(description = "标签编码")
    private String tagCode;

    @Schema(description = "标签类型:数值/字典")
    private String tagType;

    @Schema(description = "类型值/字典类")
    private String tagTypeValue;

    @Schema(description = "是否来自数据库")
    private String tagSource;

    @Schema(description = "标签数值")
    private String tagNumValue;

    @Schema(description = "账期")
    private String acctDate;

    @Schema(description = "账期类型")
    private String acctDateType;

    @Schema(description = "标签联系人电话")
    private String tagUserPhone;

    @Schema(description = "标签联系人")
    private String tagUser;

    @Schema(description = "数据库表名")
    private String dbTableName;

    @Schema(description = "数据库表字段值说明")
    private String dbTableColumnDesc;

    @Schema(description = "数据库表字段")
    private String dbTableColumn;

    @Schema(description = "数据库表字段说明")
    private String dbTableDesc;


    @Schema(description = "业务口径")
    private String businessCaliber;

    @Schema(description = "加工口径")
    private String dataCaliber;

    @Schema(description = "状态")
    private String status = "0";
    @Schema(description = "审核时间")
    private String checkTime;
    @Schema(description = "审核内容")
    private String checkContent;
    @Schema(description = "审核人")
    private String checkUser;
    @Schema(description = "平均使用量")
    private String everyDayUseCount = "1";
    @Schema(description = "90天使用量")
    private String ninetyUseCount = "1";
    @Schema(description = "是否多选字典值")
    private String moreDictValue = "0";
    @Schema(description = "标签分类id")
    private String tagTypeId;
    @Schema(description = "标签分类名字")
    private String tagTypeName;
    @Schema(description = "存活分钟数")
    private int diffMin;

    @Schema(description = "匹配类型")
    private String dbMatchType;

    /**
     * 创建时间
     */
    @Schema(name="创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;
    /**
     * 编辑时间
     */
    @Schema(name="编辑时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateDate;

    @Schema(name="标签code")
    private String code;

    @Schema(description = "采集类型（客户类型1、产品类型2）")
    private String collectionType;
}
