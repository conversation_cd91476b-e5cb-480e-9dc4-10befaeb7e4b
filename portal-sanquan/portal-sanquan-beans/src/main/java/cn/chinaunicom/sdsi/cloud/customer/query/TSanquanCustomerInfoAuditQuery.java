package cn.chinaunicom.sdsi.cloud.customer.query;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 客户资料审核
 * 
 * <AUTHOR>
 * @date 2024-05-21
 */
@Data
@Schema(description = "客户资料审核对象")
public class TSanquanCustomerInfoAuditQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "审核表id")
    private String id;

    @Schema(description = "客户id（客户资料id）")
    private String customerId;

    @Schema(description = "是否通过（0-待审核，1-审核通过，2-审核不通过）")
    private String isPassed;

    @Schema(description = "审核原因")
    private String reason;

    @Schema(description = "备用字段1")
    private String attr1;

    @Schema(description = "备用字段2")
    private String attr2;

    @Schema(description = "备用字段3")
    private String attr3;

}
