package cn.chinaunicom.sdsi.cloud.customer.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 客户-项目或订单关系表（已选）
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Data
@Schema(description = "客户-项目或订单关系表（已选）")
public class TSanquanCustomerProjectOrderRelationsSelectedVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "项目或订单ID")
	private String projectOrderId;

	@Schema(description = "客户id（已选）")
	private String customerIdSelected;

	@Schema(description = "客户名称（已选）")
	private String customerNameSelected;

	@Schema(description = "项目或订单名称")
	private String projectOrderName;

	@Schema(description = "类型（项目或订单）")
	private String type;

	@Schema(description = "项目金额")
	private String projectAmount;

	@Schema(description = "自然客户ID")
	private String natureCustId;

	@Schema(description = "自然客户名称")
	private String natureCustName;

	@Schema(description = "业务类型")
	private String businessType;

	@Schema(description = "业务名称")
	private String businessName;

	@Schema(description = "名单制客户ID")
	private String rosterCustomerId;

	@Schema(description = "名单制客户名称")
	private String rosterCustomerName;

	@Schema(description = "单位名称")
	private String companyName;

	@Schema(description = "单位ID")
	private String companyId;

	@Schema(description = "预留字段1")
	private String reservedField1;

	@Schema(description = "预留字段2")
	private String reservedField2;

	@Schema(description = "预留字段3")
	private String reservedField3;

	@Schema(description = "预留字段4")
	private String reservedField4;

	@Schema(description = "认领时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date claimDate;

	@Schema(description = "认领人")
	private String claimBy;

}