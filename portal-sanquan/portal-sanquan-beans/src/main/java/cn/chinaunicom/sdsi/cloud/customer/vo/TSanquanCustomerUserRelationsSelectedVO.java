package cn.chinaunicom.sdsi.cloud.customer.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 客户-人员信息关系表（已选）
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Data
@Schema(description = "客户-人员信息关系表（已选）")
public class TSanquanCustomerUserRelationsSelectedVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "用户ID")
	private String userId;

	@Schema(description = "用户手机号")
	private String userPhone;

	@Schema(description = "用户账号")
	private String userAccount;

	@Schema(description = "客户id（已选）")
	private String customerIdSelected;

	@Schema(description = "客户名称（已选）")
	private String customerNameSelected;

	@Schema(description = "扣费账号")
	private String billingAccount;

	@Schema(description = "自然客户ID")
	private String naturalCustomerId;

	@Schema(description = "自然客户名称")
	private String naturalCustomerName;

	@Schema(description = "业务类型")
	private String businessType;

	@Schema(description = "业务名称")
	private String businessName;

	@Schema(description = "名单制客户ID")
	private String rosterCustomerId;

	@Schema(description = "名单制客户名称")
	private String rosterCustomerName;

	@Schema(description = "单位名称(派出所)")
	private String companyName;

	@Schema(description = "单位ID")
	private String companyId;

	@Schema(description = "预留字段1")
	private String reservedField1;

	@Schema(description = "预留字段2")
	private String reservedField2;

	@Schema(description = "预留字段3")
	private String reservedField3;

	@Schema(description = "预留字段4")
	private String reservedField4;

	@Schema(description = "认领时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date claimDate;

	@Schema(description = "认领人")
	private String claimBy;

}