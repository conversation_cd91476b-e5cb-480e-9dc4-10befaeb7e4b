package cn.chinaunicom.sdsi.cloud.tag.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "反馈指派记录表")
public class FeelbackAssignTagQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private String id;

    @Schema(description = "反馈表id(预留)")
    private String feelbackId;

    @Schema(description = "潜在计划id")
    private String opportunityId;

    @Schema(description = "新的计划id")
    private String toOpportunityId;

    @Schema(description = "指派人id")
    private String assignUserId;

    @Schema(description = "指派人姓名")
    private String assignUserName;

    @Schema(description = "被指派人id")
    private String byAssignUserId;

    @Schema(description = "被指派人姓名")
    private String byAssignUserName;

    @Schema(description = "指派原因")
    private String assignSource;

    @Schema(description = "入库时间")
    private Date storageDate;

    @Schema(name = "待办code")
    private String todoCode;

    @Schema(name = "工单待办表Id")
    private String todoOppoId;

    @Schema(name = "推送类型")
    private String pushType;



    public String getStartDate() {
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate thirtyDaysAgo = today.minusDays(30); // 当前日期减去30天
        // 输出结果
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return thirtyDaysAgo.format(formatter);
    }
}
