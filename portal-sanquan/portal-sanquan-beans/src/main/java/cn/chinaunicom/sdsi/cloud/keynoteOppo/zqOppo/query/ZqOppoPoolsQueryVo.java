package cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 政企中台商机
 * </p>
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class ZqOppoPoolsQueryVo extends BaseQueryVO {


    /**
     * 商机编号
     */
    private String oppoNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 自然客户id
     */
    private String natureCustId;

    /**
     * 自然客户名称
     */
    private String natureCustName;

    /**
     * 地市
     */
    private String city;

    /**
     * 省分行业
     */
    private String industry;

    /**
     * 名单制客户经理
     */
    private String managerName;

    /**
     * 名单制客户经理OA
     */
    private String managerOa;

    /**
     * 名单制客户经理电话
     */
    private String managerPhone;

    /**
     * 预计合同总金额(万元）
     */
    private BigDecimal contractAmount;

    /**
     * 客户需求简介
     */
    private String khxqjj;

    /**
     * 客户是否明确预算
     */
    private String khsfmqys;

    /**
     * 数据插入时间
     */
    private String dayId;

    // 0:未处理 1：已处理)
    private String status;
    /**
     * 运营-流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusYY;
    /**
     * 集成交付-流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusJC;
    /**
     * 产研-流程状态（0：未处理 1：已处理 2：退回）
     */
    private String statusCY;

    /* 是否有效商机，1是，0否 */
    private String sfyxOppo;



}
