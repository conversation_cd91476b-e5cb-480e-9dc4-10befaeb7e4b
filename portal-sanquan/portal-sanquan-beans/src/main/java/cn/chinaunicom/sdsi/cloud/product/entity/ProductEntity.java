package cn.chinaunicom.sdsi.cloud.product.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 *
 *
 * <AUTHOR> 
 * @since 2024-04-11
 */
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_product")
public class ProductEntity extends BaseEntity {
	/**
	 *
	 */
	@TableId
	private String id;
	/**
	 * 产品名称
	 */
	private String name;
	/**
	 * 产品类型
	 */
	private String type;
	/**
	 * 省分行业
	 */
	private String provincialBranch;
	/**
	 * 产品输出公司
	 */
	private String exportCompany;
	/**
	 * 业务分类
	 */
	private String businessClassification;
	/**
	 * 细分领域
	 */
	private String subdivision;
	/**
	 * 业务大类
	 */
	private String businessCategory;
	/**
	 * 产品介绍
	 */
	private String presentation;
	/**
	 * 客户描述（目标客户）
	 */
	private String customerDescription;
	/**
	 * 业务场景
	 */
	private String businessScenario;
	/**
	 * 附件
	 */
	private String attachment;
	/**
	 * 案例
	 */
	private String example;
	/**
	 * 产品联系人
	 */
	private String contact;
	/**
	 * 产品支撑人
	 */
	private String supporter;
	/**
	 * 区域支撑经理
	 */
	private String regionalSupport;
	/**
	 * 地市支撑经理
	 */
	private String prefecturalSupport;
	/**
	 * 是否打标
	 */
	private String isMaking;
	/**
	 * 赋能连接
	 */
	private String empowerLink;
	/**
	 * 备用字段1
	 */
	private String attr1;
	/**
	 * 备用字段2
	 */
	private String attr2;

	/**
	 * 下发时间设置（1代表一个月）
	 */
	private String issueMonth;

	/**
	 * 产品线
	 */
	private String productLine;

	/**
	 * 产品类
	 */
	private String productCategory;

	/**
	 * 产品系列
	 */
	private String productSeries;

	/**
	 * 类型
	 */
	private String productType;

	/**
	 * 地市
	 */
	private String city;

	/**
	 * 是否行业主推
	 */
	private String isIndustryMain;

	/**
	 * 是否标杆产品
	 */
	private String isBenchmark;

}