package cn.chinaunicom.sdsi.cloud.customer.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
* IDC需求摸查-客户资料
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Data
@Schema(description = "IDC需求摸查-客户资料")
public class TSanquanCustomerIdcQuery extends BaseQueryVO {
	private static final long serialVersionUID = 1L;

	@Schema(description = "IDC需求摸查id")
	private String idcId;

	@Schema(description = "客户资料id")
	private String customerId;

	@Schema(description = "客户名称")
	private String customerName;

	@Schema(description = "名单客户名称")
	private String rosterCustomerName;

	@Schema(description = "名单制客户ID")
	private String rosterCustomerId;

	@Schema(description = "主机房情况")
	private String primaryMachineSituation;

	@Schema(description = "灾备机房情况")
	private String disasterPreparednessSituation;

	@Schema(description = "备灾系统")
	private String loadSystem;

	@Schema(description = "是否有搬迁需求")
	private String isRelocation;

	@Schema(description = "是否有扩容需求")
	private String isExpansion;

	@Schema(description = "是否有算力需求")
	private String isHashrate;

	@Schema(description = "是否有VDC需求")
	private String isVdc;

	@Schema(description = "支撑人员")
	private String supportStaff;

	@Schema(description = "备用字段1")
	private String attr1;

	@Schema(description = "备用字段2")
	private String attr2;

	@Schema(description = "备用字段3")
	private String attr3;

}