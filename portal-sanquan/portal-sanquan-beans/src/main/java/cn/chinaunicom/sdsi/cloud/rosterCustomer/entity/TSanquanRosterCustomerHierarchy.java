package cn.chinaunicom.sdsi.cloud.rosterCustomer.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 名单制客户层级结构表
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_roster_customer_hierarchy")
public class TSanquanRosterCustomerHierarchy implements Serializable {
	/**
	* 名单制客户ID
	*/
	@TableId
	private String rosterCustomerId;

	/**
	* 名单制客户名称
	*/
	private String rosterCustomerName;

	/**
	* 父级名单制客户ID
	*/
	private String parentCustId;

	/**
	* 父级名单制客户名称
	*/
	private String parentCustName;

	/**
	* 层级
	*/
	private String levels;

	/**
	* 是否是核心要客：1：是 0：否
	*/
	private String majorFlag;

	/**
	 * 主责部门
	 */
	private String majorDepartment;

	/**
	* 省份代码
	*/
	private String provinceCode;

	/**
	* 地市代码
	*/
	private String cityCode;

	/**
	* 区县代码
	*/
	private String districtCode;

	/**
	 * 状态0失效1生效
	 */
	private String state;

	/**
	 * 变更时间
	 */
	private String stateDate;

	/**
	 * 总部所在地
	 */
	private String headquarters;

	/**
	 * 行业ID
	 */
	private String tradeClusterId;

}