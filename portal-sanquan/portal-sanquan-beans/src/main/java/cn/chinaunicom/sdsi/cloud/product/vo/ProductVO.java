package cn.chinaunicom.sdsi.cloud.product.vo;

import cn.chinaunicom.sdsi.cloud.product.entity.ProductUseScene;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductOther;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: ProductVO
 * @Author: dzk
 * @Create: 2024-04-11
 **/

@Data
public class ProductVO implements Serializable {

    /**
     * 产品id
     */
    private String id;

    /**
     * 产品名称
     */
    @NotBlank(message = "产品名称不能为空")
    private String name;
    /**
     * 产品类型
     */
    @NotBlank(message = "产品模式不能为空")
    private String type;
    /**
     * 省分行业
     */
    private String provincialBranch;
    /**
     * 产品输出公司
     */
    private String exportCompany;
    /**
     * 业务分类
     */
    private String businessClassification;
    /**
     * 细分领域
     */
    private String subdivision;
    /**
     * 业务大类
     */
    private String businessCategory;
    /**
     * 产品介绍
     */
    @NotBlank(message = "产品介绍不能为空")
    private String presentation;
    /**
     * 客户描述（目标客户）
     */
    private String customerDescription;
    /**
     * 业务场景
     */
    private String businessScenario;
    /**
     * 附件
     */
    private String[] attachment;
    /**
     * 案例
     */
    private String[] example;
    /**
     * 产品联系人
     */
    private String contact;
    /**
     * 产品支撑人
     */
    private String supporter;
    /**
     * 区域支撑经理
     */
    private String regionalSupport;
    /**
     * 地市支撑经理
     */
    private String prefecturalSupport;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 编辑时间
     */
    private Date updateDate;
    /**
     * 编辑人
     */
    private Date updateBy;
    /**
     * 逻辑删除
     */
    private String deleteFlag;
    /**
     *
     */
    private Integer version;


    /**
     * 赋能连接
     */
    private String empowerLink;
    /**
     * 备用字段1
     */
    private String attr1;
    /**
     * 备用字段2
     */
    private String attr2;
    /**
     * 下发时间设置（1代表一个月）
     */
    private String issueMonth;

    /**
     * 产品线
     */
    private String productLine;

    /**
     * 产品类
     */
    private String productCategory;

    /**
     * 产品系列
     */
    private String productSeries;

    /**
     * 类型
     */
    private String productType;

    /**
     * 怎么讲解
     */
    private List<TSanquanProductOther> explainList;

    /**
     * 怎么卖
     */
    private List<TSanquanProductOther> sellList;

    /**
     * 产品功能
     */
    private List<TSanquanProductOther> productFeaturesList;

    /**
     * 产品优势
     */
    private List<TSanquanProductOther> productAdvantagesList;

    /**
     * 常见问题
     */
    private List<TSanquanProductOther> commonProblemsList;

    /**
     * 产品使用场景
     */
    private List<ProductUseScene> useSceneList;

    /**
     * 地市
     */
    private String city;

    /**
     * 是否行业主推
     */
    private String isIndustryMain;

    /**
     * 是否标杆产品
     */
    private String isBenchmark;
}
