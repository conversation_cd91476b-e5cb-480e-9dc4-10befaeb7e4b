package cn.chinaunicom.sdsi.cloud.strategy.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 策略配置地市表对象
 * @Author: hanruxiao
 * @Date: 2024-05-26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "策略配置地市表对象", description = "策略配置地市表对象")
@TableName("t_sanquan_strategy_config_city")
public class TSanquanStrategyConfigCity implements Serializable {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "")
    private String strategyConfigId;

    @Schema(name = "地市编码")
    private String cityCode;

    @Schema(name = "地市名称")
    private String cityName;


}
