package cn.chinaunicom.sdsi.cloud.product.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 字典表（下拉框数据）
 * </p>
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@TableName("t_sanquan_dictionary")
public class TSanquanDictionary implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private String id;

    /**
     * 字典编码
     */
    @TableField("`code`")
    private String code;

    /**
     * 类型
     */
    @TableField("`type`")
    private String type;

    /**
     * 排序
     */
    @TableField("`sort`")
    private Integer sort;

    /**
     * 字典名称
     */
    @TableField("`name`")
    private String name;

    /**
     * 字典值
     */
    @TableField("`value`")
    private String value;

    /**
     * 描述
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 创建时间
     */
    private Date createDate;

}
