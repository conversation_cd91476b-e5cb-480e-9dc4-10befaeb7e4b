package cn.chinaunicom.sdsi.cloud.workFeedbackLog.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 工单记录表
 *
 * <AUTHOR> 
 * @since  2024-07-22
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_work_feedback_log")
public class TSanquanWorkFeedbackLog implements Serializable {
	/**
	* 工单记录表id
	*/
	@TableId
	private String id;

	/**
	* 潜在机会id
	*/
	private String opportunityId;

	/**
	* 工单反馈id
	*/
	private String workFeedbackId;

	/**
	* 操作
	*/
	private String operation;

	/**
	* 创建时间
	*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createDate;

	/**
	* 创建人
	*/
	private String createBy;

	/**
	* 创建人名字
	*/
	private String createByName;

	/**
	 * 处理人（待处理人，推送给谁、短信发送人）
	 */
	private String toByName;

	/**
	 * 处理人工号
	 */
	private String toById;

	/**
	 * 操作内容（短信内容或者其他内容）
	 */
	private String operationContent;

	/**
	 * 操作类型（0、普通操作，1、预警督办操作）
	 */
	private String operationType;

	/**
	 * 短信发送人手机号
	 */
	private String toByPhone;

	/**
	 * 待办code
	 */
	private String todoCode;

	/**
	 * 工单待办表Id
	 */
	private String todoOppoId;

	/**
	 * 推送类型
	 */
	private String pushType;

}