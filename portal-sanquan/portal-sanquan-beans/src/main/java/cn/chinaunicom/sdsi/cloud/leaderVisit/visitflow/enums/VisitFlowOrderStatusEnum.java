package cn.chinaunicom.sdsi.cloud.leaderVisit.visitflow.enums;

/**
 * 拜访流程工单状态枚举
 */
public enum VisitFlowOrderStatusEnum {
    
    /**
     * 执行中/处理中
     */
    PROCESSING(1, "执行中"),
    
    /**
     * 已完成/已竣工
     */
    COMPLETED(3, "已完成"),
    
    /**
     * 已撤单
     */
    CANCELLED(900, "已撤单"),
    
    /**
     * 已撤回
     */
    WITHDRAWN(901, "已撤回");
    
    private final Integer code;
    private final String desc;
    
    VisitFlowOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDesc() {
        return desc;
    }
    
    /**
     * 根据状态码获取枚举
     * @param code 状态码
     * @return 对应的枚举值
     */
    public static VisitFlowOrderStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (VisitFlowOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
