package cn.chinaunicom.sdsi.cloud.customer.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 算网数智-异网情况
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_customer_arithmetic_intelligence")
public class TSanquanCustomerArithmeticIntelligence extends BaseEntity {
	/**
	* 算网数智id
	*/
	@TableId
	private String id;

	/**
	* 客户id
	*/
	private String customerId;

	/**
	 * 客户名称
	 */
	private String customerName;

	/**
	 * 名单客户名称
	 */
	private String rosterCustomerName;

	/**
	 * 名单制客户ID
	 */
	private String rosterCustomerId;

	/**
	* 序号
	*/
	private String sort;

	/**
	* 项目名称
	*/
	private String projectsName;

	/**
	* 承建方名称
	*/
	private String contractorName;

	/**
	* 到期时间
	*/
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date expireDate;

	/**
	* 备用字段1
	*/
	private String attr1;

	/**
	* 备用字段2
	*/
	private String attr2;

	/**
	* 备用字段3
	*/
	private String attr3;

	/**
	 * 业务类型
	 */
	private String businessType;

	/**
	 * 业务名称
	 */
	private String businessName;

	/**
	 * 运营商
	 */
	private String operators;

	/**
	 * 非运营商（自定义）
	 */
	private String custom;
}