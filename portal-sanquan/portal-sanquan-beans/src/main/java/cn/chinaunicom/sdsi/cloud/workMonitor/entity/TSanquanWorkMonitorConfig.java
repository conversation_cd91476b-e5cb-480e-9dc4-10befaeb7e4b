package cn.chinaunicom.sdsi.cloud.workMonitor.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工单监控规则配置
 *
 * <AUTHOR> 
 * @since  2024-08-12
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_sanquan_work_monitor_config")
public class TSanquanWorkMonitorConfig extends BaseEntity {
	/**
	* 工单监控规则配置id
	*/
	@TableId
	private String id;

	/**
	* 规则名称
	*/
	private String ruleName;

	/**
	 * 规则编码
	 */
	private String ruleCode;

	/**
	* 地市
	*/
	private String city;

	/**
	* 督办人（督办人id）
	*/
	private String supervisor;

	/**
	 * 督办人名称
	 */
	private String supervisorName;

	/**
	* 行业名称
	*/
	private String industry;

	/**
	* 工单状态
	*/
	private String workStatus;

	/**
	* 预警天数
	*/
	private Long warningDays;

	/**
	* 催办天数
	*/
	private Long urgeProcessingDays;

	/**
	* 督办天数
	*/
	private Long supervisionDays;

	/**
	 * 预警短信内容模板
	 */
	private String warningContent;

	/**
	 * 催办短信内容模板
	 */
	private String urgeProcessingContent;

	/**
	 * 督办短信内容模板
	 */
	private String supervisionContent;

	/**
	 * 是否预警
	 */
	private String isWarning;

	/**
	 * 是否催办
	 */
	private String isUrgeProcessing;

	/**
	 * 是否督办
	 */
	private String isSupervision;

	/**
	 * 是否督办
	 */
	private String customerType;
}