package cn.chinaunicom.sdsi.cloud.ls.query;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 6数统计表
 *
 * <AUTHOR>
 * @since 2024-04-25
 */
@Data
@Schema(description = "TOP30（集团+省内）六数统计表查询")
public class LsQuery extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    @Schema(description = "客户名称")
    private String custName;

    @Schema(description = "月")
    private String monthId;

}
