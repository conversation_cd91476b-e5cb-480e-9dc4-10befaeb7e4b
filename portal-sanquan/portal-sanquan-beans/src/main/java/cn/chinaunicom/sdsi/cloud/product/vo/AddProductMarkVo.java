package cn.chinaunicom.sdsi.cloud.product.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/4 16:09
 */
@Data
@Schema(description = "产品打标Vo")
public class AddProductMarkVo implements Serializable {
    private String id;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 标签类型
     */
    private String markType;

    /**
     * 标签类型id
     */
    private String markTypeId;

    /**
     * 标签类型值
     */
    private String markData;

    /**
     * 标签类型值id
     */
    private String markDataId;

    /**
     * 其他
     */
    private String other;

    /**
     * 对应大宽表中的列
     */
    private String bigColumn;

    /**
     * 标签类型
     */
    private String tagType;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;

    /**
     * 编辑人
     */
    private String updateBy;

    /**
     * 编辑时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateDate;

    /**
     * 大宽表的名称
     */
    private String dbTable;

    /**
     * 大宽表的列名
     */
    private String dbColumn;
}
