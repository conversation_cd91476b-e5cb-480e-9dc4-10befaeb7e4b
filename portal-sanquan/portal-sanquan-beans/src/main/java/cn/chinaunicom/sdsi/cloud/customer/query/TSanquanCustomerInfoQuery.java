package cn.chinaunicom.sdsi.cloud.customer.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
* 客户资料-客户经理
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Data
@Schema(description = "客户资料-客户经理")
public class TSanquanCustomerInfoQuery extends BaseQueryVO {
	private static final long serialVersionUID = 1L;

	@Schema(description = "客户id")
	private String customerId;

	@Schema(description = "客户名称")
	private String customerName;

	@Schema(description = "客户代码")
	private String customerCode;

	@Schema(description = "上级单位名称")
	private String superUnitName;

	@Schema(description = "所属行业")
	private String industry;

	@Schema(description = "细分领域")
	private String subdivision;

	@Schema(description = "客户等级")
	private String listCustomerLevel;

	@Schema(description = "客户省分")
	private String listCustomerProvince;

	@Schema(description = "所在地市")
	private String listCustomerCity;

	@Schema(description = "所在区县")
	private String listCustomerDistrict;

	@Schema(description = "联系人电话")
	private String relationTel;

	@Schema(description = "联系人名称")
	private String relationName;

	@Schema(description = "名单客户名称")
	private String rosterCustomerName;

	@Schema(description = "名单制客户ID")
	private String rosterCustomerId;

	@Schema(description = "人员规模")
	private String staffSize;

	@Schema(description = "客户经理名称")
	private String customerManagerName;

	@Schema(description = "客户经理电话")
	private String customerManagerTel;

	@Schema(description = "客户经理工号")
	private String customerManagerId;

	@Schema(description = "客户经理邮箱")
	private String customerManagerEmail;

	@Schema(description = "国家行业分类")
	private String nationalIndustry;

	@Schema(description = "国家行业分类code")
	private String nationalIndustryCode;

	@Schema(description = "二级行业")
	private String secondaryIndustry;

	@Schema(description = "二级行业code")
	private String secondaryIndustryCode;

	@Schema(description = "政企行业")
	private String enterpriseIndustry;

	@Schema(description = "政企行业code")
	private String enterpriseIndustryCode;

	@Schema(description = "业务认领-联网通信")
	private String businessClaimNetworkedCommunication;

	@Schema(description = "业务认领-算网数智")
	private String businessClaimArithmeticIntelligence;

	@Schema(description = "异网情况-联网通信")
	private String differentNetworkNetworkedCommunication;

	@Schema(description = "异网情况-算网数智")
	private String differentNetworkArithmeticIntelligence;

	@Schema(description = "idc需求摸查")
	private String idcDemandTest;

	@Schema(description = "备用字段1")
	private String attr1;

	@Schema(description = "备用字段2")
	private String attr2;

	@Schema(description = "备用字段3")
	private String attr3;

	@Schema(description = "联系地址")
	private String contactAddress;

	@Schema(description = "联系电话")
	private String contactTel;

	@Schema(description = "传真")
	private String fax;

	@Schema(description = "网站")
	private String website;

	@Schema(description = "邮箱")
	private String email;

	@Schema(description = "其他联系方式")
	private String otherContact;

	@Schema(description = "客户介绍")
	private String customerIntroduction;

	@Schema(description = "重大事件")
	private String majorEvent;

	@Schema(description = "状态（0暂存，1提交）")
	private String status;

	@Schema(description = "是否通过（0-待审核，1-审核通过，2-审核不通过）")
	private String isPassed;

	@Schema(description = "名单制客户id集合")
	private List<String> rosterCustomerIdList;

	@Schema(description = "状态集合")
	private List<String> IsPassedList;

	@Schema(description = "行业集合")
	private List<String> industryList;

	@Schema(description = "区县集合")
	private List<String> districtList;

	@Schema(description = "地市审核")
	private String cityRole;
}