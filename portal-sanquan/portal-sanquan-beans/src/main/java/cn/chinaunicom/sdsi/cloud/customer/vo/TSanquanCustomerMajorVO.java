package cn.chinaunicom.sdsi.cloud.customer.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
* 重大事件-客户资料
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Data
@Schema(description = "重大事件-客户资料")
public class TSanquanCustomerMajorVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@Schema(description = "重大事件id")
	private String id;

	@Schema(description = "重大事件名称")
	private String name;

	@Schema(description = "客户id")
	private String customerId;

	@Schema(description = "客户名称")
	private String customerName;

	@Schema(description = "名单客户名称")
	private String rosterCustomerName;

	@Schema(description = "名单制客户ID")
	private String rosterCustomerId;

	@Schema(description = "重大事件日期")
	@JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date date;

	@Schema(description = "重大事件描述")
	private String majorDesc;

	@Schema(description = "备用字段1")
	private String attr1;

	@Schema(description = "备用字段2")
	private String attr2;

	@Schema(description = "备用字段3")
	private String attr3;

}