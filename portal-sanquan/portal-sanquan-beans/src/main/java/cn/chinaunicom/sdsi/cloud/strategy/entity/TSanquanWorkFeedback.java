package cn.chinaunicom.sdsi.cloud.strategy.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工单反馈表（潜在机会推送）
 *
 * <AUTHOR>
 * @since  2024-04-16
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_sanquan_work_feedback")
public class TSanquanWorkFeedback extends BaseEntity {
	/**
	* 工单id
	*/
	@TableId(type = IdType.ASSIGN_ID)
	private String id;

	/**
	* 自然客户id
	*/
	private String customerId;

	/**
	* 商机id
	*/
	private String opportunityId;

	/**
	* 自然客户名称
	*/
	private String customerName;

	/**
	* 客户经理id
	*/
	private String customerManagerId;

	/**
	* 客户经理名称
	*/
	private String customerManager;

	/**
	* 产品id
	*/
	private String productId;

	/**
	* 产品名称
	*/
	private String productName;

	/**
	* 0执行中、1已查看(客户经理已读)、2关单 3待获取商机编号、4未读(客户经理未读)、-1待改派(已推送到地市接口人的)
	*/
	private Integer status;

	/**
	* 商机编号
	*/
	private String businessOpportunityId;

	/**
	* 账期
	*/
	private Date account;

	/**
	 * 反馈类型（已创建商机、客户无需求、客户有需求、客户需求已建成或在建、其他）
	 */
	private String feedbackType;

	/**
	* 反馈内容
	*/
	private String feedbackContent;

	/**
	* 反馈时间
	*/
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date feedbackDate;

	/**
	 * 承建方
	 */
	private String contractor;

	/**
	 * 承建时间
	 */
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
	private Date contractorDate;

	/**
	 * 异网类型（运营商/非运营商）
	 */
	private String newworkType;

	/**
	* 备用字段1 (备注信息)
	*/
	private String attr1;

	/**
	* 备用字段2
	*/
	private String attr2;

	/**
	 * 关单时间
	 */
	private Date closeTime;

	/**
	 * 项目规模（万元）
	 */
	private String projectScale;

	/**
	 * 商机情况
	 */
	private String businessOpportunities;

	/**
	 * 列表展示：地市
	 */
	@TableField(exist = false)
	private String cityCode;

	/**
	 * 匹配模式：产品选客户、客户选产品
	 */
	private String matchModel;

	/**
	 * 达产（0未达产、1达产）产品找客户的时候使用
	 */
	private String meetProduction;

	/**
	 * 营销方案（主推产品、主推+辅推产品）
	 */
	private String marketingPlan;

	/**
	 * 业务类型（未达产）
	 */
	private String businessType;

	/**
	 * 业务数量（未达产）
	 */
	private String businessNumber;

	/**
	 * 推送人
	 */
	private String pushPerson;

	/**
	 * 推送人工号
	 */
	private String pushPersonId;

	/**
	 * 处理人
	 */
	private String currentPerson;

	/**
	 * 处理人工号
	 */
	private String currentPersonId;

	/**
	 * 支撑人退回原因
	 */
	private String returnReason;

	/**
	 * 是否支撑（0、否，1、是）
	 */
	private String isBrace;

	/**
	 * 解决方案支撑人
	 */
	private String solutionSupporter;

	/**
	 * 支撑时间
	 */
	private Date supportTime;

	/**
	 * 支撑形式
	 */
	private String supportForms;

	/**
	 * 补充说明
	 */
	private String supplementaryExplanation;

	/**
	 * 待办code
	 */
	private String todoCode;

	/**
	 * 工单待办表Id
	 */
	private String todoOppoId;

	/**
	 * 推送类型
	 */
	private String pushType;

	/**
	 * 软件规模
	 */
	private String softwareScale;

	/**
	 * 毛利润
	 */
	private String grossProfit;

}
