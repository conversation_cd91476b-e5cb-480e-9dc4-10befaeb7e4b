package cn.chinaunicom.sdsi.cloud.keynoteOppo.productSelf.vo;

import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class OppoProductSelfVo implements Serializable {


    private String id;

    /**
     * 一级目录
     */
    private String oneCatalog;

    /**
     * 二级目录
     */
    private String twoCatalog;

    /**
     * 三级目录
     */
    private String threeCatalog;

    /**
     * 四级目录
     */
    private String fourCatalog;

    /**
     * 主产品
     */
    private String mainProduct;

    /**
     * 细分产品
     */
    private String subdivisionProduct;

    /**
     * 产品简介
     */
    private String synopsis;

    /**
     * 产品分类
     */
    private String classification;

    private String income;

    /**
     * 关注产品
     */
    private String followProduct;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 2025年新增产品
     */
    private String newProducts;

    /**
     * 产品成熟度
     */
    private String productMaturity;

    /**
     * 插入时间
     */
    private String createTime;


}
