package cn.chinaunicom.sdsi.cloud.customer.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户-人员信息关系表（已选）
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_customer_user_relations_selected")
public class TSanquanCustomerUserRelationsSelected implements Serializable {
	/**
	* 用户ID
	*/
	@TableId
	private String userId;

	/**
	* 用户手机号
	*/
	private String userPhone;

	/**
	* 用户账号
	*/
	private String userAccount;

	/**
	* 客户id（已选）
	*/
	private String customerIdSelected;

	/**
	 * 客户名称（已选）
	 */
	private String customerNameSelected;

	/**
	* 扣费账号
	*/
	private String billingAccount;

	/**
	* 自然客户ID
	*/
	private String naturalCustomerId;

	/**
	* 自然客户名称
	*/
	private String naturalCustomerName;

	/**
	* 业务类型
	*/
	private String businessType;

	/**
	* 业务名称
	*/
	private String businessName;

	/**
	* 名单制客户ID
	*/
	private String rosterCustomerId;

	/**
	* 名单制客户名称
	*/
	private String rosterCustomerName;

	/**
	* 单位名称(派出所)
	*/
	private String companyName;

	/**
	* 单位ID
	*/
	private String companyId;

	/**
	* 预留字段1
	*/
	private String reservedField1;

	/**
	* 预留字段2
	*/
	private String reservedField2;

	/**
	* 预留字段3
	*/
	private String reservedField3;

	/**
	* 预留字段4
	*/
	private String reservedField4;

	/**
	 * 认领时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date claimDate;

	/**
	 * 认领人
	 */
	private String claimBy;
}