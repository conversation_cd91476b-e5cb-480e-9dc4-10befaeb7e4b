package cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
@TableName("t_sanquan_leader_visit_cust")
public class LeaderVisitCust implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 拜访id
     */
    private String visitId;

    /**
     * 上级部门负责人姓名
     */
    private String parentDeptCharger;

    /**
     * 上级部门负责人oa账号
     */
    private String parentDeptChargerOa;

    /**
     * 公司领导名称
     */
    private String compLeader;

    /**
     * 公司领导oa账号
     */
    private String compLeaderOa;

    /**
     * 拜访状态（0未派单、1首次派单、2跟进、3关闭）
     */
    private String status;

    /**
     * 更新时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

    /**
     * 账期
     */
    private String dayId;


}
