package cn.chinaunicom.sdsi.cloud.strategy.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 产品到期预警
 *
 * <AUTHOR> 
 * @since  2024-05-28
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "产品到期预警表对象", description = "产品到期预警表对象")
@TableName("t_sanquan_maturity_warning")
public class TSanquanMaturityWarning extends BaseEntity {

	@TableId
	private String id;

	/**
	 * 潜在机会id
	 */
	private String opportunityId;

	/**
	* 运营商
	*/
	private String contractor;

	/**
	* 合同到期时间
	*/
	private Date contractorDate;

	/**
	* 下发时间
	*/
	private Date issueDate;

	/**
	* 名单制客户id
	*/
	private String rosterCustomerId;

	/**
	* 名单制客户名称
	*/
	private String rosterCustomerName;

	/**
	* 客户经理名称
	*/
	private String customerManager;

	/**
	* 客户经理id
	*/
	private String customerManagerId;

	/**
	* 0已推送、1未推送
	*/
	private String status;

	/**
	* 产品id
	*/
	private String productId;

	/**
	* 产品名称
	*/
	private String productName;

	/**
	* 备用字段1
	*/
	private String attr1;

	/**
	* 备用字段2
	*/
	private String attr2;

	/**
	* 备用字段3
	*/
	private String attr3;

	/**
	 * 策略结果id
	 */
	private String strategyResultId;


	/**
	 * 新策略结果id
	 */
	private String strategyResultIdNew;

	/**
	 * 所属行业
	 */
	private String industry;


	/**
	 * 预警信息id
	 */
	private String warningInfoId;
}