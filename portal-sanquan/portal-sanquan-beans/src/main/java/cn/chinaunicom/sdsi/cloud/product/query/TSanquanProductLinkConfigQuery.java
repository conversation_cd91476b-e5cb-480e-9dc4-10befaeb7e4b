package cn.chinaunicom.sdsi.cloud.product.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品链接配置表
 *
 * <AUTHOR> 
 * @since  2024-05-23
 */

@Data
public class TSanquanProductLinkConfigQuery extends BaseQueryVO {
	private static final long serialVersionUID = 1L;

	@Schema(description = "连接表id")
	private String id;

	@Schema(description = "产品id")
	private String productId;

	@Schema(description = "产品名称")
	private String productName;

	@Schema(description = "连接名称")
	private String linkName;

	@Schema(description = "连接地址")
	private String linkAddr;

	@Schema(description = "备用字段1")
	private String attr1;

	@Schema(description = "备用字段2")
	private String attr2;

	@Schema(description = "备用字段3")
	private String attr3;

	@Schema(description = "租户标识")
	private String tenantId;

	@Schema(description = "创建人")
	private String createBy;

	@Schema(description = "创建时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date createDate;

	@Schema(description = "编辑人")
	private String updateBy;

	@Schema(description = "编辑时间")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date updateDate;

	@Schema(description = "乐观锁")
	private String versions;

	@Schema(description = "逻辑删除，normal表示正常，deleted表示删除")
	private String deleteFlag = "normal";
}