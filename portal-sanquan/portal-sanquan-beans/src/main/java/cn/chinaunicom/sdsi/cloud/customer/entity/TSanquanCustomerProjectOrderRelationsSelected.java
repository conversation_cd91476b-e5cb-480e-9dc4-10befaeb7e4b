package cn.chinaunicom.sdsi.cloud.customer.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import nonapi.io.github.classgraph.json.Id;

import java.io.Serializable;
import java.util.Date;

/**
 * 客户-项目或订单关系表（已选）
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */

@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_customer_project_order_relations_selected")
public class TSanquanCustomerProjectOrderRelationsSelected implements Serializable {
	/**
	* 项目或订单ID
	*/
	@TableId
	private String projectOrderId;

	/**
	* 客户id（已选）
	*/
	private String customerIdSelected;

	/**
	 * 客户名称（已选）
	 */
	private String customerNameSelected;

	/**
	* 项目或订单名称
	*/
	private String projectOrderName;

	/**
	* 类型（项目或订单）
	*/
	private String type;

	/**
	* 项目金额
	*/
	private String projectAmount;

	/**
	* 自然客户ID
	*/
	private String natureCustId;

	/**
	* 自然客户名称
	*/
	private String natureCustName;

	/**
	* 业务类型
	*/
	private String businessType;

	/**
	* 业务名称
	*/
	private String businessName;

	/**
	* 名单制客户ID
	*/
	private String rosterCustomerId;

	/**
	* 名单制客户名称
	*/
	private String rosterCustomerName;

	/**
	* 单位名称
	*/
	private String companyName;

	/**
	* 单位ID
	*/
	private String companyId;

	/**
	* 预留字段1
	*/
	private String reservedField1;

	/**
	* 预留字段2
	*/
	private String reservedField2;

	/**
	* 预留字段3
	*/
	private String reservedField3;

	/**
	* 预留字段4
	*/
	private String reservedField4;

	/**
	 * 认领时间
	 */
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private Date claimDate;

	/**
	 * 认领人
	 */
	private String claimBy;
}