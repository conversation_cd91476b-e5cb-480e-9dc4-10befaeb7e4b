package cn.chinaunicom.sdsi.cloud.ssoPath.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 * 单点登录地址
 * </p>
 * <AUTHOR>
 * @since 2024-09-29
 */
@Data
@TableName("t_sanquan_sso_path")
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor

//@ApiModel(value = "TSanquanSsoPath对象", description = "单点登录地址")
public class TSanquanSsoPath extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 单点名称
     */
    private String ssoName;

    /**
     * 类型（唯一）
     */
    private String ssoType;

    /**
     * pc访问路径
     */
    private String pcPath;

    /**
     * app访问路径
     */
    private String appPath;

    /**
     * 需要处理的参数
     */
    private String params;

    /**
     * 权限编码
     */
    private String roleCode;

    /**
     * 客户类型（1、客户经理，2、地市接口人，3、售前支撑人）
     */
    private String customerType;

}
