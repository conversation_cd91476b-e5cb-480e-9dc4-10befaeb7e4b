package cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 商机评审流程日志表
 * </p>
 * <AUTHOR>
 * @since 2025-06-26
 */
@Data
public class OppoReviewLogQueryVo extends BaseQueryVO {


    /**
     * 主键
     */
      private String id;

    /**
     * 商机评审id
     */
    private String reviewId;

    /**
     * 商机编号
     */
    private String oppoNumber;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 环节（0营销经理:1集成交付部 2运营支撑部3产品研发部）
     */
    private String node;

    /**
     * 填写人工号
     */
    private String createBy;

    /**
     * 填写时间
     */
    private String createTime;

    /**
     * 填写人姓名
     */
    private String createName;

    /**
     * 操作类型（0：通过 1：驳回）
     */
    private String opraType;

    /**
     * 退回意见
     */
    private String backRemark;


}
