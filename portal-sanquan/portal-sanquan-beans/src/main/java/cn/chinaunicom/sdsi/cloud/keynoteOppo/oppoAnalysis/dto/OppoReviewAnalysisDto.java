package cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商机评审分析DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public class OppoReviewAnalysisDto implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;
    /**
     * 数据插入时间
     */
    private String dayId;
    /**
     * 是否有效商机(是、否或已移出)（0、否，1、是）
     */
    private String oppoStatus;
    /**
     * 商机跟进建议(最终)
     */
    private String sjgjjyResult;

    public String getId() {
        return id;
    }

    public OppoReviewAnalysisDto setId(String id) {
        this.id = id;
        return this;
    }

    public String getDayId() {
        return dayId;
    }

    public OppoReviewAnalysisDto setDayId(String dayId) {
        this.dayId = dayId;
        return this;
    }

    public String getOppoStatus() {
        return oppoStatus;
    }

    public OppoReviewAnalysisDto setOppoStatus(String oppoStatus) {
        this.oppoStatus = oppoStatus;
        return this;
    }

    public String getSjgjjyResult() {
        return sjgjjyResult;
    }

    public OppoReviewAnalysisDto setSjgjjyResult(String sjgjjyResult) {
        this.sjgjjyResult = sjgjjyResult;
        return this;
    }
}
