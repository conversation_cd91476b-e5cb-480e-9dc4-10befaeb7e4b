package cn.chinaunicom.sdsi.cloud.tag.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "标签管理查询")
public class TagQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "摸排表id")
    private String id;

    @Schema(description = "标签名字")
    private String tagName;

    @Schema(description = "标签编码")
    private String tagCode;

    @Schema(description = "标签类型:数值/字典")
    private String tagType;

    @Schema(description = "类型值/字典类")
    private String tagTypeValue;

    @Schema(description = "是否来自数据库")
    private String tagSource;

    @Schema(description = "标签数值")
    private String tagNumValue;

    @Schema(description = "账期")
    private String acctDate;

    @Schema(description = "账期类型")
    private String acctDateType;
    @Schema(description = "标签联系人电话")
    private String tagUserPhone;

    @Schema(description = "标签联系人")
    private String tagUser;

    @Schema(description = "数据库表名")
    private String dbTableName;

    @Schema(description = "数据库表字段值说明")
    private String dbTableColumnDesc;

    @Schema(description = "数据库表字段")
    private String dbTableColumn;

    @Schema(description = "数据库表字段说明")
    private String dbTableDesc;


    @Schema(description = "业务口径")
    private String businessCaliber;

    @Schema(description = "加工口径")
    private String dataCaliber;

    @Schema(description = "状态")
    private String status = "0";
    @Schema(description = "审核时间")
    private String checkTime;
    @Schema(description = "审核内容")
    private String checkContent;
    @Schema(description = "审核人")
    private String checkUser;
    @Schema(description = "标签分类id")
    private String tagTypeId;
    @Schema(description = "标签分类名字")
    private String tagTypeName;
    @Schema(description = "开始时间")
    private String startDate;
    @Schema(description = "结束时间")
    private String endDate = DateUtils.formatDateTime(new Date());
    @Schema(description = "排序名字")
    private String sortByName = "create_date";
    @Schema(description = "排序顺序")
    private String nameSort = "desc";
    @Schema(description = "存在的类型")
    private String existType;

    @Schema(description = "采集类型（客户类型1、产品类型2）")
    private String collectionType;

    public String getStartDate() {
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate thirtyDaysAgo = today.minusDays(30); // 当前日期减去30天
        // 输出结果
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return thirtyDaysAgo.format(formatter);
    }
}
