package cn.chinaunicom.sdsi.cloud.product.vo;

import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductAttachments;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductMark;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName: ProductResponseVO
 * @Author: duanzk
 * @Create: 2024-04-17
 **/
@Data
public class ProductResponseVO implements Serializable {
    /**
     * 产品id
     */
    private String id;

    /**
     * 产品名称
     */
    private String name;
    /**
     * 产品类型
     */
    private String type;
    /**
     * 省分行业
     */
    private String provincialBranch;
    /**
     * 产品输出公司
     */
    private String exportCompany;
    /**
     * 业务分类
     */
    private String businessClassification;
    /**
     * 细分领域
     */
    private String subdivision;
    /**
     * 业务大类
     */
    private String businessCategory;
    /**
     * 产品介绍
     */
    private String presentation;
    /**
     * 客户描述（目标客户）
     */
    private String customerDescription;
    /**
     * 业务场景
     */
    private String businessScenario;
    /**
     * 附件
     */
    private List<ProductAttachmentsVO> attachmentList;
    /**
     * 案例
     */
    private List<ProductAttachmentsVO> exampleList;

    private List<TSanquanProductMark> productMarkList;
    /**
     * 产品联系人
     */
    private String contact;
    /**
     * 产品支撑人
     */
    private String supporter;
    /**
     * 区域支撑经理
     */
    private String regionalSupport;
    /**
     * 地市支撑经理
     */
    private String prefecturalSupport;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 编辑时间
     */
    private Date updateDate;
    /**
     * 编辑人
     */
    private Date updateBy;
    /**
     * 逻辑删除
     */
    private String deleteFlag;
    /**
     *
     */
    private Integer version;

    /**
     * 标签内容
     */
    private String markContent;
    private String[] markIds;
    /**
     * 是否打标
     */
    private String isMaking;

    /**
     * 赋能连接
     */
    private String empowerLink;
    /**
     * 备用字段1
     */
    private String attr1;
    /**
     * 备用字段2
     */
    private String attr2;

    @Schema(description = "连接名称")
    private String linkName;

    @Schema(description = "连接地址")
    private String linkAddr;
    /**
     * 是否模型产品 0:否 1:是
     */
    private String ismodel;
    /**
     * 是否热销产品 0:否 1:是
     */
    private String ishotsale;
    /**
     * 是否主推产品 0:否 1:是
     */
    private String ismaincommend;
    /**
     * 下发时间设置（1代表一个月）
     */
    private String issueMonth;

    /**
     * 产品线
     */
    private String productLine;

    /**
     * 产品类
     */
    private String productCategory;

    /**
     * 产品系列
     */
    private String productSeries;

    /**
     * 类型
     */
    private String productType;

    /**
     * 地市
     */
    private String city;

    /**
     * 是否行业主推
     */
    private String isIndustryMain;

    /**
     * 是否标杆产品
     */
    private String isBenchmark;
}
