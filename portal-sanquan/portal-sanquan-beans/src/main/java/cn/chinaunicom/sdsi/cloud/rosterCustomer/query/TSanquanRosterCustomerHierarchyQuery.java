package cn.chinaunicom.sdsi.cloud.rosterCustomer.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
* 名单制客户层级结构表
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "名单制客户层级结构表")
public class TSanquanRosterCustomerHierarchyQuery extends BaseQueryVO {
	private static final long serialVersionUID = 1L;

	@Schema(description = "名单制客户ID")
	private String rosterCustomerId;

	@Schema(description = "名单制客户名称")
	private String rosterCustomerName;

	@Schema(description = "父级名单制客户ID")
	private String parentCustId;

	@Schema(description = "父级名单制客户名称")
	private String parentCustName;

	@Schema(description = "层级")
	private String levels;

	@Schema(description = "是否是核心要客：1：是 0：否")
	private String majorFlag;

	@Schema(description = "主责部门")
	private String majorDepartment;

	@Schema(description = "省份代码")
	private String provinceCode;

	@Schema(description = "地市代码")
	private String cityCode;

	@Schema(description = "区县代码")
	private String districtCode;

	@Schema(description = "状态0失效1生效")
	private String state;

	@Schema(description = "变更时间")
	private String stateDate;

	@Schema(description = "总部所在地")
	private String headquarters;

	@Schema(description = "行业ID")
	private String tradeClusterId;

}