package cn.chinaunicom.sdsi.cloud.leaderVisit.audit.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <p>
 * 拜访审批明细表
 * </p>
 * <AUTHOR>
 * @since 2025-07-18
 */
@Data
public class LeaderVisitAuditVo implements Serializable {


    /**
     * 拜访id
     */
    private String visitId;

    /**
     * 流程实例id
     */
    private String orderId;

    /**
     * 流程类型：跟进、关单申请
     */
    private String orderType;

    /**
     * 流程发起时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date orderTime;

    /**
     * 客户经理反馈时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date feedbackTime;

    /**
     * 客户经理反馈内容
     */
    private String feedbackContent;

    /**
     * 商机编号
     */
    private String oppoNumber;

    /**
     * 商机名称
     */
    private String oppoName;

    /**
     * 商机金额
     */
    private BigDecimal oppoAmount;

    /**
     * 商机描述
     */
    private String oppoDesc;

    /**
     * 流程状态
     */
    private String orderStatus;

    /**
     * 申请关闭说明
     */
    private String closeDesc;

    /**
     * 填写时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;


    /**
     * 地市
     */
    private String city;

    /**
     * 签到地点
     */
    private String signInPlace;

    /**
     * 拜访方式： 现场、电话
     */
    private String visitType;

    /**
     * 拜访时间
     */
    private String visitTime;

    /**
     * 拜访客户ID
     */
    private String natureCustId;

    /**
     * 拜访客户名称
     */
    private String natureCustName;

    /**
     * 拜访总结
     */
    private String visitSummary;

    /**
     * 访谈id
     */
    private String interviewId;

    /**
     * 访谈内容
     */
    private String interviewContent;

    /**
     * 客户经理
     */
    private String managerName;

    /**
     * 客户经理OA
     */
    private String managerOa;

    /**
     * 公司领导
     */
    private String compLeader;

    /**
     * 公司领导OA
     */
    private String compLeaderOa;

    /**
     * 客户经理电话
     */
    private String managerTelphone;



    /**
     * 上级部门负责人姓名
     */
    private String parentDeptCharger;

    /**
     * 上级部门负责人oa账号
     */
    private String parentDeptChargerOa;

    /**
     * 拜访状态（0未派单、1首次派单、2跟进、3关闭）
     */
    private String status;
}
