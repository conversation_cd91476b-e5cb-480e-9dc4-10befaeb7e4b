package cn.chinaunicom.sdsi.cloud.dbcp.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * <p>
 * 等保测评跟踪报表
 * </p>
 * <AUTHOR>
 * @since 2025-03-11
 */
@Data
//@ApiModel(value = "TSanquanDbcpReportVo", description = "")
public class TSanquanDbcpReportQueryVo extends BaseQueryVO {


    private String id;

    /**
     * 任务编号
     */
    private String taskCode;

    /**
     * 地市
     */
    private String city;
    private String[] citys;

    /**
     * 策略编号
     */
    private String superiorPolicyCode;

    /**
     * 策略名称
     */
    private String superiorPolicyName;

    /**
     * 当前环节 mp:摸排 gz:跟踪 qy:签约
     */
    private String currentLink;

    /**
     * 自然客户id
     */
    private String customerId;

    /**
     * 自然客户名称
     */
    private String customerName;

    /**
     * 所属行业
     */
    private String industry;

    /**
     * 客户经理名字
     */
    private String customerManager;

    /**
     * 客户经理id
     */
    private String customerManagerId;

    /**
     * 项目规模
     */
    private String projectScale;

    /**
     * 今年测评开展情况 未开展、已开展
     */
    private String evaluationSituation;

    /**
     * 测评机构
     */
    private String evaluationOrg;

    /**
     * 今年到期时间
     */
    private String expirationDate;

    /**
     * 项目预算(万元)
     */
    private String projectBudget;

    /**
     * 今年工作计划
     */
    private String workPlan;

    /**
     * 合作意向。无意向、未确定、联通、三方机构
     */
    private String cooperate;

    /**
     * 服务要求
     */
    private String serviceRequirement;

    /**
     * 启动时间
     */
    private String startDate;

    /**
     * 友商情况
     */
    private String friendBusiness;

    /**
     * 客户意向变化
     */
    private String custIntentionChange;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否编制招标文件（0、否，1、是）
     */
    private String sfbzzbwj;

    /**
     * 是否发布招标公告（0、否，1、是）
     */
    private String sffbzbgg;

    /**
     * 报名截止时间
     */
    private String bmjzDate;

    /**
     * 开标时间
     */
    private String kbDate;

    /**
     * 中标情况
     */
    private String bidSituation;

    /**
     * 项目入场时间
     */
    private String projectStartDate;

    /**
     * 项目结束时间
     */
    private String projectEndDate;

    /**
     * 项目交付评价
     */
    private String projectDeliverAppraise;

    /**
     * 项目交付评价意见
     */
    private String projectDeliverAppraiseOpinion;

    /**
     * 项目质量评价
     */
    private String projectQualityAppraise;

    /**
     * 项目质量评价意见
     */
    private String projectQualityAppraiseOpinion;

    /**
     * 客户体验评价
     */
    private String custExperienceAppraise;

    /**
     * 客户体验评价意见
     */
    private String custExperienceAppraiseOpinion;

    /**
     * 项目计划完成时间
     */
    private String projectPlanFinishDate;

    /**
     * 项目实际完成时间
     */
    private String projectRealityFinishDate;

    // 开始时间
    private String beginDate;

    // 结束时间
    private String endDate;

    /**
     * 反馈订单号
     */
    private String feedbackOrderId;

    /**
     * 反馈时间
     */
    private String feedbackDate;

    /*工单派发时间*/
    private String sendTime;

}
