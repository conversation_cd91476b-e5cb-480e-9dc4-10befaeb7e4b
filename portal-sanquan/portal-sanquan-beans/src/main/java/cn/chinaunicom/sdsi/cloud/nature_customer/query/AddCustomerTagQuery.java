package cn.chinaunicom.sdsi.cloud.nature_customer.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "客户打标VO")
public class AddCustomerTagQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;
    /**
     * 摸排表id
     */
    @Schema(description = "名单制客户ID")
    private String id;
    @Schema(description = "名单制客户ID")
    private String rosterCustomerId;
    @Schema(description = "客戶id")
    private String customerId;
    @Schema(description = "客户名称")
    private String customerName;
    @Schema(description = "客户编码")
    private String customerNo;
    @Schema(description = "标签编码")
    private String labelCode;
    @Schema(description = "标签名称")
    private String labelName;
    @Schema(description = "标签类型")
    private String labelType;
    @Schema(description = "创建人")
    private String createUser;
    @Schema(description = "创建时间")
    private String createDate;
}
