package cn.chinaunicom.sdsi.cloud.customer.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 客户资料审核对象 t_sanquan_customer_info_audit_log
 * 
 * <AUTHOR>
 * @date 2024-07-08
 */
@EqualsAndHashCode(callSuper=false)
@Accessors(chain = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_sanquan_customer_info_audit_log")
public class TSanquanCustomerInfoAuditLog {
    private static final long serialVersionUID = 1L;

    /**
     * 记录表id
     */
    @TableId
    private String id;

    /**
     * 审核表id
     */
    private String auditId;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 操作人类型（0、创建人（提交人），1、区县审核人，2、地市审核人）
     */
    private String operatorType;

    /**
     * 姓名
     */
    private String operatorName;

    /**
     * 工号
     */
    private String operatorJobNumber;

    /**
     * 操作
     */
    private String operation;

    /**
     * 操作时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date operationDate;

    /**
     * 审核状态
     */
    private String isAudit;
}
