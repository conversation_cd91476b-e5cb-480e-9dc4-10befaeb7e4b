package cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商机周报-营销经理处理情况，
 * </p>
 */
@Data
public class OppoReviewCustomerDealExcelVo implements Serializable {


    /**
     * 营销经理
     */
    private String customer;

    /**
     * 推送个数
     */
    private String pushNum;

    /**
     * 处理完成个数
     */
    private String dealNum;

    /**
     * 完成率
     */
    private String finishRate;
    /**
     * 其中：超时商机个数（处理时间，与推送时间差 > 48小时）
     */
    private String expireOppoNum;


}
