package cn.chinaunicom.sdsi.cloud.holiday.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 节假日表（包含周末、法定假日、调休等）
 * </p>
 * <AUTHOR>
 * @since 2025-07-23
 */
@Data
public class HolidayQueryVo extends BaseQueryVO {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 日期
     */
    private LocalDate holidayDate;

    /**
     * 类型：1-节假日，2-周末，3-调休
     */
    private Integer holidayType;

    /**
     * 节日名称（如：元旦、春节）
     */
    private String holidayName;

    /**
     * 是否为工作日：0-否，1-是（用于调休上班的情况）
     */
    private Boolean isWorkday;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
