package cn.chinaunicom.sdsi.cloud.strategy.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "策略匹配产品")
public class TSanquanStrtegyProductQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    @Schema(description = "策略表id")
    private String id;

    @Schema(description = "策略名字")
    private String strategyName;

    @Schema(description = "策略id")
    private String strategyId;

    @Schema(description = "产品id")
    private String productId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品编码")
    private String productCode;

    public String getStartDate() {
        LocalDate today = LocalDate.now(); // 获取当前日期
        LocalDate thirtyDaysAgo = today.minusDays(30); // 当前日期减去30天
        // 输出结果
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return thirtyDaysAgo.format(formatter);
    }
}
