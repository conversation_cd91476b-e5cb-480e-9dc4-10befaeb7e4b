package cn.chinaunicom.sdsi.cloud.product.query;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/9/14 9:08
 */
@Data
public class PersonnelQuery extends BaseQueryVO {
    /**
     * 产品人id
     */
    @TableId
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门
     */
    private String department;

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * 地市
     */
    private String prefecture;

    /**
     * 地市序号
     */
    private String prefectureId;

    /**
     * 区域
     */
    private String region;

    /**
     * 区域序号
     */
    private String regionId;

    /**
     * 上岗证
     */
    private String employmentCertificate;

    /**
     * 测评师证书
     */
    private String evaluatorCertificate;

    /**
     * 工作岗位
     */
    private String jobTitle;

    /**
     * 岗位类型（1产品负责人，2产品支撑人，3区域支撑人，4地市支撑人）
     */
    private String jobType;

    /**
     * 工号
     */
    private String jobNumber;

}
