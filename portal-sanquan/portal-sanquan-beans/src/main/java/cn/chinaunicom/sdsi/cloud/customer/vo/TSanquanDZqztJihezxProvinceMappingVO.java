package cn.chinaunicom.sdsi.cloud.customer.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 客户经理信息视图对象
 * @Author: han<PERSON><PERSON><PERSON>
 * @Date: 2024-05-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "客户经理信息视图对象", description = "客户经理信息视图对象")
public class TSanquanDZqztJihezxProvinceMappingVO {

    @Schema(name = "登录用户名")
    private String login;

    @Schema(name = "省")
    private String province;

    @Schema(name = "城市")
    private String city;

    @Schema(name = "姓名")
    private String name;

    @Schema(name = "电话")
    private String  numbers;

    @Schema(name = "账期月")
    private String monthId;

    @Schema(name = "账期日")
    private String dayId;

    private String tenantId;

}
