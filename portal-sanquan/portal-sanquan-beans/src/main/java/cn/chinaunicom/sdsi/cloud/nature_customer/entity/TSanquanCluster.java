package cn.chinaunicom.sdsi.cloud.nature_customer.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: 王永凯
 * @Date: 2024/11/6 下午2:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@TableName("t_sanquan_cluster")
@Schema(description = "聚类字典表")
public class TSanquanCluster implements Serializable {

    @Schema(description = "主键")
    @TableId
    private String id;

    @Schema(description = "聚类名称")
    private String name;

}
