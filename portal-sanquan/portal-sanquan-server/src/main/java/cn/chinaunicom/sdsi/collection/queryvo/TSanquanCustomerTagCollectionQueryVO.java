package cn.chinaunicom.sdsi.collection.queryvo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Description: 客户标签汇总表(采集表)查询对象
 * @Author: han
 * @Date: 2024-07-08
 */
@Data
@Tag(name = "客户标签汇总表(采集表)查询对象", description = "客户标签汇总表(采集表)查询对象")
public class TSanquanCustomerTagCollectionQueryVO extends BaseQueryVO {

    @Schema(name = "")
    private String customerId;

    @Schema(name = "更新时间")
    private String updateTime;

    @Schema(name = "")
    private String 客户名称;

    @Schema(name = "")
    private String 政企行业;

    @Schema(name = "")
    private String 客户经理;

    @Schema(name = "")
    private String 地市;

    @Schema(name = "")
    private String 联系电话;

    @Schema(name = "")
    private String 在网状态;

    @Schema(name = "")
    private String 收入;

    @Schema(name = "")
    private String 收入环比;

    @Schema(name = "")
    private String 收入同比;

    @Schema(name = "")
    private String 宽带收入;

    @Schema(name = "")
    private String vpn专线收入;

    @Schema(name = "")
    private String 短号码业务收入;

    @Schema(name = "")
    private String 固话收入;

    @Schema(name = "")
    private String 警务通收入;

    @Schema(name = "")
    private String 警务通融合收入;

    @Schema(name = "")
    private String 网元收入;

    @Schema(name = "")
    private String 物联网产品收入;

    @Schema(name = "")
    private String 云产品收入;

    @Schema(name = "")
    private String 增值业务收入;

    @Schema(name = "")
    private String 专线收入;

    @Schema(name = "")
    private String 大数据产品收入;

    @Schema(name = "")
    private String IDC收入;

    @Schema(name = "")
    private String 宽带数量;

    @Schema(name = "")
    private String vpn专线数量;

    @Schema(name = "")
    private String 短号码业务数量;

    @Schema(name = "")
    private String 固话数量;

    @Schema(name = "")
    private String 警务通数量;

    @Schema(name = "")
    private String 警务通融合数量;

    @Schema(name = "")
    private String 网元数量;

    @Schema(name = "")
    private String 物联网产品数量;

    @Schema(name = "")
    private String 云产品数量;

    @Schema(name = "")
    private String 增值业务数量;

    @Schema(name = "")
    private String 专线数量;

    @Schema(name = "")
    private String 大数据产品数量;

    @Schema(name = "")
    private String IDC数量;

    @Schema(name = "")
    private String 具备业务数量;

    @Schema(name = "创建人")
    private String createBy;

    private List<String> customerIdList;
}
