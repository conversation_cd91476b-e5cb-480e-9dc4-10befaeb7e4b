package cn.chinaunicom.sdsi.emailConfig.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮件定时任务
 */
@Data
@TableName("t_sanquan_email_cycle")
public class EmailSchedule implements Serializable {

    private String id;
    /* 任务名称 */
    private String taskId;
    private String cycleTime;
    /* 状态，1启用，0禁用 */
    private String status;

}
