package cn.chinaunicom.sdsi.dingding.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 钉钉秘钥配置
 */
@Data
@TableName("t_sanquan_dingding_config")
public class DingdingConfig implements Serializable {

    private String id;
    /* 任务名称 */
    private String appkey;
    private String appSecret;
    private String robotCode;
    private String chatId;
    private String openConversationId;
    private String groupRemark;
    /* 状态，1启用，0禁用 */
    private String status;

}
