package cn.chinaunicom.sdsi.dingtalkAbility.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 钉钉人员配置表查询Vo
 * 对应表：t_sanquan_dingding_contact
 */
@Data
public class DingTalkContactQueryVo extends BaseQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * OA工号
     */
    private String loginName;

    /**
     * USERID
     */
    private String userid;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 地市
     */
    private String city;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 职务/分工
     */
    private String jobPosition;

    /**
     * 业务分类
     */
    private String businessCategory;

    /**
     * 状态:0-正常,1-禁用
     */
    private Integer status;


}