package cn.chinaunicom.sdsi.opportunity.service;

import cn.chinaunicom.sdsi.cloud.opportunity.entity.TSanquanBusinessOpportunity;
import cn.chinaunicom.sdsi.cloud.opportunity.query.TSanquanBusinessOpportunityQuery;
import cn.chinaunicom.sdsi.cloud.opportunity.vo.TSanquanBusinessOpportunityVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商机编号表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-05-01
 */
public interface TSanquanBusinessOpportunityService extends IService<TSanquanBusinessOpportunity> {
    /**
     * 查询当前用户关联商机编号列表
     *
     * @param tSanquanBusinessOpportunityQuery
     * @return 商机编号表
     */
    List<TSanquanBusinessOpportunityVO> findByCreatorList(TSanquanBusinessOpportunityQuery tSanquanBusinessOpportunityQuery);

    // 获取最大的商机 时间
    String findMaxTime();

    void deleteByOppoNumber(TSanquanBusinessOpportunityQuery tSanquanBusinessOpportunityQuery);
}
