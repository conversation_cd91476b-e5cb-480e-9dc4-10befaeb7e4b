package cn.chinaunicom.sdsi.aiflow.service;

import cn.chinaunicom.sdsi.aiflow.dto.AiflowConfDTO;
import cn.chinaunicom.sdsi.aiflow.entity.AiflowConf;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;


public interface IAiflowConfService extends IService<AiflowConf> {
    /**
     * 根据模型id查询ai配置
     * @param modelId
     * @return
     */
    AiflowConf getAiflowConfByModelId(String modelId);

    /**
     * 根据模型id查询ai配置
     * @param configId
     * @return
     */
    AiflowConf getAiflowConfByConfigId(String configId);

    /**
     * 保存AI产品推荐流程配置
     *
     * @param param 保存参数
     * @return 是否成功
     */
    boolean saveAiflowConf(AiflowConfDTO param);

    /**
     * 修改AI产品推荐流程配置
     *
     * @param param 修改参数
     * @return 是否成功
     */
    boolean updateAiflowConf(AiflowConfDTO param);

    /**
     * 删除AI流程配置
     *
     * @param modelCode
     * @return 是否成功
     */
    boolean deleteAiflowConfByModelCode(String modelCode);
}
