package cn.chinaunicom.sdsi.product.service.impl;

import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductAttachments;
import cn.chinaunicom.sdsi.product.dao.TSanquanProductAttachmentsMapper;
import cn.chinaunicom.sdsi.product.service.TSanquanProductAttachmentsService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-14
 */
@Service
public class TSanquanProductAttachmentsServiceImpl extends ServiceImpl<TSanquanProductAttachmentsMapper, TSanquanProductAttachments> implements TSanquanProductAttachmentsService {

    @Autowired
    private TSanquanProductAttachmentsMapper tSanquanProductAttachmentsMapper;

    /**
     * 查询文件信息
     * @param productId 产品id
     * @return
     */
    @Override
    public List<String> getFileIdsByProductId(String productId) {

        // 构建查询条件
        LambdaQueryWrapper<TSanquanProductAttachments> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TSanquanProductAttachments::getProductId, productId); // 假设产品ID字段为"productId"

        // 执行查询操作
        List<TSanquanProductAttachments> attachments = baseMapper.selectList(lambdaQueryWrapper);

        // 提取文件ID集合
        List<String> fileIds = new ArrayList<>();
        for (TSanquanProductAttachments attachment : attachments) {
            fileIds.add(attachment.getId()); // 假设文件ID字段为"id"
        }

        return fileIds;
    }
}
