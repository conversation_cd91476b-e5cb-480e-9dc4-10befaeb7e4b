package cn.chinaunicom.sdsi.fiveMillion.service.impl;

import cn.chinaunicom.sdsi.fiveMillion.entity.FiveMillionInviteBidsList;
import cn.chinaunicom.sdsi.fiveMillion.mapper.FiveMillionInviteBidsListMapper;
import cn.chinaunicom.sdsi.fiveMillion.service.FiveMillionInviteBidsListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class FiveMillionInviteBidsListServiceImpl extends ServiceImpl<FiveMillionInviteBidsListMapper, FiveMillionInviteBidsList> implements FiveMillionInviteBidsListService {
    @Autowired
    private FiveMillionInviteBidsListMapper inviteBidsListMapper;

    @Override
    public List<FiveMillionInviteBidsList> findList(String dayId) {
        return inviteBidsListMapper.findList(dayId);
    }
}
