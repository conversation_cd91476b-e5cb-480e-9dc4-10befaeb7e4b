package cn.chinaunicom.sdsi.ls.mapper;

import cn.chinaunicom.sdsi.cloud.ls.query.LsQuery;
import cn.chinaunicom.sdsi.cloud.ls.vo.LsVO;
import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.ls.entity.LsQueryExcel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 */
@Mapper
public interface LsMapper extends BaseMapper<Tag> {

    IPage<LsVO> findPage(@Param("page") IPage page, @Param("query") LsQuery query);

    List<LsQueryExcel> findList(@Param("query") LsQuery query);

    List<LsQueryExcel> findChildrenList(@Param("query") LsQuery query);

}
