package cn.chinaunicom.sdsi.dingtalkAbility.service;

import java.io.File;
import java.util.List;

public interface DingTalkApiService {

    /**
     * 根据手机号码推送给个人文本信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param textContent 发送文本内容
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendTextMessageByMobile(String group, List<String> mobileList, String textContent, Boolean singleSend);

    /**
     * 根据手机号码推送给个人文本信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param textContent 发送文本内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendTextMessageByMobile(String group, List<String> mobileList, String textContent, String batchId, String bizName, String remark, Boolean singleSend);

    /**
     * 根据手机号码推送给个人图片信息
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param imgPath    图片路径
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendImageMessageByMobile(String group, List<String> mobileList, String imgPath, Boolean singleSend);


    /**
     * 根据手机号码推送给个人图片信息
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param imgPath    图片路径
     * @param batchId    批次ID（撤回时使用）
     * @param bizName    业务名称（日志使用）
     * @param remark     备注（日志使用）
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendImageMessageByMobile(String group, List<String> mobileList, String imgPath, String batchId, String bizName, String remark, Boolean singleSend);


    /**
     * 根据手机号码推送给个人Markdown信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendMarkdownMessageByMobile(String group, List<String> mobileList, String title, String textContent, Boolean singleSend);


    /**
     * 根据手机号码推送给个人Markdown信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendMarkdownMessageByMobile(String group, List<String> mobileList, String title, String textContent, String batchId, String bizName, String remark, Boolean singleSend);


    /**
     * 根据手机号码推送给个人文件信息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param fileName      文件名称
     * @param file       待发送的文件
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendFileMessageByMobile(String group, List<String> mobileList, String fileName, File file, Boolean singleSend);


    /**
     * 根据手机号码推送给个人文件信息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param fileName      文件名称
     * @param file       待发送的文件
     * @param batchId    批次ID（撤回时使用）
     * @param bizName    业务名称（日志使用）
     * @param remark     备注（日志使用）
     * @param singleSend     是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    int sendFileMessageByMobile(String group, List<String> mobileList, String fileName, File file, String batchId, String bizName, String remark, Boolean singleSend);

    /**
     * 使用机器人群聊功能推送文本消息
     *
     * @param group       钉钉业务分组
     * @param textContent 发送文本内容
     * @return 推送成功个数
     */
    int sendTextMessageToGroup(String group, String textContent);

    /**
     * 使用机器人群聊功能推送文本消息
     *
     * @param group       钉钉业务分组
     * @param textContent 发送文本内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @return 推送成功个数
     */
    int sendTextMessageToGroup(String group, String textContent, String batchId, String bizName, String remark);

    /**
     * 使用机器人群聊功能推送Markdown信息
     *
     * @param group       钉钉业务分组
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @return 推送成功个数
     */
    int sendMarkdownMessageToGroup(String group, String title, String textContent);

    /**
     * 使用机器人群聊功能推送Markdown信息
     *
     * @param group       钉钉业务分组
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @return 推送成功个数
     */
    int sendMarkdownMessageToGroup(String group, String title, String textContent, String batchId, String bizName, String remark);

    /**
     * 使用机器人群聊功能推送图片消息
     *
     * @param group   钉钉业务分组
     * @param imgPath 图片路径
     * @return 推送成功个数
     */
    int sendImageMessageToGroup(String group, String imgPath);

    /**
     * 使用机器人群聊功能推送图片消息
     *
     * @param group   钉钉业务分组
     * @param imgPath 图片路径
     * @param batchId 批次ID（撤回时使用）
     * @param bizName 业务名称（日志使用）
     * @param remark  备注（日志使用）
     * @return 推送成功个数
     */
    int sendImageMessageToGroup(String group, String imgPath, String batchId, String bizName, String remark);

    /**
     * 使用机器人群聊功能推送文件消息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group     钉钉业务分组
     * @param fileName  文件名称
     * @param file      待发送的文件
     * @return          推送成功个数
     */
    int sendFileMessageToGroup(String group, String fileName, File file);

    /**
     * 使用机器人群聊功能推送文件消息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group   钉钉业务分组
     * @param fileName   文件名称
     * @param file    待发送的文件
     * @param batchId 批次ID（撤回时使用）
     * @param bizName 业务名称（日志使用）
     * @param remark  备注（日志使用）
     * @return 推送成功个数
     */
    int sendFileMessageToGroup(String group, String fileName, File file, String batchId, String bizName, String remark);


    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @return 撤回成功个数
     */
    int recallMessageOnRobot(String group, List<String> processQueryKeys);

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @return 撤回成功个数
     */
    int recallMessageOnRobot(String group, String batchId);

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @param reason  撤回原因
     * @return 撤回成功个数
     */
    int recallMessageOnRobot(String group, String batchId, String reason);

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @param reason           撤回原因
     * @return 撤回成功个数
     */
    int recallMessageOnRobot(String group, List<String> processQueryKeys, String reason);

    /**
     * 撤回内部群消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @return 撤回成功个数
     */
    int recallMessageOnGroup(String group, List<String> processQueryKeys);

    /**
     * 撤回内部群消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @return 撤回成功个数
     */
    int recallMessageOnGroup(String group, String batchId);


    /**
     * 撤回内部群消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @param reason           撤回原因
     * @return 撤回成功个数
     */
    int recallMessageOnGroup(String group, List<String> processQueryKeys, String reason);

    /**
     * 撤回内部群消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @param reason  撤回原因
     * @return 撤回成功个数
     */
    int recallMessageOnGroup(String group, String batchId, String reason);

    /**
     * 上传资源文件
     *
     * @param group    钉钉分组
     * @param fileType 文件类型image 20MB,语音: voice 2MB 视频:video 20MB 文件: file 20MB
     * @param file     文件
     * @return 资源ID（可以在钉钉推送中直接当做URL来使用）
     */
    String uploadResource(String group, String fileType, File file);

}
