package cn.chinaunicom.sdsi.model.service;

import cn.chinaunicom.sdsi.model.entity.TSanquanModelType;
import cn.chinaunicom.sdsi.model.vo.TSanquanModelTypeVO;
import cn.chinaunicom.sdsi.model.queryvo.TSanquanModelTypeQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 模型分类 服务层接口
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface TSanquanModelTypeService extends IService<TSanquanModelType> {

    /**
     * 分页查询模型分类
     * 
     * @param tSanquanModelTypeQueryVO
     * @return IPage<TSanquanModelTypeVO>
     */
    IPage<TSanquanModelTypeVO> findPage(TSanquanModelTypeQueryVO tSanquanModelTypeQueryVO);

    /**
     * 查询模型分类详细信息
     *
     * @param id
     * @return TSanquanModelTypeVO
     */
    TSanquanModelTypeVO findInfo(String id);

    /**
     * 新增模型分类
     *
     * @param tSanquanModelTypeVO
     * @return String
     */
    String add(TSanquanModelTypeVO tSanquanModelTypeVO);

    /**
     * 修改模型分类
     *
     * @param tSanquanModelTypeVO
     * @return Boolean
     */
    Boolean update(TSanquanModelTypeVO tSanquanModelTypeVO);

    /**
     * 删除模型分类
     *
     * @param id
     * @return Boolean
     */
    Boolean delete(String id);

    /****
     * 检查类型编码是否存在
     * @param id
     * @param code
     * @return
     */
    Integer checkModelTypeCode(String id, String code);
    /****
     * 检查类型名称是否存在
     * @param id
     * @param name
     * @return
     */
    Integer checkModelTypeName(String id, String name);
    /****
     * 检查该类型下是否已有模型
     * @param typeId 模型类型id
     * @return 有返回true 没有返回false
     */
    Boolean checkHasModel(String typeId);

    List<TSanquanModelTypeVO> findModelTypeList(TSanquanModelTypeQueryVO tSanquanModelTypeQueryVO);
}
