package cn.chinaunicom.sdsi.dingtalkAbility.service.impl;


import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig;
import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkContact;
import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkLogs;
import cn.chinaunicom.sdsi.dingtalkAbility.enums.DingTalkLogsStatus;
import cn.chinaunicom.sdsi.dingtalkAbility.enums.DingTalkLogsType;
import cn.chinaunicom.sdsi.dingtalkAbility.request.BatchRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.GroupRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.RobotBatchSendRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.RobotGroupMessageRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.response.*;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkApiService;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkContactService;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkLogsService;
import cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee.DingTalkAuxiliaryService;
import cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee.DingTalkConfigCenter;
import cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee.DingTalkMessageService;
import cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee.DingTalkRecallService;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Primary
@Service("dingTalkDEApiService")
public class DingTalkDEApiServiceImpl implements DingTalkApiService {

    private final static Snowflake snowflake = IdUtil.createSnowflake(1, 1);

    @Autowired
    private DingTalkMessageService messageService;

    @Autowired
    private DingTalkAuxiliaryService auxiliaryService;

    @Autowired
    private DingTalkContactService contactService;

    @Autowired
    private DingTalkRecallService recallService;

    @Autowired
    private DingTalkLogsService dingTalkLogsService;

    @Autowired
    private DingTalkConfigCenter configCenter;

    /**
     * 根据手机号码推送给个人文本信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param textContent 发送文本内容
     * @param singleSend  是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendTextMessageByMobile(String group, List<String> mobileList, String textContent, Boolean singleSend) {
        return sendTextMessageByMobile(group, mobileList, textContent, null, null, null, singleSend);
    }


    /**
     * 根据手机号码推送给个人文本信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param textContent 发送文本内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @param singleSend  是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendTextMessageByMobile(String group, List<String> mobileList, String textContent, String batchId, String bizName, String remark, Boolean singleSend) {
        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (mobileList == null || mobileList.isEmpty()) {
            log.warn("手机号列表为空");
            return 0;
        }
        if (textContent == null || StrUtil.isEmpty(textContent)) {
            log.error("消息内容不能为空");
            return -1;
        }

        // 构造消息体
        RobotBatchSendRequest.MsgParam msgParam = new RobotBatchSendRequest.MsgParam();
        msgParam.setContent(textContent);
        return sendMessageByMobile(group, mobileList, "sampleText", msgParam, batchId, bizName, remark, singleSend);
    }

    /**
     * 根据手机号码推送给个人图片信息
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param imgPath    图片路径
     * @param singleSend 是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendImageMessageByMobile(String group, List<String> mobileList, String imgPath, Boolean singleSend) {
        return sendImageMessageByMobile(group, mobileList, imgPath, null, null, null, singleSend);
    }

    /**
     * 根据手机号码推送给个人图片信息
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param imgPath    图片路径
     * @param batchId    批次ID（撤回时使用）
     * @param bizName    业务名称（日志使用）
     * @param remark     备注（日志使用）
     * @param singleSend 是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendImageMessageByMobile(String group, List<String> mobileList, String imgPath, String batchId, String bizName, String remark, Boolean singleSend) {
        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (mobileList == null || mobileList.isEmpty()) {
            log.warn("手机号列表为空");
            return 0;
        }
        if (imgPath == null || StrUtil.isEmpty(imgPath)) {
            log.error("消息内容不能为空");
            return -1;
        }
        if (!FileUtil.exist(imgPath)) {
            log.error("图片文件不存在");
            return -1;
        }

        File file = FileUtil.file(imgPath);

        // 上传文件
        String mediaId = uploadResource(group, "image", file);

        // 上传文件失败，在上面的代码里已记录日志
        if (mediaId == null) {
            return 0;
        }


        // 构造消息体
        RobotBatchSendRequest.MsgParam msgParam = new RobotBatchSendRequest.MsgParam();
        msgParam.setPhotoURL(mediaId);
        return sendMessageByMobile(group, mobileList, "sampleImageMsg", msgParam, batchId, bizName, remark, singleSend);
    }


    /**
     * 根据手机号码推送给个人Markdown信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @param singleSend  是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendMarkdownMessageByMobile(String group, List<String> mobileList, String title, String textContent, Boolean singleSend) {
        return sendMarkdownMessageByMobile(group, mobileList, title, textContent, null, null, null, singleSend);
    }

    /**
     * 根据手机号码推送给个人Markdown信息
     *
     * @param group       钉钉业务分组
     * @param mobileList  手机号码列表
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @param singleSend  是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendMarkdownMessageByMobile(String group, List<String> mobileList, String title, String textContent, String batchId, String bizName, String remark, Boolean singleSend) {
        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (mobileList == null || mobileList.isEmpty()) {
            log.warn("手机号列表为空");
            return 0;
        }
        if (title == null || StrUtil.isEmpty(title)) {
            log.error("消息标题不能为空");
            return -1;
        }
        if (textContent == null || StrUtil.isEmpty(textContent)) {
            log.error("消息内容不能为空");
            return -1;
        }

        // 构造消息体
        RobotBatchSendRequest.MsgParam msgParam = new RobotBatchSendRequest.MsgParam();
        msgParam.setTitle(title);
        msgParam.setText(textContent);
        return sendMessageByMobile(group, mobileList, "sampleMarkdown", msgParam, batchId, bizName, remark, singleSend);
    }


    /**
     * 根据手机号码推送给个人文件信息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param fileName   文件名称
     * @param file       待发送的文件
     * @param singleSend 是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendFileMessageByMobile(String group, List<String> mobileList, String fileName, File file, Boolean singleSend) {
        return sendFileMessageByMobile(group, mobileList, fileName, file, null, null, null, singleSend);
    }


    /**
     * 根据手机号码推送给个人文件信息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表
     * @param file       待发送的文件
     * @param batchId    批次ID（撤回时使用）
     * @param bizName    业务名称（日志使用）
     * @param remark     备注（日志使用）
     * @param singleSend 是否单条发送（优势：可以单条撤回，劣势：时间会慢，请求次数变多）
     * @return 推送成功个数
     */
    @Override
    public int sendFileMessageByMobile(String group, List<String> mobileList, String fileName, File file, String batchId, String bizName, String remark, Boolean singleSend) {

        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (mobileList == null || mobileList.isEmpty()) {
            log.warn("手机号列表为空");
            return 0;
        }
        if (file == null) {
            log.error("资源文件不能为空");
            return -1;
        }

        // 获取文件名和扩展名
        if (fileName == null || StrUtil.isEmpty(fileName)) {
            fileName = file.getName();
        }
        String fileExt = file.getName().substring(file.getName().lastIndexOf(".") + 1).toLowerCase();

        // 定义支持的文件类型（仅xlsx/pdf/zip/rar/doc/docx）
        Set<String> supportedFileTypes = new HashSet<>(Arrays.asList(
                "xlsx", "pdf", "zip", "rar", "doc", "docx"
        ));

        // 检查文件类型是否支持
        if (!supportedFileTypes.contains(fileExt)) {
            log.error("不支持的文件类型: {}，仅支持xlsx/pdf/zip/rar/doc/docx", fileExt);
            return -1;
        }

        String mediaId = uploadResource(group, "file", file);
        // 上传文件失败，在上面的代码里已记录日志
        if (mediaId == null) {
            return 0;
        }

        // 构造消息体
        RobotBatchSendRequest.MsgParam msgParam = new RobotBatchSendRequest.MsgParam();
        msgParam.setMediaId(mediaId);
        msgParam.setFileName(fileName);
        msgParam.setFileType(fileExt);
        return sendMessageByMobile(group, mobileList, "sampleFile", msgParam, batchId, bizName, remark, singleSend);
    }


    /**
     * 使用机器人群聊功能推送文本消息
     *
     * @param group       钉钉业务分组
     * @param textContent 发送文本内容
     * @return 推送成功个数
     */
    @Override
    public int sendTextMessageToGroup(String group, String textContent) {
        return sendTextMessageToGroup(group, textContent, null, null, null);
    }

    /**
     * 使用机器人群聊功能推送文本消息
     *
     * @param group       钉钉业务分组
     * @param textContent 发送文本内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @return 推送成功个数
     */
    @Override
    public int sendTextMessageToGroup(String group, String textContent, String batchId, String bizName, String remark) {
        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (textContent == null || StrUtil.isEmpty(textContent)) {
            log.error("消息内容不能为空");
            return -1;
        }

        // 构造消息体
        RobotGroupMessageRequest.MsgParam msgParam = new RobotGroupMessageRequest.MsgParam();
        msgParam.setContent(textContent);
        return sendMessageToGroup(group, "sampleText", msgParam, batchId, bizName, remark);
    }


    /**
     * 使用机器人群聊功能推送Markdown信息
     *
     * @param group       钉钉业务分组
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @return 推送成功个数
     */
    @Override
    public int sendMarkdownMessageToGroup(String group, String title, String textContent) {
        return sendMarkdownMessageToGroup(group, title, textContent, null, null, null);
    }

    /**
     * 使用机器人群聊功能推送Markdown信息
     *
     * @param group       钉钉业务分组
     * @param title       发送消息标题
     * @param textContent 发送消息内容
     * @param batchId     批次ID（撤回时使用）
     * @param bizName     业务名称（日志使用）
     * @param remark      备注（日志使用）
     * @return 推送成功个数
     */
    @Override
    public int sendMarkdownMessageToGroup(String group, String title, String textContent, String batchId, String bizName, String remark) {
        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (title == null || StrUtil.isEmpty(title)) {
            log.error("消息标题不能为空");
            return -1;
        }
        if (textContent == null || StrUtil.isEmpty(textContent)) {
            log.error("消息内容不能为空");
            return -1;
        }

        // 构造消息体
        RobotGroupMessageRequest.MsgParam msgParam = new RobotGroupMessageRequest.MsgParam();
        msgParam.setTitle(title);
        msgParam.setText(textContent);
        return sendMessageToGroup(group, "sampleMarkdown", msgParam, batchId, bizName, remark);
    }

    /**
     * 使用机器人群聊功能推送图片消息
     *
     * @param group   钉钉业务分组
     * @param imgPath 图片路径
     * @return 推送成功个数
     */
    @Override
    public int sendImageMessageToGroup(String group, String imgPath) {
        return sendImageMessageToGroup(group, imgPath, null, null, null);
    }

    /**
     * 使用机器人群聊功能推送图片消息
     *
     * @param group   钉钉业务分组
     * @param imgPath 图片路径
     * @param batchId 批次ID（撤回时使用）
     * @param bizName 业务名称（日志使用）
     * @param remark  备注（日志使用）
     * @return 推送成功个数
     */
    @Override
    public int sendImageMessageToGroup(String group, String imgPath, String batchId, String bizName, String remark) {
        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (imgPath == null || StrUtil.isEmpty(imgPath)) {
            log.error("消息内容不能为空");
            return -1;
        }
        if (!FileUtil.exist(imgPath)) {
            log.error("图片文件不存在");
            return -1;
        }

        File file = FileUtil.file(imgPath);

        // 上传文件
        String mediaId = uploadResource(group, "image", file);
        // 上传文件失败，在上面的代码里已记录日志
        if (mediaId == null) {
            return 0;
        }

        // 构造消息体
        RobotGroupMessageRequest.MsgParam msgParam = new RobotGroupMessageRequest.MsgParam();
        msgParam.setPhotoURL(mediaId);
        return sendMessageToGroup(group, "sampleImageMsg", msgParam, batchId, bizName, remark);
    }

    /**
     * 使用机器人群聊功能推送文件消息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group    钉钉业务分组
     * @param fileName 文件名称
     * @param file     待发送的文件
     * @return 推送成功个数
     */
    @Override
    public int sendFileMessageToGroup(String group, String fileName, File file) {
        return sendFileMessageToGroup(group, fileName, file, null, null, null);
    }

    /**
     * 使用机器人群聊功能推送文件消息
     * 文件类型，支持xlsx、pdf、zip、rar、doc、docx格式。
     *
     * @param group    钉钉业务分组
     * @param fileName 文件名称
     * @param file     待发送的文件
     * @param batchId  批次ID（撤回时使用）
     * @param bizName  业务名称（日志使用）
     * @param remark   备注（日志使用）
     * @return 推送成功个数
     */
    @Override
    public int sendFileMessageToGroup(String group, String fileName, File file, String batchId, String bizName, String remark) {

        if (group == null || StrUtil.isEmpty(group)) {
            log.error("分组名称不能为空");
            return -1;
        }
        if (file == null) {
            log.error("资源文件不能为空");
            return -1;
        }

        // 获取文件名和扩展名
        if (fileName == null || StrUtil.isEmpty(fileName)) {
            fileName = file.getName();
        }
        String fileExt = file.getName().substring(file.getName().lastIndexOf(".") + 1).toLowerCase();

        // 定义支持的文件类型（仅xlsx/pdf/zip/rar/doc/docx）
        Set<String> supportedFileTypes = new HashSet<>(Arrays.asList(
                "xlsx", "pdf", "zip", "rar", "doc", "docx"
        ));

        // 检查文件类型是否支持
        if (!supportedFileTypes.contains(fileExt)) {
            log.error("不支持的文件类型: {}，仅支持xlsx/pdf/zip/rar/doc/docx", fileExt);
            return -1;
        }

        String mediaId = uploadResource(group, "file", file);
        // 上传文件失败，在上面的代码里已记录日志
        if (mediaId == null) {
            return 0;
        }

        // 构造消息体
        RobotGroupMessageRequest.MsgParam msgParam = new RobotGroupMessageRequest.MsgParam();
        msgParam.setMediaId(mediaId);
        msgParam.setFileName(fileName);
        msgParam.setFileType(fileExt);
        return sendMessageToGroup(group, "sampleFile", msgParam, batchId, bizName, remark);
    }

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnRobot(String group, List<String> processQueryKeys) {
        return recallMessageOnRobot(group, processQueryKeys, null);
    }

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnRobot(String group, String batchId) {
        return recallMessageOnRobot(group, batchId, null);
    }

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @param reason  撤回原因
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnRobot(String group, String batchId, String reason) {
        return recallMessage(group, batchId, 0, reason);
    }

    /**
     * 撤回人与机器人会话中机器人消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @param reason           撤回原因
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnRobot(String group, List<String> processQueryKeys, String reason) {
        return recallMessage(group, processQueryKeys, 0, reason);
    }

    /**
     * 撤回内部群消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnGroup(String group, List<String> processQueryKeys) {
        return recallMessageOnGroup(group, processQueryKeys, null);
    }

    /**
     * 撤回内部群消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnGroup(String group, String batchId) {
        return recallMessageOnGroup(group, batchId, null);
    }

    /**
     * 撤回内部群消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @param reason           撤回原因
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnGroup(String group, List<String> processQueryKeys, String reason) {
        return recallMessage(group, processQueryKeys, 1, reason);
    }

    /**
     * 撤回内部群消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @param reason  撤回原因
     * @return 撤回成功个数
     */
    @Override
    public int recallMessageOnGroup(String group, String batchId, String reason) {
        return recallMessage(group, batchId, 1, reason);
    }

    /**
     * 上传资源文件
     *
     * @param group    钉钉业务分组
     * @param fileType 文件类型image 20MB,语音: voice 2MB 视频:video 20MB 文件: file 20MB
     * @param file     文件
     * @return 资源ID（可以在钉钉推送中直接当做URL来使用）
     */
    @Override
    public String uploadResource(String group, String fileType, File file) {
        UploadResourceResponse resp = getResponseData(auxiliaryService.uploadResource(group, fileType, file));
        if (resp == null) {
            log.error("钉钉上传资源文件失败，原因：钉钉响应为空。");
            return null;
        }
        if (resp.getErrcode() != 0) {
            log.error("钉钉上传资源文件失败，原因：{}", resp.getErrmsg());
            return null;
        }
        return resp.getMedia_id();
    }

    /**
     * 根据手机号使用机器人对个人推送内容
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表（可以不在联系人表中的手机号码）
     * @param msgKey     消息类型：sampleText、sampleMarkdown、sampleImageMsg、sampleLink、sampleActionCard、sampleFile
     * @param msgParam   消息体：见参考文档
     * @param batchId    批次ID（撤回时使用）
     * @param bizName    业务名称（日志使用）
     * @param remark     备注（日志使用）
     * @return 0 未成功   n 成功数量
     */
    private int sendMessageByMobile(String group, List<String> mobileList, String msgKey, RobotBatchSendRequest.MsgParam msgParam, String batchId, String bizName, String remark, Boolean singleSend) {
        // 判断是否没有batchId，自动生成
        if (StrUtil.isEmpty(batchId)) {
            batchId = "PERSON_" + snowflake.nextIdStr();
            log.info("自动生成批次ID: {}", batchId);
        }

        // 分批处理（每次最多20条）
        int batchSize = singleSend ? 1 : 20;
        int totalSuccess = 0;
        List<List<String>> mobileBatches = Lists.partition(mobileList, batchSize);

        for (int i = 0; i < mobileBatches.size(); i++) {
            List<String> batch = mobileBatches.get(i);
            log.debug("正在处理第 {} 批，共 {} 个手机号", i + 1, batch.size());

            int successCount = sendMessageByMobileSingle(group, batch, msgKey, msgParam,
                    batchId, bizName, remark);

            if (successCount < 0) {
                log.error("第 {} 批处理失败，跳过后续批次", i + 1);
                return totalSuccess > 0 ? totalSuccess : -1;
            }
            totalSuccess += successCount;
        }

        log.info("消息发送完成，共处理 {} 个手机号，成功 {} 个",
                mobileList.size(), totalSuccess);
        return totalSuccess;
    }


    /**
     * 根据手机号使用机器人对个人推送内容（限制最多向20条手机号发送）
     *
     * @param group      钉钉业务分组
     * @param mobileList 手机号码列表（可以不在联系人表中的手机号码，限制最多20条手机号）
     * @param msgKey     消息类型：sampleText、sampleMarkdown、sampleImageMsg、sampleLink、sampleActionCard、sampleFile
     * @param msgParam   消息体：见参考文档
     * @param batchId    批次ID（撤回时使用）
     * @param bizName    业务名称（日志使用）
     * @param remark     备注（日志使用）
     * @return 0 未成功   n 成功数量
     */
    private int sendMessageByMobileSingle(String group, List<String> mobileList, String msgKey, RobotBatchSendRequest.MsgParam msgParam, String batchId, String bizName, String remark) {
        if (group == null || group.isEmpty()) {
            log.error("钉钉分组字段不存在，无法获取配置信息。");
            return 0;
        }
        if (mobileList == null || mobileList.isEmpty()) {
            return 0;
        }
        Map<String, String> userMap = new HashMap<>();

        LambdaQueryWrapper<DingTalkContact> lambdaWrapper = new LambdaQueryWrapper<>();
        lambdaWrapper.in(DingTalkContact::getMobile, mobileList)
                .eq(DingTalkContact::getBusinessCategory, group)
                .eq(DingTalkContact::getStatus, 0)
                .eq(DingTalkContact::getIsDeleted, false); // 注意Boolean类型用false

        // 查询已有联系人
        List<DingTalkContact> existingContacts = contactService.list(lambdaWrapper);
        Map<String, DingTalkContact> mobileToContactMap = existingContacts.stream()
                .collect(Collectors.toMap(
                        DingTalkContact::getMobile,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        List<String> useridList = new ArrayList<>();
        List<DingTalkContact> toSaveOrUpdate = new ArrayList<>();
        Date now = new Date();

        for (String mobile : mobileList) {
            /*
             * 这里逻辑为：
             *   1. 如果mobile不在数据库中，则正常获取userid后发送消息
             *   2. 如果mobile在数据库中
             *       (1) 如果数据库中userid存在，则使用数据库中的userid
             *       (2) 如果数据库中userid不存在，则正常获取userid后发送消息，最后更新数据库中该联系人的userid
             * */
            if (mobileToContactMap.containsKey(mobile)) {
                DingTalkContact contact = mobileToContactMap.get(mobile);
                if (contact.getUserid() != null) {
                    useridList.add(contact.getUserid());
                    userMap.put(mobile, contact.getUserid());
                    continue;
                }
            }

            ResponseEntity<DingTalkBaseResponse<MobileQueryResponse>> entity = auxiliaryService.queryUserIdByMobile(group, mobile);
            MobileQueryResponse data = getResponseData(entity);
            if (data == null) {
                log.warn("手机号 {} 查询钉钉userid返回数据为空", mobile);
                continue;
            } else if (data.getErrcode() != 0) {
                log.error("手机号 {} 查询钉钉userid失败，errmsg: {}", mobile, data.getErrmsg());
                continue;
            }
            String userid = data.getResult().getUserid();

            useridList.add(userid);
            userMap.put(mobile, userid);

            if (mobileToContactMap.containsKey(mobile)) {
                DingTalkContact contact = mobileToContactMap.get(mobile);
                contact.setUserid(userid);
                contact.setUpdateDate(now);
                toSaveOrUpdate.add(contact);
            }
        }

        if (useridList.isEmpty()) {
            return 0;
        }

        RobotBatchSendRequest request = RobotBatchSendRequest.builder()
                .userIds(useridList)
                .msgKey(msgKey)
                .msgParam(msgParam)
                .build();

        // 发送消息
        RobotBatchSendResponse msgData = getResponseData(messageService.batchSendRobotMessage(group, request));
        if (msgData == null) {
            log.error("钉钉发送消息失败，钉钉返回data为空。");
            return 0;
        }

        String requestStr = JSONUtil.toJsonStr(request);
        String responseStr = JSONUtil.toJsonStr(msgData);

        // 限流用户列表（未发送成功）
        List<String> flowControlledStaffIdList = msgData.getFlowControlledStaffIdList();
        // 无效用户列表
        List<String> invalidStaffIdList = msgData.getInvalidStaffIdList();
        String processQueryKey = msgData.getProcessQueryKey();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 存储日志
        ArrayList<DingTalkLogs> dingTalkLogs = new ArrayList<>();
        for (Map.Entry<String, String> entry : userMap.entrySet()) {
            String mobile = entry.getKey();
            String userid = entry.getValue();

            DingTalkLogs logs = new DingTalkLogs();
            logs.setBatchId(batchId);
            logs.setProcessQueryKey(processQueryKey);
            logs.setType(DingTalkLogsType.ROBOT_PRIVATE_CHAT);
            logs.setContent(JSONUtil.toJsonStr(msgParam));
            logs.setReceiverInfo(userid);
            logs.setTargetInfo(mobile);
            logs.setGroupRemark(group);
            logs.setCreateTime(LocalDateTime.now().format(formatter));
            if (flowControlledStaffIdList != null && flowControlledStaffIdList.contains(userid)) {
                logs.setStatus(DingTalkLogsStatus.FAILED);
                logs.setErrorCode("1");
                logs.setErrorMsg("flowControlledStaffId");
            } else if (invalidStaffIdList != null && invalidStaffIdList.contains(userid)) {
                logs.setStatus(DingTalkLogsStatus.FAILED);
                logs.setErrorCode("2");
                logs.setErrorMsg("invalidStaffId");
            } else {
                logs.setStatus(DingTalkLogsStatus.SUCCESS);
                logs.setErrorCode("0");
                logs.setErrorMsg("ok");
            }
            logs.setRequestParams(requestStr);
            logs.setResponseData(responseStr);
            logs.setBizName(bizName);
            logs.setRemark(remark);
            dingTalkLogs.add(logs);
        }

        // 更新日志信息
        if (!dingTalkLogs.isEmpty()) {
            try {
                dingTalkLogsService.saveBatch(dingTalkLogs);
                log.info("批量保存 {} 条钉钉推送日志信息", dingTalkLogs.size());
            } catch (Exception e) {
                log.error("批量保存钉钉推送日志失败", e);
            }
        }

        // 更新联系人表的userid
        if (!toSaveOrUpdate.isEmpty()) {
            try {
                contactService.saveOrUpdateBatch(toSaveOrUpdate);
                log.info("批量更新 {} 条userid缓存", toSaveOrUpdate.size());
            } catch (Exception e) {
                log.error("批量更新缓存失败", e);
            }
        }
        // 正确计算成功数量
        int flowControlledCount = flowControlledStaffIdList != null ? flowControlledStaffIdList.size() : 0;
        int invalidCount = invalidStaffIdList != null ? invalidStaffIdList.size() : 0;
        int successCount = useridList.size() - flowControlledCount - invalidCount;

        return Math.max(successCount, 0);
    }


    /**
     * 使用机器人群聊功能推送消息
     *
     * @param group    钉钉分组
     * @param msgKey   消息类型：sampleText、sampleMarkdown、sampleImageMsg、sampleLink、sampleActionCard、sampleFile
     * @param msgParam 消息体：见参考文档
     * @param batchId  批次ID（撤回时使用）
     * @param bizName  业务名称（日志使用）
     * @param remark   备注（日志使用）
     * @return 是否成功推送
     */
    private int sendMessageToGroup(String group, String msgKey, RobotGroupMessageRequest.MsgParam msgParam, String batchId, String bizName, String remark) {
        if (group == null || group.isEmpty()) {
            log.error("钉钉分组字段不存在，无法获取配置信息。");
            return 0;
        }

        if (batchId == null || batchId.isEmpty()) {
            batchId = "GROUP_" + snowflake.nextIdStr();
            log.info("自动生成批次ID: {}", batchId);
        }

        RobotGroupMessageRequest request = RobotGroupMessageRequest.builder()
                .msgKey(msgKey)
                .msgParam(msgParam)
                .build();

        RobotSendMessageResponse data = getResponseData(messageService.sendRobotGroupMessage(group, request));
        if (data == null || data.getProcessQueryKey() == null || data.getProcessQueryKey().isEmpty()) {
            log.error("钉钉发送群聊消息异常，返回值为空。");
            return 0;
        }
        String processQueryKey = data.getProcessQueryKey();

        String requestStr = JSONUtil.toJsonStr(request);
        String responseStr = JSONUtil.toJsonStr(data);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));

        // 记录日志
        DingTalkLogs logs = new DingTalkLogs();
        logs.setBatchId(batchId);
        logs.setProcessQueryKey(processQueryKey);
        logs.setType(DingTalkLogsType.NORMAL_GROUP_CHAT);
        logs.setContent(JSONUtil.toJsonStr(msgParam));
        logs.setReceiverInfo(config.getOpenConversationId());
        logs.setTargetInfo(config.getOpenConversationId());     // 这里可能存储群名称比较好，但是获取不到
        logs.setGroupRemark(group);
        logs.setCreateTime(LocalDateTime.now().format(formatter));

        // 接口没有返回错误信息，到这里执行成功
        logs.setStatus(DingTalkLogsStatus.SUCCESS);
        logs.setErrorCode("0");
        logs.setErrorMsg("ok");

        logs.setRequestParams(requestStr);
        logs.setResponseData(responseStr);
        logs.setBizName(bizName);
        logs.setRemark(remark);
        int rows = 0;
        try {
            rows = dingTalkLogsService.save(logs) ? 1 : 0;
            log.info("保存 {} 条钉钉推送日志信息", rows);
        } catch (Exception e) {
            log.error("保存钉钉推送日志失败", e);
        }

        return rows;
    }


    /**
     * 撤回消息（限制最多20条数据）
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @param type             0 撤回人与机器人会话中机器人消息 / 1 撤回内部群消息
     * @param reason           撤回原因
     * @return 撤回成功个数
     */
    private int recallMessageSingle(String group, List<String> processQueryKeys, Integer type, String reason) {
        RecallCommonResponse data;
        switch (type) {
            case 0:
                // 批量撤回人与机器人会话消息
                BatchRecallRequest batchRecallRequest = BatchRecallRequest.builder()
                        .processQueryKeys(processQueryKeys)
                        .build();
                data = getResponseData(recallService.batchRecallRobotMessages(group, batchRecallRequest));
                break;
            case 1:
                // 撤回内部群消息
                GroupRecallRequest groupRecallRequest = GroupRecallRequest.builder()
                        .processQueryKeys(processQueryKeys)
                        .build();
                data = getResponseData(recallService.recallGroupMessages(group, groupRecallRequest));
                break;
            default:
                throw new UnsupportedOperationException("暂未支持的撤回类型，请联系系统管理员。");
        }
        if (data == null) {
            return 0;
        }
        if (data.getSuccessResult().isEmpty()) {
            log.error("消息撤回失败，错误信息：{}", data.getFailedResult());
            return 0;
        }
        List<String> successResult = data.getSuccessResult();
        // 更新日志
        LambdaQueryWrapper<DingTalkLogs> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.in(DingTalkLogs::getProcessQueryKey, successResult);
        List<DingTalkLogs> logsList = dingTalkLogsService.list(lambdaQueryWrapper);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String recallTime = LocalDateTime.now().format(formatter);
        for (DingTalkLogs dingTalkLogs : logsList) {
            dingTalkLogs.setStatus(DingTalkLogsStatus.RECALLED);
            dingTalkLogs.setRecallTime(recallTime);
            dingTalkLogs.setRecallReason(reason);
        }
        try {
            dingTalkLogsService.updateBatchById(logsList);
            log.info("批量更新 {} 条钉钉推送日志信息", logsList.size());
        } catch (Exception e) {
            log.error("批量更新钉钉推送日志失败", e);
        }

        // 一个processQueryKey可能对应多条消息
        return successResult.size();
    }


    /**
     * 撤回消息
     *
     * @param group            钉钉分组
     * @param processQueryKeys 撤回消息ID列表 （每次最多传20个，在发送消息24小时内可以通过processQueryKey撤回消息，超过24小时则无法撤回消息。）
     * @param type             0 撤回人与机器人会话中机器人消息 / 1 撤回内部群消息
     * @param reason           撤回原因
     * @return 撤回成功个数
     */
    private int recallMessage(String group, List<String> processQueryKeys, Integer type, String reason) {
        if (group == null || group.isEmpty()) {
            log.error("钉钉分组字段不存在，无法获取配置信息。");
            return 0;
        }
        if (processQueryKeys == null || processQueryKeys.isEmpty()) {
            log.info("需要撤回的消息数量为0条。");
            return 0;
        }
        if (type == null || type < 0 || type > 1) {
            log.error("暂不支持此撤回类型的消息。");
            return 0;
        }
        // 分批处理（每次最多20条）
        int batchSize = 20;
        int totalSuccess = 0;
        List<List<String>> processQueryKeysBatches = Lists.partition(processQueryKeys, batchSize);

        for (int i = 0; i < processQueryKeysBatches.size(); i++) {
            List<String> batch = processQueryKeysBatches.get(i);
            log.debug("正在处理第 {} 批，共 {} 条消息需要撤回", i + 1, batch.size());

            int successCount = recallMessageSingle(group, batch, type, reason);

            if (successCount < 0) {
                log.error("第 {} 批处理失败，跳过后续批次", i + 1);
                return totalSuccess > 0 ? totalSuccess : -1;
            }
            totalSuccess += successCount;
        }

        log.info("消息撤回完成，共处理 {} 组消息，成功 {} 个",
                processQueryKeys.size(), totalSuccess);
        return totalSuccess;
    }


    /**
     * 撤回消息
     *
     * @param group   钉钉分组
     * @param batchId 撤回消息批次ID
     * @param type    0 撤回人与机器人会话中机器人消息 / 1 撤回内部群消息
     * @param reason  撤回原因
     * @return 撤回成功个数
     */
    private int recallMessage(String group, String batchId, Integer type, String reason) {
        if (group == null || group.isEmpty()) {
            log.error("钉钉分组字段不存在，无法获取配置信息。");
            return 0;
        }
        if (batchId == null || batchId.isEmpty()) {
            log.info("非法参数，撤回消息的批次ID不存在。");
            return 0;
        }
        if (type == null || type < 0 || type > 1) {
            log.error("暂不支持此撤回类型的消息。");
            return 0;
        }
        LambdaQueryWrapper<DingTalkLogs> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 判断消息发送时间需要小于24小时
        LocalDateTime twentyFourHoursAgo = LocalDateTime.now().minusHours(24);
        lambdaQueryWrapper.ge(DingTalkLogs::getCreateTime, twentyFourHoursAgo)
                .eq(DingTalkLogs::getBatchId, batchId);

        List<DingTalkLogs> list = dingTalkLogsService.list(lambdaQueryWrapper);

        // 提取所有processQueryKey字段为List<String>
        List<String> processQueryKeys = list.stream()
                .map(DingTalkLogs::getProcessQueryKey)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return recallMessage(group, processQueryKeys, type, reason);
    }


    /**
     * 解析响应类型
     *
     * @param entity 发送请求返回的entity
     * @param <T>    具体钉钉接口的响应类型
     * @return T
     */
    private <T> T getResponseData(ResponseEntity<DingTalkBaseResponse<T>> entity) {
        DingTalkBaseResponse<T> body = entity.getBody();
        if (!entity.getStatusCode().is2xxSuccessful() || body == null) {
            log.error("钉钉接口请求失败，请稍后重试。");
            return null;
        } else if (!body.getSuccess()) {
            log.error("钉钉接口返回错误，message: {}", body.getMessage());
            return null;
        }
        T data = body.getData();
        if (data == null) {
            log.error("钉钉推送异常，数字员工钉钉推送接口响应体为空。");
        }
        return data;
    }


}
