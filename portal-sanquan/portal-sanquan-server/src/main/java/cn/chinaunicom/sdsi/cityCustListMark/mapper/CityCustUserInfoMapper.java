package cn.chinaunicom.sdsi.cityCustListMark.mapper;

import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserAccessParamDTO;
import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserInfoDTO;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserAccessParam;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CityCustUserInfoMapper extends BaseMapper<CityCustUserInfo> {
    /**
     * 查询用户策略信息表
     *
     * @param page 分页参数
     * @return 分页结果
     */
    IPage<CityCustUserInfo> selectPageList(IPage<CityCustUserInfo> page, @Param("param") CityCustUserInfoDTO param);

    /**
     * 新增用户访问参数
     *
     * @param saveDTO 新增参数
     * @return 新增结果
     */
    int insertUserAccessParam(@Param("param") CityCustUserAccessParamDTO saveDTO);

    /**
     * 根据UUID查询用户访问参数
     *
     * @param uuid UUID
     * @return 用户访问参数实体
     */
    CityCustUserAccessParam selectUserAccessParamByUuid(@Param("uuid") String uuid);
}
