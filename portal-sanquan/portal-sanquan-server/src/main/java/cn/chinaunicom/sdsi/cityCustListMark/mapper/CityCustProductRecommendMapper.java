package cn.chinaunicom.sdsi.cityCustListMark.mapper;

import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustProductRecommend;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 客户推荐产品缓存表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CityCustProductRecommendMapper {

    /**
     * 根据客户名称查询推荐产品
     *
     * @param natureCustName 客户名称
     * @return CityCustProductRecommend
     */
    CityCustProductRecommend getByNatureCustName(@Param("natureCustName") String natureCustName);

    /**
     * 插入推荐产品记录
     *
     * @param entity 推荐产品实体
     * @return 影响行数
     */
    int insert(CityCustProductRecommend entity);

    /**
     * 根据客户名称更新推荐产品记录
     *
     * @param entity 推荐产品实体
     * @return 影响行数
     */
    int updateByNatureCustName(CityCustProductRecommend entity);

    /**
     * 更新状态
     *
     * @param natureCustName 客户名称
     * @param status         状态
     * @return 影响行数
     */
    int updateStatusByNatureCustName(@Param("natureCustName") String natureCustName, @Param("status") String status);

    /**
     * 更新状态和结果
     *
     * @param natureCustName 客户名称
     * @param status         状态
     * @param res            结果
     * @return 影响行数
     */
    int updateStatusAndResByNatureCustName(@Param("natureCustName") String natureCustName,
            @Param("status") String status,
            @Param("res") String res);
}