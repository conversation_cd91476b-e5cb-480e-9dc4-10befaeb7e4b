package cn.chinaunicom.sdsi.score.demension.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName t_sanquan_score_model_dimension
 */
@Data
@TableName(value ="t_sanquan_score_model_dimension")
public class ScoreModelDimension implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 评价id
     */
    private String demensionId;

}