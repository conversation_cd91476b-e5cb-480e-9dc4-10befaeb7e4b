package cn.chinaunicom.sdsi.emailDemo.service.impl;

import cn.chinaunicom.sdsi.emailConfig.entity.EmailConfig;
import cn.chinaunicom.sdsi.emailConfig.service.EmailDataService;
import cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonVO;
import cn.chinaunicom.sdsi.emailDemo.entity.EmailDemoExcelVo;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.chinaunicom.sdsi.util.excel.MySheetWrapWriteHandler;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class EmailDemoServiceImpl implements EmailDataService {

    @Override
    public List<List> getDataList(EmailConfig config, EmailPersonVO personVO) {
        List<List> dataList = Lists.newArrayList();
        List<EmailDemoExcelVo> demoExcelVoList = Lists.newArrayList();
        for(int i=0;i<10;i++){
            EmailDemoExcelVo excelVo = new EmailDemoExcelVo();
            excelVo.setId(""+i);
            excelVo.setBusinessType("测试数据");
            excelVo.setCycleName("周期展示");
            excelVo.setModule("你好");
            excelVo.setTaskName("测试任务");
            demoExcelVoList.add(excelVo);
        }
        dataList.add(demoExcelVoList);
        return dataList;
    }

    @Override
    public void buildExcelFile(EmailConfig config, EmailPersonVO personVO, List<List> list) {
        if(list.size() > 0 ){
            ExcelWriter excelWriter = null;
            try {
                excelWriter = EasyExcel.write(config.getFilePath()).autoCloseStream(Boolean.TRUE).build();
                WriteSheet writeSheet1 = EasyExcel.writerSheet(0,"测试demo")
                        .registerWriteHandler(ExcelUtils.getHorizontalCellStyleStrategyTwo())
                        .registerWriteHandler(new MySheetWrapWriteHandler())
                        .head(this.getHead())
                        .build();
                excelWriter.write(list.get(0), writeSheet1);


            } catch (Exception e) {
                e.printStackTrace();
            }finally {
                ExcelUtils.closeExcelWriter(excelWriter);
            }
        }else{

        }
    }

    @Override
    public String getErrorStr() {
        return null;
    }

    @Override
    public String getEmailContent(EmailConfig config, EmailPersonVO personVO, List<List> list) {
        return null;
    }

    @Override
    public Map<String, Object> getMyParam() {
        return null;
    }

    private List<List<String>> getHead(){
        List<List<String>> headList = Lists.newArrayList();
        headList.add(Lists.newArrayList("字段一1"));
        headList.add(Lists.newArrayList("字段一2"));
        headList.add(Lists.newArrayList("字段一3"));
        headList.add(Lists.newArrayList("字段一4"));
        headList.add(Lists.newArrayList("字段一5"));
        headList.add(Lists.newArrayList("字段一6"));
        return headList;
    }
}
