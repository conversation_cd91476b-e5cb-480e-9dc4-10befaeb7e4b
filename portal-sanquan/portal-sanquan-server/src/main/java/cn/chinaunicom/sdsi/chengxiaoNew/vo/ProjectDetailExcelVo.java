package cn.chinaunicom.sdsi.chengxiaoNew.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/27 9:43
 */
// 项目
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class ProjectDetailExcelVo implements Serializable {
    private static final long serialVersionUID = 1L;

    // 自然客户ID
    @ColumnWidth(20)
    @ExcelProperty(value = "自然客户ID")
    private String customId;

    // 自然客户名称
    @ColumnWidth(20)
    @ExcelProperty(value = "自然客户名称")
    private String customName;

    // 项目编号
    @ColumnWidth(20)
    @ExcelProperty(value = "项目编号")
    private String projectNumber;

    // 项目名称
    @ColumnWidth(20)
    @ExcelProperty(value = "项目名称")
    private String projectName;

    // 项目金额
    @ColumnWidth(20)
    @ExcelProperty(value = "项目金额(万元)")
    private String projectAmount;

    // 项目创建时间
    @ColumnWidth(20)
    @ExcelProperty(value = "项目创建时间")
    private String projectCreatedDate;

    // 项目客户经理名称
    @ColumnWidth(20)
    @ExcelProperty(value = "项目客户经理名称")
    private String projectManagerName;

    // 项目客户经理oa工号
    @ColumnWidth(20)
    @ExcelProperty(value = "项目客户经理oa工号")
    private String projectManagerLogin;

}
