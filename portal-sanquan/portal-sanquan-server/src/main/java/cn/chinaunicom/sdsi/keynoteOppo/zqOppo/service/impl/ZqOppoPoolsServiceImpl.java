package cn.chinaunicom.sdsi.keynoteOppo.zqOppo.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.holiday.query.HolidayQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoReview;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoReviewLog;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoReviewQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoReviewVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoMark;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoPools;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.query.ZqOppoPoolsQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.vo.ZqOppoPoolsVo;
import cn.chinaunicom.sdsi.dataScope.annotation.DataScope;
import cn.chinaunicom.sdsi.dataScope.annotation.DataScopeQuery;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.chinaunicom.sdsi.holiday.service.HolidayService;
import cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.service.OppoReviewLogService;
import cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.service.OppoReviewService;
import cn.chinaunicom.sdsi.keynoteOppo.zqOppo.mapper.ZqOppoPoolsMapper;
import cn.chinaunicom.sdsi.keynoteOppo.zqOppo.service.ZqOppoMarkService;
import cn.chinaunicom.sdsi.keynoteOppo.zqOppo.service.ZqOppoPoolsService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 政企中台商机 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
public class ZqOppoPoolsServiceImpl extends ServiceImpl<ZqOppoPoolsMapper, ZqOppoPools> implements ZqOppoPoolsService {

    @Autowired
    public UnifastContext unifastContext;

    @Autowired
    private ZqOppoMarkService zqOppoMarkService;

    @Autowired
    private OppoReviewService oppoReviewService;

    @Autowired
    private OppoReviewLogService oppoReviewLogService;

    @Autowired
    private HolidayService holidayService;

    public String getMaxDate(){
        return this.baseMapper.getMaxDate();
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-26
     * @param zqOppoPools
     * @return IPage<ZqOppoPools>
     **/
    @Override
//    @DataScope(cityQuery = @DataScopeQuery(required = true, column = "zop.city"))
    public IPage<ZqOppoPoolsVo> findPage(ZqOppoPoolsQueryVo zqOppoPoolsVo) {
        IPage page = QueryVoToPageUtil.toPage(zqOppoPoolsVo);
        Object o = baseMapper.findPage(page, zqOppoPoolsVo);
        return baseMapper.findPage(page, zqOppoPoolsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-26
     * @param zqOppoPools
     * @return int
     **/
    @Override
    public int update(ZqOppoPools zqOppoPools) {
        return baseMapper.updateById(zqOppoPools);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String feedback(ZqOppoPoolsVo zqOppoPoolsVo) {
        {
            // 校验是否已被评审
            OppoReviewQueryVo queryVo = new OppoReviewQueryVo();
            queryVo.setOppoNumber(zqOppoPoolsVo.getOppoNumber());
            List<OppoReviewVo> poolList = oppoReviewService.findList(queryVo);
            if(poolList.size()>0){
                ZqOppoMark zqOppoMark = zqOppoMarkService.getById(poolList.get(0).getOppoNumber());
                if(zqOppoMark != null && "1".equals(zqOppoMark.getStatus())){
                    return "该商机已被【"+poolList.get(0).getCreateNameM()+"】处理，无需再处理";
                }
            }
        }
        // 根据商机编号查询商机
        ZqOppoPoolsVo oppoPoolsDb = baseMapper.findOne(zqOppoPoolsVo.getOppoNumber());
        ZqOppoMark zqOppoMark = new ZqOppoMark();
        zqOppoMark.setOppoNumber(zqOppoPoolsVo.getOppoNumber());
        zqOppoMark.setStatus(zqOppoPoolsVo.getStatus());
        zqOppoMark.setUpdateTime(DateUtils.formatDateTime(new Date()));
        int update = zqOppoMarkService.update(zqOppoMark);
        OppoReview oppoReview = new OppoReview();
        oppoReview.setId(zqOppoPoolsVo.getReviewId());
        oppoReview.setOppoNumber(zqOppoPoolsVo.getOppoNumber());
        oppoReview.setContractAmount(zqOppoPoolsVo.getContractAmountReview());// 修改之后的合同金额
        oppoReview.setContractAmountPrev(zqOppoPoolsVo.getContractAmountPrevReview());// 修改之前的合同金额
        oppoReview.setOppoLsInfo(zqOppoPoolsVo.getOppoLsInfo());// 落实信息
        oppoReview.setSfyxOppo(zqOppoPoolsVo.getSfyxOppo());// 是否有效商机
        oppoReview.setSfjxTrack(zqOppoPoolsVo.getSfjxTrack());// 是否跟进
        oppoReview.setDayId(DateUtils.formatDateTime(new Date()));
        MallUser user = UserUtils.getUser();
        oppoReview.setCreateByM(user.getStaffId());
        oppoReview.setCreateNameM(user.getStaffName());
        oppoReview.setCreateTimeM(DateUtils.formatDateTime(new Date()));
        oppoReview.setStatusM("1");
        oppoReview.setAsrId(zqOppoPoolsVo.getAsrId());
        if("1".equals(zqOppoPoolsVo.getSfyxOppo()) && "1".equals(zqOppoPoolsVo.getSfjxTrack())){
            oppoReview.setOppoStatus("1");
            // 为空表示不是退回的单子（退回的单子带着此ID）
            if(StrUtil.isEmpty(zqOppoPoolsVo.getReviewId())){
                // 集成交付
                oppoReview.setStatusJc("0");
                // 产品研发
                oppoReview.setStatusCy("0");
                // 运营服务
                oppoReview.setStatusYy("0");
            }
        }else {
            oppoReview.setOppoStatus("0");
        }
        // 计算用时时间
        setDealTime(oppoPoolsDb.getDayId(), oppoReview);

        oppoReview.setUpdateTime(DateUtils.formatDateTime(new Date()));
        int i = oppoReviewService.addOrUpdate(oppoReview);
        // 添加商机审核日志
        OppoReviewLog oppoReviewLog = new OppoReviewLog();
        oppoReviewLog.setReviewId(oppoReview.getId());
        oppoReviewLog.setOppoNumber(oppoReview.getOppoNumber());
        oppoReviewLog.setUpdateTime(DateUtils.formatDateTime(new Date()));
        oppoReviewLog.setCreateTime(DateUtils.formatDateTime(new Date()));
        oppoReviewLog.setNode("0");
        oppoReviewLog.setCreateName(user.getStaffName());
        oppoReviewLog.setCreateBy(user.getStaffId());
        oppoReviewLogService.save(oppoReviewLog);
//        return update + i;
        return "成功";
    }

    /**
     * 计算用时时间(排除休息日)
     * @param oppoReview
     */
    private void setDealTime(String dayId, OppoReview oppoReview) {
        // 解析 dayId 为 LocalDate (格式: "yyyyMMdd")。
        LocalDate date = LocalDate.parse(dayId, DateTimeFormatter.BASIC_ISO_DATE);
        // 当天早上 9:00
        LocalDateTime nineAM = date.plusDays(1).atTime(9, 0, 0);
        // 当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算精确的小时差（如 3.5 小时）
        HolidayQueryVo holidayQueryVo = new HolidayQueryVo();
        double exactHours = holidayService.calculateWorkingHours(nineAM, now, holidayQueryVo);
        String formattedHours = String.format("%.1f", exactHours);
        oppoReview.setDealTimeM(formattedHours);
    }


    /*
     * <AUTHOR>
     * @Description 查询详情信息
     * @Date 2025-06-26
     * @param id
     * @return ZqOppoPools
     **/
    @Override
    public ZqOppoPoolsVo findOne(String oppoNumber) {
        return baseMapper.findOne(oppoNumber);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-26
     * @return List<ZqOppoPools>
     **/
    @Override
    public List<ZqOppoPoolsVo> findList(ZqOppoPoolsQueryVo zqOppoPoolsVo) {
        return baseMapper.findList(zqOppoPoolsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-26
     * @param zqOppoPools
     * @return int
     **/
    @Override
    public int add(ZqOppoPools zqOppoPools) {
        return baseMapper.insert(zqOppoPools);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-26
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 营销经理查看商机统计
     * @return
     */
    @Override
//    @DataScope(cityQuery = @DataScopeQuery(required = true, column = "zop.city"))
    public Map<String, String> firstTotal(ZqOppoPoolsQueryVo zqOppoPoolsVo) {
        return baseMapper.firstTotal(zqOppoPoolsVo);
    }

}
