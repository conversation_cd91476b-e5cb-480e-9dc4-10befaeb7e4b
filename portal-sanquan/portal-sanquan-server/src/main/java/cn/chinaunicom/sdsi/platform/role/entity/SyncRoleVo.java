package cn.chinaunicom.sdsi.platform.role.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 同步角色，人员信息
 *
 * 中台下发到t_sanquan_permission_role_user， 同步到，t_sanquan_platform_user_role及相关表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SyncRoleVo {

    String id;

    /* 三全系统编码 */
    String roleCodeSys;

    /* 三全系统名称 */
    String roleNameSys;

    /* 中台角色 */
    String roleCodeRemote;

    /**/
    String roleNameRemote;

    /* 执行sql */
    String executeSql;

    /* 执行类型，1，同步中台表 t_sanquan_permission_user_role */
    String executeType;

    /* 任务类型，update，insert，select */
    String taskType;

    /* 执行顺序 */
    String level;

    /* status，1有效，2无效 */
    String status;

    /**/
    String remark;

}
