package cn.chinaunicom.sdsi.leaderVisit.audit.service;

import cn.chinaunicom.sdsi.cloud.leaderVisit.audit.entity.LeaderVisitAudit;
import cn.chinaunicom.sdsi.cloud.leaderVisit.audit.query.LeaderVisitAuditQueryVo;
import cn.chinaunicom.sdsi.cloud.leaderVisit.audit.vo.LeaderVisitAuditVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
/**
 * <p>
 * 拜访审批明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface LeaderVisitAuditService extends IService<LeaderVisitAudit> {

    // 分页查询
    IPage<LeaderVisitAuditVo> findPage(LeaderVisitAuditQueryVo leaderVisitAuditVo);

    // 根据id查询
    LeaderVisitAudit findOne(String id);

    // 查询列表
    List<LeaderVisitAudit> findList(LeaderVisitAuditQueryVo leaderVisitAuditVo);

    // 新增
    int add(LeaderVisitAudit leaderVisitAudit);

    // 修改
    int update(LeaderVisitAudit leaderVisitAudit);

    // 删除
    int delete(String id);

    // 定时发送提醒（客户经理提醒、地市接口人提醒）
    void sendRemind();
}
