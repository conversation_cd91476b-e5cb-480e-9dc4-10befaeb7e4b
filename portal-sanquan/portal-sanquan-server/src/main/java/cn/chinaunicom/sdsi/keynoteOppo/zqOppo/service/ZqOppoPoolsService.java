package cn.chinaunicom.sdsi.keynoteOppo.zqOppo.service;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoPools;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.query.ZqOppoPoolsQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.vo.ZqOppoPoolsVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 政企中台商机 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
public interface ZqOppoPoolsService extends IService<ZqOppoPools> {

    // 分页查询
    IPage<ZqOppoPoolsVo> findPage(ZqOppoPoolsQueryVo zqOppoPoolsVo);

    // 修改
    int update(ZqOppoPools zqOppoPools);

    // 反馈
    String feedback(ZqOppoPoolsVo zqOppoPools);

    // 根据id查询
    ZqOppoPoolsVo findOne(String oppoNumber);

    // 查询列表
    List<ZqOppoPoolsVo> findList(ZqOppoPoolsQueryVo zqOppoPoolsVo);

    // 新增
    int add(ZqOppoPools zqOppoPools);

    // 删除
    int delete(String id);

    // 营销经理查看商机统计
    Map<String, String> firstTotal(ZqOppoPoolsQueryVo zqOppoPoolsVo);

    String getMaxDate();
}
