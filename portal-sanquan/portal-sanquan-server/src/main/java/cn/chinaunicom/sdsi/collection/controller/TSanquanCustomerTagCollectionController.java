package cn.chinaunicom.sdsi.collection.controller;

import cn.chinaunicom.sdsi.collection.vo.TSanquanCustomerTagCollectionVO;
import cn.chinaunicom.sdsi.collection.queryvo.TSanquanCustomerTagCollectionQueryVO;
import cn.chinaunicom.sdsi.collection.service.TSanquanCustomerTagCollectionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 客户标签汇总表(采集表) 控制器
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@RestController
@RequestMapping("/collection/collection")
public class TSanquanCustomerTagCollectionController extends BaseController {

    @Autowired
    private TSanquanCustomerTagCollectionService tSanquanCustomerTagCollectionService;

    /*
     * <AUTHOR>
     * @description 分页查询客户标签汇总表(采集表)
     * @since 2024-07-08
     * @param tSanquanCustomerTagCollectionQueryVO
     * @return BasePageResponse<TSanquanCustomerTagCollectionVO>
     **/
    @Operation(summary = "分页查询客户标签汇总表(采集表)", description ="分页查询客户标签汇总表(采集表)数据")
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanCustomerTagCollectionVO> findPage(TSanquanCustomerTagCollectionQueryVO tSanquanCustomerTagCollectionQueryVO) {
        return pageOk(tSanquanCustomerTagCollectionService.findPage(tSanquanCustomerTagCollectionQueryVO));
    }

    /*
     * <AUTHOR>
     * @description 查询客户标签汇总表(采集表)详细信息
     * @since 2024-07-08
     * @param customerId
     * @return BaseResponse<TSanquanCustomerTagCollectionVO>
     **/
    @Operation(summary = "查询客户标签汇总表(采集表)详细信息", description ="查询客户标签汇总表(采集表)详细信息")
    @GetMapping("/findInfo")
    public BaseResponse<TSanquanCustomerTagCollectionVO> findInfo(String customerId) {
        return ok(tSanquanCustomerTagCollectionService.findInfo(customerId));
    }

    /*
     * <AUTHOR>
     * @description 新增客户标签汇总表(采集表)
     * @since 2024-07-08
     * @param tSanquanCustomerTagCollectionVO
     * @return BaseResponse<String>
     **/
    @Operation(summary = "新增客户标签汇总表(采集表)", description ="新增客户标签汇总表(采集表)")
    @PostMapping("/add")
    public BaseResponse<String> add(@RequestBody TSanquanCustomerTagCollectionVO tSanquanCustomerTagCollectionVO) {
        return ok(tSanquanCustomerTagCollectionService.add(tSanquanCustomerTagCollectionVO));
    }

    /*
     * <AUTHOR>
     * @description 修改客户标签汇总表(采集表)
     * @since 2024-07-08
     * @param tSanquanCustomerTagCollectionVO
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "修改客户标签汇总表(采集表)", description ="修改客户标签汇总表(采集表)")
    @PostMapping("/update")
    public BaseResponse<Boolean> update(@RequestBody TSanquanCustomerTagCollectionVO tSanquanCustomerTagCollectionVO) {
        return ok(tSanquanCustomerTagCollectionService.update(tSanquanCustomerTagCollectionVO));
    }

    /*
     * <AUTHOR>
     * @description 删除客户标签汇总表(采集表)
     * @since 2024-07-08
     * @param customerId
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "删除客户标签汇总表(采集表)", description ="删除客户标签汇总表(采集表)")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String customerId) {
        return ok(tSanquanCustomerTagCollectionService.delete(customerId));
    }

}
