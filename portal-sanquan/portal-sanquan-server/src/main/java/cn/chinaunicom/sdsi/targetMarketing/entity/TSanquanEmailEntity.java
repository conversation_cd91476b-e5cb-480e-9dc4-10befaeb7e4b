package cn.chinaunicom.sdsi.targetMarketing.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 推送合约到期客户邮件
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class TSanquanEmailEntity {

    @Schema(name = "地市名称")
    @ExcelProperty(value = "地市名称")
    @ColumnWidth(20)
    private String cityName;

    @Schema(name = "客户名称")
    @ExcelProperty(value = "客户名称")
    @ColumnWidth(40)
    private String customerName;

    @Schema(name = "客户级别")
    @ExcelProperty(value = "客户级别")
    @ColumnWidth(20)
    private String customerLevel;

    @Schema(name = "靶向营销类型")
    @ExcelProperty(value = "靶向营销类型")
    @ColumnWidth(15)
    private String workOrderType;

    @Schema(name = "到期月份")
    @ExcelProperty(value = "到期月份")
    @ColumnWidth(15)
    private String expireMonth;

    @Schema(name = "到期用户数")
    @ExcelProperty(value = "到期用户数")
    @ColumnWidth(15)
    private String expireNum;

    @Schema(name = "客户经理")
    @ExcelProperty(value = "客户经理")
    @ColumnWidth(20)
    private String managerName;

    @Schema(name = "行业")
    @ExcelProperty(value = "行业")
    @ColumnWidth(15)
    private String industry;

    @Schema(name = "业务号码")
    @ExcelProperty(value = "业务号码")
    @ColumnWidth(60)
    private String businessCode;


}
