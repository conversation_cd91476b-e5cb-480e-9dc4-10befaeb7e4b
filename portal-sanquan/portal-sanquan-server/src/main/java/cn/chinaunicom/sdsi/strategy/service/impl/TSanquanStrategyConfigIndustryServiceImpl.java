package cn.chinaunicom.sdsi.strategy.service.impl;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrategyConfigIndustry;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrategyConfigIndustryVO;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrategyConfigIndustryQuery;
import cn.chinaunicom.sdsi.strategy.service.TSanquanStrategyConfigIndustryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.chinaunicom.sdsi.strategy.dao.TSanquanStrategyConfigIndustryMapper;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;

/**
 * 策略配置行业表业务实现类
 * 
 * <AUTHOR>
 * @date 2024-05-26
 */
@Service
public class TSanquanStrategyConfigIndustryServiceImpl extends ServiceImpl<TSanquanStrategyConfigIndustryMapper, TSanquanStrategyConfigIndustry> implements TSanquanStrategyConfigIndustryService {

    @Autowired
    private UnifastContext unifastContext;

    /**
     * 分页查询策略配置行业表
     * 
     * @param tSanquanStrategyConfigIndustryQueryVO
     * @return IPage<TSanquanStrategyConfigIndustryVO>
     */
    @Override
    public IPage<TSanquanStrategyConfigIndustryVO> findPage(TSanquanStrategyConfigIndustryQuery tSanquanStrategyConfigIndustryQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanStrategyConfigIndustryQueryVO);
        return baseMapper.findPage(page, tSanquanStrategyConfigIndustryQueryVO);
    }

    /**
     * 查询策略配置行业表详细信息
     * 
     * @param id
     * @return TSanquanStrategyConfigIndustryVO
     */
    @Override
    public TSanquanStrategyConfigIndustryVO findInfo(String id) {
        TSanquanStrategyConfigIndustry tSanquanStrategyConfigIndustry = baseMapper.selectById(id);
        TSanquanStrategyConfigIndustryVO tSanquanStrategyConfigIndustryVO = new TSanquanStrategyConfigIndustryVO();
        BeanUtils.copyProperties(tSanquanStrategyConfigIndustry, tSanquanStrategyConfigIndustryVO);
        return tSanquanStrategyConfigIndustryVO;
    }

    /**
     * 新增策略配置行业表
     * 
     * @param tSanquanStrategyConfigIndustryVO
     * @return String
     */
    @Override
    public String add(TSanquanStrategyConfigIndustryVO tSanquanStrategyConfigIndustryVO) {
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
//            tSanquanStrategyConfigIndustryVO.setTenantId(tenantId);
        }
         TSanquanStrategyConfigIndustry tSanquanStrategyConfigIndustry = new TSanquanStrategyConfigIndustry();
         BeanUtils.copyProperties(tSanquanStrategyConfigIndustryVO, tSanquanStrategyConfigIndustry);
         baseMapper.insert(tSanquanStrategyConfigIndustry);
         return tSanquanStrategyConfigIndustry.getId();
    }

    /**
     * 修改策略配置行业表
     * 
     * @param tSanquanStrategyConfigIndustryVO
     * @return Boolean
     */
    @Override
    public Boolean update(TSanquanStrategyConfigIndustryVO tSanquanStrategyConfigIndustryVO) {
        TSanquanStrategyConfigIndustry tSanquanStrategyConfigIndustry = new TSanquanStrategyConfigIndustry();
        BeanUtils.copyProperties(tSanquanStrategyConfigIndustryVO, tSanquanStrategyConfigIndustry);
        return baseMapper.updateById(tSanquanStrategyConfigIndustry) > 0;
    }

    /**
     * 删除策略配置行业表
     * 
     * @param id
     * @return Boolean
     */
    @Override
    public Boolean delete(String id) {
        return baseMapper.deleteById(id) > 0;
    }

}
