package cn.chinaunicom.sdsi.customerPicture.service;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileBusinessInfo;
import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 三全客户画像-客户业务信息-收入金额表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
public interface CustomerProfileBusinessInfoService extends IService<CustomerProfileBusinessInfo> {

    // 根据客户id查看年累计收入
    CustomerProfileBusinessInfo getBusinessYearTotalFee(CustomerPictureVo businessInfo);



}
