package cn.chinaunicom.sdsi.holiday.mapper;

import cn.chinaunicom.sdsi.cloud.holiday.entity.Holiday;
import cn.chinaunicom.sdsi.cloud.holiday.query.HolidayQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 节假日表（包含周末、法定假日、调休等） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Mapper
public interface HolidayMapper extends BaseMapper<Holiday> {

    // 分页查询
    IPage<Holiday> findPage(@Param("page") IPage page, @Param("query") HolidayQueryVo holidayVo);

    // 查询列表
    List<Holiday> findList(@Param("query") HolidayQueryVo holidayVo);
}
