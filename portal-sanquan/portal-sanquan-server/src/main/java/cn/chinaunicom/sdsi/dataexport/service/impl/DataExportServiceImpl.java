package cn.chinaunicom.sdsi.dataexport.service.impl;

import cn.chinaunicom.sdsi.aiflow.entity.AiflowTaskData;
import cn.chinaunicom.sdsi.dataexport.dto.DataExportQueryDTO;
import cn.chinaunicom.sdsi.dataexport.entity.DataExport;
import cn.chinaunicom.sdsi.dataexport.mapper.DataExportMapper;
import cn.chinaunicom.sdsi.dataexport.service.IDataExportService;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
public class DataExportServiceImpl implements IDataExportService {

    @Autowired
    private DataExportMapper dataExportMapper;

    /**
     * 查询数据导出列表
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    @Override
    public IPage<DataExport> selectDataExportList(DataExportQueryDTO param) {
        IPage<DataExport> page = QueryVoToPageUtil.toPage(param);
        return dataExportMapper.selectDataExportList(page, param);
    }

    /**
     * 导出数据
     *
     * @param param    查询参数
     */
    @Override
    public void exportDataExport(DataExportQueryDTO param) {
        try {
            List<DataExport> dataList = dataExportMapper.selectAllDataExport(param);
            ExcelUtils.exportExcel(dataList, DataExport.class, "数据导出列表", "sheet1");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
