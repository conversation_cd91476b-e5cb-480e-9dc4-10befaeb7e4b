package cn.chinaunicom.sdsi.platform.role.service;

import cn.chinaunicom.sdsi.platform.role.entity.RoleMenu;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface IRoleMenuService extends IService<RoleMenu> {
    /**
     * 批量插入
     *
     * <AUTHOR>
     * @Date 2024/7/22 13:47
     */
    void insertBatchSomeColumn(@Param("list") List<RoleMenu> batchList);


    void deleteByRoleId(@Param("roleId") String roleId);
}
