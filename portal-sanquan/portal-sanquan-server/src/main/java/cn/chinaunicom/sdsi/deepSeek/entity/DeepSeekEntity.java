package cn.chinaunicom.sdsi.deepSeek.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 *三全客户 deepSeek 数据
 *
 * <AUTHOR>
 * @since  2024-10-24
 */
@Data
@TableName("t_sanquan_deepseek")
public class DeepSeekEntity {

    private static final long serialVersionUID = 1L;

    Long id;

    String userId;

    String content;

    String pushContent;

    Date createTime;

}
