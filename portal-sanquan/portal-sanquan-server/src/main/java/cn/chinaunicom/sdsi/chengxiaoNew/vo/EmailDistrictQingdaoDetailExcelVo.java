package cn.chinaunicom.sdsi.chengxiaoNew.vo;

import cn.chinaunicom.sdsi.chengxiaoNew.vo.qingdao.EmailFeebackDynamicVo;
import cn.chinaunicom.sdsi.chengxiaoNew.vo.qingdao.EmailFeebackStaticVo;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 青岛区县领导 工单靶向任务明细
 * @version 1.0
 * @data 2024/12/26 11:12
 */
@Data
//@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(30) // 标题单元格高度
public class EmailDistrictQingdaoDetailExcelVo implements Serializable {

    /*地市*/
    @ColumnWidth(40)
    @ExcelProperty(value = "策略名称")
    String marketingName;

    @ColumnWidth(30)
    @ExcelProperty(value = "任务id")
    String taskId;

    @ColumnWidth(15)
    @ExcelProperty(value = "策略创建人")
    String marketCreator;

    @ColumnWidth(30)
    @ExcelProperty(value = "客户名称")
    String natureCustName;

    @ColumnWidth(15)
    @ExcelProperty(value = "省分行业")
    String provinceIndustryName;

    @ColumnWidth(15)
    @ExcelProperty(value = "客户地市")
    String cityName;

    @ColumnWidth(20)
    @ExcelProperty(value = "执行人组织")
    String gridName;

    @ColumnWidth(10)
    @ExcelProperty(value = "执行人")
    String executorName;

    @ColumnWidth(15)
    @ExcelProperty(value = "执行人工号")
    String executorOa;

    @ColumnWidth(15)
    @ExcelProperty(value = "任务下发时间")
    String sendTime;

    /*1待执行，2已关单，3待改派，4跟进中，5到期未执行*/
    @ColumnWidth(12)
    @ExcelProperty(value = "任务状态")
    String status;

    @ColumnWidth(20)
    @ExcelProperty(value = "最新反馈情况")
    String visitResult;

    @ColumnWidth(18)
    @ExcelProperty(value = "反馈时间")
    String visitTime;

    @ColumnWidth(100)
    @ExcelProperty(value = "反馈信息")
    String feebackInfo;

    /* 静态反馈信息列表 */
    @ExcelIgnore
    List<EmailFeebackStaticVo> feebackInfoStatic;

    /* 动态反馈信息列表 */
    @ExcelIgnore
    List<EmailFeebackDynamicVo> feebackInfoDynamic;

    public String getVisitResult() {
        if(StringUtils.isNotEmpty(feebackInfoStatic) && feebackInfoStatic.size() > 0){
            if(StringUtils.isNotEmpty(feebackInfoStatic.get(0).getVisitSummarize()) && !"null".equals(feebackInfoStatic.get(0).getVisitSummarize()))
                return feebackInfoStatic.get(0).getVisitSummarize();
        }
        if(StringUtils.isNotEmpty(feebackInfoDynamic) && feebackInfoDynamic.size() > 0){
            for (EmailFeebackDynamicVo emailFeebackDynamicVo : feebackInfoDynamic) {
                if("反馈结果".equals(emailFeebackDynamicVo.getFieldName())){
                    return emailFeebackDynamicVo.getFieldValue();
                }
            }
        }
        return visitResult;
    }

    public String getVisitTime() {
        if(StringUtils.isNotEmpty(feebackInfoStatic) && feebackInfoStatic.size() > 0){
            return feebackInfoStatic.get(0).getBackTime();
        }
        if(StringUtils.isNotEmpty(feebackInfoDynamic) && feebackInfoDynamic.size() > 0){
            for (EmailFeebackDynamicVo emailFeebackDynamicVo : feebackInfoDynamic) {
                if("反馈时间".equals(emailFeebackDynamicVo.getFieldName())){
                    return emailFeebackDynamicVo.getFieldValue();
                }
            }
        }
        return visitTime;
    }

    public String getFeebackInfo() {
        if(StringUtils.isNotEmpty(feebackInfoStatic) && feebackInfoStatic.size() > 0){
            StringBuilder isStatic = new StringBuilder();
//            isStatic.append("[");
            int i=1;
            for (EmailFeebackStaticVo emailFeebackStaticVo : feebackInfoStatic) {
               /* if(!"[".equals(isStatic.toString())){
                    isStatic.append(",");
                }*/
                if(feebackInfoStatic.size()>1){
                    isStatic.append(" "+(i++)+"：");
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitResult()) && !"null".equals(emailFeebackStaticVo.getVisitResult())){
                    isStatic.append("反馈结果:"+emailFeebackStaticVo.getVisitResult());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getBackTime()) && !"null".equals(emailFeebackStaticVo.getBackTime())){
                    isStatic.append(" ,反馈时间:"+emailFeebackStaticVo.getBackTime());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getIsContinueVisit()) && !"null".equals(emailFeebackStaticVo.getIsContinueVisit())){
                    isStatic.append(" ,是否继续拜访:"+emailFeebackStaticVo.getIsContinueVisit());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getNoVisitReason()) && !"null".equals(emailFeebackStaticVo.getNoVisitReason())){
                    isStatic.append(" ,无法执行原因:"+emailFeebackStaticVo.getNoVisitReason());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getNextTime()) && !"null".equals(emailFeebackStaticVo.getNextTime())){
                    isStatic.append(" ,下次执行时间:"+emailFeebackStaticVo.getNextTime());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitTheme()) && !"null".equals(emailFeebackStaticVo.getVisitTheme())){
                    isStatic.append(" ,拜访主题:"+emailFeebackStaticVo.getVisitTheme());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitTime()) && !"null".equals(emailFeebackStaticVo.getVisitTime())){
                    isStatic.append(" ,拜访时间:"+emailFeebackStaticVo.getVisitTime());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitSite()) && !"null".equals(emailFeebackStaticVo.getVisitSite())){
                    isStatic.append(" ,拜访地点:"+emailFeebackStaticVo.getVisitSite());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitPerson()) && !"null".equals(emailFeebackStaticVo.getVisitPerson())){
                    isStatic.append(" ,访谈人姓名:"+emailFeebackStaticVo.getVisitPerson());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitPhone()) && !"null".equals(emailFeebackStaticVo.getVisitPhone())){
                    isStatic.append(" ,访谈人联系方式:"+emailFeebackStaticVo.getVisitPhone());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitContent()) && !"null".equals(emailFeebackStaticVo.getVisitContent())){
                    isStatic.append(" ,访谈内容:"+emailFeebackStaticVo.getVisitContent());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getVisitSummarize()) && !"null".equals(emailFeebackStaticVo.getVisitSummarize())){
                    isStatic.append(" ,拜访总结:"+emailFeebackStaticVo.getVisitSummarize());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getKeyRecommendedProduct()) && !"null".equals(emailFeebackStaticVo.getKeyRecommendedProduct())){
                    isStatic.append(" ,重点推荐产品:"+emailFeebackStaticVo.getKeyRecommendedProduct());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getIsBusiOppWilling()) && !"null".equals(emailFeebackStaticVo.getIsBusiOppWilling())){
                    isStatic.append(" ,是否有商机意向:"+emailFeebackStaticVo.getIsBusiOppWilling());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getBusiOpptName()) && !"null".equals(emailFeebackStaticVo.getBusiOpptName())){
                    isStatic.append(" ,商机名称:"+emailFeebackStaticVo.getBusiOpptName());
                }
                if(StringUtils.isNotEmpty(emailFeebackStaticVo.getBusinessType()) && !"null".equals(emailFeebackStaticVo.getBusinessType())){
                    isStatic.append(" ,业务类型:"+emailFeebackStaticVo.getBusinessType());
                }

//                isStatic.append("}");
            }
//            isStatic.append("]");
            return isStatic.toString();
        }
        if(StringUtils.isNotEmpty(feebackInfoDynamic) && feebackInfoDynamic.size() > 0){
            StringBuilder isDynamic = new StringBuilder();
//            isDynamic.append("{");
            for (EmailFeebackDynamicVo emailFeebackDynamicVo : feebackInfoDynamic) {
                /*if(!"{".equals(isDynamic.toString())){
                    isDynamic.append(" ,");
                }*/
                if(StringUtils.isNotEmpty(emailFeebackDynamicVo.getFieldValue()) && !"null".equals(emailFeebackDynamicVo.getFieldValue())){
                    isDynamic.append(emailFeebackDynamicVo.getFieldName()).append(":").append(emailFeebackDynamicVo.getFieldValue());
                }
            }
//            isDynamic.append("}");
            return isDynamic.toString();
        }
        return feebackInfo;
    }

    /*1待执行，2已关单，3待改派，4跟进中，5到期未执行*/
    public String getStatus() {
        if("1".equals(status)){
            return "待执行";
        }else if("2".equals(status)){
            return "已关单";
        }else if("3".equals(status)){
            return "待改派";
        }else if("4".equals(status)){
            return "跟进中";
        }else if("5".equals(status)){
            return "到期未执行";
        }
        return status;
    }
}
