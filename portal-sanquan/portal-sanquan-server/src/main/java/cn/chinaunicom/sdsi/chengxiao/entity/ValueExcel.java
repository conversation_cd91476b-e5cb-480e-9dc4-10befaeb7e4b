package cn.chinaunicom.sdsi.chengxiao.entity;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("ch_t_sanquan_chengxiao_zhuanhua_result")
public class ValueExcel implements Serializable {

    private static final long serialVersionUID = 1L;


    /*地市*/
    String cityName;

    /*场景，（靶向营销有）*/
    String busScene;

    /*任务数*/
    String taskNum;

    /*未执行数*/
    String weiZhixing;

    /*已执行数*/
    String zhixing;

    /*执行率*/
    String zhixingRate;

    /*转化数*/
    String zhuanhuaNum;

    /*转化率*/
    String zhuanhuaRate;


}
