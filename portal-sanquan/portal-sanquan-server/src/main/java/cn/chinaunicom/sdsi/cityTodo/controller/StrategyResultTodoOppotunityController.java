package cn.chinaunicom.sdsi.cityTodo.controller;


import cn.chinaunicom.sdsi.cityTodo.service.IStrategyResultTodoOppotunityService;
import cn.chinaunicom.sdsi.cityTodo.vo.StrategyResultTodoOppoVo;
import cn.chinaunicom.sdsi.cityTodo.vo.SupportSituationResVo;
import cn.chinaunicom.sdsi.cityTodo.vo.SupportSituationVo;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkFeedbackVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@RestController
@RequestMapping("/strategy/result/todo/oppotunity")
public class StrategyResultTodoOppotunityController extends BaseController {

    @Autowired
    private IStrategyResultTodoOppotunityService iStrategyResultTodoOppotunityService;

    /**
     * 根据工单待办表id查询工单详情信息
     * @param todoOppoId
     * @return
     */
    @GetMapping("/getOppoWorkInfoById")
    @Operation(summary = "根据工单待办表id查询工单详情信息", description = "根据工单待办表id查询工单详情信息")
    public BaseResponse<TSanquanWorkFeedbackVO> getOppoWorkInfoById(String todoOppoId){
        return ok(iStrategyResultTodoOppotunityService.getOppoWorkInfoById(todoOppoId));
    }

    /**
     * 支撑情况统计（营销经理）
     * @param supportSituationVo
     * @return
     */
    @GetMapping("/getSupportSituation")
    @Operation(summary = "支撑情况（营销经理）", description = "支撑情况（营销经理）")
    public BasePageResponse<SupportSituationResVo> getSupportSituation(SupportSituationVo supportSituationVo){
        return pageOk(iStrategyResultTodoOppotunityService.getSupportSituation(supportSituationVo));
    }

    /**
     * 支撑人员情况统计（营销经理）
     * @param supportSituationVo
     * @return
     */
    @GetMapping("/getSupportStaffSituation")
    @Operation(summary = "支撑情况（营销经理）", description = "支撑情况（营销经理）")
    public BasePageResponse<SupportSituationResVo> getSupportStaffSituation(SupportSituationVo supportSituationVo){
        return pageOk(iStrategyResultTodoOppotunityService.getSupportStaffSituation(supportSituationVo));
    }


    /**
     * 导出支撑情况统计
     * @param query 查询条件
     */
    @Operation(summary = "导出支撑情况统计", description = "导出支撑情况统计")
    @PostMapping("/exportSupportSituation")
    public void exportSupportSituation(@RequestBody SupportSituationVo query) {
        iStrategyResultTodoOppotunityService.exportSupportSituation(query);
    }

    /**
     * 导出支撑人员情况统计
     * @param query 查询条件
     */
    @Operation(summary = "支撑人员情况统计", description = "支撑人员情况统计")
    @PostMapping("/exportSupportStaffSituation")
    public void exportSupportStaffSituation(@RequestBody SupportSituationVo query) {
        iStrategyResultTodoOppotunityService.exportSupportStaffSituation(query);
    }


    /**
     * 支撑人员情况统计详情信息（营销经理）
     * @param supportSituationVo
     * @return
     */
    @GetMapping("/supportStaffSituationPageInfo")
    @Operation(summary = "支撑人员情况统计详情信息（营销经理）", description = "支撑人员情况统计详情信息（营销经理）")
    public BasePageResponse<StrategyResultTodoOppoVo> supportStaffSituationPageInfo(SupportSituationVo supportSituationVo){
        return pageOk(iStrategyResultTodoOppotunityService.supportStaffSituationPageInfo(supportSituationVo));
    }
}
