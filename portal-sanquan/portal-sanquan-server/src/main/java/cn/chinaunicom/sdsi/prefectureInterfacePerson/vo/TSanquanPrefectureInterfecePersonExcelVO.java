package cn.chinaunicom.sdsi.prefectureInterfacePerson.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 地市接口人对象
 * @Author: dzk
 * @Date: 2024-05-31
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class TSanquanPrefectureInterfecePersonExcelVO {

    @ColumnWidth(20)
    @ExcelProperty(value = "工号")
    private String jobNumber;

    @ColumnWidth(20)
    @ExcelProperty(value = "姓名")
    private String name;

    @ColumnWidth(20)
    @ExcelProperty(value = "地市接口人电话")
    private String tel;

    @ColumnWidth(20)
    @ExcelProperty(value = "所属行业")
    private String industry;

    @ColumnWidth(20)
    @ExcelProperty(value = "省分（省名称）")
    private String listCustomerProvince;

    @ColumnWidth(20)
    @ExcelProperty(value = "所在地市（地市名称）")
    private String listCustomerCity;

    @ColumnWidth(20)
    @ExcelProperty(value = "所在区县（区县名称）")
    private String listCustomerDistrict;

    @ColumnWidth(20)
    @ExcelProperty(value = "客户等级（1.地市、2.区县副总）")
    private String listCustomerLevel;

    @ColumnWidth(20)
    @ExcelProperty(value = "审核权限（0.不审核、1.审核）")
    private String type;
}
