package cn.chinaunicom.sdsi.emailConfig.service;

import cn.chinaunicom.sdsi.emailConfig.entity.EmailConfig;
import cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


@Service
public interface EmailDataService {

    /* 组织涉及的表格数据，excel数据 */
    List<List> getDataList(EmailConfig config, EmailPersonVO personVO);

    /**
     * @param list excel数据与邮件正文不一致时，自行查询，
     * @return
     */
    void buildExcelFile(EmailConfig config, EmailPersonVO personVO,List<List> list);

    /* 返回错误信息 */
    String getErrorStr();

    /* 数据库配置无法满足时，通过此接口返回邮件正文简介。数据库t_sanquan_email_config的emailContent字段，配置含有${getEmailContent}，表示应用此方法获取 */
    String getEmailContent(EmailConfig config, EmailPersonVO personVO,List<List> list);

    /**
     * 渲染ftl模板自定义特殊参数
     * @return
     */
    Map<String,Object> getMyParam();

}
