package cn.chinaunicom.sdsi.product.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity.TSanquanPrefectureInterfacePerson;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanPersonnel;
import cn.chinaunicom.sdsi.cloud.product.query.PersonnelQuery;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.prefectureInterfacePerson.vo.TSanquanPrefectureInterfecePersonExcelVO;
import cn.chinaunicom.sdsi.product.dao.TSanquanPersonnelMapper;
import cn.chinaunicom.sdsi.product.service.TSanquanPersonnelService;
import cn.chinaunicom.sdsi.product.vo.PersonneExcelVo;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 产品支撑人信息
 *
 * <AUTHOR> 
 * @since  2024-04-20
 */
@Service
public class TSanquanPersonnelServiceImpl extends ServiceImpl<TSanquanPersonnelMapper, TSanquanPersonnel> implements TSanquanPersonnelService {

    /**
     * 根据工号查询当前人的信息
     * @param jobNumber
     * @return
     */
    @Override
    public TSanquanPersonnel findPersonnelJobNumber(String jobNumber) {
        return this.getOne(Wrappers.<TSanquanPersonnel>lambdaQuery().eq(TSanquanPersonnel::getJobNumber, jobNumber)
                .eq(TSanquanPersonnel::getDeleteFlag, "normal"));
    }

    /**
     * 根据工号删除当前人信息
     * @param id
     * @return
     */
    @Override
    public Boolean delete(String id) {
        TSanquanPersonnel personnelDb = this.getById(id);
        personnelDb.setDeleteFlag("deleted");
        return this.saveOrUpdate(personnelDb);
    }

    /**
     * 查询所有的经理信息
     *
     * @return
     */
    @Override
    public List<TSanquanPersonnel> findList(TSanquanPersonnel tSanquanPersonnel) {
        List<TSanquanPersonnel> tSanquanPersonnels = this.baseMapper.getList(tSanquanPersonnel);
        return tSanquanPersonnels;
    }

    /**
     * 分页查询
     * @param query
     * @return
     */
    @Override
    public IPage<TSanquanPersonnel> findPage(PersonnelQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return this.baseMapper.findPage(page, query);
    }

    /**
     * 导入人员信息
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importData(MultipartFile file) {
        try {
            // 1、解析Excel文件并转换为实体对象列表
            List<PersonneExcelVo> excelData = ExcelUtils.importExcel(file, PersonneExcelVo.class);
            // 2、已经存在的数据
            List<PersonneExcelVo> existData = new ArrayList<>();
            // 不存在的数据
            List<TSanquanPersonnel> noExistData = new ArrayList<>();
            // 数据处理
            for (PersonneExcelVo excelDatum : excelData) {
                if (excelDatum != null && StringUtils.isNotEmpty(excelDatum.getJobNumber())) {
                    TSanquanPersonnel tSanquanPersonnelDb = this.getOne(Wrappers.<TSanquanPersonnel>lambdaQuery()
                            .eq(TSanquanPersonnel::getJobNumber, excelDatum.getJobNumber())
                            .eq(TSanquanPersonnel::getDeleteFlag, "normal"));
                    TSanquanPersonnel tSanquanPersonnel = new TSanquanPersonnel();
                    BeanUtils.copyProperties(excelDatum, tSanquanPersonnel);
                    if(tSanquanPersonnelDb != null){
                        tSanquanPersonnel.setId(tSanquanPersonnelDb.getId());
                    }
                    if(!StringUtils.isNotEmpty(tSanquanPersonnel.getJobType())){
                        tSanquanPersonnel.setJobType("0");
                    }
                    noExistData.add(tSanquanPersonnel);
                }
            }
            // 批量添加
            this.saveOrUpdateBatch(noExistData);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 导出人员模版
     */
    @Override
    public void exportData() {
        try {
            // 3、导出数据
            ExcelUtils.exportExcel(null, PersonneExcelVo.class, "模板", "人员信息");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据用户表中的数据添加产品使用的人员信息
     * @param userList
     * @return
     */
    @Override
    public Boolean addUserToPersonnel(List<UserVo> userList) {
        if (CollectionUtils.isNotEmpty(userList)) {
            // 找出需要新增的用户列表
            List<TSanquanPersonnel> personnelList = userList.stream()
                    .map(item -> {
                        // 工号存在的不进行添加
                        TSanquanPersonnel personnelJobNumber = findPersonnelJobNumber(item.getJobNum());
                        if (personnelJobNumber == null) {
                            TSanquanPersonnel personnel = new TSanquanPersonnel();
                            personnel.setJobNumber(item.getJobNum());
                            personnel.setPhone(item.getPhone());
                            personnel.setDepartmentId(item.getDeptId());
                            personnel.setName(item.getName());
                            personnel.setJobType("0");
                            personnel.setPrefecture(item.getCity());
                            personnel.setRegion(item.getCounty());
                            personnel.setCreateDate(new Date());
                            personnel.setCreateBy(UserUtils.getUser().getLoginName());
                            return personnel;
                        }
                        return null;
                    })
                    .filter(personnel -> personnel != null)
                    .collect(Collectors.toList());

            // 批量保存
            if (CollectionUtils.isNotEmpty(personnelList)) {
                saveBatch(personnelList);
            }
        }
        return true;
    }
}