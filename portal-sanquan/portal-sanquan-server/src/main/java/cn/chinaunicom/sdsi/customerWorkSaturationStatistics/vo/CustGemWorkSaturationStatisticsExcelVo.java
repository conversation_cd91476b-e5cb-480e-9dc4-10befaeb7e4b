package cn.chinaunicom.sdsi.customerWorkSaturationStatistics.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 客户经理工单饱和度统计
 * </p>
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class CustGemWorkSaturationStatisticsExcelVo implements Serializable {

    /**
     * 执行人OA（客户经理OA）
     */
    @ExcelProperty(value = "客户经理OA")
    private String managerNo;

    /**
     * 执行人名称（客户经理名称）
     */
    @ExcelProperty(value = "客户经理名称")
    private String managerName;

    /**
     * 执行人业务号码（客户经理电话）
     */
    @ExcelProperty(value = "客户经理电话")
    private String managerMobile;

    /**
     * 总任务数
     */
    @ExcelProperty(value = "总任务数")
    private String totals;

    /**
     * 待执行数量(状态1)
     */
    @ExcelProperty(value = "待执行数量")
    private String pendingTasks;

    /**
     * 已关单数量(状态2)
     */
    @ExcelProperty(value = "已关单数量")
    private String closedTasks;

    /**
     * 待改派数量(状态3)
     */
    @ExcelProperty(value = "待改派数量")
    private String reassignTasks;

    /**
     * 跟进中数量(状态4)
     */
    @ExcelProperty(value = "跟进中数量")
    private String followingTasks;

    /**
     * 到期未执行数量(状态5)
     */
    @ExcelProperty(value = "到期未执行数量")
    private String expiredTasks;

    @ExcelProperty(value = "账期月")
    private String monthId;

    @ExcelProperty(value = "账期天")
    private String dayId;

    /**
     * 饱和度
     */
    @ExcelProperty(value = "饱和度")
    private String saturation;


}
