package cn.chinaunicom.sdsi.platform.system.controller;

import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.platform.service.entity.SysLogininfor;
import cn.chinaunicom.sdsi.platform.service.query.SysLogininforQuery;
import cn.chinaunicom.sdsi.platform.system.service.ISysLogininforService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统访问记录
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/logininfor")
public class SysLogininforController extends BaseController
{
    @Autowired
    private ISysLogininforService logininforService;

    /**
     * 分页查询
     * @param logininforQuery
     * @return
     */
    @GetMapping("/findPage")
    public BasePageResponse<SysLogininfor> findPage(SysLogininforQuery logininforQuery)
    {
        return pageOk(logininforService.findPage(logininforQuery));
    }

    @GetMapping("/list")
    public BaseResponse<List<SysLogininfor>> list(SysLogininforQuery logininfor)
    {
        List<SysLogininfor> list = logininforService.selectLogininforList(logininfor);
        return ok(list);
    }

    /**
     * 新增系统登录日志
     * @param logininfor
     * @return
     */
    @PostMapping("/add")
    public Integer add(@RequestBody SysLogininfor logininfor) {
        return logininforService.insertLogininfor(logininfor);
    }

    /**
     * 导出系统登录日志
     * @param logininfor
     */
    @PostMapping("/export")
    public void export(SysLogininforQuery logininfor)
    {
        logininforService.exportData(logininfor);
    }

    @GetMapping("/delete/{infoIds}")
    public BaseResponse<Boolean> remove(@PathVariable Long[] infoIds)
    {
        return ok(logininforService.deleteLogininforByIds(infoIds));
    }

    @GetMapping("/clean")
    public BaseResponse clean()
    {
        logininforService.cleanLogininfor();
        return ok("success");
    }

}
