package cn.chinaunicom.sdsi.customer.dao;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerIdc;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerIdcQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerIdcVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* IDC需求摸查-客户资料
*
* <AUTHOR> 
* @since  2024-05-14
*/
@Mapper
public interface TSanquanCustomerIdcMapper extends BaseMapper<TSanquanCustomerIdc> {

    List<TSanquanCustomerIdcVO> selectByCustomerIdList(@Param("id") String id);

    IPage<TSanquanCustomerIdc> findPage(@Param("page") IPage page,@Param("query") TSanquanCustomerIdcQuery tSanquanCustomerIdcQuery);

    List<TSanquanCustomerIdc> findList(@Param("query") TSanquanCustomerIdcQuery query);
}