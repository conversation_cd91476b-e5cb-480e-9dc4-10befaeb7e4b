package cn.chinaunicom.sdsi.dingding.controller;

import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportConfig;
import cn.chinaunicom.sdsi.dingding.entity.DayProductYXMarketingManagerInfo;
import cn.chinaunicom.sdsi.common.entity.PoiExcelEntity;
import cn.chinaunicom.sdsi.dingding.service.DayProductYXMarketingManagerInfoService;
import cn.chinaunicom.sdsi.dingding.util.DingDingPushMes;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingManagerInfoQueryVo;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingManagerInfoVo;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkApiFactory;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkApiService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.OsUtil;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.chinaunicom.sdsi.tourism.util.TemplateFacory;
import cn.chinaunicom.sdsi.util.excel.ExcelPoiUtil;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.chinaunicom.sdsi.util.word.ExcelToImgSanquanUtil3;
import cn.hutool.core.lang.UUID;
import com.alibaba.excel.ExcelWriter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钉钉推送产品营销日报，图片
 */
@Slf4j
@RestController
@RequestMapping("/dingdingDayProduct")
public class DingDingDayProductController extends BaseController {
    @Autowired
    DayProductYXMarketingManagerInfoService managerInfoService;

    @Autowired
    DingDingPushMes pushMesUtil;

    @Autowired
    private DingTalkApiFactory dingTalkApiFactory;

    String dingdingMsg = "";

    private static  String GROUP = "dayProduct";

    /**
     * 推送钉钉日报文件
     */
    @GetMapping("/pushDingDing")
    public BaseResponse pushDingDing(){
        log.info("pushDingDing推送钉钉日报触发");
        try{
            dingdingMsg = "";
            new TemplateFacory();// 初始化文件根目录
            String excelFileNameMarket = this.writeToFileMarket();
            String excelFileNameManager = this.writeToFileManager();

            if(StringUtils.isNotEmpty(excelFileNameMarket) && StringUtils.isNotEmpty(excelFileNameManager)){
                String imagePath1 = this.excelToImg(excelFileNameMarket,"market");
                String imagePath2 = this.excelToImg(excelFileNameManager,"manager");
                if(imagePath1 != null && imagePath2 != null){
                    List<String> imageUrls = Lists.newArrayList();
                    imageUrls.add(imagePath1);
                    imageUrls.add(imagePath2);
                    log.info("pushDingDing推送这里正常");
                    String filePath = ExcelToImgSanquanUtil3.mergeImage(imageUrls,"comparePicture"+new Date().getTime());
                    if(OsUtil.isWindows()){
                        try{
                            pushMesUtil.closeToken();
                            pushMesUtil.pushImgAndTextGroup(filePath,dingdingMsg,"dayProduct","2");
                        }catch (Exception e){
                            log.error("推送错误：",e);
                        }finally {
                            pushMesUtil.closeToken();
                        }
                    }else{
                        String BIZ_NAME = "王鑫日报推送";
                        DingTalkApiService dingTalkApiService = dingTalkApiFactory.getBaseService(false);
                        String batchId = UUID.fastUUID().toString();
                        StringBuilder builder = new StringBuilder();
                        builder.append(dingdingMsg);
                        String mediaId1 = dingTalkApiService.uploadResource(GROUP, "image", new File(filePath));
                        builder.append("\r\n\r\n").append("![](").append(mediaId1).append(")");
                        dingTalkApiService.sendMarkdownMessageToGroup(GROUP, "产品营销日报", dingdingMsg, batchId, BIZ_NAME, "王鑫产品营销日报推送");
                    }
                }else{
                    log.info("未正确生成图片");
                }
            }

        }catch (Exception e){
            log.error("系统错误",e);
            log.error(""+e.getStackTrace());
        }
        return ok("成功");
    }

    /**
     * 推送钉钉，先写入excel 文件， 策略表格
     * @return
     */
    public String writeToFileMarket(){
        ExcelWriter excelWriter = null;
        String excelFileName =  "产品营销-策略" + new Date().getTime() + ".xlsx";
        try{
            // 第一个表格，策略统计表
            List<DayProductYXMarketingManagerInfoVo> marketingStatics = managerInfoService.getMarketingStatics(null);
            marketingStatics.forEach(market->{
                if(StringUtils.isNotEmpty(market.getSendTime())){
                    StringBuilder str = new StringBuilder();
                    LocalDate localDate = LocalDate.parse(market.getSendTime());
                    String strDate = localDate.getMonthValue()+"月"+localDate.getDayOfMonth()+"日";

                    int workingDays = 0;
                    LocalDate currentDate = localDate;
                    while (!currentDate.isAfter(LocalDate.now())) {
                        // 排除周末
                        if (currentDate.getDayOfWeek() != DayOfWeek.SATURDAY && currentDate.getDayOfWeek() != DayOfWeek.SUNDAY) {
                            workingDays++;
                        }
                        currentDate = currentDate.plusDays(1);
                    }

                    str.append(strDate).append("下发").append(market.getTargetGdCn()).append("个工单,已派单").append(workingDays).append("天");
                    market.setRemark(str.toString());
                }
            });
            List<List<Object>> marketExcelDataList = getMarketTableData(marketingStatics);

            PoiExcelEntity excelEntity = new PoiExcelEntity();
            excelEntity.setData(marketExcelDataList);
            excelEntity.setFileName(excelFileName);
            excelEntity.setFilePath(TemplateFacory.UPLOAD_PATCH);
            excelEntity.setHeadData(getHeaderMarketFile());
            excelEntity.setColWidth((short) 10);
            excelEntity.setSheetName("按策略统计");
            excelEntity.setSheetIndex(0);
//            excelEntity.setRowHeight((short) (60*256));
            excelEntity.setMergeCellPos(Lists.newArrayList(new int[]{marketingStatics.size(),marketingStatics.size(),0,1}));

            ExcelPoiUtil poiUtil = new ExcelPoiUtil();
            poiUtil.factoryPoiExcel(excelEntity);
            Sheet sheet = poiUtil.getSheet(0);
            sheet.setColumnWidth(0,8*256);
            sheet.setColumnWidth(1,25*256);
            sheet.setColumnWidth(2,10*256);
            sheet.setColumnWidth(3,10*256);
            sheet.setColumnWidth(4,10*256);
            sheet.setColumnWidth(5,10*256);
            sheet.setColumnWidth(6,10*256);
            sheet.setColumnWidth(7,15*256);
            sheet.setColumnWidth(8,35*256);

            poiUtil.outPutFileExcel(excelEntity);
            return excelFileName;
        }catch (Exception e){
            log.error("系统异常:",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }
        return null;
    }

    /**
     * 营销经理表格
     * @param queryVo
     * @return
     */
    public String writeToFileManager(){
        ExcelWriter excelWriter = null;
        String excelFileName = "产品营销-营销经理" + new Date().getTime() + ".xlsx";
        try{
            ExcelPoiUtil poiUtil = new ExcelPoiUtil();
            // 第二个表格，营销经理维度统计数据
            List<DayProductYXMarketingManagerInfoVo> dataList2 = managerInfoService.findManagerTotalListDingDing(new DayProductYXMarketingManagerInfoQueryVo());
            List<DayProductYXMarketingManagerInfo> weekList = managerInfoService.getLastWeekDataByManager();
            Map<String, DayProductYXMarketingManagerInfo> map = weekList.stream()
                    .collect(Collectors.toMap(
                            DayProductYXMarketingManagerInfo::getMarketingManagerOa, // Key mapper
                            item -> item,                                  // Value mapper
                            (existing, replacement) -> existing           // Merge function（如果有重复key，保留现有的）
                    ));
            List<List<Object>> managerExcelDataList = getCustomerTableData(dataList2,map);
            // 组织推送信息
            getDingDingMSG(managerExcelDataList);

            PoiExcelEntity excelEntity = new PoiExcelEntity();
            excelEntity.setFileName(excelFileName);
            excelEntity.setFilePath(TemplateFacory.UPLOAD_PATCH);
            excelEntity.setHeadData(getHeaderMarketFile());
//            excelEntity.setColWidth((short) 15);
            excelEntity.setSheetName("按策略统计");
            excelEntity.setSheetIndex(0);
            excelEntity.setHeadData(getHeaderCustomerFile());
            excelEntity.setData(managerExcelDataList);
            excelEntity.setMergeCellPos(Lists.newArrayList(new int[]{0,1,0,0},new int[]{0,1,1,1},new int[]{0,0,2,4},new int[]{0,0,5,10}));
            poiUtil.factoryPoiExcel(excelEntity);
            Sheet sheet = poiUtil.getSheet(0);
            sheet.setColumnWidth(0,10*256);
            sheet.setColumnWidth(1,15*256);
            sheet.setColumnWidth(2,11*256);
            sheet.setColumnWidth(3,12*256);
            sheet.setColumnWidth(4,15*256);
            sheet.setColumnWidth(5,11*256);
            sheet.setColumnWidth(6,11*256);
            sheet.setColumnWidth(7,11*256);
            sheet.setColumnWidth(8,11*256);
            sheet.setColumnWidth(9,11*256);
            sheet.setColumnWidth(10,15*256);

            poiUtil.outPutFileExcel(excelEntity);
            return excelFileName;
        }catch (Exception e){
            log.error("系统异常:",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }
        return null;
    }

    //    @GetMapping("/excelToImg")
    public String excelToImg(String excelFileName,String title){
        String imageName = "imageDayProduct_"+title+new Date().getTime()+".png";
        try {
            HashMap<String, Object> map = new HashMap<>();
            map.put("FitToPagesWide", 5);
            map.put("FitToPagesTall", 15);
//            map.put("dpiX", 400);
//            map.put("dpiY", 400);
            String imagePath = ExcelToImgSanquanUtil3.convertExcelToImage(excelFileName, imageName,0,map);
            if(!"error".equals(imagePath)){
                return imagePath;
            }
            return null;
        } catch (Exception e) {
            log.error("");
            e.printStackTrace();
        }
        return null;

    }

    private List<List<Object>> getMarketTableData(List<DayProductYXMarketingManagerInfoVo> marketingStatics){
        List<List<Object>> excelDataList = Lists.newArrayList();
        int i=0;
        // 策略统计table组织数据
        for ( DayProductYXMarketingManagerInfoVo marketingStatic : marketingStatics) {
            i++;
            List<Object> listItem = Lists.newArrayList();
            if(i==marketingStatics.size()){
                listItem.add("合计");
            }else{
                listItem.add(i);
            }
            listItem.add(marketingStatic.getStrategyName());
            listItem.add(marketingStatic.getTargetGdCn());
            listItem.add(marketingStatic.getFinishGdCn());
            listItem.add(marketingStatic.getWcl() == null ? "0%" : marketingStatic.getWcl() );
            listItem.add(marketingStatic.getOppoCn());
            listItem.add(marketingStatic.getOppoRate() == null ? "0%" : marketingStatic.getOppoRate());
            listItem.add(marketingStatic.getOppoFee());
            listItem.add(marketingStatic.getRemark() == null ? "" : marketingStatic.getRemark());
            excelDataList.add(listItem);
        }
        return excelDataList;
    }

    private List<List<Object>> getCustomerTableData(List<DayProductYXMarketingManagerInfoVo> marketingStatics,Map<String, DayProductYXMarketingManagerInfo> lastWeekMap){
        List<List<Object>> excelDataList = Lists.newArrayList();
        // 策略统计table组织数据
        int i=1;
        for ( DayProductYXMarketingManagerInfoVo marketingStatic : marketingStatics) {
            List<Object> listItem = Lists.newArrayList();
            if(marketingStatic.getMarketingManagerName() != null ){ // && !marketingStatic.getMarketingManagerName().contains("合计")
                DayProductYXMarketingManagerInfo lastWeekData = lastWeekMap.get(marketingStatic.getMarketingManagerOa());
                listItem.add(i++);
                listItem.add(marketingStatic.getMarketingManagerName());
                listItem.add(lastWeekData != null ? (StringUtils.isEmpty(lastWeekData.getFinishGdCn()) ? "0" : lastWeekData.getFinishGdCn() ): "0");
                listItem.add(lastWeekData != null ? (StringUtils.isEmpty(lastWeekData.getOppoCn()) ? "0" : lastWeekData.getOppoCn() ): "0");
                listItem.add(lastWeekData != null ? (StringUtils.isEmpty(lastWeekData.getOppoFee()) ? "0" : lastWeekData.getOppoFee()) : "0.00");
                listItem.add(marketingStatic.getTargetGdCnTotal() == null ? "0" : marketingStatic.getTargetGdCnTotal());
                listItem.add(marketingStatic.getFinishGdCnTotal() == null ? "0" : marketingStatic.getFinishGdCnTotal());
                listItem.add(marketingStatic.getWclTotal() == null ? "0%" : marketingStatic.getWclTotal() );
                listItem.add(marketingStatic.getOppoCnTotal() == null ? "0" : marketingStatic.getOppoCnTotal());
                listItem.add(marketingStatic.getOppoRateTotal() == null ? "0%" : marketingStatic.getOppoRateTotal());
                listItem.add(marketingStatic.getOppoFeeTotal() == null ? "0.00" : marketingStatic.getOppoFeeTotal());
                excelDataList.add(listItem);
            }
        }
        /*DayProductYXMarketingManagerInfo lastWeekData = lastWeekMap.get(marketingStatic.getMarketingManagerOa());
        if(marketingStatics.size()>0 && marketingStatics.get(0).getMarketingManagerName() != null && marketingStatics.get(0).getMarketingManagerName().contains("合计")){
            List<Object> listItem = Lists.newArrayList();
            listItem.add(marketingStatics.get(0).getMarketingManagerName());
            listItem.add(marketingStatics.get(0).getFinishGdCn());
            listItem.add(marketingStatics.get(0).getOppoCn());
            listItem.add(marketingStatics.get(0).getOppoFee());

            listItem.add(lastWeekData != null ? lastWeekData.getFinishGdCn() : "");
            listItem.add(lastWeekData != null ? lastWeekData.getOppoCn() : "");
            listItem.add(lastWeekData != null ? lastWeekData.getOppoFee() : "");

            listItem.add(marketingStatics.get(0).getTargetGdCnTotal());
            listItem.add(marketingStatics.get(0).getFinishGdCnTotal());
            listItem.add(marketingStatics.get(0).getWclTotal() == null ? "0%" : marketingStatics.get(0).getWclTotal() );

            listItem.add(marketingStatics.get(0).getOppoCnTotal());
            listItem.add(marketingStatics.get(0).getOppoRateTotal() == null ? "0%" : marketingStatics.get(0).getOppoRateTotal());
            listItem.add(marketingStatics.get(0).getOppoFeeTotal());
            excelDataList.add(listItem);
        }*/
        return excelDataList;
    }

    private void getDingDingMSG(List<List<Object>> managerList){
        List<ActivityReportConfig> headMarketing = managerInfoService.selectActivityZqytReportList();

        StringBuilder result = new StringBuilder();
        LocalDate localDate = LocalDate.now();
        result.append("截止"+localDate.getMonthValue()+"月").append(localDate.getDayOfMonth()).append("日，累计下发");
        int so = 1;
        result.append(headMarketing.size()).append("个产品");
        List<Object> dataList = managerList.get(managerList.size()-1);
        if(dataList.size()>9){
            result.append(dataList.get(5)).append("个工单，已完成").append(dataList.get(6)).append("个工单闭环，完成率").append(dataList.get(7)).append("，");
            result.append("累计转换商机").append(dataList.get(8)).append("个，商机转换率").append(dataList.get(9)).append("，形成商机规模").append(dataList.get(10)).append("万元。");
            this.dingdingMsg = result.toString();
        }else{
            this.dingdingMsg = "";
        }
    }

    /**
     * 写入本地文件，策略表头
     */
    public List<List<String>> getHeaderMarketFile(){
        List<List<String>> headList = Lists.newArrayList();
        headList.add(Lists.newArrayList("序号","策略名称","目标工单","完成工单","完成率","商机个数","商机率","商机规模(万元)","备注"));
        return headList;
    }
    public List<List<String>> getHeaderCustomerFile(){
        List<List<String>> headList = Lists.newArrayList();
        headList.add(Lists.newArrayList("序号","营销经理",managerInfoService.weekDateStr(),"","","累计数据","","","","",""));
        headList.add(Lists.newArrayList("序号","营销经理","完成工单","商机个数","商机规模(万元)","目标工单","完成工单","完成率","商机个数","商机率","商机规模(万元)"));
        return headList;
    }


}
