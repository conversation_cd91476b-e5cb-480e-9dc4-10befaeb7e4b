package cn.chinaunicom.sdsi.policeStationStatistics.controller;

import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.utils.FileUtils;
import cn.chinaunicom.sdsi.plugins.office.excel.IExcelFileService;
import cn.chinaunicom.sdsi.plugins.office.excel.entity.ExcelSheet;
import cn.chinaunicom.sdsi.policeStationStatistics.entity.PoliceStationStatisticsQuery;
import cn.chinaunicom.sdsi.policeStationStatistics.entity.PoliceStationStatisticsVo;
import cn.chinaunicom.sdsi.policeStationStatistics.service.IPoliceStationStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/9/12 9:58
 */
@RestController
@RequestMapping("/policeStationStatistics")
@Slf4j
public class PoliceStationStatisticsController extends BaseController {
    @Autowired
    private IPoliceStationStatisticsService baseService;
    @Autowired
    private IExcelFileService excelFileService;

    @Operation(summary = "分页", description = "分页")
    @GetMapping("/findPage")
    public BasePageResponse<PoliceStationStatisticsVo> findPage(PoliceStationStatisticsQuery query) {
        return pageOk(baseService.findPage(query));
    }

    @Operation(summary = "导出", description = "导出")
    @PostMapping("/export")
    public void export(@RequestBody PoliceStationStatisticsQuery query, HttpServletResponse response) throws IOException {
        FileInputStream in = null;
        ServletOutputStream out = null;
        String realPath = "";
        try {
            List<PoliceStationStatisticsVo> baseGroup = baseService.findList(query);
            ExcelSheet<PoliceStationStatisticsVo> excelSheet = new ExcelSheet<>();
            excelSheet.addSheet("报表统计", PoliceStationStatisticsVo.class, baseGroup);
            realPath = FileUtils.bytesToFile(excelFileService.createFile(excelSheet), FileUtils.tempFileName(".xlsx"));
            // 2. 下载的文件名是啥？
            String fileName = realPath.substring(realPath.lastIndexOf("\\") + 1);
            // 3. 设置想办法让浏览器能够支持(Content-Disposition)下载我们需要的东西,中文文件名URLEncoder.encode编码，否则有可能乱码
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            // 4. 获取下载文件的输入流
            in = new FileInputStream(realPath);
            // 5. 创建缓冲区
            int len = 0;
            byte[] buffer = new byte[1024];
            // 6. 获取OutputStream对象
            out = response.getOutputStream();
            // 7. 将FileOutputStream流写入到buffer缓冲区,使用OutputStream将缓冲区中的数据输出到客户端！
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
//            in.close();
//            out.close();
//            boolean b = new File(realPath).delete();
//            System.err.println("下载成功:" + b);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("************************");
        }finally {
            // 关闭资源
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            boolean b = new File(realPath).delete();
            System.err.println("下载成功:" + b);
        }
    }
}
