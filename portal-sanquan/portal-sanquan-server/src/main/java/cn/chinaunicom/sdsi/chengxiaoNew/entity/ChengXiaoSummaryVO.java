package cn.chinaunicom.sdsi.chengxiaoNew.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 *   成效转化汇总
 * </p>
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
public class ChengXiaoSummaryVO extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    /*数据来源*/
    String dataSource;

    /*场景，（靶向营销有）*/
    String busScene;

    /*省份*/
    String provName;

    /*地市*/
    String cityName;

    /*区县*/
    String districtName;

    /*任务数量*/
    String taskNum;

    /*执行数量*/
    String zhixing;

    /*未执行数量*/
    String weiZhixing;

    /*执行率*/
    String zhixingRate;

    /*商机数量*/
    String oppoNum;

    /*商机金额*/
    String oppoAmount;

    /*转化数*/
    String zhuanhuaNum;

    /*转化率*/
    String zhuanhuaRate;

    /* 金额转化率 */
    String zhuanhuaAmountRate;

    /*项目数量*/
    String projectNum;

    /*项目金额*/
    String projectAmount;

    /*带动新业务数量*/
    String newDevelopBusiness;

    /*新业务收入*/
    String newDevelopBusinessAmount;

    /*新业务数量提升率*/
    String newDevelopBusinessRate;

    /*新业务收入提升率*/
    String newDevelopBusinessAmountRate;

    /*数据库分组字段类型，1：地市，2：行业，3：区县，4：营服，5：*/
    String columnType;

    String startTime;
    String endTime;
    String createTime;

    /* 省分行业 */
    String provinceIndustryName;

    /* 省分细分行业 */
    String provinceDetailIndustryName;

    /* 总部行业 */
    String headIndustryName;

    public String getDataSource() {
        if(StringUtils.isEmpty(dataSource)){
            return "-";
        }
        return dataSource;
    }

    public String getBusScene() {
        if(StringUtils.isEmpty(busScene)){
            return "-";
        }
        return busScene;
    }

    public String getProvName() {
        if(StringUtils.isEmpty(provName)){
            return "-";
        }
        return provName;
    }

    public String getCityName() {
        if(StringUtils.isEmpty(cityName)){
            return "-";
        }
        return cityName;
    }

    public String getDistrictName() {
        if(StringUtils.isEmpty(districtName)){
            return "-";
        }
        return districtName;
    }

    public String getTaskNum() {
        if(StringUtils.isEmpty(taskNum)){
            return "-";
        }
        return taskNum;
    }

    public String getZhixing() {
        if(StringUtils.isEmpty(zhixing)){
            return "-";
        }
        return zhixing;
    }

    public String getWeiZhixing() {
        if(StringUtils.isEmpty(weiZhixing)){
            return "-";
        }
        return weiZhixing;
    }

    public String getOppoNum() {
        if(StringUtils.isEmpty(oppoNum)){
            return "-";
        }
        return oppoNum;
    }

    public String getZhuanhuaNum() {
        if(StringUtils.isEmpty(zhuanhuaNum)){
            return "-";
        }
        return zhuanhuaNum;
    }

    public String getZhuanhuaAmountRate() {
        if(StringUtils.isNotEmpty(zhuanhuaAmountRate)){
            BigDecimal bd = new BigDecimal(zhuanhuaAmountRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            return bd+"%";
        }else{
            return "-";
        }
    }

    public String getProjectNum() {
        if(StringUtils.isEmpty(projectNum)){
            return "-";
        }
        return projectNum;
    }

    public String getNewDevelopBusiness() {
        if(StringUtils.isEmpty(newDevelopBusiness)){
            return "-";
        }
        return newDevelopBusiness;
    }

    public String getCreateTime() {
        if(StringUtils.isEmpty(createTime)){
            return "-";
        }
        return createTime;
    }

    public String getZhixingRate() {
        if(StringUtils.isNotEmpty(zhixingRate)){
            BigDecimal bd = new BigDecimal(zhixingRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            if("0.00".equals(bd.toPlainString()) && StringUtils.isNotEmpty(busScene) && busScene.contains("等保测评")){
                return "-";
            }
            return bd+"%";
        }else{
            return "-";
        }
    }

    public String getZhuanhuaRate() {
        if(StringUtils.isNotEmpty(zhuanhuaRate)){
            BigDecimal bd = new BigDecimal(zhuanhuaRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            return bd+"%";
        }else{
            return "-";
        }
    }

    public String getOppoAmount() {
        if(StringUtils.isNotEmpty(oppoAmount)){
            BigDecimal bd = new BigDecimal(oppoAmount).setScale(2, RoundingMode.HALF_UP);
            return bd.toPlainString();
        }else{
            return "-";
        }
    }

    public String getProjectAmount() {
        if(StringUtils.isNotEmpty(projectAmount)){
            BigDecimal bd = new BigDecimal(projectAmount).setScale(2, RoundingMode.HALF_UP);
            return bd.toPlainString();
        }else{
            return "-";
        }
    }

    public String getNewDevelopBusinessAmount() {
        if(StringUtils.isNotEmpty(newDevelopBusinessAmount)){
            BigDecimal bd = new BigDecimal(newDevelopBusinessAmount).setScale(2, RoundingMode.HALF_UP);
            return bd.toPlainString();
        }else{
            return "-";
        }
    }

    public String getNewDevelopBusinessRate() {
        if(StringUtils.isNotEmpty(newDevelopBusinessRate)){
            BigDecimal bd = new BigDecimal(newDevelopBusinessRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            if("0.00".equals(bd.toPlainString())){
                return "-";
            }
            return bd+"%";
        }else{
            return "-";
        }
    }

    public String getNewDevelopBusinessAmountRate() {
        if(StringUtils.isNotEmpty(newDevelopBusinessAmountRate)){
            BigDecimal bd = new BigDecimal(newDevelopBusinessAmountRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            if("0.00".equals(bd.toPlainString())){
                return "-";
            }
            return bd+"%";
        }else{
            return "-";
        }
    }
}
