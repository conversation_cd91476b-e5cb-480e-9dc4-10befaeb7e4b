package cn.chinaunicom.sdsi.dingding.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 产品营销-地市
 */
@Data
@TableName("t_sanquan_dm_d_cpyx_marketing_ds")
public class DayProductYXMarketingDS {

    private String dayId;

    /**
     * RH指标
     */
    private String rhIndex;

    /**
     * 地市ID
     */
    private String cityId;

    /**
     * 地市名称
     */
    private String cityName;

    /**
     * 目标工单数
     */
    private String targetGdCn;

    /**
     * 完成工单数
     */
    private String finishGdCn;

    /**
     * 完成率
     */
    private String wcl;

    /**
     * 商机数
     */
    private String oppoCn;

    /**
     * 商机金额
     */
    private String oppoFee;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

}
