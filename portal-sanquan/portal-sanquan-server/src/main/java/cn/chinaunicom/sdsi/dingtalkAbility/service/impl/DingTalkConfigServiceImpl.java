package cn.chinaunicom.sdsi.dingtalkAbility.service.impl;

import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig;
import cn.chinaunicom.sdsi.dingtalkAbility.mapper.DingTalkConfigMapper;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkConfigService;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkConfigQueryVo;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkConfigVo;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
public class DingTalkConfigServiceImpl extends ServiceImpl<DingTalkConfigMapper, DingTalkConfig> implements DingTalkConfigService {


    @Override
    public IPage<DingTalkConfigVo> findPage(DingTalkConfigQueryVo vo) {
        IPage<DingTalkConfigVo> page = QueryVoToPageUtil.toPage(vo);
        IPage<DingTalkConfigVo> resultPage = baseMapper.findPage(page, vo);

        // 转换为VO并脱敏
        return resultPage.convert(config -> {
            DingTalkConfigVo newVo = new DingTalkConfigVo();
            BeanUtils.copyProperties(config, newVo);
            // 对APP_SECRET进行脱敏处理
            if (StrUtil.isNotBlank(newVo.getAppSecret())) {
                newVo.setAppSecret(desensitize(newVo.getAppSecret()));
            }
            return newVo;
        });
    }

    @Override
    public Boolean updateDingTalkConfig(DingTalkConfigVo vo) {
        DingTalkConfig config = new DingTalkConfig();
        if (StrUtil.isBlank(vo.getAppSecret())) {
            vo.setAppSecret(null);
        }
        BeanUtils.copyProperties(vo, config);
        return updateById(config);
    }

    @Override
    public Boolean insertDingTalkConfig(DingTalkConfigVo vo) {
        if (vo.getStatus() != null && vo.getStatus().equals("1") ) {
            // 校验是否有重名
            LambdaQueryWrapper<DingTalkConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DingTalkConfig::getStatus, "1")
                    .eq(DingTalkConfig::getGroupRemark, vo.getGroupRemark());
            if (count(wrapper) > 0) {
                throw new IllegalArgumentException("重复的钉钉分组名称");
            }
        }

        DingTalkConfig config = new DingTalkConfig();
        BeanUtils.copyProperties(vo, config);
        return save(config);
    }

    @Override
    public Boolean updateStatus(DingTalkConfigVo vo) {
        if (vo.getStatus().equals("1")) {
            DingTalkConfig oldConfig = getById(vo.getId());
            // 校验是否有重名
            LambdaQueryWrapper<DingTalkConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(DingTalkConfig::getStatus, "1")
                    .eq(DingTalkConfig::getGroupRemark, oldConfig.getGroupRemark());
            if (count(wrapper) > 0) {
                throw new IllegalArgumentException("重复的钉钉分组名称或已启用该配置");
            }
        }
        DingTalkConfig config = new DingTalkConfig();
        config.setId(vo.getId());
        config.setStatus(vo.getStatus());
        return updateById(config);
    }

    @Override
    public Boolean deleteDingTalkConfig(String id) {
        return removeById(id);
    }

    @Override
    public Boolean copyDingTalkConfig(String id) {
        DingTalkConfig config = getById(id);
        config.setId(null);
        config.setStatus("0");  // 默认禁用
        config.setGroupRemark(config.getGroupRemark() + "_copy");
        return save(config);
    }

    /**
     * 脱敏处理方法
     */
    private String desensitize(String secret) {
        if (secret == null || secret.length() < 6) {
            return "******";
        }
        // 保留前3位和后3位，中间用*代替
        int length = secret.length();
        return secret.substring(0, 3) + "****" + secret.substring(length - 3);
    }
}
