package cn.chinaunicom.sdsi.tokenValidate;

import cn.chinaunicom.sdsi.common.http.UniHttpClient;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/06 16:45
 */
@Aspect
@Component
@Slf4j
public class TokenValidationAspect {

    @Autowired
    private RedisUtils redisUtils;

    @Value("${auth.url.ssoSessionSessionUrl}")
    private String sessionUrl;

    @Pointcut("@annotation(TokenValidate)") // 切点，匹配所有带有 @TokenValidate 注解的方法
    public void tokenValidateMethods() {}

    @Before("tokenValidateMethods() && @annotation(tokenValidate)")
    public void validateToken(JoinPoint joinPoint, TokenValidate tokenValidate) {
        // 获取目标方法的所有参数
        Object[] args = joinPoint.getArgs();
        // 获取注解中指定的类类型
        Class<?> targetClass = tokenValidate.tokenClass();
        String authSessionId = null;
        // 遍历参数，寻找指定类型的参数
        for (Object arg : args) {
            if (targetClass.isInstance(arg)) {
                try {
                    // 使用反射调用getToken方法，假设所有VO都有此方法
                    Method getTokenMethod = targetClass.getMethod("getToken");
                    authSessionId = (String) getTokenMethod.invoke(arg);
                    break;
                } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                    throw new RuntimeException("无法获取token", e);
                }
            }
        }
        // Token 校验逻辑
        if (StringUtils.isEmpty(authSessionId)) {
            throw new ServiceErrorException("请携带鉴权参数！");
        }
        String redisKey = "ZQ_Token:" + authSessionId;
        String cachedToken = (String) redisUtils.get(redisKey);
        if (cachedToken != null) {
            // Token 已缓存，跳过验证
            return;
        }
        System.err.println("Token：" + authSessionId);
        // 如果没有缓存，验证 token
        String url = String.format("%s%s", sessionUrl, authSessionId);
        String staffInfoStr = UniHttpClient.get(url);
        JSONObject responseObject = new JSONObject(staffInfoStr);
        if ("success".equals(responseObject.getStr("rspInfo"))) {
            // 验证成功，缓存 token
            redisUtils.set(redisKey, authSessionId, 600L); // 10分钟缓存
        } else {
            // 验证失败
            throw new ServiceErrorException("Token 验证失败！");
        }
    }
}
