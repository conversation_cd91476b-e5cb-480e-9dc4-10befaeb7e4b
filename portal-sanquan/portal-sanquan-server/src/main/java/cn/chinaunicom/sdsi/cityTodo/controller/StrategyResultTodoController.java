package cn.chinaunicom.sdsi.cityTodo.controller;


import cn.chinaunicom.sdsi.cityTodo.service.IStrategyResultTodoService;
import cn.chinaunicom.sdsi.cityTodo.vo.StrategyResultTodoQuery;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.opportunity.vo.TSanquanPotentialOpportunityResponseVO;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 策略推送至地市记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@RestController
@RequestMapping("/strategy/result/todo")
public class StrategyResultTodoController extends BaseController {

    @Autowired
    private IStrategyResultTodoService iStrategyResultTodoService;

    /**
     * 根据todoCode 查询潜在机会
     * @param todoCode
     * @return
     */
    @GetMapping("/getOppoId")
    @Operation(summary = "查询todoCode", description = "查询todoCode")
    public BaseResponse<List<String>> getOppoId(String todoCode){
        return ok(iStrategyResultTodoService.getOppoId(todoCode));
    }

    /**
     * 根据todoCode 查询潜在机会详情信息（客户经理、营销经理）一对一关系
     * @param query
     * @return
     */
    @GetMapping("/getOppoInfo")
    @Operation(summary = "查询潜在机会详情信息（客户经理、营销经理）一对一关系", description = "查询潜在机会详情信息（客户经理、营销经理）一对一关系")
    public BasePageResponse<TSanquanPotentialOpportunityResponseVO> getOppoInfo(StrategyResultTodoQuery query){
        return pageOk(iStrategyResultTodoService.getOppoInfo(query));
    }
}
