package cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee;

import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig;
import cn.chinaunicom.sdsi.dingtalkAbility.request.*;
import cn.chinaunicom.sdsi.dingtalkAbility.response.*;
import cn.chinaunicom.sdsi.dingtalkAbility.util.DingHttpUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
public class DingTalkMessageService {

    private final DingHttpUtil httpUtil;

    private final DingTalkConfigCenter configCenter;

    public DingTalkMessageService(DingHttpUtil httpUtil, DingTalkConfigCenter configCenter) {
        this.httpUtil = httpUtil;
        this.configCenter = configCenter;
    }


    /**
     * 发送工作通知
     * @param group
     * @param request
     * @return
     */
    public ResponseEntity<DingTalkBaseResponse<WorkNotificationResponse>> sendWorkNotification(String group, WorkNotificationRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/push/work/message";
        return httpUtil.postRequest(url, config, request, WorkNotificationResponse.class);
    }

    /**
     * 机器人单聊批量发送
     * @param group
     * @param request
     * @return
     */
    public ResponseEntity<DingTalkBaseResponse<RobotBatchSendResponse>> batchSendRobotMessage(String group, RobotBatchSendRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/pushRobotBatchSend";
        if (request.getRobotCode() == null || request.getRobotCode().isEmpty()) {
            request.setRobotCode(config.getRobotCode());
        }
        return httpUtil.postRequest(url, config, request, RobotBatchSendResponse.class);
    }

    /**
     * 群里@人(webhook机器人)
     * @param group
     * @param request
     * @return
     */
    public ResponseEntity<DingTalkBaseResponse<CommonMessageResponse>> webhookSendMessage(String group, WebhookSendRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/webhookSendMsg";
        return httpUtil.postRequest(url, config, request, CommonMessageResponse.class);
    }

    /**
     * 机器人发送普通群聊消息
     * @param group
     * @param request
     * @return
     */
    public ResponseEntity<DingTalkBaseResponse<RobotSendMessageResponse>> sendRobotGroupMessage(String group, RobotGroupMessageRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/pushRobotGroupMessages";
        if (request.getRobotCode() == null || request.getRobotCode().isEmpty()) {
            request.setRobotCode(config.getRobotCode());
        }
        if (request.getOpenConversationId() == null || request.getOpenConversationId().isEmpty()) {
            request.setOpenConversationId(config.getOpenConversationId());
        }
        return httpUtil.postRequest(url, config, request, RobotSendMessageResponse.class);
    }

//    /**
//     * 发送普通卡片 【未使用测试】
//     * @param group
//     * @param request
//     * @return
//     * @throws JsonProcessingException
//     */
//    public ResponseEntity<DingTalkBaseResponse<RobotSendMessageResponse>> sendInteractiveCard(String group, InteractiveCardRequest request) {
//        DingTalkConfig config = configCenter.getConfig(group)
//                .orElseThrow(() -> new RuntimeException("配置不存在"));
//        String url = configCenter.getBaseUrl() + "/dingtalk/interactiveCards";
//        return httpUtil.postRequest(url, config, request, RobotSendMessageResponse.class);
//    }

//    /**
//     * 发送高级互动卡片 【未使用测试】
//     * @param group
//     * @param request
//     * @return
//     * @throws JsonProcessingException
//     */
//    public ResponseEntity<DingTalkBaseResponse<AdvancedInteractiveCardResponse>> pushInteractiveCard(String group, AdvancedInteractiveCardRequest request) {
//        DingTalkConfig config = configCenter.getConfig(group)
//                .orElseThrow(() -> new RuntimeException("配置不存在"));
//        String url = configCenter.getBaseUrl() + "/dingtalk/pushInteractiveCards";
//        return httpUtil.postRequest(url, config, request, AdvancedInteractiveCardResponse.class);
//    }

//    /**
//     * 更新高级互动卡片   【未使用测试】
//     * @param group
//     * @param request
//     * @return
//     * @throws JsonProcessingException
//     */
//    public ResponseEntity<DingTalkBaseResponse<RobotSendMessageResponse>> updateInteractiveCard(String group, UpdateInteractiveCardRequest request) {
//        DingTalkConfig config = configCenter.getConfig(group)
//                .orElseThrow(() -> new RuntimeException("配置不存在"));
//        String url = configCenter.getBaseUrl() + "/dingtalk/updateInteractiveCards";
//        return httpUtil.postRequest(url, config, request, RobotSendMessageResponse.class);
//    }

    /**
     * 发送DING消息
     * @param group
     * @param request
     * @return
     * @throws JsonProcessingException
     */
    public ResponseEntity<DingTalkBaseResponse<DingMessageResponse>> sendDingMessage(String group, DingMessageRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/sendDingMsg";
        return httpUtil.postRequest(url, config, request, DingMessageResponse.class);
    }

}