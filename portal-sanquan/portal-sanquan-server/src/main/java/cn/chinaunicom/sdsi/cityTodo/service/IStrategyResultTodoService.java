package cn.chinaunicom.sdsi.cityTodo.service;

import cn.chinaunicom.sdsi.cityTodo.entity.StrategyResultTodo;
import cn.chinaunicom.sdsi.cityTodo.vo.StrategyResultTodoQuery;
import cn.chinaunicom.sdsi.opportunity.vo.TSanquanPotentialOpportunityResponseVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 策略推送至地市记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface IStrategyResultTodoService extends IService<StrategyResultTodo> {

    // 根据todoCode 查询潜在机会
    List<String> getOppoId(String todoCode);

    // 根据todoCode 查询潜在机会详情信息（客户经理、营销经理）一对一关系
    IPage<TSanquanPotentialOpportunityResponseVO> getOppoInfo(StrategyResultTodoQuery query);
}
