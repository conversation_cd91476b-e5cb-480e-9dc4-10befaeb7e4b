package cn.chinaunicom.sdsi.customer.dao;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerMajor;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerMajorQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerMajorVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 重大事件-客户资料
*
* <AUTHOR> 
* @since  2024-05-14
*/
@Mapper
public interface TSanquanCustomerMajorMapper extends BaseMapper<TSanquanCustomerMajor> {

    List<TSanquanCustomerMajorVO> selectByCustomerIdList(@Param("id") String id);

    //分页
    IPage<TSanquanCustomerMajor> findPage(@Param("page") IPage page,@Param("query") TSanquanCustomerMajorQuery tSanquanCustomerMajorQuery);

    //列表
    List<TSanquanCustomerMajor> findList(TSanquanCustomerMajorQuery query);
}