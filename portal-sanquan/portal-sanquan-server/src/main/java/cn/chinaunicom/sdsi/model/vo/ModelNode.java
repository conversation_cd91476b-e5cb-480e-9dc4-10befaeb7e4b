package cn.chinaunicom.sdsi.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 模型表视图对象
 * @Author: han
 * @Date: 2024-07-19
 */
@Data
public class ModelNode implements Serializable {
    private String value;
    private String label;
    List<ModelNode> children;

    public ModelNode(){}

    public ModelNode(String value, String label) {
        this.value = value;
        this.label = label;
    }
}
