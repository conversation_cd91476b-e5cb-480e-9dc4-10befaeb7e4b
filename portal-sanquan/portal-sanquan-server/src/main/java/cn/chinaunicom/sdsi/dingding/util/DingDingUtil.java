package cn.chinaunicom.sdsi.dingding.util;

import cn.chinaunicom.sdsi.dingding.entity.DingdingConfig;
import cn.chinaunicom.sdsi.emailConfig.service.EmailConfigService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.File;
import java.nio.file.Files;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.time.temporal.WeekFields;
import java.util.*;

@Component
@Slf4j
public class DingDingUtil {

    @Value("${dingding.robotCode}")
    private String ROBOT_CODE;

    @Value("${dingding.apiNet}")
    private String apiNet;

    @Value("${dingding.appKey}")
    private String APP_KEY;

    @Value("${dingding.appSecret}")
    private String APP_SECRET;

    @Value("${dingding.openConversationId}")
    private String OpenConversationId;

    String apidingtalk = "apidingtalk/";
    String dingtalkapi = "dingtalkapi/";

    @Autowired
    EmailConfigService emailConfigService;

    // 钉钉生产应用秘钥配置
//    private static final String APP_KEY = "ding7r40k3erasnpxtqz";
//    private static final String APP_SECRET = "88XEi6hPMCIhEpXlxugLb7g8GpNPI5nQTJaH4mkatt6xK5MCtDdv4m2uE-J-Y4VM";
//    private static final String ROBOT_CODE = "ding7r40k3erasnpxtqz";
//    private static final String OpenConversationId = "cidSDn4QDbL2GaDKTbiOvYOjQ=="; // 目标群ID
//    private static final String CHAT_ID = "cidSDn4QDbL2GaDKTbiOvYOjQ=="; // 目标群标识

    // 钉钉生产机器人秘钥配置
/*    private static final String APP_KEY = "dingx3u2zttcjkti6ptf";
    private static final String APP_SECRET = "7XSdwGILJWLeJNPTpU37mWMT4TKRjUjjYhMvrewRJ_9yX145COy65ENyo37xLeM6";
    private static final String ROBOT_CODE = "ding7r40k3erasnpxtqz";
    private static final String OpenConversationId = "cidSDn4QDbL2GaDKTbiOvYOjQ=="; // 目标群ID
    private static final String CHAT_ID = "cidSDn4QDbL2GaDKTbiOvYOjQ=="; // 目标群标识*/

    // 马鉴
/*    private static final String APP_KEY = "dinggphzbboelqyjadwr";
    private static final String APP_SECRET = "jgpipcHb2aKDuko89ksFXbGJ_AaLsS5bjaIxtcDnzFPSqSEppVAN47-hE7yAl1oD";
    private static final String ROBOT_CODE = "dinggphzbboelqyjadwr";
    private static final String CHAT_ID = "071051654522786406"; // 目标群ID
    private static final String OpenConversationId = "cidb9Tzz3wFGhQH70P7KwLRsQ=="; // 目标群标识*/

// 测试更改 马鉴应用
    public void initTest(){
        APP_KEY = "dinggphzbboelqyjadwr";
        APP_SECRET = "jgpipcHb2aKDuko89ksFXbGJ_AaLsS5bjaIxtcDnzFPSqSEppVAN47-hE7yAl1oD";
        ROBOT_CODE = "dinggphzbboelqyjadwr";
        OpenConversationId = "cidb9Tzz3wFGhQH70P7KwLRsQ==";

        apidingtalk = "https://api.dingtalk.com";
        dingtalkapi = "https://oapi.dingtalk.com";
        apiNet = "";

      /*  // 生产
        APP_KEY =  "ding7r40k3erasnpxtqz";
        APP_SECRET = "88XEi6hPMCIhEpXlxugLb7g8GpNPI5nQTJaH4mkatt6xK5MCtDdv4m2uE-J-Y4VM";
        OpenConversationId = "cidSDn4QDbL2GaDKTbiOvYOjQ==";
        ROBOT_CODE = "ding7r40k3erasnpxtqz";*/
    }

    // 配置钉钉秘钥
    public void dingdingConfig(String group){
        if(group == null){
            group = "sanquan";
        }
        List<DingdingConfig> configList = emailConfigService.getDingdingConfig(group);
        if(configList.size()>0){
            DingdingConfig config = configList.get(0);
            APP_KEY = config.getAppkey();
            APP_SECRET = config.getAppSecret();
            ROBOT_CODE = config.getRobotCode();
            OpenConversationId = config.getOpenConversationId();
        }
        log.info("配置key信息：" + JSON.toJSONString(configList));
    }

    /**
     * 获取钉钉API访问令牌
     */
    public String getAccessToken2() {
        RestTemplate restTemplate = new RestTemplate();
//        String url = String.format("https://oapi.dingtalk.com/gettoken?appkey=%s&appsecret=%s",APP_KEY, APP_SECRET);
        String url = String.format(apiNet+"/gettoken?appkey=%s&appsecret=%s",APP_KEY, APP_SECRET);
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        return (String) response.getBody().get("access_token");
    }
    public String getAccessToken() {
//        String url = "https://api.dingtalk.com/v1.0/oauth2/accessToken";
        String GET_ACCESS_TOKEN_URL = apiNet+apidingtalk+"/v1.0/oauth2/accessToken";
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        JSONObject requestBody = new JSONObject();
        requestBody.put("appKey", APP_KEY);
        requestBody.put("appSecret", APP_SECRET);
        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    GET_ACCESS_TOKEN_URL,
                    HttpMethod.POST,
                    requestEntity,
                    String.class);
            if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
                log.error("AccessTokenService_getTokenFromApiServer getAccessToken return error, status={}, body={}",
                        response.getStatusCode(), response.getBody());
                return null;
            }
            JSONObject jsonResponse = JSON.parseObject(response.getBody());
            String token = jsonResponse.getString("accessToken");
            return token;
        } catch (HttpClientErrorException e) {
            log.error("AccessTokenService_getTokenFromApiServer getAccessToken throw HttpClientErrorException, " +
                    "status={}, body={}", e.getStatusCode(), e.getResponseBodyAsString(), e);
        } catch (Exception e) {
            log.error("AccessTokenService_getTokenFromApiServer getAccessToken throw Exception", e);
        }
        return null;
    }

    /**
     * 上传文件到钉钉服务器
     */
    public String uploadFileToDingTalk(String accessToken, File file) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
//        String url = "https://oapi.dingtalk.com/media/upload?access_token=" + accessToken + "&type=file";
        String url = apiNet+dingtalkapi+"/media/upload?access_token=" + accessToken + "&type=file";

        // 准备文件内容
        byte[] fileBytes = Files.readAllBytes(file.toPath());

        // 构建multipart请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        LinkedMultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
        parts.add("media", new HttpEntity<>(fileBytes, createFileHeaders(file)));

        HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(parts, headers);

        // 发送上传请求
        ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                Map.class
        );

        return (String) response.getBody().get("media_id");
    }

    /**
     * 创建文件上传头信息
     */
    public HttpHeaders createFileHeaders(File file) {
        HttpHeaders fileHeaders = new HttpHeaders();
        fileHeaders.setContentDispositionFormData("media", file.getName());
        fileHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return fileHeaders;
    }

    /**
     * 发送文本到指定群
     */
    public void sendTextToGroup(String accessToken, String content) {
        String url = apiNet+apidingtalk+"/v1.0/robot/groupMessages/send";
        // 构建消息体
        JSONObject msgParam = new JSONObject();
        msgParam.put("content",content);

        Map<String, Object> message = new HashMap<>();
        message.put("openConversationId", OpenConversationId);
        message.put("robotCode", ROBOT_CODE);
        message.put("msgKey", "sampleText");
        message.put("msgParam", msgParam.toJSONString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("x-acs-dingtalk-access-token",accessToken);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        System.out.println("文本推送结果》》》: " + response.getBody());
        log.info("钉钉推送文本结果：" + response.getBody());
    }

    /**
     * 发送文件到指定群
     */
    public void sendFileToGroup(String accessToken, String mediaId, String fileName) {


//        String url = "https://api.dingtalk.com/v1.0/robot/groupMessages/send";
        String url = apiNet+apidingtalk+"/v1.0/robot/groupMessages/send";
        // 构建消息体
        JSONObject msgParam = new JSONObject();
        msgParam.put("mediaId",mediaId);
        msgParam.put("fileName",fileName+".xlsx");
        msgParam.put("fileType","xlsx");

        Map<String, Object> message = new HashMap<>();
        message.put("openConversationId", OpenConversationId);
        message.put("robotCode", ROBOT_CODE);
        message.put("msgKey", "sampleFile");
        message.put("msgParam", msgParam.toJSONString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("x-acs-dingtalk-access-token",accessToken);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        System.out.println("文件推送结果》》》: " + response.getBody());
        log.info("钉钉推送附件结果：" + response.getBody());
    }

    /**
     * 发送图片到指定群
     */
    public void sendImgToGroup(String accessToken,String medaId) {


//        String url = "https://api.dingtalk.com/v1.0/robot/groupMessages/send";
        String url = apiNet+apidingtalk+"/v1.0/robot/groupMessages/send";
        // 构建消息体
        JSONObject msgParam = new JSONObject();
//        msgParam.put("photoURL","https://img1.baidu.com/it/u=837510728,3756639739&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=800");
        msgParam.put("photoURL",medaId);

        Map<String, Object> message = new HashMap<>();
        message.put("openConversationId", OpenConversationId);
        message.put("robotCode", ROBOT_CODE);
        message.put("msgKey", "sampleImageMsg");
        message.put("msgParam", msgParam.toJSONString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("x-acs-dingtalk-access-token",accessToken);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        System.out.println("文件推送结果》》》: " + response.getBody());
        log.info("钉钉推送附件结果：" + response.getBody());
    }
    /**
     * 发送图片+文本到指定群
     * order 为1，表示 文本在上，图片在下。为2，表示文本在下，图片在上
     */
    public void sendImgAndTextToGroup(String accessToken,String content,String medaId,String order) {

        String url = apiNet+apidingtalk+"/v1.0/robot/groupMessages/send";
        // 构建消息体
        JSONObject msgParam = new JSONObject();
        msgParam.put("title","测试");
        String contentPush = "";
        if("1".equals(order)){
            contentPush = content + "\r\n\r\n" + "![](" + medaId + ")";
        }else{
            contentPush = "![](" + medaId + ")"+ "\r\n\r\n" + content;
        }
        msgParam.put("text",contentPush);

        Map<String, Object> message = new HashMap<>();
        message.put("openConversationId", OpenConversationId);
        message.put("robotCode", ROBOT_CODE);
        message.put("msgKey", "sampleMarkdown");
        message.put("msgParam", msgParam.toJSONString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("x-acs-dingtalk-access-token",accessToken);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        System.out.println("文件推送结果》》》: " + response.getBody());
        log.info("钉钉推送附件结果：" + response.getBody());
    }

    public void sendImgAndTextToGroup(String accessToken,String content,List<String> medaId,String order) {

        String url = apiNet+apidingtalk+"/v1.0/robot/groupMessages/send";
        // 构建消息体
        JSONObject msgParam = new JSONObject();
        msgParam.put("title","测试");

        String contentPush = "";
        for (String meda : medaId) {
            if("1".equals(order)){
                contentPush +=  "\r\n\r\n" + "![](" + meda + ")";
            }else{
                contentPush += "![](" + meda + ")"+ "\r\n\r\n" ;
            }
        }
        if("1".equals(order)){
            contentPush = content + contentPush;
        }else{
            contentPush = contentPush + content;
        }

        msgParam.put("text",contentPush);

        Map<String, Object> message = new HashMap<>();
        message.put("openConversationId", OpenConversationId);
        message.put("robotCode", ROBOT_CODE);
        message.put("msgKey", "sampleMarkdown");
        message.put("msgParam", msgParam.toJSONString());

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("x-acs-dingtalk-access-token",accessToken);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(message, headers);

        // 发送请求
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
        System.out.println("文件推送结果》》》: " + response.getBody());
        log.info("钉钉推送附件结果：" + response.getBody());
    }


    /**
     * 生成加签（如果需要）
     */
    private String generateSign(Long timestamp) throws Exception {
        String stringToSign = timestamp + "\n" + APP_SECRET;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(APP_SECRET.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        return Base64.getEncoder().encodeToString(signData);
    }




}
