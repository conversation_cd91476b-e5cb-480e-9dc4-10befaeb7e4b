package cn.chinaunicom.sdsi.customer.service;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerInfo;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerInfoQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerInfoAddVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerInfoVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.rosterCustomer.vo.TSanquanRosterCustomerHierarchyTreeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 客户资料-客户经理
 *
 * <AUTHOR>
 * @since  2024-05-15
 */
public interface TSanquanCustomerInfoService extends IService<TSanquanCustomerInfo> {

    // 分页查询
    IPage<TSanquanCustomerInfoVO> findPage(TSanquanCustomerInfoQuery tSanquanCustomerInfoQuery);

    // 根据id查询
    TSanquanCustomerInfoAddVO findOne(String id);

    // 查询列表
    List<TSanquanCustomerInfoVO> findList(TSanquanCustomerInfoQuery query);

    // 新增
    String addOrUpdate(TSanquanCustomerInfoAddVO tSanquanCustomerInfoAddVO);

    // 修改
    int update(TSanquanCustomerInfo tSanquanCustomerInfo);

    // 删除
    Boolean delete(String id);

    //查询详情
    TSanquanCustomerInfoAddVO findDetail(String id);

    // 分页查询(审核的时候使用)
    IPage<TSanquanCustomerInfoVO> findPageAudit(TSanquanCustomerInfoQuery tSanquanCustomerInfoQuery);

    //根据客户经理工号查询（名单制客户ID）
    List<String> findRosterCustomerId(String id);

    //根据客户经理工号查询（名单制客户ID）
    List<String> findRosterCustomerIds(TSanquanCustomerInfoQuery tSanquanCustomerInfoQuery);

    //根据名单制客户id，查询客户经理信息
    List<TSanquanDZqztJihezxProvinceMappingVO> findCustomerManagerInfo(String rosterCustomerId);

    //验证客户名称是否存在
    Integer verifyCustomerName(String customerName,String customerId);

    //查询树的子节点
    List<TSanquanRosterCustomerHierarchyTreeVO> findTreeList(TSanquanCustomerInfoQuery tSanquanCustomerInfoQuery);

    // 客户数量
    int getCustomerNum(TSanquanCustomerInfoQuery query);
}