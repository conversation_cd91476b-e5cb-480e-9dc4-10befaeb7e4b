package cn.chinaunicom.sdsi.marketing.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 地市营销经理支撑对象 t_sanquan_marketing_manager
 * @Author: han
 * @Date: 2024-10-29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "地市营销经理支撑对象", description = "地市营销经理支撑对象")
@TableName("t_sanquan_marketing_manager")
public class TSanquanMarketingManager implements Serializable {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "地市")
    private String city;

    @Schema(name = "营销经理账号")
    private String marketingManagerOa;

    @Schema(name = "营销经理名称")
    private String marketingManagerName;

    @Schema(name = "是否组长0:否 1:是")
    private String isGroupLeader;

    @Schema(name = "手机号码")
    private String telphone;
    /**
     * 创建人
     */
    @Schema(name="创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(name="创建时间")
    @TableField(fill = FieldFill.INSERT,jdbcType = JdbcType.TIMESTAMP)
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;

    /**
     * 逻辑删除，normal表示正常，deleted表示删除
     */
    @TableLogic(value = "normal", delval = "deleted")
    @Schema(name="逻辑删除，normal表示正常，deleted表示删除")
    private String deleteFlag = "normal";
}
