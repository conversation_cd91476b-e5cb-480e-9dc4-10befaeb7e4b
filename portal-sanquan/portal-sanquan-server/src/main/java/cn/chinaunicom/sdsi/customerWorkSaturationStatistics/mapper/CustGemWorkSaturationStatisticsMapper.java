package cn.chinaunicom.sdsi.customerWorkSaturationStatistics.mapper;

import cn.chinaunicom.sdsi.cloud.customerWorkSaturationStatistics.entity.CustGemWorkSaturationStatistics;
import cn.chinaunicom.sdsi.cloud.customerWorkSaturationStatistics.query.CustGemWorkSaturationStatisticsQueryVo;
import cn.chinaunicom.sdsi.customerWorkSaturationStatistics.vo.CustGemWorkSaturationStatisticsExcelVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 客户经理工单饱和度统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Mapper
public interface CustGemWorkSaturationStatisticsMapper extends BaseMapper<CustGemWorkSaturationStatistics> {

    // 分页查询
    IPage<CustGemWorkSaturationStatistics> findPage(@Param("page") IPage page, @Param("query") CustGemWorkSaturationStatisticsQueryVo tSanquanCustGemWorkSaturationStatisticsVo);

    // 查询列表
    List<CustGemWorkSaturationStatistics> findList(@Param("query") CustGemWorkSaturationStatisticsQueryVo tSanquanCustGemWorkSaturationStatisticsVo);

    // 导出列表数据
    List<CustGemWorkSaturationStatisticsExcelVo> exportExcelList(@Param("query") CustGemWorkSaturationStatisticsQueryVo workSaturationStatisticsVo);
}
