/*
 * Copyright (c) 2022, <EMAIL>. All rights reserved.
 */

package cn.chinaunicom.sdsi.keynoteOppo.asr.util;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import okhttp3.Request;

import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @version V1.0
 * @file cn.chinaunicom.sdsi.gateway.sdk.entity.HMacAuthRequest
 * @description
 * @date 2022-04-21
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
public class HMacAuthRequestBuilder {

	/**
	 * url
	 */
	private UrlBuilder url;

	/**
	 * 方法
	 */
	private Method method;

	/**
	 * 日期 默认当前时间
	 */
	private String date;
	/**
	 * 加密算法
	 */
	private String algorithm;
	/**
	 * 用户名
	 */
	private String username;
	/**
	 * 密钥
	 */
	private String password;
	/**
	 * 协议 默认 HTTP/1.1
	 */
	private String protocol = "HTTP/1.1";


	public HMacAuthRequestBuilder url(UrlBuilder url) {
		this.url = url;
		return this;
	}

	public HMacAuthRequestBuilder method(Method method) {
		this.method = method;
		return this;
	}

	public HMacAuthRequestBuilder date(String date) {
		this.date = date;
		return this;
	}

	public HMacAuthRequestBuilder algorithm(String algorithm) {

		if (!HMacAuthUtil.ALGORITHM_RANGE.contains(algorithm)) {
			throw new IllegalArgumentException("当前算法不支持,目前支持hmac-sha1,hmac-sha256");
		}

		this.algorithm = algorithm;

		return this;
	}

	public HMacAuthRequestBuilder username(String username) {
		this.username = username;
		return this;
	}

	public HMacAuthRequestBuilder password(String password) {
		this.password = password;
		return this;
	}

	public HMacAuthRequestBuilder protocol(String protocol) {
		this.protocol = protocol;
		return this;
	}


	public static HMacAuthRequestBuilder builder() {

		return new HMacAuthRequestBuilder();
	}

	public HttpRequest build() {
		validationInitial();

		String authorization = HMacAuthUtil.buildAuthorization(
				this.url,
				this.algorithm,
				this.username,
				this.date,
				this.password,
				this.method.name(),
				this.url.getPathStr(),
				this.protocol
		);

		// Hutool 5.4.1 兼容实现
		HttpRequest request = new HttpRequest(this.url.toString());
		request.setMethod(this.method);
		request.header("Host", this.url.getAuthority());
		request.header("Date", this.date);
		request.header("Authorization", authorization);

		return request;
	}


	public HttpRequest build(String url, Method method) {
		this.url = UrlBuilder.of(url, Charset.defaultCharset());
		this.method = method;
		return this.build();
	}


	public Request.Builder buildOkHttpRequestBuilder() {
		validationInitial();

		String authorization = HMacAuthUtil.buildAuthorization(
				this.url,
				this.algorithm,
				this.username,
				this.date,
				this.password,
				this.method.name(),
				this.url.getPathStr(),
				this.protocol
		);

		return new Request.Builder()
				.url(url.toString())
				.method(this.method.name(), null)
				.addHeader("Date", this.date)
				.addHeader("Authorization", authorization);
	}

	private void validationInitial() {
		validationParams();

		if (this.date == null) {
			this.date = HMacAuthUtil.getGMTDate();
		}
	}

	private void validationParams() {
		if (this.url == null) {
			throw new IllegalArgumentException("url为必输项");
		}

		if (this.method == null) {
			throw new IllegalArgumentException("method为必输项");
		}

		if (this.algorithm == null) {
			throw new IllegalArgumentException("algorithm为必输项");
		}

		if (this.username == null) {
			throw new IllegalArgumentException("username为必输项");
		}

		if (this.password == null) {
			throw new IllegalArgumentException("password为必输项");
		}
	}
}
