package cn.chinaunicom.sdsi.activityReport.controller;

import cn.chinaunicom.sdsi.activityReport.dto.ActivityReportConfigDto;
import cn.chinaunicom.sdsi.activityReport.dto.ActivityReportQueryDTO;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportCityStatistics;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportConfig;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportDetail;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportPersonStatistics;
import cn.chinaunicom.sdsi.activityReport.service.IActivityReportConfigService;
import cn.chinaunicom.sdsi.activityReport.service.IActivityReportService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 专项活动报表配置列表
 */
@RestController
@RequestMapping("/activity/reportConfig")
public class ActivityReportConfigController extends BaseController {
    @Autowired
    private IActivityReportConfigService activityReportConfigService;

    /**
     * 专项活动报表配置列表
     */
    @GetMapping("/queryActivityReportConfig")
    public BasePageResponse<ActivityReportConfig> queryActivityReportConfig(ActivityReportConfigDto param) {
        return pageOk(activityReportConfigService.selectActivityReportConfigList(param));
    }

    /**
     * 查询配置项列表
     * @param
     * @return
     */
    @PostMapping("/queryActivityReportConfigOptions")
    public BaseResponse queryActivityReportConfigOptions() {
        return new BaseResponse<>(activityReportConfigService.selectActivityReportConfigOptions());
    }

    /**
     * 更新专项活动报表配置
     * @param param
     * @return
     */
    @PostMapping("/updateActivityReportConfig")
    public BaseResponse updateActivityReportConfig(@RequestBody ActivityReportConfig param) {
        return new BaseResponse<>(activityReportConfigService.updateActivityReportConfig(param));
    }
}
