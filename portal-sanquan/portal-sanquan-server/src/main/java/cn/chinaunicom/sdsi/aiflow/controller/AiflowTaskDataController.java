package cn.chinaunicom.sdsi.aiflow.controller;

import cn.chinaunicom.sdsi.aiflow.dto.AiflowTaskDataDTO;
import cn.chinaunicom.sdsi.aiflow.entity.AiflowTaskData;
import cn.chinaunicom.sdsi.aiflow.service.IAiflowTaskDataService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/aiflow/data")
public class AiflowTaskDataController extends BaseController {
    @Autowired
    private IAiflowTaskDataService aiflowTaskDataService;
    /**
     * 获取模型数据
     */
    @PostMapping("/getAiFlowData")
    public BasePageResponse<AiflowTaskData> getAiFlowData(@RequestBody AiflowTaskDataDTO param) {
        IPage<AiflowTaskData> page = aiflowTaskDataService.selectAiflowTaskData(param);
        return pageOk(page);
    }
    /**
     * 获取模型数据
     */
    @PostMapping("/exportAiFlowData")
    public void exportAiFlowData(@RequestBody AiflowTaskDataDTO param) {
        aiflowTaskDataService.exportAiFlowData(param);
    }

    /**
     * 获取模型数据账期
     */
    @PostMapping("/getAiFlowDataTimest")
    public BaseResponse<List<String>> getAiFlowDataTimest(@RequestBody AiflowTaskDataDTO param) {
        List<String> timests = aiflowTaskDataService.selectAiflowTaskDataTimest(param);
        return ok(timests);
    }
}
