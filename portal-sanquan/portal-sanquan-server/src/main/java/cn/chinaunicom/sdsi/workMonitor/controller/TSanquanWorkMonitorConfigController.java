package cn.chinaunicom.sdsi.workMonitor.controller;

import cn.chinaunicom.sdsi.cloud.workMonitor.entity.TSanquanWorkMonitorConfig;
import cn.chinaunicom.sdsi.cloud.workMonitor.query.TSanquanWorkMonitorConfigQuery;
import cn.chinaunicom.sdsi.cloud.workMonitor.vo.TSanquanWorkMonitorConfigVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.workMonitor.service.TSanquanWorkMonitorConfigService;
import cn.chinaunicom.sdsi.workMonitor.vo.WorkMonitorStatusFirstListVO;
import cn.chinaunicom.sdsi.workMonitor.vo.WorkMonitorStatusSecoundListVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 工单监控规则配置
*
* <AUTHOR> 
* @since  2024-08-12
*/
@RestController
@RequestMapping("/work/monitorConfig")
@Tag(name="工单监控规则配置")
public class TSanquanWorkMonitorConfigController extends BaseController {

    @Autowired
    private TSanquanWorkMonitorConfigService tanquanWorkMonitorConfigService;

    /**
     * 分页查询
     * @param query
     * @return
     */
    @GetMapping("/findPage")
    @Operation(summary = "分页", description = "分页")
    public BasePageResponse<TSanquanWorkMonitorConfigVO> findPage(TSanquanWorkMonitorConfigQuery query) {
        return pageOk(tanquanWorkMonitorConfigService.findPage(query));
    }

    /**
     * 查询列表
     * @param query
     * @return
     */
    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<TSanquanWorkMonitorConfigVO>> findInfo(TSanquanWorkMonitorConfigQuery query) {
        return ok(tanquanWorkMonitorConfigService.findList(query));
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("/findOne")
    @Operation(summary = "根据id查询", description = "根据id查询")
    public BaseResponse<TSanquanWorkMonitorConfigVO> findOne(String id) {
        return ok(tanquanWorkMonitorConfigService.findOneById(id));
    }

    /**
     * 保存数据
     * @param entity
     * @return
     */
    @PostMapping("/addOrUpdate")
    @Operation(summary = "保存数据", description = "保存数据")
    public BaseResponse<Boolean> addOrUpdate(@RequestBody TSanquanWorkMonitorConfig entity) {
        return ok(tanquanWorkMonitorConfigService.addOrUpdate(entity));
    }

    /**
     * 修改数据
     * @param vo
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "修改")
    public BaseResponse<Boolean> update(@RequestBody TSanquanWorkMonitorConfigVO vo){
        return ok(tanquanWorkMonitorConfigService.updateMonitorConfig(vo));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @GetMapping("/delete")
    @Operation(summary = "删除", description = "删除")
    public BaseResponse<Boolean> delete(String id) {
        return ok(tanquanWorkMonitorConfigService.removeById(id));
    }

    /**
     * 根据地市、行业、状态查看当前的规则是否已经存在
     * @param vo
     * @return
     */
    @GetMapping("/checkExists")
    @Operation(summary = "查看当前的规则是否已经存在", description = "查看当前的规则是否已经存在")
    public BaseResponse<Boolean> checkExists(TSanquanWorkMonitorConfigVO vo) {
        return ok(tanquanWorkMonitorConfigService.checkExists(vo));
    }

    /**
     * 工单监控数
     * @param query
     * @return
     */
    @Operation(summary = "工单监控数", description = "工单监控数")
    @GetMapping("/getMonitorNum")
    public BasePageResponse<WorkMonitorStatusFirstListVO> getMonitorNum(TSanquanWorkMonitorConfigQuery query) {
        return pageOk(tanquanWorkMonitorConfigService.getMonitorNum(query));
    }


    /**
     * 工单监控详情
     * @param query
     * @return
     */
    @Operation(summary = "工单监控详情", description = "工单监控详情")
    @GetMapping("/getMonitorDetails")
    public BasePageResponse<WorkMonitorStatusSecoundListVO> getMonitorDetails(TSanquanWorkMonitorConfigQuery query) {
        return pageOk(tanquanWorkMonitorConfigService.getMonitorDetails(query));
    }

    /**
     * 根据地市、行业、状态查看当前的规则
     * @param vo
     * @return
     */
    @GetMapping("/getByIndustryAndStatus")
    @Operation(summary = "查看当前的规则", description = "查看当前的规则")
    public BaseResponse<TSanquanWorkMonitorConfig> getByIndustryAndStatus(TSanquanWorkMonitorConfigVO vo) {
        return ok(tanquanWorkMonitorConfigService.getByIndustryAndStatus(vo));
    }

}