package cn.chinaunicom.sdsi.private5GWorkflow.service.impl;

import cn.chinaunicom.sdsi.private5GWorkflow.entity.QryOsslBusiColbInfoByOrderIdRequest;
import cn.chinaunicom.sdsi.private5GWorkflow.entity.QryOsslBusiColbInfoByOrderIdResponse;
import cn.chinaunicom.sdsi.private5GWorkflow.entity.*;
import cn.chinaunicom.sdsi.private5GWorkflow.enums.ProcessStepEnum;
import cn.chinaunicom.sdsi.private5GWorkflow.service.AbilityWorkFlowService;
import cn.chinaunicom.sdsi.private5GWorkflow.utils.WyjCryptEntity;
import cn.chinaunicom.sdsi.private5GWorkflow.utils.WyjCryptUtils;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.chinaunicom.sdsi.util.ability.AbilityPlatformService;
import cn.chinaunicom.sdsi.util.http.OkHttpUtils;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @program: sanquan_server
 * @ClassName AbilityWorkFlowServiceImpl
 * @description: 5G端到端流程-能力调用
 * @author: majian
 * @date: 2025-04-09 14:40
 * @Version 1.0
 **/
@Service
public class AbilityWorkFlowServiceImpl implements AbilityWorkFlowService {
    private static final Logger logger = LoggerFactory.getLogger(AbilityWorkFlowServiceImpl.class);
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OkHttpUtils okHttpUtils;
    @Value("${zqztability.appId}")
    private String appId;
    @Value("${zqztability.appSecret}")
    private String appSecret;
    @Value("${zqztability.clyyAppId}")
    private String clyyAppId;
    @Value("${zqztability.clyyAppSecret}")
    private String clyyAppSecret;
    @Value("${zqztability.url.selectprojectData}")
    private String selectprojectDataURL;
    @Value("${zqztability.url.getApproveHistory}")
    private String getApproveHistoryURL;
    @Value("${zqztability.url.decisionProgressQuery}")
    private String decisionProgressQueryURL;
    @Value("${zqztability.url.jikeOrderCenter}")
    private String jikeOrderCenterUrl;
    @Value("${zqztability.url.orderCenterflowTrack}")
    private String orderCenterflowTrackUrl;
    @Value("${zqztability.url.qryOsslBusiColbInfoByOrderId}")
    private String qryOsslBusiColbInfoByOrderIdUrl;
    @Value("${zqztability.url.getYwxyDdrInfoByNo}")
    private String getYwxyDdrInfoByNoUrl;
    @Value("${zqztability.apiAuth.getApproveHistory.wyjAppId}")
    private String wyjAppId;
    @Value("${zqztability.apiAuth.getApproveHistory.wyjRequestPublicKey}")
    private String wyjRequestPublicKey;
    @Value("${zqztability.apiAuth.getApproveHistory.wyjRequestPrivateKey}")
    private String wyjRequestPrivateKey;
    @Value("${zqztability.apiAuth.getApproveHistory.wyjResponsePublicKey}")
    private String wyjResponsePublicKey;
    @Value("${zqztability.apiAuth.getApproveHistory.wyjResponsePrivateKey}")
    private String wyjResponsePrivateKey;

    /**
     * 调用统一流程：投资申请列表查询接口
     *
     * @param param
     * @return
     */
    @Override
    public JSONObject selectProjectData(WorkFlowDTO param) {
        logger.info("统一流程：投资申请列表查询接口入参：{}", JSON.toJSONString(param));
        AbilityPlatformService abilityPlatformService = new AbilityPlatformService(restTemplate, appId, appSecret, selectprojectDataURL);
        JSONObject request = new JSONObject(true) {
            {
                put("city", param.getCity());
                put("inc", param.getInc());
                put("user", param.getUser());
                put("stime", param.getStime());
                put("etime", param.getEtime());
                put("project", param.getProject());
                put("client", param.getClient());
                put("status", param.getStatus());
                put("changjing", param.getChangjing());
                put("number", param.getNumber());
                put("pageNum", param.getPageNum());
                put("pageSize", param.getPageSize());
            }
        };
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("SELECT_PROJECT_DATA_REQ", request);
        JSONObject attached = new JSONObject();
        attached.put("MEDIA_INFO", "");

        logger.info("统一流程-能开：投资申请列表查询-入参：{}", JSON.toJSONString(request));
        // JSONObject resp = getLocalData("selectprojectData");
        JSONObject abilityResp = abilityPlatformService.doPost(attached, uniBssBody);
        JSONObject resp = analysisAbilityResp(abilityResp, "SELECT_PROJECT_DATA_RSP");
        logger.info("统一流程-能开：投资申请列表查询-出参：{}", JSON.toJSONString(request));

        JSONArray data = resp.getJSONArray("data");
        Integer total = resp.getInteger("total");
        List<WorkFlowEntity> dataList = new ArrayList<>();
        if (data != null && data.size() > 0) {
            dataList = data.toJavaList(WorkFlowEntity.class);
        }
        JSONObject result = new JSONObject();
        result.put("data", dataList);
        result.put("total", total);
        return result;
    }

    /**
     * 调用统一流程：投资申请项目实施流程审批查询接口
     *
     * @param step
     * @param param
     * @return
     */
    @Override
    public WorkFlowDetailEntity getApproveHistory(ProcessStepEnum step, WorkFlowEntity param) {
        logger.info("统一流程：投资申请项目实施流程审批查询接口入参：{}", JSON.toJSONString(param));
        AbilityPlatformService abilityPlatformService = new AbilityPlatformService(restTemplate,appId, appSecret, getApproveHistoryURL);
        JSONObject request = new JSONObject(true);
        String incident = "";
        String flowName = "";
        if (step == ProcessStepEnum.TZSQ) {
            incident = param.getInc();
            flowName = param.getFlowname();
        } else if (step == ProcessStepEnum.XMSS) {
            incident = param.getProinc();
            flowName = param.getProflowname();
        }
        request.put("processname", flowName);
        request.put("incident", incident);
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("GET_APPROVE_HISTORY_REQ", request);
        JSONObject attached = new JSONObject();
        attached.put("MEDIA_INFO", "");

        logger.info("统一流程-能开：投资申请项目实施流程审批查询-入参：{}", JSON.toJSONString(request));
        // JSONObject resp = getLocalData("getApproveHistory");
        JSONObject abilityResp = abilityPlatformService.doPost(attached, uniBssBody);
        JSONObject resp = analysisAbilityResp(abilityResp, "GET_APPROVE_HISTORY_RSP");
        logger.info("统一流程-能开：投资申请项目实施流程审批查询-出参：{}", JSON.toJSONString(resp));

        JSONArray data = resp.getJSONArray("data");
        List<WorkFlowDetailNodeEntity> dataList = new ArrayList<>();
        if (data != null && data.size() > 0) {
            dataList = data.toJavaList(WorkFlowDetailNodeEntity.class);
        }
        HashMap<String, String> pendingStatusMap = new HashMap() {
            {
                put("0", "处理中");
                put("1", "已完成");
                put("2", "已删除");
                put("3", "取消");
            }
        };
        dataList.stream().forEach(item -> {
            String pendingStatus = item.getPendingStatus();
            item.setPendingStatusRemark(
                    pendingStatusMap.containsKey(pendingStatus) ? pendingStatusMap.get(pendingStatus) : pendingStatus);
        });
        WorkFlowDetailEntity workFlowDetail = new WorkFlowDetailEntity(step.getProcessName(), dataList);
        return workFlowDetail;
    }

    /**
     * 调用统一流程：投资申请项目实施流程审批查询接口
     *
     * @param step
     * @param param
     * @return
     */
    @Override
    public WorkFlowDetailEntity decisionProgressQuery(ProcessStepEnum step, WorkFlowEntity param) {
        logger.info("沃易建：投资决策数据查询接口入参：{}", JSON.toJSONString(param));
        AbilityPlatformService abilityPlatformService = new AbilityPlatformService(restTemplate, appId, appSecret, decisionProgressQueryURL);
        // 基础入参
        JSONObject requestParam = new JSONObject(true);
        // 流程实例单号
        requestParam.put("matterCode", "");
        requestParam.put("processInstanceNumber", param.getInc());
        String requestParamStr = JSONObject.toJSONString(requestParam);
        WyjCryptUtils wyjCryptUtils = WyjCryptUtils.getInstance(wyjAppId, wyjRequestPublicKey, wyjRequestPrivateKey, wyjResponsePublicKey, wyjResponsePrivateKey);
        WyjCryptEntity wyjEncryptEntity = wyjCryptUtils.encrypt(requestParamStr);
        // 重新组装入参
        JSONObject request = new JSONObject(true);
        request.put("svcCont", wyjEncryptEntity.getEncryptData());
        JSONObject tcpCont = new JSONObject(true);
        tcpCont.put("appId", wyjAppId);
        tcpCont.put("randomId", UUID.randomUUID().toString().replace("-", ""));
        tcpCont.put("reqTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        tcpCont.put("securityKey", wyjEncryptEntity.getEncryptSecurityKey());
        request.put("tcpCont", tcpCont);
        // 拼接能开参数
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("DECISION_PROGRESS_QUERY_REQ", request);
        JSONObject attached = new JSONObject();
        attached.put("MEDIA_INFO", "");

        logger.info("沃易建-能开：投资决策数据查询-入参：{}", JSON.toJSONString(request));
        // JSONObject resp = getLocalData("decisionProgressQueryAbility");
        JSONObject abilityResp = abilityPlatformService.doPost(attached, uniBssBody);
        JSONObject resp = analysisAbilityResp(abilityResp, "DECISION_PROGRESS_QUERY_RSP");
        logger.info("沃易建-能开：投资决策数据查询-出参：{}", JSON.toJSONString(resp));
        JSONObject dataRows = resp.getJSONObject("dataRows");
        String resSvcRetCont = dataRows.getString("svcRetCont");
        JSONObject resTcpCont = dataRows.getJSONObject("tcpCont");
        String resSecurityKey = resTcpCont.getString("securityKey");
        WyjCryptEntity decryptCryptEntity = wyjCryptUtils.decrypt(resSvcRetCont, resSecurityKey);
        String decryptData = decryptCryptEntity.getDecryptData();
        logger.info("沃易建-能开：投资决策数据查询-解密数据：{}", decryptData);
        // 处理步骤数据
        List<WorkFlowDetailNodeEntity> dataList = new ArrayList<>();
        if (StringUtils.isNotEmpty(decryptData) && !"null".equals(decryptData)) {
            // JSONObject decryptJSON = JSONObject.parseObject(decryptData);
            // JSONArray progressInfo = decryptJSON.getJSONArray("progressInfo");
            JSONArray progressInfo = JSONArray.parseArray(decryptData);
            if (progressInfo != null && progressInfo.size() > 0) {
                dataList = progressInfo.toJavaList(WorkFlowDetailNodeEntity.class);
            }
            HashMap<String, String> pendingStatusMap = new HashMap() {
                {
                    put("0", "处理中");
                    put("1", "已完成");
                    put("2", "退回");
                    put("3", "终止");
                    put("4", "撤回");
                    put("5", "转派");
                }
            };
            dataList.stream().forEach(item -> {
                String pendingStatus = item.getPendingStatus();
                item.setPendingStatusRemark(pendingStatusMap.containsKey(pendingStatus) ? pendingStatusMap.get(pendingStatus) : pendingStatus);
            });
        }
        WorkFlowDetailEntity workFlowDetail = new WorkFlowDetailEntity(step.getProcessName(), dataList);
        return workFlowDetail;
    }

    /**
     * 订单详细查询接口
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @Override
    public JikeOrderCenterResponse jikeOrderCenterRequest(JikeOrderCenterRequest param) {
        JSONObject testResp = getLocalData("jikeOrderCenterRequest");
        if (testResp != null) {
            return JikeOrderCenterResponse.fromJsonData(testResp.getJSONObject("DATA"));
        }
        AbilityPlatformService abilityPlatformService = new AbilityPlatformService(restTemplate, clyyAppId, clyyAppSecret, jikeOrderCenterUrl);
        JSONObject requestReq = new JSONObject(true);
        requestReq.put("APP_ID", param.getAppId());
        if (param.getOrderId() != null && !param.getOrderId().isEmpty()) {
            requestReq.put("ORDER_ID", param.getOrderId());
        }
        if (param.getSourceOrderId() != null && !param.getSourceOrderId().isEmpty()) {
            requestReq.put("SOURCE_ORDER_ID", param.getSourceOrderId());
        }
        if (param.getSourceMall() != null && !param.getSourceMall().isEmpty()) {
            requestReq.put("SOURCE_MALL", param.getSourceMall());
        }
        if (param.getIfGetGoodsAttrs() != null && !param.getIfGetGoodsAttrs().isEmpty()) {
            requestReq.put("IF_GET_GOODSATTRS", param.getIfGetGoodsAttrs());
        }
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("REQUEST_REQ", requestReq);
        JSONObject attached = new JSONObject();
        attached.put("MEDIA_INFO", "");
        JSONObject abilityResp = abilityPlatformService.doPost(attached, uniBssBody);
        JSONObject resp = analysisAbilityResp(abilityResp, "REQUEST_RSP");
        JSONObject respData = resp.getJSONObject("DATA");
        // 转换JSON数据为实体类
        JikeOrderCenterResponse result = JikeOrderCenterResponse.fromJsonData(respData);
        return result;
    }

    /**
     * 跟踪轨迹查询接口
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @Override
    public OrderFlowTrackResponse orderFlowTrackRequest(OrderFlowTrackRequest param) {
       /* JSONObject testResp = getLocalData("orderFlowTrackRequest");
        if (testResp != null) {
            JSONArray respDataArray = testResp.getJSONArray("DATA");
            return respDataArray != null && respDataArray.size() > 0 ? OrderFlowTrackResponse.fromJsonData(respDataArray.getJSONObject(0)) : new OrderFlowTrackResponse();
        }*/
        AbilityPlatformService abilityPlatformService = new AbilityPlatformService(restTemplate, clyyAppId, clyyAppSecret, orderCenterflowTrackUrl);
        JSONObject requestReq = new JSONObject(true);
        requestReq.put("ORDER_ID", param.getOrderId());
        requestReq.put("APP_ID", param.getAppId());
        // 处理订单属性参数
        if (param.getParam() != null && !param.getParam().isEmpty()) {
            JSONArray paramArray = new JSONArray();
            for (OrderFlowTrackRequest.OrderParam orderParam : param.getParam()) {
                JSONObject paramJson = new JSONObject();
                paramJson.put("PARAM_CODE", orderParam.getParamCode());
                paramJson.put("PARAM_VALUE", orderParam.getParamValue());
                paramArray.add(paramJson);
            }
            requestReq.put("PARAM", paramArray);
        }
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("GET_FLOW_TRACK_REQ", requestReq);
        JSONObject attached = new JSONObject();
        attached.put("MEDIA_INFO", "");
        JSONObject abilityResp = abilityPlatformService.doPost(attached, uniBssBody);
        JSONObject resp = analysisAbilityResp(abilityResp, "GET_FLOW_TRACK_RSP");
        JSONArray respDataArray = resp.getJSONArray("DATA");
        // 转换JSON数据为实体类，取第一个DATA元素
        OrderFlowTrackResponse result = new OrderFlowTrackResponse();
        if (respDataArray != null && respDataArray.size() > 0) {
            JSONObject respData = respDataArray.getJSONObject(0);
            result = OrderFlowTrackResponse.fromJsonData(respData);
        }
        return result;
    }

    /**
     * 协调单信息查询
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @Override
    public QryOsslBusiColbInfoByOrderIdResponse queryOsslBusiColbInfoByOrderId(QryOsslBusiColbInfoByOrderIdRequest param) {
        /*JSONObject testResp = getLocalData("queryOsslBusiColbInfoByOrderId");
        if (testResp != null) {
            JSONObject dataJson = testResp.getJSONObject("data");
            return QryOsslBusiColbInfoByOrderIdResponse.fromJsonData(dataJson);
        }*/
        AbilityPlatformService abilityPlatformService = new AbilityPlatformService(restTemplate, clyyAppId, clyyAppSecret, qryOsslBusiColbInfoByOrderIdUrl);
        // 构建请求参数
        JSONObject request = new JSONObject();
        if (param.getSpecialLinePpenId() != null) {
            request.put("specialLinePpenId", param.getSpecialLinePpenId());
        }
        if (param.getInvestInstanceAppalId() != null) {
            request.put("investInstanceAppalId", param.getInvestInstanceAppalId());
        }
        JSONObject uniBssBody = new JSONObject();
        uniBssBody.put("QRY_OSSL_BUSI_COLB_INFO_BY_ORDER_ID_REQ", request);
        JSONObject attached = new JSONObject();
        attached.put("MEDIA_INFO", "");
        JSONObject abilityResp = abilityPlatformService.doPost(attached, uniBssBody);
        JSONObject resp = analysisAbilityResp(abilityResp, "QRY_OSSL_BUSI_COLB_INFO_BY_ORDER_ID_RSP");
        JSONObject respData = resp.getJSONObject("data");
        QryOsslBusiColbInfoByOrderIdResponse result = QryOsslBusiColbInfoByOrderIdResponse.fromJsonData(respData);
        return result;
    }

    /**
     * 电子运维查询
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @Override
    public GetYwxyDdrInfoByNoResponse getYwxyDdrInfoByNo(GetYwxyDdrInfoByNoRequest param) {
      /*  JSONObject testResp = getLocalData("getYwxyDdrInfoByNo");
        if (testResp != null) {
            return GetYwxyDdrInfoByNoResponse.fromJsonData(testResp);
        }*/
        try {
            JSONObject requestBody = new JSONObject();
            requestBody.put("no", param.getNo());
            logger.info("电子运维查询调用开始，入参：{}",requestBody.toJSONString());
            String responseStr = okHttpUtils.postData(getYwxyDdrInfoByNoUrl, requestBody.toJSONString());
            logger.info("电子运维查询调用完成，出参：{}",responseStr);
            GetYwxyDdrInfoByNoResponse result = new GetYwxyDdrInfoByNoResponse();
            if (responseStr != null && !responseStr.startsWith("Error")) {
                JSONObject responseBody = JSONObject.parseObject(responseStr);
                String code = responseBody.getString("code");
                String msg = responseBody.getString("msg");
                if ("1".equals(code)) {
                    String dataStr = responseBody.getString("data");
                    if (dataStr != null && !dataStr.isEmpty()) {
                        JSONObject dataJson = JSONObject.parseObject(dataStr);
                        result = GetYwxyDdrInfoByNoResponse.fromJsonData(dataJson);
                    }
                } else {
                    logger.error("山东联通业务响应管理查询接口调用失败，code：{}，msg：{}", code, msg);
                }
            } else {
                logger.error("山东联通业务响应管理查询接口调用失败，响应：{}", responseStr);
            }
            return result;
        } catch (Exception e) {
            logger.error("山东联通业务响应管理查询接口调用异常：", e);
            return new GetYwxyDdrInfoByNoResponse();
        }
    }

    /**
     * 解析能开响应
     *
     * @param abilityResp
     * @param abilityRespKey
     * @return
     */
    private JSONObject analysisAbilityResp(JSONObject abilityResp, String abilityRespKey) {
        JSONObject resp = new JSONObject();
        if (abilityResp != null && abilityResp.getJSONObject("reqJson") != null
                && abilityResp.getJSONObject("rspJson") != null) {
            JSONObject rspBody = abilityResp.getJSONObject("rspJson").getJSONObject("UNI_BSS_BODY");
            if (rspBody != null) {
                if (rspBody.getJSONObject(abilityRespKey) != null) {
                    resp = rspBody.getJSONObject(abilityRespKey);
                }
            }
        }
        return resp;
    }

    private JSONObject getLocalData(String type) {
        switch (type) {
            case "selectprojectData":
                return getSelectProjectDataSample();
            case "getApproveHistory":
                return getApproveHistorySample();
            case "decisionProgressQuery":
                return getDecisionProgressQuerySample();
            case "decisionProgressQueryAbility":
                return getDecisionProgressQueryAbilitySample();
            case "jikeOrderCenterRequest":
                return getJikeOrderCenterSample();
            case "orderFlowTrackRequest":
                return getOrderFlowTrackSample();
            case "queryOsslBusiColbInfoByOrderId":
                return getQryOsslBusiColbInfoByOrderIdSample();
            case "getYwxyDdrInfoByNo":
                return getYwxyDdrInfoByNoSample();
            default:
                return null;
        }
    }

    /**
     * 生成投资申请列表查询接口测试数据
     */
    private JSONObject getSelectProjectDataSample() {
        JSONArray selectprojectData = new JSONArray();
        for (int i = 0; i < 10; i++) {
            JSONObject selectprojectObj = new JSONObject() {
                {
                    put("city", "济南");
                    put("inc", "8");
                    put("flownam", "申请流程名称");
                    put("proinc", "");
                    put("proflowname", "项目实施流程名称");
                    put("projecttype", "实际应用型");
                    put("user", "沈刚");
                    put("stime", "2026-03-01 23:59:59");
                    put("etime", "2026-03-01 23:59:59");
                    put("project", "山能永锋精细化工(山东)有限公司实际应用型钢焦一体化临港5G智慧钢铁项目");
                    put("client", "山能永锋精细化工(山东)有限公司");
                    put("status", "处理中");
                    put("changjing", "工业互联网");
                    put("number", "");
                    put("wtfx",
                            "本项目建设涉及四大车运行区域5G全覆園挣痉潷从，采用2.1G和3.5G双频组网，5G信号强度、信噪比、时延等指标可用，保证四大车5G专网高可用性、高可靠性，实现数据5G独立专网、基站独享、焦化四大车数据采集和控制处理中本地落地");
                    put("xqfx", "5G独立专网、基站独享、焦化四大车数据采集和控制");
                }
            };
            boolean isWc = RandomUtil.randomBoolean();
            selectprojectObj.put("inc", i);
            selectprojectObj.put("status", isWc ? "处理中" : "已完成");
            if (isWc) {
                Boolean jc = RandomUtil.randomBoolean();
                selectprojectObj.put("number", jc ? "投资决策单号" : "");
                selectprojectObj.put("proinc", RandomUtil.randomBoolean() ? "项目实施流程实例单号" : "");
            }
            selectprojectData.add(selectprojectObj);
        }
        return new JSONObject() {
            {
                put("ReturnCode", "200");
                put("ReturnMsg", "成功");
                put("total", 25);
                put("data", selectprojectData);
            }
        };
    }

    /**
     * 生成投资申请项目实施流程审批查询接口测试数据
     */
    private JSONObject getApproveHistorySample() {
        JSONArray getApproveHistoryData = new JSONArray();
        for (int i = 0; i < 4; i++) {
            JSONObject getApproveHistoryObj = new JSONObject() {
                {
                    put("startTime", "2026-03-01 23:59:59");
                    put("endTime ", "2026-03-01 23:59:59");
                    put("stepName", "步骤名称");
                    put("userName", "处理人");
                    put("userCode", "处理人账号");
                    put("userTel", "18612345678");
                    put("notion", "同意：同意。审批状态 0处理中，1已完成，2已删除, 3取消");
                    put("pendingStatus", 0);
                }
            };
            getApproveHistoryObj.put("stepName", "步骤名称" + i);
            getApproveHistoryObj.put("pendingStatus", i);
            getApproveHistoryData.add(getApproveHistoryObj);
        }
        return new JSONObject() {
            {
                put("ReturnCode", "200");
                put("ReturnMsg", "成功");
                put("data", getApproveHistoryData);
            }
        };
    }

    /**
     * 生成投资决策数据查询接口测试数据
     */
    private JSONObject getDecisionProgressQuerySample() {
        JSONArray progressInfoData = new JSONArray();
        for (int i = 0; i < 6; i++) {
            JSONObject progressInfoObj = new JSONObject() {
                {
                    put("startTime", "2017-12-31 11:07:54");
                    put("endTime ", "2018-01-01 10:10:18");
                    put("stepName", "需求编制");
                    put("userName", "处理人");
                    put("userCode", "chenyx");
                    put("userTel", "18612345678");
                    put("notion", "请协助资源核查  0处理中、1已完成、2退回、3终止、4 撤回  5转派 ");
                    put("pendingStatus", 0);
                }
            };
            progressInfoObj.put("stepName", "步骤名称" + i);
            progressInfoObj.put("pendingStatus", i);
            progressInfoData.add(progressInfoObj);
        }
        return new JSONObject() {
            {
                put("code", "200");
                put("msg", "成功");
                put("progressInfo", progressInfoData);
            }
        };
    }

    /**
     * 生成投资决策数据查询接口-能开测试数据
     */
    private JSONObject getDecisionProgressQueryAbilitySample() {
        JSONObject decisionProgressQueryRes = new JSONObject();
        decisionProgressQueryRes.put("retCode", "000");
        decisionProgressQueryRes.put("retValue", "投资决策数据查询接口调用成功！");
        JSONObject custDataRows = new JSONObject();
        custDataRows.put("svcRetCont",
                "sElXrpMNa6l4o9ygSeo+CYTgCl7dmbeQnR8HX3/4M3r7pDrND9NfTmpgy7kWT9GnhOAKXt2Zt5CdHwdff/gzeoiOJ+y8tAa7YojJDhWPTrE0+ls0/ex4NhX61uDQrALgnN6lbT5daPFClfnD+Cs0Xwa9g5/AumgRpH/F5WjDcjfoev/nuvKSKcjYi1RQyobwLA/93aagDAYBD7cYqpEW87G2a8KLGsW6kFxc0q8qK0ogNYRSGiqttM0LWsbxDKeq5vSqyJUgr3O01weiSu5toFiEVbYUwyYBbyC9R6B/45LGkJTYBNKPSXiDABVC99faIoO90ZUCqE1LmuV2gvyHwGTH0TZBJdvIU818BfEvEgj3rzFj/GUiI3p8dFjF1edZL+Lf5DHAuW1QmlgeNaQmIn5R1TcxOucx52YC0Vi3zBBEzn/ZcQzS0bAX5yzXxbgh03s936Zg67IsXKm4AanUug==");
        JSONObject custTcpCont = new JSONObject();
        custTcpCont.put("rspCode", "000");
        custTcpCont.put("rspMsg", "投资决策数据查询接口调用成功！");
        custTcpCont.put("appId", "d033d8d6a4714108aaa0c6f05a78fbba");
        custTcpCont.put("messageId", "076aa8a3f869417fbca5fc24a4e47512");
        custTcpCont.put("securityKey",
                "BEJw7v1EbpUp3TIwxTepOXpVLHEuOar3SqLwcPbqJ7Jc2/Cq+uVA+OUtTJPh7r70oNewOmTa8g8OoRFSUriAmCmKu/7kkpQA79A=");
        custDataRows.put("tcpCont", custTcpCont);
        decisionProgressQueryRes.put("dataRows", custDataRows);
        decisionProgressQueryRes.put("rowCount", null);
        return decisionProgressQueryRes;
    }

    /**
     * 订单详细查询接口测试数据
     */
    private JSONObject getJikeOrderCenterSample() {
        String jsonStr = "{\"UNI_BSS_BODY\":{\"REQUEST_RSP\":{\"DATA\":{\"FLOW_VARIABLE\":[{\"NAME\":\"rentTriggerCount\",\"VALUE\":\"2\"}],\"DELIVERY\":[],\"REQUEST\":{\"SOURCE_ORDER_ID\":\"825070943429798\",\"SOURCE_MALL\":\"24\",\"SOURCE_MALL_NAME\":\"运营平台销售模块\",\"APP_ID\":\"XAkrsbpYlL\",\"VERSION\":1,\"SERIAL_NO\":\"20250709101102755703537258496\"},\"PAYMENTS\":[],\"CONTRACT\":[{\"ATTACHMENTS\":[{\"CATAGORY\":\"13\",\"NAME\":\"photo.jpg\",\"ACCESSORY_ID\":\"76aba2da54cb0dd62c4238ecfabc7fbc\",\"URL\":\"20230718/mHZZUNDgcY/df3bdcc0ff284e9cbb4931fcd3fcca45.jpg\"}],\"INSTANCE_ID\":\"HT2507090000121_25070***********\",\"ORDER_ID\":25070***********,\"CONTRACT_TYPE\":\"01\",\"CHANGE_TYPE\":\"0\",\"BATCH_CODE\":\"20230718091559978\",\"NAME\":\"测试客户zy0321\",\"CONTRACT_CODE\":\"17XY2023071800001\",\"ATTRS\":[{\"CODE\":\"contractAmount\",\"END_DATE\":\"\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"11111.11\",\"START_DATE\":\"\"},{\"CODE\":\"provinceCode\",\"END_DATE\":\"\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"17\",\"START_DATE\":\"\"},{\"CODE\":\"contractStatusCode\",\"END_DATE\":\"\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"205\",\"START_DATE\":\"\"},{\"CODE\":\"CONTRACT_LIFECYCLE\",\"END_DATE\":\"\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"\",\"START_DATE\":\"\"},{\"CODE\":\"EXECUTOR_START\",\"END_DATE\":\"\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"\",\"START_DATE\":\"\"},{\"CODE\":\"EXECUTOR_END\",\"END_DATE\":\"\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"\",\"START_DATE\":\"\"}],\"LEVEL_ATTRS\":{\"E_X_E_C_U_T_O_R__S_T_A_R_T\":\"\",\"PROVINCE_CODE\":\"17\",\"CONTRACT_AMOUNT\":\"11111.11\",\"CONTRACT_STATUS_CODE\":\"205\",\"C_O_N_T_R_A_C_T__L_I_F_E_C_Y_C_L_E\":\"\",\"E_X_E_C_U_T_O_R__E_N_D\":\"\"}}],\"FLOW_DATA\":[{\"FLOW_VALUE\":\"[]\",\"FLOW_DATA_ID\":\"\",\"ORDER_ID\":25070***********,\"FLOW_KEY\":\"UserCenterOrderSync\",\"TASK_ID\":\"A0007073174\"},{\"FLOW_VALUE\":\"[{\\\"code\\\":\\\"lm_5260\\\",\\\"grouptype\\\":\\\"0\\\",\\\"groupId\\\":\\\"20250709104438690\\\",\\\"value\\\":\\\"2025-07-09\\\"},{\\\"code\\\":\\\"REC_10057\\\",\\\"grouptype\\\":\\\"0\\\",\\\"groupId\\\":\\\"20250709104438690\\\",\\\"value\\\":\\\"2025-07-09\\\"}]\",\"FLOW_DATA_ID\":\"\",\"ORDER_ID\":25070***********,\"FLOW_KEY\":\"OrderStartRent\",\"TASK_ID\":\"A0007073173\"}],\"ORDER_RELATION\":{\"ATTRS\":[]},\"DISCOUNT\":[{\"ATTACHMENTS\":[{\"CATAGORY\":\"14\",\"CATAGORY_NAME\":\"资费审批表\",\"NAME\":\"1-1.jpeg\",\"URL\":\"20250709/3/86f92f62cce64946850e0e71a7c4f2ac.jpeg\"}],\"INSTANCE_ID\":\"ZK2507090000070_25070***********\",\"DISCOUNT_CODE\":\"YL202507091308\",\"ORDER_ID\":25070***********,\"CHANGE_TYPE\":\"0\",\"NAME\":\"1-1\",\"TYPE\":\"14\",\"DEST_ITEM_ID\":\"YL202507091308\"}],\"GOODS\":[{\"FEES\":[{\"DISCNT_CODE\":\"90020365\",\"CODE\":\"65041023\",\"INSTANCE_ID\":\"FY2507090000411_25070***********\",\"ORDER_ID\":25070***********,\"CHANGE_TYPE\":\"0\",\"GOODS_INST_ID\":\"SP2507090000183_25070***********\",\"FEE_NAME\":\"虚拟专网UPF分流月功能费\",\"TYPE\":\"monthly\",\"DEST_ITEM_ID\":\"6000000024903508\",\"ROOT_ORDER_ID\":25070***********,\"ATTRS\":[{\"CODE\":\"200000169\",\"END_DATE\":\"**************\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"12\",\"START_DATE\":\"**************\"}],\"LEVEL_ATTRS\":{\"200000169\":\"12\"}}],\"GROUP_TYPE\":\"\",\"CODE\":\"********\",\"ORDER_ID\":\"25070***********\",\"DISCOUNT_INST_ID\":\"ZK2507090000070_25070***********\",\"CHANGE_TYPE\":\"0\",\"CONTRACT\":\"$.contract[instanceId = 'HT2507090000121_25070***********']\",\"COUNT\":1,\"DISCOUNT\":\"$.discount[instanceId = 'ZK2507090000070_25070***********']\",\"PAY_RELATION\":[{\"EFFECTIVE_MODE\":\"\",\"END_DATE\":\"**************\",\"ORDER_ID\":25070***********,\"CHANGE_TYPE\":\"0\",\"PAY_LIMIT\":\"0\",\"PAY_WAY\":\"0\",\"PAYITEM_CODE\":\"0\",\"ROOT_ORDER_ID\":25070***********,\"FEE_CYCLE\":\"1\",\"IS_DEFAULT\":\"1\",\"INSTANCE_ID\":\"GX2507090000141_25070***********\",\"GOODS_INST_ID\":\"SP2507090000183_25070***********\",\"PAYITEM_CODE_NAME\":\"全部\",\"ACCOUNT_CYCLE\":\"1\",\"ACCOUNT_INST_ID\":\"ZH2507090000115_25070***********\",\"START_DATE\":\"**************\",\"ACCOUNT\":\"$.accounts[instanceId = 'ZH2507090000115_25070***********']\"}],\"USER_INST_ID\":\"YH2507090000205_25070***********\",\"CATAGORY_ID_CHAIN\":\"2004652,2504602,2504614\",\"ROOT_ORDER_ID\":25070***********,\"PRODUCTS\":[{\"CODE\":\"********\",\"ORDER_ID\":25070***********,\"CHANGE_TYPE\":\"0\",\"TYPE\":\"base\",\"ROOT_ORDER_ID\":25070***********,\"LEVEL_ATTRS\":{\"*********\":\"测试\"},\"ATTRS\":[{\"CODE\":\"*********\",\"END_DATE\":\"**************\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"测试\",\"START_DATE\":\"**************\"}],\"PRODUCT_CATE\":\"1008130\",\"INSTANCE_ID\":\"CP2507090000233_25070***********\",\"GOODS_INST_ID\":\"SP2507090000183_25070***********\",\"NAME\":\"虚拟专网-网络纯享版\",\"BRAND_CODE\":\"MECB\",\"SERVICE_TYPE_CODE\":\"WV\"}],\"GOODS_TYPE\":\"单商品\",\"GOOD_FLAG\":\"\",\"INSTANCE_ID\":\"SP2507090000183_25070***********\",\"SALE_GOODS_NAME\":\"虚拟专网-网络纯享版\",\"SALE_GOODS_CODE\":\"********\",\"CATAGORY_NAME\":\"虚拟专网-网络纯享版\",\"NAME\":\"虚拟专网-网络纯享版\",\"CONTRACT_INST_ID\":\"HT2507090000121_25070***********\",\"CATAGORY_ID\":\"2004923\"}],\"LINKMAN\":{\"ADDRESS\":\"\",\"PERSON_ID_TYPE\":\"\",\"CHANGE_TYPE\":\"0\",\"POSTCODE\":\"\",\"FIXED_PHONE\":\"\",\"REMARK\":\"\",\"PERSON_NUM\":\"\",\"PHONE\":\"19800000000\",\"NAME\":\"张三\",\"START_TIME\":\"**************\",\"END_TIME\":\"**************\",\"FAX\":\"\",\"EMAIL\":\"<EMAIL>\"},\"USERS\":[{\"DISTRICT_CODE_NAME\":\"莱城乡镇\",\"SERIAL_NUMBER\":\"170MEC52214059\",\"DISTRICT_CODE\":\"172092\",\"ORDER_ID\":25070***********,\"CITY_CODE\":\"170\",\"PAY_MODE\":\"0\",\"PROVINCE_CODE\":\"17\",\"CHANGE_TYPE\":\"0\",\"BILL_TYPE\":\"2\",\"REMARK\":\"\",\"CHARGE_CYCLE\":\"1\",\"USER_ID\":\"****************\",\"FEE_CYCLE\":\"1\",\"ATTRS\":[{\"CODE\":\"CON3015\",\"END_DATE\":\"**************\",\"CHANGE_TYPE\":\"0\",\"TYPE\":\"STRING\",\"VALUE\":\"0\",\"START_DATE\":\"**************\"}],\"LEVEL_ATTRS\":{\"C_O_N3015\":\"0\"},\"PROVINCE_CODE_NAME\":\"山东\",\"INSTANCE_ID\":\"YH2507090000205_25070***********\",\"USER_DIFF_CODE\":\"00\",\"NAME\":\"测试客户zy0321\",\"CITY_CODE_NAME\":\"济南\",\"ACCOUNT_CYCLE\":\"1\",\"OPEN_DATE\":\"**************\"}],\"ACCEPT\":{\"PARENT_DEPARTMENT_ID\":\"1709327\",\"DEPARTMENT_NAME\":\"长清高校营销服务中心\",\"CODE\":\"jksz-ceshisd\",\"EPARCHY_CODE_NAME\":\"济南\",\"PROVINCE_CODE\":\"17\",\"DEPARTMENT_ID\":\"1708040\",\"PROVINCE_CODE_NAME\":\"山东\",\"COUNTY_CODE\":\"172006\",\"AREA_CODE\":\"17a1iw\",\"MOBILE_PHONE\":\"***********\",\"NAME\":\"测试山东\",\"CERT_NUM\":\"\",\"STAFF_ID\":\"**********\",\"EPARCHY_CODE\":\"170\"},\"OA_ACCEPT\":{\"COUNTY_CODE\":\"\",\"ORDER_ID\":25070***********,\"PROVINCE_CODE\":\"09\",\"NAME\":\"测试山东\",\"PROVINCE_NAME\":\"总部\",\"EPARCHY_NAME\":\"\",\"ROOT_ORDER_ID\":25070***********,\"MARK\":2,\"OA_CODE\":\"jksz-ceshisd\",\"EPARCHY_CODE\":\"000\",\"COUNTY_NAME\":\"\"},\"DEVELOPER\":{\"IS_SEND_SMS\":\"0\",\"PHONE\":\"***********\",\"CHANGE_TYPE\":\"0\",\"NAME\":\"好吧\",\"DEPART_ID\":\"17a4189\",\"CHANNEL_NAME\":\"德州市乐陵市黄夹营业厅\",\"CHANNEL_ID\":\"17a4189\",\"STAFF_ID\":\"**********\"},\"USE_CUSTOMER\":[],\"ACCOUNTS\":[{\"ORDER_ID\":25070***********,\"EPARCHY_CODE_NAME\":\"济南\",\"IS_GROUP_ACCOUNT\":\"1\",\"PAY_MODE\":\"0\",\"PROVINCE_CODE\":\"17\",\"BSS_ID\":\"****************\",\"CHANGE_TYPE\":\"0\",\"CYCLE\":\"1\",\"CBSS_ID\":\"****************\",\"ACCOUNT_ID\":\"****************\",\"PROVINCE_CODE_NAME\":\"山东\",\"IS_DEFAULT\":1,\"INSTANCE_ID\":\"ZH2507090000115_25070***********\",\"PAY_MODE_NAME\":\"现金\",\"CUST_ID\":\"****************\",\"NAME\":\"测试客户zy0321\",\"EPARCHY_CODE\":\"170\"}],\"SUPPORT_MANAGER\":{\"STAFF_CODE\":\"jksz-ceshisd\",\"PHONE\":\"***********\",\"CHANGE_TYPE\":\"0\",\"NAME\":\"测试山东\",\"START_TIME\":\"**************\",\"END_TIME\":\"**************\"},\"RELATIONS\":[],\"ORDER\":{\"ORDER_TYPE\":\"0\",\"ORDER_ID\":25070***********,\"IS_EFFECT\":1,\"SEND_PROVINCE\":\"\",\"DEALMAN_CODE\":\"\",\"COMPLETE_DATE\":\"2025-07-09\",\"GOODS_NAMES\":\"虚拟专网-网络纯享版\",\"SUB_ORDER_ID\":\"[]\",\"TASK_CODE\":\"\",\"CHANGE_FEES\":\"0\",\"GOODS_CODES\":\"********\",\"GOODS_CATAGORIES_NAME\":\"虚拟专网-网络纯享版\",\"SERIAL_NUMBER\":\"170MEC52214059\",\"RECEIVE_DISTRICT\":\"\",\"FLOW_NODE_ID\":\"OrderArchive#UPF-DG_release_20250325\",\"ORDER_VERSION\":25,\"RECEIVE_PROVINCE\":\"\",\"CURRENT_VERSION\":25,\"ATTRS\":[{\"CODE\":\"ORD10303\",\"END_DATE\":\"**************\",\"TYPE\":\"STRING\",\"VALUE\":\"1\",\"START_DATE\":\"**************\"}],\"TASK_NAME\":\"\",\"ORDER_DATE\":\"2025-07-09\",\"STATUS\":\"1\",\"OA_DEALMAN_ID\":\"jksz-ceshisd,jksz-ceshisd\",\"SEND_CITY\":\"\",\"ATTACHMENTS\":[{\"CATAGORY\":\"09\",\"CATAGORY_NAME\":\"三方协议\",\"NAME\":\"910.xlsx\",\"UPLOAD_TIME\":\"2025-07-09 10:44:01\",\"URL\":\"20250709/3/d20206340e1b4da59f11efcdb34537c7.xlsx\"}],\"BIZ_TYPE\":\"1001\",\"EPARCHY_CODE_NAME\":\"济南\",\"BIZ_TYPE_NAME\":\"业务订购\",\"FLOW_NODE_NAME\":\"订单归档\",\"REMARK\":\"123456\",\"FLOW_INSTANCE_ID\":\"c64dfaba5ffe4c2493167df3c4ce9848\",\"LEVEL_ATTRS\":{\"O_R_D10303\":\"1\"},\"PROVINCE_CODE_NAME\":\"山东\",\"SEND_DISTRICT\":\"\",\"ALL_DEALMAN_CODES\":\"jksz-ceshisd\",\"STATUS_NAME\":\"已竣工\",\"START_TIME\":\"2025-07-09 10:44:19\",\"DEALMAN_NAME\":\"\",\"PROVINCE_CODE\":\"17\",\"USER_NAME\":\"\",\"GOODS_CATAGORIES\":\"2004923\",\"ROOT_ORDER_ID\":25070***********,\"DEALMAN_ID\":\"\",\"START_RENT_DATE\":\"2025-07-09 00:00:00\",\"UPDATE_METHOD\":\"1\",\"ORDER_TYPE_NAME\":\"正常订单\",\"RECEIVE_CITY\":\"\",\"TASK_ID\":\"\",\"EPARCHY_CODE\":\"170\"},\"CUSTOMER\":{\"CONTACT_NAME\":\"张雨\",\"BSS_ID\":\"****************\",\"GROUP_ID\":\"1716017209210012353\",\"ATTRS\":[{\"CODE\":\"custState\",\"END_DATE\":\"**************\",\"VALUE\":\"0\",\"START_DATE\":\"**************\"}],\"LEVEL_ATTRS\":{\"CUST_STATE\":\"0\"},\"CBSS_ID\":\"****************\",\"EPARCHY_PROVINCE\":\"17\",\"CUST_ID\":\"****************\",\"CUST_TYPE\":\"2\",\"NAME\":\"测试客户zy0321\",\"EPARCHY_CITY\":\"170\",\"EPARCHY_PROVINCE_NAME\":\"山东\",\"EPARCHY_CITY_NAME\":\"济南\",\"CONTACT_PHONE\":\"18614518773\"}},\"MESSAGE\":\"访问成功\",\"STATUS\":0}}}";
        JSONObject fullResponse = JSONObject.parseObject(jsonStr);
        return fullResponse.getJSONObject("UNI_BSS_BODY").getJSONObject("REQUEST_RSP");
    }

    /**
     * 跟踪轨迹查询接口测试数据
     */
    private JSONObject getOrderFlowTrackSample() {
        String jsonStr = "{\"GET_FLOW_TRACK_RSP\":{\"STATUS\":0,\"DATA\":[{\"ORDER\":{\"FLOW_NODE_ID\":\"OrderArchive#UPF-DG_release_20250325\",\"BIZ_TYPE_NAME\":\"业务订购\",\"ORDER_TYPE_NAME\":\"正常订单\",\"FLOW_NODE_NAME\":\"订单归档\",\"ORDER_TYPE\":\"0\",\"BIZ_TYPE\":\"1001\",\"IS_PARENT\":0,\"GOODS_CATAGORIES_NAME\":\"虚拟专网-网络纯享版\",\"STATUS_NAME\":\"已竣工\",\"GROUP_ID\":\"9090190254710024551\",\"SERIAL_NUMBER\":\"901MEC52203774\",\"STATUS\":\"1\",\"ORDER_ID\":2504231000346000,\"GOODS_CATAGORIES\":\"2004923\"},\"FLOW\":[{\"SERIAL_NUMBER\":\"901MEC52203774\",\"ORDER_ID\":\"2504231000346000\",\"FLOW_CODE\":\"c5b2df57e5c84fb78e5040577d583c9d_release_20250325\",\"FLOW_DATA\":[{\"DEAL_RESULT\":\"处理成功\",\"RULE_NAME\":\"流程结束\",\"ASSIGNEE_NAME\":\"\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"06be411f23db455583159f0e54ad3cd6\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"9976308504d6495f_release_20250325\",\"ASSIGNEE\":\"system\",\"TASK_TYPE\":\"2\",\"ID\":\"A0006931018\",\"MODULE_ID\":\"b6a0416025454565b2f1e2754c5335f6\",\"TIME_OUT\":1,\"SERVICE_MODULE_CODE\":\"OrderSolved\",\"FLOW_NODE_NAME\":\"订单归档\",\"NODE_KEY\":\"a774793b0b92495a_release_20250325\",\"ASSIGNEE_PHONE\":\"\",\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-24 10:45:38\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-24 10:45:38\",\"RULE_CODE\":\"a774793b0b92495a_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"MODULE_CODE\":\"OrderSolved\",\"STEP_CODE\":\"OrderArchive\"},{\"DEAL_RESULT\":\"处理成功\",\"RULE_NAME\":\"用户资料同步\",\"ASSIGNEE_NAME\":\"\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"445020e21ed84e83a49a741c484d4b20\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"1b712b5e96124310_release_20250325\",\"TASK_TYPE\":\"2\",\"ID\":\"A0006931017\",\"MODULE_ID\":\"ecdf1311c58844fba85389d2ae0d8279\",\"TIME_OUT\":1,\"SERVICE_MODULE_CODE\":\"UserCenterOrderSync\",\"FLOW_NODE_NAME\":\"订单起租\",\"NODE_KEY\":\"dc3bbebd4fb24458_release_20250325\",\"ASSIGNEE_PHONE\":\"\",\"FLOW_ID\":922337207236842,\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-24 10:45:37\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-24 10:45:34\",\"RULE_CODE\":\"dc3bbebd4fb24458_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"MODULE_CODE\":\"UserCenterOrderSync\",\"DEAL_OPTION\":\"入库成功！\",\"STEP_CODE\":\"OrderStartRent\"},{\"DEAL_RESULT\":\"处理成功\",\"RULE_NAME\":\"自动起租\",\"ASSIGNEE_NAME\":\"\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"445020e21ed84e83a49a741c484d4b20\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"1b712b5e96124310_release_20250325\",\"ASSIGNEE\":\"system\",\"TASK_TYPE\":\"2\",\"ID\":\"A0006931016\",\"MODULE_ID\":\"85164570721148758098533db00138e5\",\"TIME_OUT\":1,\"SERVICE_MODULE_CODE\":\"AutoRent\",\"FLOW_NODE_NAME\":\"订单起租\",\"NODE_KEY\":\"2b3dcc1c07aa4734_release_20250325\",\"ASSIGNEE_PHONE\":\"\",\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-24 10:45:34\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-24 10:45:34\",\"RULE_CODE\":\"2b3dcc1c07aa4734_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"MODULE_CODE\":\"AutoRent\",\"STEP_CODE\":\"OrderStartRent\"},{\"DEAL_RESULT\":\"处理成功\",\"RULE_NAME\":\"业务开通\",\"ASSIGNEE_NAME\":\"\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"b4909e59358c47ad898744e9fc071abb\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"b4adfe1def5d44c0_release_20250325\",\"TASK_TYPE\":\"2\",\"ID\":\"A0006930181\",\"MODULE_ID\":\"ae284270f2f54c80bcce0b45865727b7\",\"TIME_OUT\":1,\"SERVICE_MODULE_CODE\":\"virtualPrivateNetwork\",\"FLOW_NODE_NAME\":\"业务开通\",\"NODE_KEY\":\"e1edd2fdcbde419d_release_20250325\",\"ASSIGNEE_PHONE\":\"\",\"FLOW_ID\":922337207236577,\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-24 10:45:32\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-23 17:44:25\",\"RULE_CODE\":\"e1edd2fdcbde419d_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"MODULE_CODE\":\"virtualPrivateNetwork\",\"DEAL_OPTION\":\"\",\"STEP_CODE\":\"OrderServiceOpen\"},{\"DEAL_RESULT\":\"审核通过\",\"RULE_NAME\":\"省项目经理审核\",\"ASSIGNEE_NAME\":\"测试名\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"f5c2b787e77645e68bfd57483d3b9821\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"5dbe464b0ccf404d_release_20250325\",\"ASSIGNEE\":\"9100300230\",\"TASK_TYPE\":\"1\",\"ID\":\"A0006930179\",\"MODULE_ID\":\"804a734d96fe450a84ec77b494c27113\",\"TIME_OUT\":1,\"SERVICE_MODULE_CODE\":\"OrderOpenProvinceAudit\",\"FLOW_NODE_NAME\":\"订单审核\",\"NODE_KEY\":\"7da8ed95a610406c_release_20250325\",\"ASSIGNEE_PHONE\":\"15652670936\",\"FLOW_ID\":922337207236575,\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-23 17:44:25\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-23 17:44:17\",\"RULE_CODE\":\"7da8ed95a610406c_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"MODULE_CODE\":\"OrderOpenProvinceAudit\",\"DEAL_OPTION\":\"\",\"STEP_CODE\":\"OrderAudit\"},{\"DEAL_RESULT\":\"审核通过\",\"RULE_NAME\":\"地市项目经理审核\",\"ASSIGNEE_NAME\":\"测试名\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"f5c2b787e77645e68bfd57483d3b9821\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"5dbe464b0ccf404d_release_20250325\",\"ASSIGNEE\":\"9100300230\",\"TASK_TYPE\":\"1\",\"ID\":\"A0006930178\",\"MODULE_ID\":\"bdbd0be63ade4fbea0a625f46549f7e6\",\"TIME_OUT\":1,\"SERVICE_MODULE_CODE\":\"OrderOpenCityAudit\",\"FLOW_NODE_NAME\":\"订单审核\",\"NODE_KEY\":\"8ddc0008d4d147b3_release_20250325\",\"ASSIGNEE_PHONE\":\"15652670936\",\"FLOW_ID\":922337207236574,\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-23 17:44:17\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-23 17:44:02\",\"RULE_CODE\":\"8ddc0008d4d147b3_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"MODULE_CODE\":\"OrderOpenCityAudit\",\"DEAL_OPTION\":\"\",\"STEP_CODE\":\"OrderAudit\"},{\"DEAL_RESULT\":\"处理成功\",\"RULE_NAME\":\"订单归集\",\"ASSIGNEE_NAME\":\"\",\"FLOW_INST_ID\":\"89ab651843cb49628c4af965d85a62d3\",\"INST_ID\":\"6a2e8b328c0a41268ae975533c952c00\",\"STATUS\":1,\"FLOW_NODE_CODE\":\"c74fe90294184049_release_20250325\",\"ASSIGNEE\":\"system\",\"TASK_TYPE\":\"2\",\"ID\":\"A0006930177\",\"MODULE_ID\":\"\",\"TIME_OUT\":1,\"FLOW_NODE_NAME\":\"订单归集\",\"NODE_KEY\":\"0708c81cdecf4f67_release_20250325\",\"ASSIGNEE_PHONE\":\"\",\"FLOW_COMPLETE_STATE\":\"1\",\"FINISH_DT\":\"2025-04-23 17:44:02\",\"IS_INFORM_TASK\":\"0\",\"CREATE_DT\":\"2025-04-23 17:44:02\",\"RULE_CODE\":\"0708c81cdecf4f67_release_20250325\",\"ORDER_ID\":\"2504231000346000\",\"STEP_CODE\":\"OrderCollection\"}]}]}]}}";
        JSONObject fullResponse = JSONObject.parseObject(jsonStr);
        return fullResponse.getJSONObject("GET_FLOW_TRACK_RSP");
    }

    /**
     * 协调单信息查询接口测试数据
     */
    private JSONObject getQryOsslBusiColbInfoByOrderIdSample() {
        String jsonStr = "{\"UNI_BSS_ATTACHED\":{\"MEDIA_INFO\":\"\"},\"UNI_BSS_BODY\":{\"QRY_OSSL_BUSI_COLB_INFO_BY_ORDER_ID_RSP\":{\"rspCode\":\"0000\",\"rspInfo\":\"查询成功\",\"data\":{\"areaId\":\"本部\",\"busiColbNtfShtNo\":\"2025-XT0000078692\",\"submitTime\":\"2025-04-23 09:24:51\",\"custName\":\"党政用户调整\",\"serviceType\":\"国际漫游业务\",\"sheetTitle\":\"测试是否发送待办\",\"busiReqContent\":\"\",\"limitDate\":\"2025-04-30 12:00:00\",\"responseTime\":\"2025-04-30 12:00:00\",\"applyType\":\"特殊业务响应\",\"custMgrName\":\"测试名\",\"custMgrTel\":\"17899999999\",\"sheetStatus\":\"派单处理中\",\"flowinstId\":\"2025042309244681621881686400000001921680316100002805363471897366\",\"todoLogInfoList\":[{\"createTime\":\"2025-04-23 09:24:49.0\",\"endTime\":\"2025-04-23 09:24:50.0\",\"nodeName\":\"发起人\",\"staffName\":\"张志杰\",\"staffId\":\"zhangzj1113\",\"staffTel\":\"15615315258\",\"dealResult\":\"\"},{\"createTime\":\"2025-04-23 09:24:51.0\",\"nodeName\":\"省分一站式审核\",\"staffName\":\"张志杰\",\"staffId\":\"zhangzj1113\",\"staffTel\":\"15615315258\",\"dealResult\":\"\"}]}}},\"UNI_BSS_HEAD\":{\"RESP_DESC\":\"Success\",\"APP_ID\":\"fwK6J1s32V\",\"RESP_CODE\":\"00000\",\"TIMESTAMP\":\"2025-07-11 11:19:42.216\",\"TRANS_ID\":\"20250711111942216364882\"}}";
        JSONObject fullResponse = JSONObject.parseObject(jsonStr);
        return fullResponse.getJSONObject("UNI_BSS_BODY").getJSONObject("QRY_OSSL_BUSI_COLB_INFO_BY_ORDER_ID_RSP");
    }

    /**
     * 电子运维查询接口测试数据
     */
    private JSONObject getYwxyDdrInfoByNoSample() {
        String jsonStr = "{\"INSID\":\"12480478\",\"P_PERSON\":\"丁智伟\",\"BR_TITLE\":\"青岛智家工程师工作号配置\",\"D_SLBMMC\":\"青岛分公司\",\"RQ_SUBMITTER\":\"邢皓\",\"CONNMENT\":\"请协助在核心网上配置以下固话号码到智家工程师工作号管控平台。\\r\\n青岛号段：\\r\\n89847000-********，********-********，********-********，********-********，********-********，********-********，********-********，********-********\",\"FTIME\":*************,\"CITY\":\"\",\"D_SLR\":\"孟祥勇\",\"P_UNIT\":\"山东联通公司\",\"D_SSGNDD\":\"省分业务响应\",\"BR_NO\":\"山东联通网调【2025】响应24481号\",\"D_DDDSFSHR\":\"谢绍富\",\"D_YDYHSJDDNRMS\":\"请省分孟祥勇及青岛执行。\",\"FLOWS\":[{\"PRO_ACCOUNT\":\"xingh11\",\"STATUS\":\"1\",\"PRO_NAME\":\"邢皓\",\"END_DATE\":\"2025-05-26 09:56:15\",\"START_DATE\":\"2025-05-26 09:56:15\",\"STEP\":\"开始节点\",\"PRO_TEL\":\"***********\",\"EVENT\":\"业务响应主流程\"},{\"PRO_ACCOUNT\":\"mabo1\",\"STATUS\":\"1\",\"PRO_NAME\":\"马波\",\"END_DATE\":\"2025-05-27 08:58:39\",\"START_DATE\":\"2025-05-27 08:58:39\",\"STEP\":\"任务转交\",\"PRO_TEL\":\"***********\",\"EVENT\":\"任务转交他人\"},{\"PRO_ACCOUNT\":\"sd_dingzw\",\"STATUS\":\"1\",\"PRO_NAME\":\"丁智伟\",\"END_DATE\":\"2025-05-27 11:06:47\",\"START_DATE\":\"2025-05-27 11:06:47\",\"STEP\":\"省业务部门经理签发\",\"PRO_TEL\":\"***********\",\"EVENT\":\"工作已经接收\"},{\"PRO_ACCOUNT\":\"xingh11\",\"STATUS\":\"1\",\"PRO_NAME\":\"邢皓\",\"END_DATE\":\"2025-05-27 16:21:37\",\"START_DATE\":\"2025-05-27 14:15:24\",\"STEP\":\"省分业务部门填写申请单\",\"PRO_TEL\":\"***********\"},{\"PRO_ACCOUNT\":\"mabo1\",\"STATUS\":\"1\",\"PRO_NAME\":\"马波\",\"END_DATE\":\"2025-05-30 20:24:15\",\"START_DATE\":\"2025-05-30 20:24:15\",\"STEP\":\"任务转交\",\"PRO_TEL\":\"***********\",\"EVENT\":\"任务转交他人\"}]}";
        JSONObject dataJson = JSONObject.parseObject(jsonStr);
        return dataJson;
    }
}
