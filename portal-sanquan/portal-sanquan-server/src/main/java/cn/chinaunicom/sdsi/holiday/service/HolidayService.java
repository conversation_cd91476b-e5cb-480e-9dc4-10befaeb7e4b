package cn.chinaunicom.sdsi.holiday.service;

import cn.chinaunicom.sdsi.cloud.holiday.entity.Holiday;
import cn.chinaunicom.sdsi.cloud.holiday.query.HolidayQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
/**
 * <p>
 * 节假日表（包含周末、法定假日、调休等） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
public interface HolidayService extends IService<Holiday> {

    // 计算两个 LocalDateTime 之间的实际工作时间（排除休息日）
    double calculateWorkingHours(LocalDateTime start, LocalDateTime end, HolidayQueryVo holidayVo);

    // 从开始时间累计增加指定天数（自动跳过节假日）
    Date calculateSkipHoliday(Date start, int addDays, HolidayQueryVo holidayVo);

    // 分页查询
    IPage<Holiday> findPage(HolidayQueryVo holidayVo);

    // 根据id查询
    Holiday findOne(String id);

    // 查询列表
    List<Holiday> findList(HolidayQueryVo holidayVo);

    // 新增
    int add(Holiday holiday);

    // 修改
    int update(Holiday holiday);

    // 删除
    int delete(String id);

}
