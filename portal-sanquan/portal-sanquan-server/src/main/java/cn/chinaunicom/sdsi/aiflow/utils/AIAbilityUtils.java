package cn.chinaunicom.sdsi.aiflow.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

@Component
public class AIAbilityUtils {
    @Autowired
    private HttpUtils httpUtils;
    @Value("${ds.ability.url}")
    private String abilityDsUrl;
    @Value("${rag.ability.url}")
    private String ragAbilityDsUrl;
    /**
     * 调用ds接口
     * @param prompt
     * @return
     */
    public String callDsApi(String prompt){
        try {
            JSONObject param = new JSONObject();
            param.put("user_id","zhaoy590");
            param.put("stream",true);
            param.put("model","qwen2");
            JSONObject message = new JSONObject();
            message.put("role","user");
            message.put("content",prompt);
            JSONArray messages = new JSONArray(){{add(message);}};
            param.put("messages",messages);
            String response = "关键词1\n" +
                    "关键词2\n" +
                    "关键词3\n" +
                    "关键词4\n" +
                    "关键词5\n" +
                    "关键词6\n" +
                    "关键词7\n" +
                    "关键词8";
            response = httpUtils.dsPostData(abilityDsUrl, JSONObject.toJSONString(param));
            response = response.replaceAll("(?s)<think>.*?</think>","");
            response = response.replaceFirst("^\\n","");
            response = response.replaceFirst("^\\n","");
            return response;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 调用rag接口
     * @param knowId
     * @param query
     * @return
     */
    public String callRagApi(String knowId,String  query){
        try {
            JSONObject request = new JSONObject();
            request.put("knowledge_base_name",knowId);
            request.put("query",query);
            request.put("history",new ArrayList<>());
            request.put("top_k",1);
            request.put("stream",false);
            request.put("user_id","lxw");

             String response = "{\n" +
                     "    \"code\": 200,\n" +
                     "    \"data\": [\n" +
                     "        {\n" +
                     "            \"fileName\": \"产品信息.xlsx\",\n" +
                     "            \"img\": \"\",\n" +
                     "            \"fileUrl\": {\n" +
                     "                \"knowledge_base_name\": \"1894337739579932674\",\n" +
                     "                \"user_id\": \"lxw\",\n" +
                     "                \"file_name\": \"产品信息.xlsx\"\n" +
                     "            },\n" +
                     "            \"pageContent\": \"下文与产品信息.xlsx有关，产品名称:物联网\\n产品定位:“基于中国联通5G+北斗+物联网全域覆盖能力，打造‘端-管-云-用’一体化的智慧农业数字底座，助力农业农村现代化与粮食安全战略落地”\\n目标客户:政府农业部门，规模化种植企业，畜牧养殖企业单位，农产品流通企业，农业畜牧业等事业单位\\n产品介绍:1. **感知层**：  \\n   - 5G智能传感器：土壤墒情（pH值/氮磷钾）、气象微站（光照/降雨/风速）  \\n   - 北斗农机终端：定位精度达厘米级，作业轨迹回放、面积自动核算  \\n   - 畜禽生物体征监测：RFID耳标+体温脉搏传感，发情期/疫病早期预警  \\n2. **网络层**：  \\n   - 联通自研农业专网：NB-IoT+LoRa混合组网，覆盖偏远农田（续航>3年）  \\n   - 安全通道：物联网卡APN隔离，数据加密传输（SM4国密算法）  \\n3. **平台层**：  \\n   - 联通“农业大脑”云平台：AI模型库（病虫害识别、产量预测）、区块链溯源接口  \\n   - 政府监管子平台：耕地“非农化”AI巡检、农机补贴发放数据核验  \\n4. **应用层**：  \\n   - 手机APP/小程序：农户实时告警（极端天气）、农技专家远程诊断  \\n   - 政府大屏指挥系统：区域产量热力图、灾害应急调度可视化  \\n差异化功能:To 政府：\\n“通过物联网构建‘天空地’一体化农业监管网络，实现‘农田上图、农机上云、灾害预警’，保障粮食安全战略落地”\\nTo 企业：\\n“从‘靠经验种地’到‘用数据种地’，亩均成本降低20%，优质优价销售溢价提升15%”\\n政策指引:粮食安全战略：\\n对应产品功能：耕地“非粮化”AI监测、智能灌溉节水（符合《国家节水行动方案》）\\n申报路径：协助客户申请“高标准农田建设专项资金”（中央财政补贴亩均1500元）\\n数字乡村试点：\\n对应产品功能：村级政务“一屏统管”、农产品直播电商基础设施\\n政策红利：入选国家数字乡村试点县可获500-1000万财政补贴\\n冷链物流骨干网：\\n对应产品功能：冷链车辆定位+温湿度监控云平台\\n补贴机制：商务部《农产品供应链体系建设》补助冷藏车单价30%\",\n" +
                     "            \"iNum\": 1\n" +
                     "        },\n" +
                     "        {\n" +
                     "            \"fileName\": \"产品信息3.xlsx\",\n" +
                     "            \"img\": \"\",\n" +
                     "            \"fileUrl\": {\n" +
                     "                \"knowledge_base_name\": \"1894337739579932674\",\n" +
                     "                \"user_id\": \"lxw\",\n" +
                     "                \"file_name\": \"产品信息3.xlsx\"\n" +
                     "            },\n" +
                     "            \"pageContent\": \"下文与产品信息3.xlsx有关，日期:\\n产品名称:NB-IOT智慧水务抄表平台\\n业务类型:物联网基础产品\\n匹配场景:\\n产品标签:\\n目标客户:水务集团、公司，建筑开发商\\n产品经理姓名:\\n产品经理联系方式:\\n销售经理姓名:\\n销售经理联系方式:\\n产品介绍:智能抄表平台致力于实现抄表业务的综合智能化。系统规范标准接口，兼容不同厂商和不同类型的设备接入。可以灵活配置设备参数和设备报警等信息，通过智能水表基础数据的采集和智能化分析处理、展现，实现抄表业务综合管理，方便水司的日常管理工作，及时掌握用户用水的情况。同时，智能抄表平台可与水务营收系统进行无缝交换，实现数据共享。\\n产品图片:\\n产品优势:高兼容性、海量接入\\n高兼容性：能兼容多类型水表、多厂家设备的接入。 海量接入：NB-IoT具有大连接、广覆盖、低功耗的特点，为远程抄表系统提供了更高效的网络。\\n产品功能:1、水表管理 对水表基础信息进行统一管理，包括增删改查。对于上传的数据进行统一查看。 2、人员数据管理 对于居民、商业用户信息进行管理。 3、水务数据管理 将水表与人员数据进行联合，查询用户用水情况及表具状态。对超收成功率进行统计。 4、告警管理 对智能表具上传的告警数据进行管理。 5、维修管理 对于告警信息进行派单维修管理。 6、财务管理 完成阶梯水价的制定 7、用户管理 进行用户角色管理、组织机构管理。\\n产品资费:\\n产品资费图片:\\n培训附件:\\n产品案例:\\n案例图片:\\n结算:\\n受理:\\n交付:\\n计收:\\n是否有效:\\n创建时间:\\n是否箭头产品:\\n核心卖点:\\n支持人员-图片:\\n产品一单清-图片:\\n产品形态-图片:\\n目标场景:\",\n" +
                     "            \"iNum\": 2\n" +
                     "        },\n" +
                     "        {\n" +
                     "            \"fileName\": \"产品(1)(1).xlsx\",\n" +
                     "            \"img\": \"\",\n" +
                     "            \"fileUrl\": {\n" +
                     "                \"knowledge_base_name\": \"1894337739579932674\",\n" +
                     "                \"user_id\": \"lxw\",\n" +
                     "                \"file_name\": \"产品(1)(1).xlsx\"\n" +
                     "            },\n" +
                     "            \"pageContent\": \"下文与产品(1)(1).xlsx有关，日期:\\n产品名称:物联网连接服务产品（山东）\\n业务类型:其他\\n匹配场景:\\n产品标签:\\n目标客户:\\n产品经理姓名:\\n产品经理联系方式:\\n销售经理姓名:\\n销售经理联系方式:\\n产品介绍:\\n产品图片:\\n产品优势:\\n产品功能:\\n产品资费:\\n产品资费图片:\\n培训附件:\\n产品案例:\\n案例图片:\\n结算:\\n受理:\\n交付:\\n计收:\\n是否有效:\\n创建时间:\\n是否箭头产品:\\n核心卖点:\\n支持人员-图片:\\n产品一单清-图片:\\n产品形态-图片:\\n目标场景:\",\n" +
                     "            \"iNum\": 3\n" +
                     "        }\n" +
                     "    ]\n" +
                     "}";
            response = httpUtils.postData(ragAbilityDsUrl, JSONObject.toJSONString(request));
            return response;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
}
