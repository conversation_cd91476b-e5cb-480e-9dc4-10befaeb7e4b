package cn.chinaunicom.sdsi.customerPicture.mapper;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOrder;
import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 三全客户画像-订单信息-订单数量表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Mapper
public interface CustomerProfileOrderMapper extends BaseMapper<CustomerProfileOrder> {
    // 年累计订单数量
    CustomerProfileOrder getYearOrderNum(CustomerPictureVo pictureVo);
}
