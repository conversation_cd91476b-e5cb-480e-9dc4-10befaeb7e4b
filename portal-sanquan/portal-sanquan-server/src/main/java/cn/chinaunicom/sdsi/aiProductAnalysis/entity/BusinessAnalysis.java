package cn.chinaunicom.sdsi.aiProductAnalysis.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class BusinessAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 数据类型：项目/商机/标讯
     */
    private String dataType;

    /**
     * 项目/商机/标讯名称
     */
    private String name;

    /**
     * 项目/商机/标讯内容描述
     */
    private String content;

    /**
     * 其他信息
     */
    private String otherInfo;

    /**
     * 内容是否有意义：0-无意义，1-有意义
     */
    private String checkContentStatus;

    /**
     * 打标的需求关键词，逗号分隔的字符串格式
     */
    private String keywords;

    /**
     * 打标的产品名称，逗号分隔的字符串格式
     */
    private String product;

    /**
     * 处理状态：0-待处理，1-正在处理，2-已处理，3-处理失败
     */
    private String processStatus;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;
}