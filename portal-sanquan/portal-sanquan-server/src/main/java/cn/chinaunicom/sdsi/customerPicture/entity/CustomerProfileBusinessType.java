package cn.chinaunicom.sdsi.customerPicture.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * 三全客户画像-客户业务信息-在用业务表
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@TableName("t_sanquan_customer_profile_business_type")
public class CustomerProfileBusinessType {

    private static final long serialVersionUID = 1L;

    private String natureCustId;
    private String natureCustName;
    private String serviceKindName;
}
