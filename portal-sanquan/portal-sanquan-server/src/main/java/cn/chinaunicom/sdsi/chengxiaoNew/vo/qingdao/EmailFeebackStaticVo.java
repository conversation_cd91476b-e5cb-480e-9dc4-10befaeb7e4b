package cn.chinaunicom.sdsi.chengxiaoNew.vo.qingdao;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/26 11:12
 */
@Data
public class EmailFeebackStaticVo implements Serializable {

    /*反馈结果，1执行成功，0无法执行*/
    String visitResult;

    /*反馈时间*/
    String backTime;

    /*是否继续拜访（1是，0否）*/
    String isContinueVisit;

    /*无法执行原因*/
    String noVisitReason;

    /*下次执行时间*/
    String nextTime;

    /*拜访主题*/
    String visitTheme;

    /*拜访时间*/
    String visitTime;

    /*拜访地点*/
    String visitSite;

    /*访谈人姓名*/
    String visitPerson;

    /*访谈人联系方式*/
    String visitPhone;

    /*访谈内容*/
    String visitContent;

    /*拜访总结*/
    String visitSummarize;

    /*重点推荐产品*/
    String keyRecommendedProduct;

    /*是否有商机意向，1是，0否*/
    String isBusiOppWilling;

    /*商机名称*/
    String busiOpptName;

    /*业务类型,1移网，2固网，3创新，4其它*/
    String businessType;

    public String getVisitResult() {
        if("1".equals(visitResult)){
            return "执行成功";
        }else if("0".equals(visitResult)){
            return "无法执行";
        }
        return visitResult;
    }

    public String getIsContinueVisit() {
        if("1".equals(isContinueVisit)){
            return "是";
        }else if("0".equals(isContinueVisit)){
            return "否";
        }
        return isContinueVisit;
    }

    public String getIsBusiOppWilling() {
        if("1".equals(isBusiOppWilling)){
            return "是";
        }else if("0".equals(isBusiOppWilling)){
            return "否";
        }
        return isBusiOppWilling;
    }

    public String getBusinessType() {
        if("1".equals(businessType)){
            return "移网";
        }else if("2".equals(businessType)){
            return "固网";
        }else if("3".equals(businessType)){
            return "创新";
        }else if("4".equals(businessType)){
            return "其它";
        }
        return businessType;
    }
}
