package cn.chinaunicom.sdsi.score.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Delegate;

import java.io.Serializable;

/**
 * 
 * @TableName t_sanquan_score_model
 */
@TableName(value ="t_sanquan_score_model")
@Data
public class ScoreModel implements Serializable {
    /**
     * 
     */
    @TableId
    private String id;

    /**
     * 模版名称
     */
    private String modelName;

    /**
     * 模版编码
     */
    private String modelCode;

    /**
     * 所属行业
     */
    private String industry;

    /**
     * 总分值
     */
    private String totalScore;

    /**
     * 备注
     */
    private String mobo;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 删除标识
     */
    @Delegate
    private String deleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}