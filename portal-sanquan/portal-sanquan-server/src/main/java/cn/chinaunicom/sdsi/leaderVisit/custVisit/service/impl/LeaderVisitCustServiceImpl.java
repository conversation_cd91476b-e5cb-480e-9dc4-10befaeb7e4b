package cn.chinaunicom.sdsi.leaderVisit.custVisit.service.impl;

import cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity.LeaderVisitCust;
import cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.query.LeaderVisitCustQueryVo;
import cn.chinaunicom.sdsi.leaderVisit.custVisit.mapper.LeaderVisitCustMapper;
import cn.chinaunicom.sdsi.leaderVisit.custVisit.service.LeaderVisitCustService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 * 大客户经理拜访表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Service
public class LeaderVisitCustServiceImpl extends ServiceImpl<LeaderVisitCustMapper, LeaderVisitCust> implements LeaderVisitCustService {

    @Autowired
    public UnifastContext unifastContext;

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-18
     * @param leaderVisitCustVo
     * @return IPage<LeaderVisitCust>
     **/
    @Override
    public IPage<LeaderVisitCust> findPage(LeaderVisitCustQueryVo leaderVisitCustVo) {
        IPage page = QueryVoToPageUtil.toPage(leaderVisitCustVo);
        return baseMapper.findPage(page, leaderVisitCustVo);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-18
     * @param id
     * @return LeaderVisitCust
     **/
    @Override
    public LeaderVisitCust findOne(String id) {
        return baseMapper.selectById(id);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-18
     * @return List<LeaderVisitCust>
     **/
    @Override
    public List<LeaderVisitCust> findList(LeaderVisitCustQueryVo leaderVisitCustVo) {
        return baseMapper.findList(leaderVisitCustVo);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-18
     * @param leaderVisitCust
     * @return int
     **/
    @Override
    public int add(LeaderVisitCust leaderVisitCust) {
        return baseMapper.insert(leaderVisitCust);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-18
     * @param leaderVisitCust
     * @return int
     **/
    @Override
    public int update(LeaderVisitCust leaderVisitCust) {
        return baseMapper.updateById(leaderVisitCust);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-18
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
