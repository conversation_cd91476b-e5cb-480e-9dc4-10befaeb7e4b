package cn.chinaunicom.sdsi.nature_customer.service.impl;

import cn.chinaunicom.sdsi.cloud.nature_customer.entity.AddCustomerTag;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.NatureCoustomer;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.AddCustomerTagQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.NatureCoustomerQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO;
import cn.chinaunicom.sdsi.cloud.product.vo.AddProductMarkVo;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.nature_customer.mapper.AddCustomerTagMapper;
import cn.chinaunicom.sdsi.nature_customer.mapper.NatureCoustomerMapper;
import cn.chinaunicom.sdsi.nature_customer.service.AddCustomerService;
import cn.chinaunicom.sdsi.nature_customer.service.NatureCoustomerService;
import cn.chinaunicom.sdsi.tag.entity.TagDbVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 策略推荐结果表业务实现类
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class AddCustomerServiceImpl extends ServiceImpl<AddCustomerTagMapper, AddCustomerTag> implements AddCustomerService {


    @Override
    public IPage<AddCustomerTagVO> findPage(AddCustomerTagQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return baseMapper.findPage(page, query);
    }

    @Override
    public List<AddCustomerTagVO> findList(AddCustomerTagQuery query) {
        return baseMapper.findList(query);
    }

    @Override
    public AddCustomerTagVO findOneById(AddCustomerTagQuery query) {
        return baseMapper.findOneById(query);
    }

    @Override
    public List<String> findLabelNameByCustomer(AddCustomerTagQuery query) {
        return baseMapper.findLabelNameByCustomer(query);
    }

    @Override
    public void insertBatchSomeColumn(List<AddCustomerTag> batchList) {
        baseMapper.insertBatchSomeColumn(batchList);
    }

    @Override
    public void deleteByRosterCustomerId(List<String> deleteRosterIds) {
        baseMapper.deleteByRosterCustomerId(deleteRosterIds);
    }

    @Override
    public List<AddCustomerTagVO> findBigTable(String date) {
        return baseMapper.findBigTable(date);
    }

    @Override
    public List<TagDbVo> findBigTableColumn(Map<String, Object> params) {
        return baseMapper.findBigTableColumn(params);
    }

    @Override
    public List<AddCustomerTagVO> findDictBigColumn(Map<String, Object> params) {
        return baseMapper.findDictBigColumn(params);
    }

    @Override
    public List<AddCustomerTagVO> findDictBigColumnDetail(Map<String, Object> params) {
        return baseMapper.findDictBigColumnDetail(params);
    }

    /**
     * 查看客户标签数
     * @return
     */
    @Override
    public int getCustomerLabelNum(AddCustomerTagQuery query) {
        return baseMapper.selectCustomerLabelNum(query);
    }

    /**
     * 查询产品打标的数据
     * @param date
     * @return
     */
    @Override
    public List<AddProductMarkVo> findProductBigTable(String date) {
        return baseMapper.findProductBigTable(date);
    }
}
