package cn.chinaunicom.sdsi.businessOppoPushBrief.util;

import cn.chinaunicom.sdsi.businessOppoPushBrief.entity.BusinessOppoPushBrief;
import cn.chinaunicom.sdsi.tourism.util.TemplateFacory;
import cn.chinaunicom.sdsi.util.word.ExcelToImgSanquanUtil3;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

public class ExcelExporter {

    private static final String[] HEADERS = {
            "地市", "商机来源","商机名称", "客户名称", "省分行业", "客户经理",
            "预计合同总金额（万元）", "客户需求简介", "初审是否通过", "产互营销经理",
            "商机核查是否通过", "商机详细信息", "下一步跟进建议", "跟进类型",
            "适配自研产品", "是否可借助硬件类产品框架集采优势", "产品机会"
    };

    private static final int[] COLUMN_WIDTHS = {
            2000, 5000, 5000, 5000, 5000, 4000,
            7000, 12000, 5000, 5000, 5000,
            15000, 12000, 3000, 4000, 10500,
            5000
    };

    public static String generate(Map<String, Object> dataList) {
        new TemplateFacory();

        HashMap<String, Object> map = new HashMap<>();
        map.put("FitToPagesWide", 5);
        map.put("FitToPagesTall", 15);
//        map.put("dpiX", 200);
//        map.put("dpiY", 200);

        String excelFileName = "excel_" + UUID.randomUUID() + ".xls";
        String imgFileName = "img_" + UUID.randomUUID() + ".png";
        String excelFilePath = TemplateFacory.UPLOAD_PATCH + File.separator + excelFileName;
        exportToExcel(dataList, excelFilePath);
        String imgPic = ExcelToImgSanquanUtil3.convertExcelToImage(excelFileName, imgFileName, 0, map);
        // TODO: 删除excel文件

        return imgPic;
    }

    private static void exportToExcel(Map<String, Object> data, String fileName) {
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(fileName)) {
            List<BusinessOppoPushBrief> dataList = (List<BusinessOppoPushBrief>) data.get("dataList");
            List<Integer> visibleRows = (List<Integer>) data.get("visibleRows");
            List<Short> colorRows = (List<Short>) data.get("colorRows");

            Sheet sheet = workbook.createSheet("商机数据");
            setupColumnWidths(sheet, visibleRows);


            Font headerFont = workbook.createFont();
            headerFont.setFontHeightInPoints((short) 14);
            headerFont.setFontName("宋体");


            Font dataFont = workbook.createFont();
            dataFont.setFontHeightInPoints((short) 11);
            dataFont.setFontName("宋体");

            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setFont(headerFont);

            CellStyle dataStyle = createDataStyle(workbook);
            dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);  // 垂直居中
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            dataStyle.setFont(dataFont);


            // 创建表头
            createHeaderRow(sheet, headerStyle, visibleRows, colorRows);

            // 填充数据
            fillDataRows(sheet, dataList, visibleRows, dataStyle);

            workbook.write(outputStream);
            System.out.println("Excel文件已成功创建：" + fileName);
        } catch (IOException e) {
            System.err.println("导出Excel文件时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void setupColumnWidths(Sheet sheet, List<Integer> visibleRows) {
        for (int i = 0; i < visibleRows.size(); i++) {
            sheet.setColumnWidth(i, COLUMN_WIDTHS[visibleRows.get(i)]);
        }
    }

    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        setBorderStyle(style, BorderStyle.THIN);
        return style;
    }

    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        setBorderStyle(style, BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }

    private static void setBorderStyle(CellStyle style, BorderStyle borderStyle) {
        style.setBorderBottom(borderStyle);
        style.setBorderTop(borderStyle);
        style.setBorderLeft(borderStyle);
        style.setBorderRight(borderStyle);
    }

    private static void createHeaderRow(Sheet sheet, CellStyle headerStyle, List<Integer> visibleRows, List<Short> colorRows) {
        Row headerRow = sheet.createRow(0);
        for (int i = 0; i < visibleRows.size(); i++) {
            Cell cell = headerRow.createCell(i);

            if (colorRows.get(i) == -1) {
                cell.setCellValue(HEADERS[visibleRows.get(i)]);
                cell.setCellStyle(headerStyle);
            } else {
                CellStyle newStyle = sheet.getWorkbook().createCellStyle();
                newStyle.cloneStyleFrom(headerStyle); // 复制现有样式

                newStyle.setFillForegroundColor(colorRows.get(i));
                newStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND); // 必须设置填充模式

                cell.setCellValue(HEADERS[visibleRows.get(i)]);
                cell.setCellStyle(newStyle);
            }
        }
    }

    private static void fillDataRows(Sheet sheet, List<BusinessOppoPushBrief> dataList, List<Integer> visibleRows, CellStyle dataStyle) {
        int rowIndex = 1;
        for (BusinessOppoPushBrief item : dataList) {
            Row row = sheet.createRow(rowIndex++);
            createDataCells(row, item, visibleRows, dataStyle);
        }
    }

    private static void createDataCells(Row row, BusinessOppoPushBrief item, List<Integer> visibleRows, CellStyle style) {
        int colIndex = 0;
        HashSet<Integer> set = new HashSet<>(visibleRows);
        if (set.contains(0)) {
            createSafeCell(row, colIndex++, item.getCity(), style);
        }
        if (set.contains(1)) {
            createSafeCell(row, colIndex++, item.getOpportunitySource(), style);
        }
        if (set.contains(2)) {
            createSafeCell(row, colIndex++, item.getOpportunityName(), style);
        }
        if (set.contains(3)) {
            createSafeCell(row, colIndex++, item.getCustomerName(), style);
        }
        if (set.contains(4)) {
            createSafeCell(row, colIndex++, item.getProvinceIndustry(), style);
        }
        if (set.contains(5)) {
            createSafeCell(row, colIndex++, item.getAccountManager(), style);
        }

        if (set.contains(6)) {
            // 处理数值类型
            Cell amountCell = row.createCell(colIndex++);
            amountCell.setCellValue(item.getEstimatedContractAmount());
            amountCell.setCellStyle(style);
        }
        // 处理文本类型
        if (set.contains(7)) {
            createSafeCell(row, colIndex++, item.getCustomerDemand(), style);
        }
        if (set.contains(8)) {
            createSafeCell(row, colIndex++, String.valueOf(item.getInitialReviewPassed()), style);
        }
        if (set.contains(9)) {
            createSafeCell(row, colIndex++, item.getMarketingManager(), style);
        }
        if (set.contains(10)) {
            createSafeCell(row, colIndex++, String.valueOf(item.getOpportunityVerified()), style);
        }
        if (set.contains(11)) {
            createSafeCell(row, colIndex++, item.getOpportunityDetails(), style);
        }
        if (set.contains(12)) {
            createSafeCell(row, colIndex++, item.getNextStepSuggestion(), style);
        }
        if (set.contains(13)) {
            createSafeCell(row, colIndex++, item.getFollowUpType(), style);
        }
        if (set.contains(14)) {
            createSafeCell(row, colIndex++, String.valueOf(item.getAdaptSelfDevelopedProduct()), style);
        }
        if (set.contains(15)) {
            createSafeCell(row, colIndex++, String.valueOf(item.getCanLeverageHardwareProcurement()), style);
        }
        if (set.contains(16)) {
            createSafeCell(row, colIndex, item.getProductOpportunity(), style);
        }
    }

    private static void createSafeCell(Row row, int colIndex, String value, CellStyle style) {
        Cell cell = row.createCell(colIndex);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }

}