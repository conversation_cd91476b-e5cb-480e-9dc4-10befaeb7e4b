package cn.chinaunicom.sdsi.leaderVisit.leaderVisit.service;

import cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.entity.ZqLeaderVisit;
import cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.vo.ZqLeaderVisitQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
/**
 * <p>
 * 高层拜访数据表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
public interface ZqLeaderVisitService extends IService<ZqLeaderVisit> {

    // 分页查询
    IPage<ZqLeaderVisit> findPage(ZqLeaderVisitQueryVo zqLeaderVisitVo);

    // 根据id查询
    ZqLeaderVisit findOne(String id);

    // 查询列表
    List<ZqLeaderVisit> findList(ZqLeaderVisitQueryVo zqLeaderVisitVo);

    // 新增
    int add(ZqLeaderVisit zqLeaderVisit);

    // 修改
    int update(ZqLeaderVisit zqLeaderVisit);

    // 删除
    int delete(String id);

}
