package cn.chinaunicom.sdsi.todo.mapper;

import cn.chinaunicom.sdsi.todo.entity.TSanquanTodo;
import cn.chinaunicom.sdsi.todo.vo.TSanquanTodoVO;
import cn.chinaunicom.sdsi.todo.queryvo.TSanquanTodoQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 三全项目内部待办Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-11
 */
@Mapper
public interface TSanquanTodoMapper extends BaseMapper<TSanquanTodo> {

    /**
     * 分页查询三全项目内部待办
     * 
     * @param page,tSanquanTodoQueryVO
     * @return 三全项目内部待办
     */
    IPage<TSanquanTodoVO> findPage(@Param("page") IPage page, @Param("query") TSanquanTodoQueryVO tSanquanTodoQueryVO);

}
