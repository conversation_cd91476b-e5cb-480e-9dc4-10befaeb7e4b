package cn.chinaunicom.sdsi.customerVisit.vo;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 客户拜访记录表
 * </p>
 * <AUTHOR>
 * @since 2025-03-07
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class TSanquanCustomerVisitExcel implements Serializable {

    private static final long serialVersionUID = 1L;


    /*营销活动标识*/
    String yxFlag;

    /*统计账期*/
    String dayId;

    /*所属地市*/
    String cityName;



    /*当日实际走访数量*/
    String sjBfCn;

    /*当日商机数量*/
    String sjCn;

    /*当日商机转化率*/
    String sjRate;

    /*实际走访数(本周)*/
    String sjBfCnWeek;

    /*有效走访数(本周)*/
    String yxbfCnWeek;

    /*有效走访率(本周)*/
    String yxbfRateWeek;

    /*商机个数(本周)*/
    String sjCnWeek;

    /*商机转化率(%)(本周)*/
    String sjRateWeek;

    /*签约数(本周)*/
    String qyCnWeek;

    /*签约转化率（%）(本周)*/
    String qyRateWeek;

    /*签约金额(本周)*/
    String qyFeeWeek;



    /*累计实际走访数量*/
    String sjBfCnTotal;

    /*累计有效走访数量*/
    String yxbfCnTotal;

    /*累计有效走访率*/
    String yxbfRateTotal;

    /*累计商机个数*/
    String sjCnTotal;

    /*累计商机转化率*/
    String sjRateTotal;

    /*累计签约数*/
    String qyCn;

    /*累计签约转化率*/
    String qyRateTotal;

    /*累计签约金额*/
    String qyFeeTotal;

    public String getSjRate() {
        if(StringUtils.isNotEmpty(sjRate) && !sjRate.contains("%")){
            return sjRate + "%";
        }
        return sjRate;
    }

    public String getYxbfRateWeek() {
        if(StringUtils.isNotEmpty(yxbfRateWeek) && !yxbfRateWeek.contains("%")){
            return yxbfRateWeek + "%";
        }
        return yxbfRateWeek;
    }

    public String getSjRateWeek() {
        if(StringUtils.isNotEmpty(sjRateWeek) && !sjRateWeek.contains("%")){
            return sjRateWeek + "%";
        }
        return sjRateWeek;
    }

    public String getQyRateWeek() {
        if(StringUtils.isNotEmpty(qyRateWeek) && !qyRateWeek.contains("%")){
            return qyRateWeek + "%";
        }
        return qyRateWeek;
    }

    public String getYxbfRateTotal() {
        if(StringUtils.isNotEmpty(yxbfRateTotal) && !yxbfRateTotal.contains("%")){
            return yxbfRateTotal + "%";
        }
        return yxbfRateTotal;
    }

    public String getSjRateTotal() {
        if(StringUtils.isNotEmpty(sjRateTotal) && !sjRateTotal.contains("%")){
            return sjRateTotal + "%";
        }
        return sjRateTotal;
    }

    public String getQyRateTotal() {
        if(StringUtils.isNotEmpty(qyRateTotal) && !qyRateTotal.contains("%")){
            return qyRateTotal + "%";
        }
        return qyRateTotal;
    }

}
