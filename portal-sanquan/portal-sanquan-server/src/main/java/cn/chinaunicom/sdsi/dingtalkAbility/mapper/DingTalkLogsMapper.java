package cn.chinaunicom.sdsi.dingtalkAbility.mapper;

import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkLogs;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkLogsQueryVo;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkLogsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;


@Mapper
@Component
public interface DingTalkLogsMapper extends BaseMapper<DingTalkLogs> {


    IPage<DingTalkLogsVo> findPage(@Param("page") IPage page, @Param("vo") DingTalkLogsQueryVo vo);
}
