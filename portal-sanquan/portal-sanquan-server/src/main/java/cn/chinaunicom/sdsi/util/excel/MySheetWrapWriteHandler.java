package cn.chinaunicom.sdsi.util.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

/**
 * 自动换行单元格
 */
@Slf4j
public class MySheetWrapWriteHandler implements CellWriteHandler {

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {

        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        int cellStyleLen = workbook.getNumCellStyles();
        for(int i=0;i<cellStyleLen;i++){
            if(!isHead){
                // 设置自动换行
                workbook.getCellStyleAt(i).setWrapText(true);

                // 可选：设置垂直居中
                workbook.getCellStyleAt(i).setVerticalAlignment(VerticalAlignment.CENTER);
            }

        }


    }

    /**
     * 动态设置单元格行高。（实体类取消ContentRowHeight注解符生效）
     * @param context
     */
    public void afterCellDispose(CellWriteHandlerContext context) {
        // 或者根据内容自动调整高度
        Cell cell = context.getCell();
        Sheet sheet = cell.getSheet();

        // 计算自适应行高
        if (cell.getCellType() == CellType.STRING) {
            String content = cell.getStringCellValue();
            if (content != null && !content.isEmpty()) {
                int columnWidth = sheet.getColumnWidth(cell.getColumnIndex());
                int charsPerLine = (int) (columnWidth / 256);
                if (charsPerLine > 0) {
                    // 计算需要的行数
                    int lineCount = (int) Math.ceil((double) content.length()*2 / charsPerLine);

                    // 设置行高
                    float a = context.getRow().getHeightInPoints();
                    float b = (lineCount) * 20;
                    int res = Float.compare(a,b);
                    if(res<0){
                        context.getRow().setHeightInPoints(b);
                    }

                }
            }
        }

    }
}
