package cn.chinaunicom.sdsi.todo.queryvo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 三全项目内部待办查询对象
 * @Author: han
 * @Date: 2024-06-11
 */
@Data
@Tag(name = "三全项目内部待办查询对象", description = "三全项目内部待办查询对象")
public class TSanquanTodoQueryVO extends BaseQueryVO {

    @Schema(name = "待办名称")
    private String dbname;

    @Schema(name = "代办人")
    private String dbstaff;

    @Schema(name = "代办人姓名")
    private String dbstaffName;

    @Schema(name = "0:待办 1:已办")
    private String status;

    @Schema(name = "业务id")
    private String businessId;

    @Schema(name = "待办类型 opportunity:潜在商机")
    private String type;

    @Schema(name = "创建人")
    private String createBy;
}
