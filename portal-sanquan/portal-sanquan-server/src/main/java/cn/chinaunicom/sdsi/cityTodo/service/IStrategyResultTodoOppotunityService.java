package cn.chinaunicom.sdsi.cityTodo.service;

import cn.chinaunicom.sdsi.cityTodo.entity.StrategyResultTodoOppotunity;
import cn.chinaunicom.sdsi.cityTodo.vo.StrategyResultTodoOppoVo;
import cn.chinaunicom.sdsi.cityTodo.vo.SupportSituationResVo;
import cn.chinaunicom.sdsi.cityTodo.vo.SupportSituationVo;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.AddCustomerTag;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkFeedbackVO;
import cn.chinaunicom.sdsi.cloud.workMonitor.vo.TSanquanWorkMonitorConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
public interface IStrategyResultTodoOppotunityService extends IService<StrategyResultTodoOppotunity> {
     void insertBatchSomeColumn(@Param("list") List<StrategyResultTodoOppotunity> batchList);
     List<String> findWaitPushByTodoCode(@Param("todoCode") String todoCode);
     int  findOppoCountByTodoCode(@Param("todoCode") String todoCode );

    // 根据代表详情表Id查询
    TSanquanWorkFeedbackVO getOppoWorkInfoById(String todoOppoId);

    // 根据todoCode和潜在机会 查询工单代办表中id
    StrategyResultTodoOppotunity findByTodoCodeAndOppoId(String todoCode, String oppoIdStr);

    // 根据工单监控状态，查询工单详情信息
    List<StrategyResultTodoOppoVo> findWorkTodoList(TSanquanWorkMonitorConfigVO tSanquanWorkMonitorConfigVo);

    // 支撑情况（营销经理）
    IPage<SupportSituationResVo> getSupportSituation(SupportSituationVo supportSituationVo);

    // 支撑人员情况统计（营销经理）
    IPage<SupportSituationResVo> getSupportStaffSituation(SupportSituationVo supportSituationVo);

    // 导出支撑情况统计
    void exportSupportSituation(SupportSituationVo query);

    // 导出支撑人员情况统计
    void exportSupportStaffSituation(SupportSituationVo query);

    // 支撑人员情况统计详情信息
    IPage<StrategyResultTodoOppoVo> supportStaffSituationPageInfo(SupportSituationVo supportSituationVo);
}
