package cn.chinaunicom.sdsi.shuangxianProtocolExpiration.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.hutool.core.date.DateTime;
import lombok.Data;


/**
 * 双线协议资费到期
 */
@Data
public class ShuangxianProtocolExpirationMsgVO extends BaseQueryVO  {

    /*发展人工号*/
    String developerLoginNo;
    /*发展人姓名*/
    String developerName;
    /*发展人手机号码*/
    String developerMobile;
    /*发展人邮件地址*/
    String developerEmail;

    /*小CEO名称*/
    String ceoStaffName;
    /*小CEO手机号码*/
    String ceoMobile;
    /*小CEO邮件地址*/
    String ceoEmail;

    /*客户名称*/
    String custName;
    /*产品ID*/
    String productId;
    /*产品名称*/
    String productName;
    /*资费到期时间*/
    String endDate;

    String businessType;

    /**
     * 短信内容
     */
    String productContent;
    String telephone;
    String email;
    String name;
    String content;
    String emailContent;
    String emailTitle;
    String dayId;
    String monthId;

    String startTime;
    String endTime;

    public String getContent() {
        StringBuilder builder = new StringBuilder();
        if(StringUtils.isNotEmpty(productContent) && StringUtils.isNotEmpty(telephone)){
            builder.append("(").append(this.name).append("):您为(").append(custName);
            builder.append(")订购的").append(productContent).append("协议资费最早将于(");
            // 格式化结束时间
            String endDateFormat = DateTime.of(endDate, "yyyyMMddHHmmss").toString("yyyy年M月d日");

            builder.append(endDateFormat).append(")到期，请尽快登录CB系统修改，避免产生额外费用，谢谢！");
        }
        return builder.toString();
    }

    public String getEmailContent() {
        StringBuilder builder = new StringBuilder();
        if(StringUtils.isNotEmpty(productContent) && StringUtils.isNotEmpty(email)){
            builder.append("尊敬的(").append(this.name).append("):")
                    .append(System.lineSeparator())
                    .append(System.lineSeparator())
                    .append("您好！")
                    .append(System.lineSeparator())
                    .append(System.lineSeparator())
                    .append("您为(").append(custName);
            builder.append(")订购的").append(productContent).append("协议资费最早将于(");
            // 格式化结束时间
            String endDateFormat = DateTime.of(endDate, "yyyyMMddHHmmss").toString("yyyy年M月d日");
            builder.append(endDateFormat).append(")到期。")
                    .append(System.lineSeparator())
                    .append(System.lineSeparator())
                    .append("为确保客户体验并避免产生额外费用，请您尽快登录CB系统进行资费调整或续约操作。")
                    .append(System.lineSeparator())
                    .append(System.lineSeparator())
                    .append("感谢您的配合！");
        }
        return builder.toString();
    }

    public String getEmailTitle() {
        StringBuilder builder = new StringBuilder();
        if(StringUtils.isNotEmpty(custName) && StringUtils.isNotEmpty(email)){
            builder.append("关于客户").append(custName).append("协议资费即将到期的提醒");
        } else {
            builder.append("关于客户协议资费即将到期的提醒");
        }
        return builder.toString();
    }
}
