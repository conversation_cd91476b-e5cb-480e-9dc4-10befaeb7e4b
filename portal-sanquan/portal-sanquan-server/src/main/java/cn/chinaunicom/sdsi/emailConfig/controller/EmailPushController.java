package cn.chinaunicom.sdsi.emailConfig.controller;

import cn.chinaunicom.sdsi.chengxiaoNew.service.impl.EmailDistricServiceImpl;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductAttachments;
import cn.chinaunicom.sdsi.emailConfig.entity.EmailConfig;
import cn.chinaunicom.sdsi.emailConfig.entity.EmailSchedule;
import cn.chinaunicom.sdsi.emailConfig.service.EmailConfigService;
import cn.chinaunicom.sdsi.emailConfig.service.EmailDataService;
import cn.chinaunicom.sdsi.emailConfig.service.EmailPersonService;
import cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonVO;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.product.service.MinioService;
import cn.chinaunicom.sdsi.tourism.MailDemoQQ;
import cn.chinaunicom.sdsi.tourism.MailInfo;
import cn.chinaunicom.sdsi.tourism.MailUtil;
import cn.chinaunicom.sdsi.tourism.service.EmailLogService;
import cn.chinaunicom.sdsi.tourism.util.TemplateFacory;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.mail.EmailAttachment;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * eamil推送工具类入口
 */
@RestController
@RequestMapping("/emailPush")
@Slf4j
public class EmailPushController {

    @Autowired
    EmailConfigService emailConfigService;
    @Autowired
    EmailPersonService emailPersonService;
    @Autowired
    EmailDistricServiceImpl districService;

    @Autowired
    MinioService minioService;
    @Autowired
    private EmailLogService emailLogService;

    // minio 目录 sanquan
    @Value("${minio.bucketName}")
    private String bucketName;

    @Autowired
    private ApplicationContext context;


    @RequestMapping("/scheduleCycleType")
    public void scheduleCycleType(String cycle){
        QueryWrapper queryWrapper = new QueryWrapper();
        if(StringUtils.isNotEmpty(cycle)){
            queryWrapper.eq("cycle_type",cycle);
        }
        queryWrapper.eq("status","1");
        List<EmailConfig> configList = emailConfigService.getBaseMapper().selectList(queryWrapper);
        if(configList.size()>0){
            emailFactory(configList);
        }else{
            log.error("未查询到要推送的任务");
            EmailConfig config = new EmailConfig();
            EmailPersonVO person = new EmailPersonVO();
            person.setUserName("admin");
            config.setBusinessType("未知");
            config.setModule("未知");
            config.setTaskName("未知");
            emailLogService.insertEmailLog(config,person,null,"失败", "未查询到要推送的任务");
        }

    }

    public void emailFactory(List<EmailConfig> configList){
        Boolean isTest = false;// 发版时改为false
        String osName = System.getProperty("os.name");
        if (osName.startsWith("Windows") ) {
            isTest = true;
        }
        TemplateFacory factory = new TemplateFacory(isTest ? "win" : "linux");
        EmailConfig configLog = new EmailConfig();
        EmailPersonVO emailPersonLog = new EmailPersonVO();
        try{
            factory.setBucketName(bucketName);
            for (EmailConfig configParent : configList) {
                BeanUtils.copyProperties(configParent,configLog);
                EmailPersonVO personParamVO = new EmailPersonVO();
                personParamVO.setDataType(configParent.getDataType());
                personParamVO.setTaskId(configParent.getId());
                List<EmailPersonVO> personList = emailPersonService.getPersonList(personParamVO);
                if(personList.size()==0){
                    log.info("任务【{}】，未配置推送人员信息",configParent.getTaskName());
                    emailLogService.insertEmailLog(configParent,new EmailPersonVO(),null,"失败","未配置推送人员信息");
                    continue;
                }
                for (EmailPersonVO emailPersonVO : personList) {
                    BeanUtils.copyProperties(emailPersonVO,emailPersonLog);
                    EmailConfig config = new EmailConfig();
                    BeanUtils.copyProperties(configParent,config);
                    if(!StringUtils.isNotEmpty(emailPersonVO.getEmail())){
                        emailPersonVO.setEmail(emailPersonVO.getJobNum() + "@chinaunicom.cn");
                    }

                    List<List> list = this.getInstanceService(config).getDataList(config,emailPersonVO);
                    if(list.size() == 0){
                        log.info("数据为空，{}，{}，{}",emailPersonVO.getEmail());
                        String errorStr = this.getInstanceService(config).getErrorStr();
                        emailLogService.insertEmailLog(config,emailPersonVO,null,"失败",StringUtils.isNotEmpty(errorStr) ? errorStr : "数据为空");
                        continue;
                    }
                    String fileName = StringUtils.isNotEmpty(config.getFileName()) ? config.getFileName() : config.getTaskName();
                    String fileNew = factory.buildFileName(fileName+"-"+emailPersonVO.getJobNum()+"-",new Date());// 临时附件名称
                    config.setFileName(fileNew);
                    config.setFilePath(factory.getFilePath(fileNew));
                    // 生成本地临时文件
                    this.getInstanceService(config).buildExcelFile(config,emailPersonVO, list);
                    if(!factory.validExcelFileExist(fileNew)){
                        log.error("生成临时文件失败");
                        emailLogService.insertEmailLog(config,emailPersonVO,null,"失败","生成临时文件失败");
                        continue;
                    }
                    if(StringUtils.isNotEmpty(config.getEmailTitle())){
                        String content = config.getEmailTitle();
                        if(config.getEmailTitle().contains("${param}") && "1".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getCity());
                            config.setEmailTitle(content);
                        }else if(config.getEmailTitle().contains("${param}") && "2".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getDistrictName());
                            config.setEmailTitle(content);
                        }else if(config.getEmailTitle().contains("${param}") && "3".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getGridName());
                            config.setEmailTitle(content);
                        }else if(config.getEmailTitle().contains("${param}") && "4".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getIndustry());
                            config.setEmailTitle(content);
                        }else if(config.getEmailTitle().contains("${param}") && "10".equals(config.getDataType())){// 全省级别
                            content = content.replace("${param}","");
                            config.setEmailTitle(content);
                        }
                    }
                    if(StringUtils.isNotEmpty(config.getEmailContent()) && config.getEmailContent().contains("${getEmailContent}")){
                        // 自动获取邮件正文简介
                        String content = this.getInstanceService(config).getEmailContent(config,emailPersonVO,list);
                        if(!StringUtils.isNotEmpty(content)){
                            log.error("获取邮件正文简介异常");
                            emailLogService.insertEmailLog(config,emailPersonVO,null,"失败",this.getInstanceService(config).getErrorStr());
                            continue;
                        }
                        String sqlContent = config.getEmailContent();
                        sqlContent = sqlContent.replace("${getEmailContent}",content);
                        config.setEmailContent(sqlContent);
                    }else if(StringUtils.isNotEmpty(config.getEmailContent())){
                        String content = config.getEmailContent();
                        if(config.getEmailContent().contains("${param}") && "1".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getCity());
                            config.setEmailContent(content);
                        }else if(config.getEmailContent().contains("${param}") && "2".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getDistrictName());
                            config.setEmailContent(content);
                        }else if(config.getEmailContent().contains("${param}") && "3".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getGridName());
                            config.setEmailContent(content);
                        }else if(config.getEmailContent().contains("${param}") && "4".equals(config.getDataType())){
                            content = content.replace("${param}",emailPersonVO.getIndustry());
                            config.setEmailContent(content);
                        }else if(config.getEmailContent().contains("${param}") && "10".equals(config.getDataType())){// 全省级别
                            content = content.replace("${param}","");
                            config.setEmailContent(content);
                        }
                    }
                    if(StringUtils.isNotEmpty(config.getEmailContent()) && config.getEmailContent().contains("${截止日期}")){
                        LocalDate localDate = LocalDate.now();
                        String str = "截止"+localDate.getYear()+"年"+localDate.getMonthValue()+"月"+localDate.getDayOfMonth() +"日";
                        String content = config.getEmailContent();
                        content = content.replace("${截止日期}",str);
                        config.setEmailContent(content);
                    }
                    // 构建email邮件正文html内容
                    Map<String, Object> mapParam = this.getInstanceService(config).getMyParam();
                    String contentExcel = factory.readHtmlFile(list.get(0),fileNew, config.getTemplate(),config.getEmailTitle(),mapParam);
                    if(cn.chinaunicom.sdsi.quartz.util.StringUtils.isEmpty(contentExcel)){
                        log.error("未生成汇总邮件正文文件内容");
                        emailLogService.insertEmailLog(config,emailPersonVO,null,"失败","未生成汇总邮件正文文件内容");
                        continue;
                    }
                    if("1".equals(config.getIsFile())){
                        TSanquanProductAttachments attachments = minioService.fileLoad(factory.getFilePath(fileNew));
                        config.setAttachmentId(attachments.getId());
                    }
                    try{
                        pushEmail(isTest,factory,emailPersonVO,config,config.getEmailContent()+contentExcel);
                        emailLogService.insertEmailLog(config,emailPersonVO,config.getEmailContent()+contentExcel,"成功",null);
                        log.info("邮件发送成功");
                    }catch (Exception e){
                        log.error("推送邮件异常，{}",e.getMessage());
                        emailLogService.insertEmailLog(configLog,emailPersonLog,null,"失败",e.getMessage());
                        e.printStackTrace();
                    }
                }
            }
        }catch (Exception e){
            log.error("系统错误，{}",e);
            emailLogService.insertEmailLog(configLog,emailPersonLog,null,"失败",e.getMessage());
        }

    }

    private EmailDataService getInstanceService(EmailConfig config) throws Exception{
        try {
            Class ins = Class.forName(config.getServiceName());
            ins.newInstance().getClass();
            EmailDataService dataService = (EmailDataService) context.getBean(ins);
            return dataService;
        } catch (Exception e) {
            log.error("类转换异常：{}",e.getLocalizedMessage());
            e.printStackTrace();
            throw new Exception(e);
        }
        /*switch (template){
           case "aa" : return context.getBean(EmailDistricServiceImpl.class);
        }*/
    }
    private EmailDataService pushEmail(Boolean isTest,TemplateFacory factory,EmailPersonVO personVO,EmailConfig config,String contentExcel) throws Exception {
        if(isTest && 1==2){
            MailDemoQQ demoQQ = new MailDemoQQ();
            demoQQ.send(config.getEmailTitle(),config.getEmailContent()+contentExcel,config.getFileName(),config.getFileName(),personVO.getEmail());
        }else{
            MailUtil mailUtil = new MailUtil();
            MailInfo mailInfo = new MailInfo();
            List<String> list = new ArrayList<>();
            list.add(personVO.getEmail());
            mailInfo.setToAddress(list);// 收件人
            mailInfo.setSubject(config.getSubject());
            mailInfo.setContent(contentExcel);
            // 设置附件信息
            if("1".equals(config.getIsFile())){
                List<EmailAttachment> files = Lists.newArrayList();
                EmailAttachment attachment = new EmailAttachment();
                String fileName = factory.buildFileName(config.getEmailTitle()+"-",new Date(),TemplateFacory.FORMAT_DATE_FILE);// 附件名称
                attachment.setName(fileName + ".xlsx");
                attachment.setPath(factory.getFilePath(config.getFileName()));
                files.add(attachment);
                mailInfo.setAttachments(files);
            }
            mailUtil.sendEmail(mailInfo);
        }
        return null;
    }




}
