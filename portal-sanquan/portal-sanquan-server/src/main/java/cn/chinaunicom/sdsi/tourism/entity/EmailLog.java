package cn.chinaunicom.sdsi.tourism.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-11-20
 */
@Data
@TableName("t_sanquan_email_log")
public class EmailLog {

    private static final long serialVersionUID = 1L;

    String id;
    String userId;
    String userName;
    /*职位*/
    String position;
    /*状态，1成功，2失败*/
    String status;
    /*email*/
    String email;
    /*周期类型*/
    String cycle;
    /*业务类型，对应 t_sanquan_tourism 的 businessType */
    String cycleType;

    /*业务类型，默认（文旅）。对应 t_sanquan_tourism的 groupName或industry */
    String businessType;
    /* 数据范围（地市/行业） */
    String city;
    /* 数据范围（区县/战客） */
    String country;
    /*发送内容*/
    String content;
    /*附件地址*/
    String filePath;
    /*异常信息*/
    String remark;
    /*任务名称*/
    String taskName;

    String createBy;

    String createDate;

}
