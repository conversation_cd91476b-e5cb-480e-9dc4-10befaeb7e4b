package cn.chinaunicom.sdsi.util.excel;

import cn.chinaunicom.sdsi.common.entity.PoiExcelEntity;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * poi组件 excel, 老板本。
 */
@Slf4j
public class ExcelPoiUtil {

    public Workbook workbook = null;

    public ExcelPoiUtil(){
        System.out.println("这里进来了");
        if(workbook == null){
            workbook = new XSSFWorkbook();
        }
    }

    public void closeWorkbook(){
        try {
            this.workbook.close();
            this.workbook = null;
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    public Sheet getSheet(int len){
        return this.workbook.getSheetAt(len);
    }

    public void outPutFileExcel(PoiExcelEntity excelEntity){
        // 写入文件
        try (FileOutputStream outputStream = new FileOutputStream(excelEntity.getFilePath()+ File.separator + excelEntity.getFileName())) {
            this.workbook.write(outputStream);
            System.out.println("Excel文件生成成功！");
        } catch (IOException e) {
            log.error("poi写入excel异常，",e);
            e.printStackTrace();
        } finally {
            this.closeWorkbook();
        }
    }

    /**
     * 支持任意sheet页，创建任意个表格，及合并单元格
     * @param excelEntity
     * @return
     */
    public Sheet factoryPoiExcel(PoiExcelEntity excelEntity){
        int sheetNo = this.workbook.getNumberOfSheets();
        Sheet sheet = null;
        if(sheetNo > excelEntity.getSheetIndex()){
            sheet = this.workbook.getSheetAt(excelEntity.getSheetIndex());
        }else{
            sheet = this.workbook.createSheet(excelEntity.getSheetName());
        }
        this.fillSheetWithMergedHeader(sheet,excelEntity);

        return sheet;
    }

    private void fillSheetWithMergedHeader(Sheet sheet,PoiExcelEntity excelEntity) {
        new ExcelPoiUilNew().fillSheetWithMergedHeader(sheet,excelEntity);
    }

}
