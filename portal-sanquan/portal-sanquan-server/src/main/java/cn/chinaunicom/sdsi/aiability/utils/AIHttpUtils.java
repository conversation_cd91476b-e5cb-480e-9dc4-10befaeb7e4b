package cn.chinaunicom.sdsi.aiability.utils;

import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class AIHttpUtils {
    private static final Logger logger = LoggerFactory.getLogger(AIHttpUtils.class);
    private final OkHttpClient okHttpClient;
    private static final int DEFAULT_CONNECT_TIMEOUT = 10_000;
    private static final int DEFAULT_READ_TIMEOUT = 30_000;
    private static final int DEFAULT_WRITE_TIMEOUT = 30_000;

    public AIHttpUtils() {
        this.okHttpClient =  new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
                .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.MILLISECONDS)
                .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
                .build();
    }
    public String postData(String url, String jsonBody) throws IOException {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try (Response response = okHttpClient.newCall(request).execute()) {
            ResponseBody responseBody = response.body();
            String responseString = responseBody != null ? responseBody.string() : null;
            if (!response.isSuccessful()) {
                if (responseBody != null) {
                    responseBody.close();
                }
            }
            return responseString;
        } catch (IOException e) {
            logger.error("IOException during POST request to {}: {}", url, e.getMessage(), e);
            throw e;
        }
    }
}
