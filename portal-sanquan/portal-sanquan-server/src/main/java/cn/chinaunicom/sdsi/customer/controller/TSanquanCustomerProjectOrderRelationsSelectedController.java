package cn.chinaunicom.sdsi.customer.controller;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerProjectOrderRelationsSelected;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerProjectOrderRelationsSelectedQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerProjectOrderRelationsSelectedVO;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerProjectOrderRelationsSelectedService;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerProjectOrderRelationsSelectedService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 客户-项目或订单关系表（已选）
*
* <AUTHOR> 
* @since  2024-05-15
*/
@RestController
@RequestMapping("/customer/projectOrderRelationsSelected")
@Tag(name="客户-项目或订单关系表（已选）")
public class TSanquanCustomerProjectOrderRelationsSelectedController extends BaseController {
    @Autowired
    private TSanquanCustomerProjectOrderRelationsSelectedService tSanquanCustomerProjectOrderRelationsSelectedService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since ${date}
     * @param tSanquanCustomerProjectOrderRelationsSelectedQuery
     * @return BasePageResponse<TSanquanCustomerProjectOrderRelationsSelected>
     **/
    @GetMapping("/findPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<TSanquanCustomerProjectOrderRelationsSelected> findPage(TSanquanCustomerProjectOrderRelationsSelectedQuery tSanquanCustomerProjectOrderRelationsSelectedQuery){
        return pageOk(tSanquanCustomerProjectOrderRelationsSelectedService.findPage(tSanquanCustomerProjectOrderRelationsSelectedQuery));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since ${date}
     * @param id
     * @return BaseResponse<TSanquanCustomerProjectOrderRelationsSelected>
     **/
    @GetMapping("/findOne")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<TSanquanCustomerProjectOrderRelationsSelected> findOne(String id) {
        return ok(tSanquanCustomerProjectOrderRelationsSelectedService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since ${date}
     * @return BaseResponse<List<TSanquanCustomerProjectOrderRelationsSelected>>
     **/
    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<TSanquanCustomerProjectOrderRelationsSelected>> findList(TSanquanCustomerProjectOrderRelationsSelectedQuery query) {
        return ok(tSanquanCustomerProjectOrderRelationsSelectedService.findList(query));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since ${date}
     * @param tSanquanCustomerProjectOrderRelationsSelected
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    @Operation(summary = "新增", description = "新增")
    public BaseResponse<Boolean> add(@RequestBody TSanquanCustomerProjectOrderRelationsSelected tSanquanCustomerProjectOrderRelationsSelected){
        return ok(tSanquanCustomerProjectOrderRelationsSelectedService.add(tSanquanCustomerProjectOrderRelationsSelected));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since ${date}
     * @param tSanquanCustomerProjectOrderRelationsSelected
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    @Operation(summary = "修改", description = "修改")
    public BaseResponse<Boolean> update(@RequestBody TSanquanCustomerProjectOrderRelationsSelected tSanquanCustomerProjectOrderRelationsSelected) {
        return ok(tSanquanCustomerProjectOrderRelationsSelectedService.update(tSanquanCustomerProjectOrderRelationsSelected));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since ${date}
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    @Operation(summary = "删除", description = "删除")
    public BaseResponse<Boolean> delete(String id) {
        return ok(tSanquanCustomerProjectOrderRelationsSelectedService.delete(id));
    }

}