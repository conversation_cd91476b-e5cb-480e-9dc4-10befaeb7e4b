package cn.chinaunicom.sdsi.chengxiao.entity;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("ch_t_sanquan_chengxiao_zhuanhua_result")
public class XianSuoExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /*地市*/
    @ExcelProperty(title = "地市",width = 100)
    String cityName;

    /*数据来源*/
    @ExcelProperty(title = "数据来源",width = 160)
    String dataSource;

    /*任务数量*/
    @ExcelProperty(title = "任务数量",width = 100)
    String taskNum;

    /*执行数量*/
    @ExcelProperty(title = "执行数量",width = 100)
    String zhixing;

    /*执行率*/
    @ExcelProperty(title = "执行率",width = 100)
    String zhixingRate;

    /*转化数*/
    @ExcelProperty(title = "转化数",width = 100)
    String zhuanhuaNum;

    /*转化率*/
    @ExcelProperty(title = "转化率",width = 100)
    String zhuanhuaRate;

    /*商机数量*/
    @ExcelProperty(title = "商机数量",width = 100)
    String oppoNum;

    /*商机金额*/
    @ExcelProperty(title = "商机金额",width = 100)
    String oppoAmount;

    /*项目数量*/
    @ExcelProperty(title = "项目数量",width = 120)
    String projectNum;

    /*项目金额*/
    @ExcelProperty(title = "项目金额",width = 120)
    String projectAmount;

}
