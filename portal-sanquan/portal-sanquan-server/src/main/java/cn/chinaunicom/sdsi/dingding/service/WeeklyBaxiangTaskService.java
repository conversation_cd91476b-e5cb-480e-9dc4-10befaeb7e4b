package cn.chinaunicom.sdsi.dingding.service;

import cn.chinaunicom.sdsi.dingding.entity.WeeklyBaxiangPolicyTask;
import cn.chinaunicom.sdsi.dingding.entity.WeeklyBaxiangTotalTask;
import cn.chinaunicom.sdsi.dingding.mapper.WeekBaxiangTaskMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
public class WeeklyBaxiangTaskService {

    @Autowired
    WeekBaxiangTaskMapper taskMapper;

    public List<WeeklyBaxiangTotalTask> selectTotalTask(){
        String day = LocalDate.now().plusDays(-1).toString();
        day = day.replaceAll("-","");
        return taskMapper.selectTotalTask(day);
    }

    public List<WeeklyBaxiangPolicyTask> selectPolicyTask(){
        String day = LocalDate.now().plusDays(-1).toString();
        day = day.replaceAll("-","");
        return taskMapper.selectPolicyTask(day);
    }

    public String selectSendTotal(){
        String day = LocalDate.now().plusDays(-1).toString();
        day = day.replaceAll("-","");
        return taskMapper.selectSendTotal(day);
    }

    public List<String> selectMarketingList(){
        String day = LocalDate.now().plusDays(-1).toString();
        day = day.replaceAll("-","");
        return taskMapper.selectMarketingList(day);
    }
}
