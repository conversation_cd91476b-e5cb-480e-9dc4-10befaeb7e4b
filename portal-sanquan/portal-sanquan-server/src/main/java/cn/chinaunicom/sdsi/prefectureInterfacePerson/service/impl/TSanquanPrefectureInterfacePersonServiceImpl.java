package cn.chinaunicom.sdsi.prefectureInterfacePerson.service.impl;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerUserRelations;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity.TSanquanPrefectureInterfacePerson;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.query.TSanquanPrefectureInterfacePersonQuery;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.customer.vo.TSanquanCustomerUserRelationsExcelVO;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.prefectureInterfacePerson.dao.TSanquanPrefectureInterfacePersonMapper;
import cn.chinaunicom.sdsi.prefectureInterfacePerson.service.TSanquanPrefectureInterfacePersonService;
import cn.chinaunicom.sdsi.prefectureInterfacePerson.vo.TSanquanPrefectureInterfecePersonExcelVO;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 地市接口人表
 *
 * <AUTHOR> 
 * @since  2024-05-30
 */
@Service
public class TSanquanPrefectureInterfacePersonServiceImpl extends ServiceImpl<TSanquanPrefectureInterfacePersonMapper, TSanquanPrefectureInterfacePerson> implements TSanquanPrefectureInterfacePersonService {

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @Override
    public Boolean delete(String id) {
        return this.removeById(id);
    }

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    @Override
    public IPage<TSanquanPrefectureInterfacePersonVO> findPage(TSanquanPrefectureInterfacePersonQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        IPage<TSanquanPrefectureInterfacePersonVO> tSanquanPrefectureInterfacePersonVOIPage = this.baseMapper.findPage(page, query);
        // 遍历查询结果，进行脱敏处理
//        List<TSanquanPrefectureInterfacePersonVO> records = tSanquanPrefectureInterfacePersonVOIPage.getRecords();
//        for (TSanquanPrefectureInterfacePersonVO personVO : records) {
//            if (personVO.getTel() != null) {
//                // 对 tel 字段进行脱敏处理
//                personVO.setTel(maskPhoneNumber(personVO.getTel()));
//            }
//        }
        // 返回处理后的数据
        return tSanquanPrefectureInterfacePersonVOIPage;
    }

    // 手机号处理
    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber != null && phoneNumber.length() == 11) {
            return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
        }
        return phoneNumber;
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public TSanquanPrefectureInterfacePersonVO findOne(String id) {
        TSanquanPrefectureInterfacePerson byId = this.getById(id);
        TSanquanPrefectureInterfacePersonVO tSanquanPrefectureInterfacePersonVO = new TSanquanPrefectureInterfacePersonVO();
        if (byId != null) {
            BeanUtils.copyProperties(byId, tSanquanPrefectureInterfacePersonVO);
        }
        return tSanquanPrefectureInterfacePersonVO;
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    @Override
    public List<TSanquanPrefectureInterfacePersonVO> findList(TSanquanPrefectureInterfacePersonQuery query) {
        return this.baseMapper.findList(query);
    }

    /**
     * 新增或者修改
     *
     * @param tSanquanPrefectureInterfacePersonVO
     * @return
     */
    @Override
    public String addOrUpdate(TSanquanPrefectureInterfacePersonVO tSanquanPrefectureInterfacePersonVO) {
        if (tSanquanPrefectureInterfacePersonVO == null) {
            throw new IllegalArgumentException("输入参数不能为空");
        }
        // 根据工号查询
        TSanquanPrefectureInterfacePerson tSanquanPrefectureInterfacePersonDb = baseMapper.selectByJobNumber(tSanquanPrefectureInterfacePersonVO.getJobNumber());
        TSanquanPrefectureInterfacePerson tSanquanPrefectureInterfacePerson = new TSanquanPrefectureInterfacePerson();
        // 检查工号是否已经存在且不是同一个记录
        if (tSanquanPrefectureInterfacePersonDb != null && !tSanquanPrefectureInterfacePersonDb.getId().equals(tSanquanPrefectureInterfacePersonVO.getId())) {
            throw new IllegalArgumentException("工号已经存在！！");
        }
        BeanUtils.copyProperties(tSanquanPrefectureInterfacePersonVO, tSanquanPrefectureInterfacePerson);
        this.saveOrUpdate(tSanquanPrefectureInterfacePerson);
        return tSanquanPrefectureInterfacePersonVO.getId();
    }

    /**
     * 修改
     *
     * @param tSanquanPrefectureInterfacePerson
     * @return
     */
    @Override
    public Boolean update(TSanquanPrefectureInterfacePerson tSanquanPrefectureInterfacePerson) {
        return baseMapper.updateById(tSanquanPrefectureInterfacePerson) > 0;
    }

    /**
     * 数据导入（地市接口人信息）
     *
     * @param file
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importData(MultipartFile file) {
        try {
            // 1、解析Excel文件并转换为实体对象列表
            List<TSanquanPrefectureInterfecePersonExcelVO> excelData = ExcelUtils.importExcel(file, TSanquanPrefectureInterfecePersonExcelVO.class);
            // 2、已经存在的数据
            List<TSanquanPrefectureInterfecePersonExcelVO> existData = new ArrayList<>();
            // 不存在的数据
            List<TSanquanPrefectureInterfacePerson> noExistData = new ArrayList<>();
            // 数据处理
            for (TSanquanPrefectureInterfecePersonExcelVO excelDatum : excelData) {
                TSanquanPrefectureInterfacePerson tSanquanPrefectureInterfacePersonDB = this.getOne(Wrappers.<TSanquanPrefectureInterfacePerson>lambdaQuery()
                        .eq(TSanquanPrefectureInterfacePerson::getJobNumber, excelDatum.getJobNumber())
                        .eq(TSanquanPrefectureInterfacePerson::getDeleteFlag, "normal"));
                TSanquanPrefectureInterfacePerson tSanquanPrefectureInterfacePerson = new TSanquanPrefectureInterfacePerson();
                // 已存在不在进行添加
                if (tSanquanPrefectureInterfacePersonDB != null) {
                    String dbListCustomerDistrict = tSanquanPrefectureInterfacePersonDB.getListCustomerDistrict();
                    if (StringUtils.isNotEmpty(excelDatum.getListCustomerDistrict()) &&
                            (!StringUtils.isEmpty(dbListCustomerDistrict) && !dbListCustomerDistrict.contains(excelDatum.getListCustomerDistrict()))) {
                        // 只有当数据库中的 listCustomerDistrict 不为空且不包含新数据时，才添加 ','
                        excelDatum.setListCustomerDistrict(dbListCustomerDistrict + (dbListCustomerDistrict.endsWith(",") ? "" : ",") + excelDatum.getListCustomerDistrict());
                    } else if (StringUtils.isEmpty(dbListCustomerDistrict)) {
                        // 数据库中为空，直接设置新值
                        excelDatum.setListCustomerDistrict(excelDatum.getListCustomerDistrict());
                    }
                    tSanquanPrefectureInterfacePerson.setId(tSanquanPrefectureInterfacePersonDB.getId());
                }
                if (excelDatum != null && StringUtils.isNotEmpty(excelDatum.getJobNumber())) {
                    BeanUtils.copyProperties(excelDatum, tSanquanPrefectureInterfacePerson);
                    noExistData.add(tSanquanPrefectureInterfacePerson);
                }
            }
            // 批量添加
            this.saveOrUpdateBatch(noExistData);
            return true;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 导出模板
     */
    @Override
    public void exportData() {
        try {
            // 3、导出数据
            ExcelUtils.exportExcel(null, TSanquanPrefectureInterfecePersonExcelVO.class, "模板", "地市接口人员信息");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据工号查询地市接口人信息
     * @param jobNumber
     * @return
     */
    @Override
    public TSanquanPrefectureInterfacePersonVO findPerson(String jobNumber) {
        if(!StringUtils.isNotEmpty(jobNumber)){
            throw new ServiceErrorException("工号不能为空");
        }
        TSanquanPrefectureInterfacePerson tSanquanPrefectureInterfacePerson = this.getOne(Wrappers.<TSanquanPrefectureInterfacePerson>lambdaQuery()
                .eq(TSanquanPrefectureInterfacePerson::getJobNumber, jobNumber)
                .eq(TSanquanPrefectureInterfacePerson::getDeleteFlag, "normal"));
        TSanquanPrefectureInterfacePersonVO tSanquanPrefectureInterfacePersonVO = new TSanquanPrefectureInterfacePersonVO();
        if(tSanquanPrefectureInterfacePerson != null){
            BeanUtils.copyProperties(tSanquanPrefectureInterfacePerson,tSanquanPrefectureInterfacePersonVO);
        }
        return tSanquanPrefectureInterfacePersonVO;
    }

    /**
     * 验证工号是否存在
     * @param jobNumber
     * @param id
     * @return
     */
    @Override
    public Integer verifyJobNumber(String jobNumber, String id) {
        return baseMapper.verifyJobNumber(jobNumber,id);
    }

    @Override
    public Map<String, List<HashMap<String, String>>> getCityInterfacePersonByCity(TSanquanPrefectureInterfacePersonQuery query) {
        List<HashMap<String, String>> list = baseMapper.getCityInterfacePersonByCity(query);
        Map<String, List<HashMap<String, String>>> resultMap = new HashMap<>();
        for(HashMap<String, String> map : list){
            String city = map.get("list_customer_city")==null?"":map.get("list_customer_city").replaceAll("市","");
            String job_number = map.get("job_number");
            String name = map.get("name");
            HashMap<String, String> cityMap = new HashMap<>();
            cityMap.put("jobNumber",job_number);
            cityMap.put("name",name);

            if(resultMap.containsKey(city)){
                List<HashMap<String, String>> cityMapList = resultMap.get(city);
                cityMapList.add(cityMap);
                resultMap.put(city,cityMapList);
            }else{
                List<HashMap<String, String>> cityMapList = new ArrayList<>();
                cityMapList.add(cityMap);
                resultMap.put(city,cityMapList);
            }
        }
        return resultMap;
    }

}