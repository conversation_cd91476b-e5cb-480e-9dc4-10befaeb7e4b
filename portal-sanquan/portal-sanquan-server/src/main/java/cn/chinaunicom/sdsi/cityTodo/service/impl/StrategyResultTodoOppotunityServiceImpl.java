package cn.chinaunicom.sdsi.cityTodo.service.impl;

import cn.chinaunicom.sdsi.cityTodo.entity.StrategyResultTodoOppotunity;
import cn.chinaunicom.sdsi.cityTodo.mapper.StrategyResultTodoOppotunityMapper;
import cn.chinaunicom.sdsi.cityTodo.service.IStrategyResultTodoOppotunityService;
import cn.chinaunicom.sdsi.cityTodo.vo.*;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanPersonnel;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkFeedbackVO;
import cn.chinaunicom.sdsi.cloud.strategy.workFeedbackNumber.entity.TSanquanWorkFeedbackNumber;
import cn.chinaunicom.sdsi.cloud.workMonitor.vo.TSanquanWorkMonitorConfigVO;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.product.service.TSanquanPersonnelService;
import cn.chinaunicom.sdsi.statistics.vo.StatisticsDetailResultExcelVO;
import cn.chinaunicom.sdsi.statistics.vo.StatisticsResultExcelVO;
import cn.chinaunicom.sdsi.strategy.constant.WorkFeedbackConstant;
import cn.chinaunicom.sdsi.strategy.workFeedbackNumber.service.TSanquanWorkFeedbackNumberService;
import cn.chinaunicom.sdsi.util.excel.CustomSheetWriteHandler;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Service
public class StrategyResultTodoOppotunityServiceImpl extends ServiceImpl<StrategyResultTodoOppotunityMapper, StrategyResultTodoOppotunity> implements IStrategyResultTodoOppotunityService {

    @Autowired
    private TSanquanPersonnelService tSanquanPersonnelService;

    @Autowired
    private TSanquanWorkFeedbackNumberService tSanquanWorkFeedbackNumberService;

    @Override
    public void insertBatchSomeColumn(List<StrategyResultTodoOppotunity> batchList) {
        this.baseMapper.insertBatchSomeColumn(batchList);
    }

    @Override
    public List<String> findWaitPushByTodoCode(String todoCode) {
        return baseMapper.findWaitPushByTodoCode(todoCode);
    }

    @Override
    public int findOppoCountByTodoCode(String todoCode) {
        return this.baseMapper.findOppoCountByTodoCode(todoCode);
    }

    /**
     * 根据代表详情表Id查询
     * @param todoOppoId
     * @return
     */
    @Override
    public TSanquanWorkFeedbackVO getOppoWorkInfoById(String todoOppoId) {
        TSanquanWorkFeedbackVO tSanquanWorkFeedbackVO = this.baseMapper.findInfoByTodoOppoId(todoOppoId);
        if (tSanquanWorkFeedbackVO != null) {
            // 人名处理
            processSupportPersonNames(tSanquanWorkFeedbackVO);
            // 查询达产
            if (StringUtils.isNotEmpty(tSanquanWorkFeedbackVO.getMeetProduction()) && "已达产".equals(tSanquanWorkFeedbackVO.getMeetProduction())) {
                queryAndSetFeedbackNumbers(tSanquanWorkFeedbackVO, null, WorkFeedbackConstant.IS_MAIN);
            }
            // 查询辅推（联网通信）
            queryAndSetFeedbackNumbers(tSanquanWorkFeedbackVO, WorkFeedbackConstant.BUSINESS_TYPE_LWTX, WorkFeedbackConstant.IS_NOT_MAIN);
            // 查询辅推（算网数智）
            queryAndSetFeedbackNumbers(tSanquanWorkFeedbackVO, WorkFeedbackConstant.BUSINESS_TYPE_SWSZ, WorkFeedbackConstant.IS_NOT_MAIN);
        }
        return tSanquanWorkFeedbackVO;
    }

    /**
     * 根据todoCode和潜在机会 查询工单代办表中id
     * @param todoCode
     * @param oppoIdStr
     * @return
     */
    @Override
    public StrategyResultTodoOppotunity findByTodoCodeAndOppoId(String todoCode, String oppoIdStr) {
        return this.baseMapper.findByTodoCodeAndOppoId(todoCode,oppoIdStr);
    }

    // 抽取方法处理人名
    private void processSupportPersonNames(TSanquanWorkFeedbackVO feedbackVO) {
        String personId = feedbackVO.getSupportPerson();
        if (StringUtils.isNotEmpty(personId)) {
            TSanquanPersonnel tSanquanPersonnel = tSanquanPersonnelService.getById(personId);
            if (tSanquanPersonnel != null) {
                feedbackVO.setSupportPersonName(tSanquanPersonnel.getName());
            }
        }
    }

    // 查询业务数据
    private void queryAndSetFeedbackNumbers(TSanquanWorkFeedbackVO tSanquanWorkFeedbackVO, String businessCategory, String isMain) {
        List<TSanquanWorkFeedbackNumber> resultList = tSanquanWorkFeedbackNumberService.lambdaQuery()
                .eq(TSanquanWorkFeedbackNumber::getIsMain, isMain)
                .eq(StringUtils.isNotEmpty(businessCategory), TSanquanWorkFeedbackNumber::getBusinessCategory, businessCategory)
                .eq(TSanquanWorkFeedbackNumber::getFeedbackId, tSanquanWorkFeedbackVO.getId())
                .list();
        if (CollectionUtils.isNotEmpty(resultList)) {
            if (WorkFeedbackConstant.IS_MAIN.equals(isMain)) {
                tSanquanWorkFeedbackVO.setDcForm(resultList);
            } else if (WorkFeedbackConstant.BUSINESS_TYPE_LWTX.equals(businessCategory)) {
                tSanquanWorkFeedbackVO.setLwtxForm(resultList);
            } else if (WorkFeedbackConstant.BUSINESS_TYPE_SWSZ.equals(businessCategory)) {
                tSanquanWorkFeedbackVO.setSwszForm(resultList);
            }
        }
    }


    /**
     * 根据工单监控状态，查询工单详情信息
     * @param tSanquanWorkMonitorConfigVo 规则配置信息
     * @return
     */
    @Override
    public List<StrategyResultTodoOppoVo> findWorkTodoList(TSanquanWorkMonitorConfigVO tSanquanWorkMonitorConfigVo) {
        return baseMapper.selectWorkTodoList(tSanquanWorkMonitorConfigVo);
    }

    /**
     * 支撑情况（营销经理）
     * @param supportSituationVo
     * @return
     */
    @Override
    public IPage<SupportSituationResVo> getSupportSituation(SupportSituationVo supportSituationVo) {
        IPage page = QueryVoToPageUtil.toPage(supportSituationVo);
        return baseMapper.selectSupportSituation(page,supportSituationVo);
    }

    /**
     * 支撑人员情况统计（营销经理）
     * @param supportSituationVo
     * @return
     */
    @Override
    public IPage<SupportSituationResVo> getSupportStaffSituation(SupportSituationVo supportSituationVo) {
        IPage page = QueryVoToPageUtil.toPage(supportSituationVo);
        return baseMapper.getSupportStaffSituation(page,supportSituationVo);
    }

    /**
     * 导出支撑情况统计
     * @param query
     */
    @Override
    public void exportSupportSituation(SupportSituationVo query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        page.setSize(1000);
        IPage<SupportSituationResVo> supportSituationResVoIPage = baseMapper.selectSupportSituation(page, query);
        List<SupportSituationResVo> records = supportSituationResVoIPage.getRecords();
        try {
            // 导出数据
            ExcelUtils.exportExcel(records, SupportSituationExcelVo.class, "支撑情况统计", "支撑情况统计");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出支撑人员情况统计
     * @param query
     */
    @Override
    public void exportSupportStaffSituation(SupportSituationVo query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        page.setSize(100000);
        IPage<SupportSituationResVo> supportSituationResVoIPage = baseMapper.getSupportStaffSituation(page, query);
        List<SupportSituationResVo> records = supportSituationResVoIPage.getRecords();
        records.forEach(record -> {
            record.setMarketingManageNameId(null);
        });
        try {
            // 导出数据
            ExcelUtils.exportExcel(records, SupportSituationStaffExcelVo.class, "支撑人员信息统计", "支撑人员信息统计");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 支撑人员情况统计详情信息
     * @param supportSituationVo
     * @return
     */
    @Override
    public IPage<StrategyResultTodoOppoVo> supportStaffSituationPageInfo(SupportSituationVo supportSituationVo) {
        IPage page = QueryVoToPageUtil.toPage(supportSituationVo);
        IPage<StrategyResultTodoOppoVo> strategyResultTodoOppoVoIPage = baseMapper.selectSupportStaffSituationPageInfo(page, supportSituationVo);
        strategyResultTodoOppoVoIPage.getRecords().stream().map(item -> {
            String todoName = String.format("%s%s潜在商机支撑待办处理", item.getCustomerName(), (cn.chinaunicom.sdsi.quartz.util.StringUtils.isNotEmpty(item.getProductName()) ? item.getProductName() : ""));
            item.setWorkName(todoName);
            return item;
        }).collect(Collectors.toList());
        return strategyResultTodoOppoVoIPage;
    }

}
