package cn.chinaunicom.sdsi.prompt.entity;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * 提示词模板表实体类
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SysPromptTemplate {
    /** 模板ID */
    private Long id;
    /** 提示词ID */
    private Long promptId;
    /** 模板名称 */
    private String templateName;
    /** 模板内容 */
    private String templateContent;
    /** 模板顺序 */
    private Integer templateOrder;
    /** 创建者 */
    private String createBy;
    /** 创建时间 */
    private Date createTime;
    /** 更新者 */
    private String updateBy;
    /** 更新时间 */
    private Date updateTime;
}
