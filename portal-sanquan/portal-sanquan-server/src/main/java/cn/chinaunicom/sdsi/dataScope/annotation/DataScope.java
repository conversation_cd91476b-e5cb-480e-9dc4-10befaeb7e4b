package cn.chinaunicom.sdsi.dataScope.annotation;

import java.lang.annotation.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/8/8 16:45
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataScope {
    //地市查询信息
    DataScopeQuery cityQuery() default @DataScopeQuery(required = false, column = "list_customer_city");

    //线索查询信息
    DataScopeQuery xiansuoQuery() default @DataScopeQuery(required = false, column = "data_source");

    //标讯查询信息
    DataScopeQuery biaoxunQuery() default @DataScopeQuery(required = false, column = "data_source");

    //区县查询信息
    DataScopeQuery districtQuery() default @DataScopeQuery(required = false, column = "list_district");

    //行业查询字段
    DataScopeQuery industryQuery() default @DataScopeQuery(required = false, column = "industry");

    //查询查询字段
    DataScopeQuery productQuery() default @DataScopeQuery(required = false, column = "product_id");

    //本人查询字段
    DataScopeQuery userQuery() default @DataScopeQuery(required = false, column = "creator");

    //营服查询字段
    DataScopeQuery gridQuery() default @DataScopeQuery(required = false, column = "grid_name");

    //细分行业查询字段
    DataScopeQuery detailIndustryQuery() default @DataScopeQuery(required = false, column = "province_detail_industry_name");

}
