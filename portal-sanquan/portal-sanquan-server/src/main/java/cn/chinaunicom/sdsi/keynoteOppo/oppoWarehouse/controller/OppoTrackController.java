package cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.controller;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoTrackQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoTrackVo;
import cn.chinaunicom.sdsi.keynoteOppo.marketingAspect.FilterByUserCity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.service.OppoTrackService;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoTrack;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 商机跟踪表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@RestController
@RequestMapping("/oppoWarehouse/track")
public class OppoTrackController extends BaseController {

    @Autowired
    private OppoTrackService oppoTrackService;

    /**
     * 分页查询 跟踪记录
     * @return
     */
    @GetMapping("/findPageList")
    @FilterByUserCity(cityField = "city")
    public BasePageResponse<OppoTrackVo> findPageList(OppoTrackQueryVo oppoTrackVo){
        return pageOk(oppoTrackService.findPageList(oppoTrackVo));
    }

    /*
     * <AUTHOR>
     * @description 历史记录分页查询
     **/
    @GetMapping("/findPageHis")
    public BasePageResponse<OppoTrackVo> findPageHis(OppoTrackQueryVo oppoTrackVo){
        return pageOk(oppoTrackService.findPageHis(oppoTrackVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-06-26
     * @param id
     * @return BaseResponse<OppoTrack>
     **/
    @GetMapping("/findOne")
    public BaseResponse<OppoTrack> findOne(String id) {
        return ok(oppoTrackService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-06-26
     * @return BaseResponse<List<OppoTrack>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<OppoTrackVo>> findList(OppoTrackQueryVo oppoTrackVo) {
        return ok(oppoTrackService.findList(oppoTrackVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-06-26
     * @param oppoTrack
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody OppoTrackVo oppoTrack){
        return ok(oppoTrackService.add(oppoTrack));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-06-26
     * @param oppoTrack
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody OppoTrack oppoTrack) {
        return ok(oppoTrackService.update(oppoTrack));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-06-26
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(oppoTrackService.delete(id));
    }
}
