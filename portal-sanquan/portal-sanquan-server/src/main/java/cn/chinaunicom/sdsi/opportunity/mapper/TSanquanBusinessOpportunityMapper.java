package cn.chinaunicom.sdsi.opportunity.mapper;

import cn.chinaunicom.sdsi.cloud.opportunity.entity.TSanquanBusinessOpportunity;
import cn.chinaunicom.sdsi.cloud.opportunity.query.TSanquanBusinessOpportunityQuery;
import cn.chinaunicom.sdsi.cloud.opportunity.vo.TSanquanBusinessOpportunityVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机编号表表Mapper接口
 *
 * <AUTHOR>
 * @since 2024-05-01
 */
@Mapper
public interface TSanquanBusinessOpportunityMapper extends BaseMapper<TSanquanBusinessOpportunity> {

    /**
     * 查询当前用户关联商机编号列表
     *
     * @param tSanquanBusinessOpportunityQuery
     * @return 商机编号表
     */
    List<TSanquanBusinessOpportunityVO> findByCreatorList(@Param("query") TSanquanBusinessOpportunityQuery tSanquanBusinessOpportunityQuery);

    String findMaxTime();

    void deleteByOppoNumber(@Param("query") TSanquanBusinessOpportunityQuery tSanquanBusinessOpportunityQuery);
}
