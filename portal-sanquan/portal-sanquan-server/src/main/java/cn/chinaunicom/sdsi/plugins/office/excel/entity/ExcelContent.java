package cn.chinaunicom.sdsi.plugins.office.excel.entity;

import lombok.Data;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/8/16 21:04
 */
@Data
public class ExcelContent {
    private int rowSpan = 0;
    private List<DataParseResult> dataParseResults = new LinkedList<>();
    private short backgroundColor=55;
    private int fontSize = 20;

    public void addDataParseResult(DataParseResult dataParseResult) {
        dataParseResults.add(dataParseResult);
    }

    public void addMaxRow(int rowSpan) {
        if (rowSpan > this.rowSpan) {
            this.rowSpan = rowSpan;
        }
    }

    public List<DataParseResult> getDataParseResults() {
        dataParseResults = dataParseResults.stream().sorted(Comparator.comparing(DataParseResult::getCellShort)).collect(Collectors.toList());
        return dataParseResults;
    }
}
