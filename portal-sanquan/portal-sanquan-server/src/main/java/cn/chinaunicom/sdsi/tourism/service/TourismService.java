package cn.chinaunicom.sdsi.tourism.service;

import cn.chinaunicom.sdsi.tourism.entity.TaskDetail;
import cn.chinaunicom.sdsi.tourism.entity.TaskSummary;
import cn.chinaunicom.sdsi.tourism.entity.Tourism;
import cn.chinaunicom.sdsi.tourism.entity.TourismVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 文旅任务发送配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
public interface TourismService extends IService<Tourism> {

    // 按区域查询汇总数据
    List<TaskSummary> getSummaryData(List<String> citys);
    // 按行业查询汇总数据
    List<TaskSummary> getSummaryIndustryData(List<String> industrys);
    List<TaskDetail> getTaskDetail(String city,String country);
    /*行业明细*/
    List<TaskDetail> getIndustryDetail(List<String> industry);

    List<Tourism> getTourismCityByType(TourismVO tourism);

    List<Tourism> getBusinessTypeByCycle(TourismVO tourism);

    // 分页查询
    IPage<Tourism> findPage(TourismVO tourismVO);

    // 根据id查询
    Tourism findOne(String id);

    // 查询列表
    List<Tourism> findList();

    // 新增
    int add(Tourism tourism);

    // 修改
    int update(Tourism tourism);

    // 删除
    int delete(String id);

}
