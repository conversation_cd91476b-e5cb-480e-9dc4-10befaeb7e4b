package cn.chinaunicom.sdsi.strategy.vo;

import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkFeedbackVO;
import com.alibaba.excel.metadata.data.WriteCellData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.net.URL;

/**
 *
 * 工单详情信息导出VO
 *
 * @Author: 王永凯
 * @Date: 2024/5/7 15:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "工单详情信息导出VO")
public class TSanquanWorkFeedbackInfoExportVO extends TSanquanWorkFeedbackVO implements Serializable {

    /**
     * 潜在机会(商机)信息
     */
    @Schema(description = "地市")
    private String cityCode;

    /**
     * 未拜访理由
     */
    @Schema(description = "未拜访理由")
    private String reason;

    /**
     * 摸排现场照片
     */
    @Schema(description = "摸排现场照片")
    private String storagePath;

    /**
     * 摸排现场照片展示路径
     */
    @Schema(description = "摸排现场照片展示路径")
    private URL fileUrl;

    private WriteCellData<Void> writeCellDataFile;

}
