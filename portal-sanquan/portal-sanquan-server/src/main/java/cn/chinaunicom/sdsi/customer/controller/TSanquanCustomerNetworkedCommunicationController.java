package cn.chinaunicom.sdsi.customer.controller;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerNetworkedCommunication;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerNetworkedCommunicationQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerNetworkedCommunicationPercentageVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerNetworkedCommunicationVO;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerNetworkedCommunicationService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 联网通信-异网情况
*
* <AUTHOR> 
* @since  2024-05-15
*/
@RestController
@RequestMapping("/customer/networkedCommunication")
@Tag(name="联网通信-异网情况")
public class TSanquanCustomerNetworkedCommunicationController extends BaseController {

    @Autowired
    private TSanquanCustomerNetworkedCommunicationService tSanquanCustomerNetworkedCommunicationService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since ${date}
     * @param tSanquanCustomerNetworkedCommunicationQuery
     * @return BasePageResponse<TSanquanCustomerNetworkedCommunication>
     **/
    @GetMapping("/findPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<TSanquanCustomerNetworkedCommunication> findPage(TSanquanCustomerNetworkedCommunicationQuery tSanquanCustomerNetworkedCommunicationQuery){
        return pageOk(tSanquanCustomerNetworkedCommunicationService.findPage(tSanquanCustomerNetworkedCommunicationQuery));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since ${date}
     * @param id
     * @return BaseResponse<TSanquanCustomerNetworkedCommunication>
     **/
    @GetMapping("/findOne")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<TSanquanCustomerNetworkedCommunication> findOne(String id) {
        return ok(tSanquanCustomerNetworkedCommunicationService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since ${date}
     * @return BaseResponse<List<TSanquanCustomerNetworkedCommunication>>
     **/
    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<TSanquanCustomerNetworkedCommunication>> findList(TSanquanCustomerNetworkedCommunicationQuery query) {
        return ok(tSanquanCustomerNetworkedCommunicationService.findList(query));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since ${date}
     * @param tSanquanCustomerNetworkedCommunication
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    @Operation(summary = "新增", description = "新增")
    public BaseResponse<Integer> add(@RequestBody TSanquanCustomerNetworkedCommunication tSanquanCustomerNetworkedCommunication){
        return ok(tSanquanCustomerNetworkedCommunicationService.add(tSanquanCustomerNetworkedCommunication));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since ${date}
     * @param tSanquanCustomerNetworkedCommunication
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    @Operation(summary = "修改", description = "修改")
    public BaseResponse<Integer> update(@RequestBody TSanquanCustomerNetworkedCommunication tSanquanCustomerNetworkedCommunication) {
        return ok(tSanquanCustomerNetworkedCommunicationService.update(tSanquanCustomerNetworkedCommunication));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since ${date}
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    @Operation(summary = "删除", description = "删除")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanCustomerNetworkedCommunicationService.delete(id));
    }

    /**
     * 查询各个占比（根据客户id）
     * @param id
     * @return
     */
    @GetMapping("findPercentage")
    @Operation(summary = "查询各个占比", description = "查询各个占比")
    public BaseResponse<List<TSanquanCustomerNetworkedCommunicationPercentageVO>> findPercentage(String id) {
        return ok(tSanquanCustomerNetworkedCommunicationService.findPercentage(id));
    }
}