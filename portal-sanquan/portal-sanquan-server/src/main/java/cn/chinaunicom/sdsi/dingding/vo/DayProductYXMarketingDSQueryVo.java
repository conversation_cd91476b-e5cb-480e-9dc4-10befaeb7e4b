package cn.chinaunicom.sdsi.dingding.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;

/**
 * 产品营销-地市
 */
@Data
public class DayProductYXMarketingDSQueryVo extends BaseQueryVO {

    private String dayId;

    /**
     * RH指标
     */
    private String rhIndex;

    /**
     * 营销经理OA账号
     */
    private String marketingManagerOa;

    /**
     * 营销经理姓名
     */
    private String marketingManagerName;

    /**
     * 目标工单数
     */
    private String targetGdCn;

    /**
     * 完成工单数
     */
    private String finishGdCn;

    /**
     * 完成率
     */
    private String wcl;

    /**
     * 商机数
     */
    private String oppoCn;

    /**
     * 商机金额
     */
    private String oppoFee;

    /**
     * 策略ID
     */
    private String strategyId;

    /**
     * 策略名称
     */
    private String strategyName;

}
