package cn.chinaunicom.sdsi.targetMarketing.vo;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 靶向营销任务信息 EXCEL导出，enum字段映射
 * @Author: han
 * @Date: 2024-07-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TSanquanWorkOrderEnumExcel {

    /* （1待执行，2已关单，3待改派，4跟进中，5到期未执行， */
    private String status;

    @Schema(name = "反馈结果（1执行成功；0执行失败）")
    private String visitResult;

    @Schema(name = "工单表的业务场景")
    private String businessScene;

    @Schema(name = "是否继续拜访字段(1继续，0否)")
    private String isContinueVisit;

    @Schema(name = "是否需要支撑（1是，0否）")
    private String isNeedSupport;

    @Schema(name = "是否有商机意向（1是，0否）")
    private String isBusiOppWilling;

    @Schema(name = "是否录入异网信息   0：否，1：是")
    private String isInsertDiff;

    public String getIsInsertDiff() {
        if("1".equals(isInsertDiff)){
            return "是";
        }else if("0".equals(isInsertDiff)){
            return "否";
        }else if(isInsertDiff.contains("null")){
            return "";
        }
        return isInsertDiff;
    }

    public String getIsBusiOppWilling() {
        if("1".equals(isBusiOppWilling)){
            return "是";
        }else if("0".equals(isBusiOppWilling)){
            return "否";
        }else if(isBusiOppWilling.contains("null")){
            return "";
        }
        return isBusiOppWilling;
    }

    public String getIsNeedSupport() {
        if("1".equals(isNeedSupport)){
            return "是";
        }else if("0".equals(isNeedSupport)){
            return "否";
        }else if(isNeedSupport.contains("null")){
            return "";
        }
        return isNeedSupport;
    }

    public String getIsContinueVisit() {
        if("1".equals(isContinueVisit)){
            return "继续";
        }else if("0".equals(isContinueVisit)){
            return "否";
        }else if(isContinueVisit.contains("null")){
            return "";
        }
        return isContinueVisit;
    }

    public String getBusinessScene() {
        if("47".equals(businessScene)){
            return "潜在商机(山东)";
        }else if("5".equals(businessScene)){
            return "行业产品推介";
        }else if("10".equals(businessScene)){
            return "收入波动";
        }else if("11".equals(businessScene)){
            return "业务到期";
        }else if("16".equals(businessScene)){
            return "终端合约到期";
        }else if("6".equals(businessScene)){
            return "合同到期";
        }else if("33".equals(businessScene)){
            return "互联网专线/云宽带流失预测";
        }else if("49".equals(businessScene)){
            return "互联网专线/云联网高流量预警";
        }else if(businessScene.contains("null")){
            return "";
        }
        return businessScene;
    }



    public String getStatus() {
        if("1".equals(status)){
            return "待执行";
        }else if("2".equals(status)){
            return "已关单";
        }else if("3".equals(status)){
            return "待改派";
        }else if("4".equals(status)){
            return "跟进中";
        }else if("5".equals(status)){
            return "到期未执行";
        }else if(status.contains("null")){
            return "";
        }
        return status;
    }

    public String getVisitResult() {
        if("1".equals(visitResult)){
            return "执行成功";
        }else if("0".equals(visitResult)){
            return "执行失败";
        }else if(visitResult.contains("null")){
            return "";
        }
        return visitResult;
    }

    public String getVisitResult(String value) {
        setVisitResult(value);
        return getVisitResult();
    }

    public String getStatus(String value) {
        setStatus(value);
        return getStatus();
    }

    public String getBusinessScene(String value) {
        setBusinessScene(value);
        return getBusinessScene();
    }

    public String getIsContinueVisit(String value) {
        setIsContinueVisit(value);
        return getIsContinueVisit();
    }

    public String getIsNeedSupport(String value) {
        setIsNeedSupport(value);
        return getIsNeedSupport();
    }

    public String getIsBusiOppWilling(String value) {
        setIsBusiOppWilling(value);
        return getIsBusiOppWilling();
    }

    public String getIsInsertDiff(String value) {
        setIsInsertDiff(value);
        return getIsInsertDiff();
    }

    public String getConverOppt(String value) {
        if(StringUtils.isNotEmpty(value) && !"null".equals(value) && !"".equals(value)){
            return "是";
        }
        return "否";
    }

}
