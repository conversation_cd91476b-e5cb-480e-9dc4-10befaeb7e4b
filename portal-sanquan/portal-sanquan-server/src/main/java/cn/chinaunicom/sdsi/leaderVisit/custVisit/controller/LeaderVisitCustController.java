package cn.chinaunicom.sdsi.leaderVisit.custVisit.controller;

import cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.query.LeaderVisitCustQueryVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.leaderVisit.custVisit.service.LeaderVisitCustService;
import cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity.LeaderVisitCust;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 大客户经理拜访表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping("/custVisit")
public class LeaderVisitCustController extends BaseController {

    @Autowired
    private LeaderVisitCustService leaderVisitCustService;

    /**
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-07-18
     * @param leaderVisitCustVo
     * @return BasePageResponse<LeaderVisitCust>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<LeaderVisitCust> findPage(LeaderVisitCustQueryVo leaderVisitCustVo){
        return pageOk(leaderVisitCustService.findPage(leaderVisitCustVo));
    }

    /**
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-07-18
     * @param id
     * @return BaseResponse<LeaderVisitCust>
     **/
    @GetMapping("/findOne")
    public BaseResponse<LeaderVisitCust> findOne(String id) {
        return ok(leaderVisitCustService.findOne(id));
    }

    /**
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-07-18
     * @return BaseResponse<List<LeaderVisitCust>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<LeaderVisitCust>> findList(LeaderVisitCustQueryVo leaderVisitCustVo) {
        return ok(leaderVisitCustService.findList(leaderVisitCustVo));
    }

    /**
     * <AUTHOR>
     * @description 新增
     * @since 2025-07-18
     * @param leaderVisitCust
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody LeaderVisitCust leaderVisitCust){
        return ok(leaderVisitCustService.add(leaderVisitCust));
    }

    /**
     * <AUTHOR>
     * @description 修改
     * @since 2025-07-18
     * @param leaderVisitCust
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody LeaderVisitCust leaderVisitCust) {
        return ok(leaderVisitCustService.update(leaderVisitCust));
    }

    /**
     * <AUTHOR>
     * @description 删除
     * @since 2025-07-18
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(leaderVisitCustService.delete(id));
    }
}
