package cn.chinaunicom.sdsi.common.service;

import cn.chinaunicom.sdsi.common.entity.PoiExcelEntity;
import cn.chinaunicom.sdsi.util.excel.ExcelPoiUilNew;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 简单报表，通用service导出功能
 *     复杂报表，自定义service，重写 renderWorkbook()
 */
@Service
@Slf4j
public class PoiExportPublicService extends PoiExcelService{


    @Override
    public void renderWorkbook(Workbook workbook, PoiExcelEntity poiExcelEntity, List... data) {
        ExcelPoiUilNew poi = new ExcelPoiUilNew();
        // sheet1
        List<List<Object>> excelList = poi.convertToExcelData(data[0]);
        poiExcelEntity.setData(excelList);
        poiExcelEntity.setStartRow(poiExcelEntity.getStartRow());
        poiExcelEntity.setSheetIndex(poiExcelEntity.getSheetIndex());
        poi.factoryPoiExcel(workbook,poiExcelEntity);
    }

    @Override
    public void renderWorkbookCell(Workbook workbook, PoiExcelEntity excelEntity, List... data) {

    }
}
