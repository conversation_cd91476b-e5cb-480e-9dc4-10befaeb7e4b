package cn.chinaunicom.sdsi.strategy.controller;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyConfiguration;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyConfigurationQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyConfigurationVO;
import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.strategy.entity.StrategyConfig;
import cn.chinaunicom.sdsi.strategy.service.TSanquanStrtegyConfigurationService;
import cn.chinaunicom.sdsi.tag.service.TagService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.C;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标签 控制器
 */
@RestController
@RequestMapping("/strtegy/configuration")
@Slf4j
public class TSanquanStrtegyConfigurationController extends BaseController {

    @Autowired
    private TSanquanStrtegyConfigurationService tSanquanStrtegyConfigurationService;


    @Operation(summary = "分页", description = "分页")
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanStrtegyConfigurationVO> findPage(TSanquanStrtegyConfigurationQuery query) {
        return pageOk(tSanquanStrtegyConfigurationService.findPage(query));
    }

    @Operation(summary = "查询列表", description = "查询列表")
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanStrtegyConfigurationVO>> findInfo(TSanquanStrtegyConfigurationQuery query) {
        return ok(tSanquanStrtegyConfigurationService.findList(query));
    }

    @Operation(summary = "根据id查询", description = "根据id查询")
    @GetMapping("/findOne")
    public BaseResponse<TSanquanStrtegyConfigurationVO> findOne(String id) {
        System.err.println(id);

        TSanquanStrtegyConfigurationQuery query = new TSanquanStrtegyConfigurationQuery();
        query.setId(id);
        System.err.println(query.getId());
        return ok(tSanquanStrtegyConfigurationService.findOneById(query));
    }


    @Operation(summary = "保存数据", description = "保存数据")
    @PostMapping("/save")
    public BaseResponse<Boolean> add(@RequestBody TSanquanStrtegyConfiguration entity) {
        return ok(tSanquanStrtegyConfigurationService.saveOrUpdate(entity));
    }

    @Operation(summary = "保存数据", description = "保存数据")
    @PostMapping("/submit")
    public BaseResponse<Boolean> submit(@RequestBody StrategyConfig entity) {
        try {
            tSanquanStrtegyConfigurationService.addConfig(entity);
            return ok(true);
        } catch (Exception e) {
            e.printStackTrace();
            return ok(false);
        }
    }


    @Operation(summary = "查看详情", description = "查看详情")
    @GetMapping("/viewConfig")
    public BaseResponse<StrategyConfig> viewConfig(String id) {
        return new BaseResponse<>(this.tSanquanStrtegyConfigurationService.findByConfigId(id));
    }

    @Operation(summary = "查看详情", description = "查看详情")
    @GetMapping("/stopConfig")
    public BaseResponse<Boolean> stopConfig(String id) {
        TSanquanStrtegyConfiguration tc = tSanquanStrtegyConfigurationService.getById(id);
        tc.setExecuteStatus("1");
        return new BaseResponse<>(this.tSanquanStrtegyConfigurationService.saveOrUpdate(tc));
    }


//    @Operation(summary = "统计", description = "统计")
//    @PostMapping("/statistics")
//    public BaseResponse<Map<String, Integer>> statistics(@RequestBody TagQuery entity) {
//        Map<String, Integer> json = new HashMap<>();
//        int a = baseService.findByMonthCount(entity);
//        int b = baseService.findByTotalCount(entity);
//        json.put("monthCount", a);
//        json.put("totalCount", b);
//        return ok(json);
//    }

    @Operation(summary = "删除", description = "删除")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String id) {
        return ok(tSanquanStrtegyConfigurationService.removeById(id));
    }

}
