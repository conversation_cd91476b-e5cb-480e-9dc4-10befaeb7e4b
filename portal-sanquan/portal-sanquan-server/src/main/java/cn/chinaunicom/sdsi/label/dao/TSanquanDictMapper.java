package cn.chinaunicom.sdsi.label.dao;

import cn.chinaunicom.sdsi.cloud.label.dto.TSanquanDictPageDTO;
import cn.chinaunicom.sdsi.cloud.label.entity.TSanquanDictDetails;
import cn.chinaunicom.sdsi.cloud.label.entity.TSanquanDictType;
import cn.chinaunicom.sdsi.cloud.label.entity.TSanquanDictValue;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: 王永凯
 * @Date: 2024/5/11 13:52
 */
@Mapper
public interface TSanquanDictMapper extends BaseMapper<TSanquanDictType> {
    IPage<TSanquanDictType> queryDictList(IPage page, TSanquanDictPageDTO pageDTO);





    List<String> getMaxDictCode();

    void saveType(TSanquanDictDetails  tSanquanDictDetails);

    TSanquanDictType getTypeById(String id);

    List<TSanquanDictValue> getValueById(String id);

    int deleteTypeByIds(@Param("item")List<String> ids);

    int deleteValueByIds(@Param("item") List<String> ids);


    Integer saveValue(List<TSanquanDictValue> tSanquanDictValues);

    Integer dictTypeModify(@Param("item") List<TSanquanDictValue> tSanquanDictValues);

    Integer addDictType(TSanquanDictType tSanquanDictType);

    TSanquanDictType getdicttype(String dictname);

    Integer changedelete(String id);

    List<TSanquanDictValue> dictDetails(String id);

    TSanquanDictType getdicttypeOnly(String typeId);


    List<TSanquanDictType> findAll();


    List<TSanquanDictValue> finddictValueFindListById(String id);

    Integer dictTypeModifyone(@Param("user") TSanquanDictValue tSanquanDictValue);

    List<TSanquanDictValue> findDetails(@Param("id") String id,@Param("search") String search);
}
