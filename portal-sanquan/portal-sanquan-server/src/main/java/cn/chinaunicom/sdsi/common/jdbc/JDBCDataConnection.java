package cn.chinaunicom.sdsi.common.jdbc;

import cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO;
import cn.chinaunicom.sdsi.cloud.product.vo.AddProductMarkVo;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.chinaunicom.sdsi.nature_customer.service.AddCustomerService;
import cn.chinaunicom.sdsi.tag.entity.TagDbVo;
import cn.chinaunicom.sdsi.tag.service.TagService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class JDBCDataConnection {
    @Value("${spring.datasource.driver-class-name}")
    private String drive;
    @Value("${spring.datasource.url}")
    private String url;
    @Value("${spring.datasource.username}")
    private String user;
    @Value("${spring.datasource.password}")
    private String password;
    @Resource
    private TagService tagService;
    @Resource
    private AddCustomerService addCustomerService;


    public void updateBigColumnsTable(String date){
        //插入新的客户id
        tagService.insertCustomerId(date);
        // 查询的数据库表
        List<AddCustomerTagVO> addCustomerTagVOTableList = addCustomerService.findBigTable(date);
        //遍历表名->关联表的处理
        for (AddCustomerTagVO tableVo : addCustomerTagVOTableList) {
            //查询表关联的列
            Map<String, Object> params = new HashMap<>();
            StringBuilder updateBuilder = new StringBuilder("update t_sanquan_customer_tag_collection collection,  ");
            StringBuilder setBuilder = new StringBuilder(" set collection.customer_id=b.customer_id, collection.update_time='" + date + "' ");
            params.put("date", date);
            params.put("dbTableName", tableVo.getDbTable());
            List<TagDbVo> addCustomerTagVOColumnList = this.addCustomerService.findBigTableColumn(params);
            StringBuilder searchBuilder = new StringBuilder("(select customer_id");
            for (TagDbVo columnVo : addCustomerTagVOColumnList) {
                searchBuilder.append(",");
                searchBuilder.append(columnVo.getDbTableColumn()).append(" as '").append(columnVo.getTagName()).append("' ");
                setBuilder.append(",");
                setBuilder.append("collection.").append(columnVo.getTagName()).append(" = ").append("b.").append(columnVo.getTagName()).append(" ");
            }
            searchBuilder.append(" from ").append(tableVo.getDbTable()).append(" group by customer_id ) b ");
            updateBuilder.append(searchBuilder.toString()).append(setBuilder.toString());
            updateBuilder.append(" where collection.customer_id=b.customer_id");
            System.err.println("数据库管理:\n" + updateBuilder.toString());
            tagService.updateSQL(updateBuilder.toString());

        }
    }
    /**
     * 客户数据采集
     *
     * <AUTHOR>
     * @Date 2024/6/13 11:14
     */
    public void customerDataCollection() {
        String date = DateUtils.formatDate(DateUtils.minusDays(new Date(), 1));
        // 数据库连接对象
        Connection conn = null;
        // 用于执行SQL语句的对象
        Statement stmt = null;
        // 结果集对象
        ResultSet rs = null;
        try {
            // 加载数据库驱动
            Class.forName(drive);
            // 建立数据库连接
            conn = DriverManager.getConnection(url, user, password);
            // 创建Statement对象执行查询
            stmt = conn.createStatement();
            //插入新的客户id
            tagService.insertCustomerId(date);
            // 查询的数据库表
            List<AddCustomerTagVO> addCustomerTagVOTableList = addCustomerService.findBigTable(date);
            //遍历表名->关联表的处理
            for (AddCustomerTagVO tableVo : addCustomerTagVOTableList) {
                //查询表关联的列
                Map<String, Object> params = new HashMap<>();
                StringBuilder updateBuilder = new StringBuilder("update t_sanquan_customer_tag_collection collection,  ");
                StringBuilder setBuilder = new StringBuilder(" set collection.customer_id=b.customer_id, collection.update_time='" + date + "' ");
                params.put("date", date);
                params.put("dbTableName", tableVo.getDbTable());
                List<TagDbVo> addCustomerTagVOColumnList = this.addCustomerService.findBigTableColumn(params);
                StringBuilder searchBuilder = new StringBuilder("(select customer_id");
                for (TagDbVo columnVo : addCustomerTagVOColumnList) {
                    searchBuilder.append(",");
                    searchBuilder.append(columnVo.getDbTableColumn()).append(" as '").append(columnVo.getTagName()).append("' ");
                    setBuilder.append(",");
                    setBuilder.append("collection.").append(columnVo.getTagName()).append(" = ").append("b.").append(columnVo.getTagName()).append(" ");
                }
                searchBuilder.append(" from ").append(tableVo.getDbTable()).append(" group by customer_id ) b ");
                updateBuilder.append(searchBuilder.toString()).append(setBuilder.toString());
                updateBuilder.append(" where collection.customer_id=b.customer_id");
                System.err.println("数据库管理:\n" + updateBuilder.toString());
                stmt.execute(updateBuilder.toString());
                //  currentLocal++;
            }
            //字典的处理->关联
            Map<String, Object> params = new HashMap<>();
            params.put("date", date);
            // 客户标签分组->字典
            List<AddCustomerTagVO> dictGroupList = addCustomerService.findDictBigColumn(params);
            for (AddCustomerTagVO vo : dictGroupList) {
                params.put("customerId", vo.getCustomerId());
                params.put("bigColumn", vo.getBigColumn());
                // 客户标签拼接处理
                List<AddCustomerTagVO> dictGroupListDetails = addCustomerService.findDictBigColumnDetail(params);
                StringBuilder dictBuilder = new StringBuilder("(select customer_id, '");
                int i = 0;
                for (AddCustomerTagVO voDetail : dictGroupListDetails) {
                    if (i > 0) {
                        dictBuilder.append(",");
                    }
                    dictBuilder.append(voDetail.getLabelName());
                    i++;
                }
                dictBuilder.append("' as '").append(vo.getBigColumn()).append("' from t_sanquan_customer_tag where update_date='").append(date).append("' and (db_table='' or db_column='') GROUP BY customer_id ");
                dictBuilder.append(") b ");
                StringBuilder updateDictBuilder = new StringBuilder("update t_sanquan_customer_tag_collection collection,  ");
                updateDictBuilder.append(dictBuilder.toString());
                updateDictBuilder.append(" set collection.customer_id=b.customer_id, update_time='").append(date).append("',");
                updateDictBuilder.append("collection.").append(vo.getBigColumn()).append("=").append("b.").append(vo.getBigColumn());
                updateDictBuilder.append(" where collection.customer_id=b.customer_id and collection.customer_id='").append(vo.getCustomerId()).append("' ");
                System.err.println("更新字典加工列语句:\n" + updateDictBuilder.toString());
                stmt.execute(updateDictBuilder.toString());
            }
        } catch (Exception e) {
            System.err.println("客户信息采集异常"+e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            try {
                if (rs != null) {
                    rs.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (stmt != null) {
                    stmt.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (conn != null) {
                    conn.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 产品信息标签采集
     * @param date
     */
    public void updateProductBigColumnsTable(String date) {
        // 插入新的产品id
        tagService.insertProductId(date);
        // 查询的数据库表
        List<AddProductMarkVo> addProductTagVOTableList = addCustomerService.findProductBigTable(date);
        // 遍历表名->关联表的处理
        for (AddProductMarkVo tableVo : addProductTagVOTableList) {
            // 查询表关联的列
            Map<String, Object> params = new HashMap<>();
            StringBuilder updateBuilder = new StringBuilder("update t_sanquan_product_tag_collection collection,  ");
            StringBuilder setBuilder = new StringBuilder(" set collection.product_id=b.product_id, collection.update_time='" + date + "' ");
            params.put("date", date);
            params.put("dbTableName", tableVo.getDbTable());
            List<TagDbVo> addCustomerTagVOColumnList = this.addCustomerService.findBigTableColumn(params);

            // 拼接的查询的表中数据
            StringBuilder searchBuilder = new StringBuilder();
            if("t_sanquan_product".equals(tableVo.getDbTable())){
                searchBuilder.append("(select id as product_id");
            }else{
                searchBuilder.append("(select product_id");
            }
            for (TagDbVo columnVo : addCustomerTagVOColumnList) {
                searchBuilder.append(",");
                searchBuilder.append(columnVo.getDbTableColumn()).append(" as '").append(columnVo.getTagName()).append("' ");
                setBuilder.append(",");
                setBuilder.append("collection.").append(columnVo.getTagName()).append(" = ").append("b.").append(columnVo.getTagName()).append(" ");
            }
            if("t_sanquan_product".equals(tableVo.getDbTable())){
                searchBuilder.append(" from ").append(tableVo.getDbTable()).append(" group by id ) b ");
            }else{
                searchBuilder.append(" from ").append(tableVo.getDbTable()).append(" group by product_id ) b ");
            }
            updateBuilder.append(searchBuilder.toString()).append(setBuilder.toString());
            updateBuilder.append(" where collection.product_id=b.product_id");
            System.err.println("数据库管理:\n" + updateBuilder.toString());
            tagService.updateSQL(updateBuilder.toString());
        }
    }
}
