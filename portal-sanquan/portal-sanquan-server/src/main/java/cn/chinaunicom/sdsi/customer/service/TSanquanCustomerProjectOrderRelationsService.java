package cn.chinaunicom.sdsi.customer.service;



import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerProjectOrderRelations;
import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerProjectOrderRelationsSelected;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerProjectOrderRelationsQuery;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerProjectOrderRelationsSelectedQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 客户-项目或订单关系表
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */
public interface TSanquanCustomerProjectOrderRelationsService extends IService<TSanquanCustomerProjectOrderRelations> {

    //分页查询
    IPage<TSanquanCustomerProjectOrderRelations> findPage(TSanquanCustomerProjectOrderRelationsQuery query);

    //查询列表
    List<TSanquanCustomerProjectOrderRelations> findList(TSanquanCustomerProjectOrderRelationsQuery query);

    //导出
    void exportCustomerProjectOrderRelationsData(TSanquanCustomerProjectOrderRelationsQuery query);

    //导入
    List<TSanquanCustomerProjectOrderRelations> importCustomerProjectOrderRelationsData(MultipartFile file);

    //新增
    String add(TSanquanCustomerProjectOrderRelations tSanquanCustomerProjectOrderRelations);
}