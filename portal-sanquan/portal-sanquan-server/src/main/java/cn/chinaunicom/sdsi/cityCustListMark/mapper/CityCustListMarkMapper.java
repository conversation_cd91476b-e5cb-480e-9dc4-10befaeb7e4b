package cn.chinaunicom.sdsi.cityCustListMark.mapper;

import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustListQueryDTO;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustListMark;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustListWithMark;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户打标数据表 Mapper 接口
 *
 * <AUTHOR>
 */
@Mapper
public interface CityCustListMarkMapper {
    /**
     * 查询待处理任务
     *
     * @return
     */
    List<CityCustListMark> getPendingTaskList();

    /**
     * 判断是否存在相同custid数据
     *
     * @param natureCustId
     * @return
     */
    int verifyCustMarkExist(@Param("natureCustId") String natureCustId);

    /**
     * 根据 natureCustId 查询实体信息
     *
     * @param natureCustId
     * @return
     */
    CityCustListMark getByNatureCustId(@Param("natureCustId") String natureCustId);

    /**
     * 插入基础信息
     *
     * @param entity
     * @return
     */
    int insert(CityCustListMark entity);

    /**
     * 根据 natureCustId 更新信息
     *
     * @param entity
     * @return
     */
    int updateByNatureCustId(CityCustListMark entity);

    /**
     * 根据natureCusiId更新状态
     *
     * @param entity
     * @return
     */
    int updateStatusByNatureCustId(CityCustListMark entity);

    /**
     * 校验客户经理收藏是否存在
     * @param entity
     * @return
     */
    int verifyCustCollectionExist(CityCustListMark entity);

    /**
     * 更新客户经理收藏
     * @param entity
     * @return
     */
    int updateCustCollection(CityCustListMark entity);
    /**
     * 插入客户经理收藏
     * @param entity
     * @return
     */
    int insertCustCollection(CityCustListMark entity);

    /**
     * 根据名单制客户经理工号查询客户列表（分页）
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 分页查询结果
     */
    IPage<CityCustListWithMark> selectCustListByManagerOa(IPage page, @Param("param") CityCustListQueryDTO param);
}
