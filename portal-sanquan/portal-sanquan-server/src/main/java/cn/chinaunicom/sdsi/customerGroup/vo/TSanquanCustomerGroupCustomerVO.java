package cn.chinaunicom.sdsi.customerGroup.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 客户群关联客户视图对象
 * @Author: han<PERSON>xiao
 * @Date: 2024-05-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "客户群关联客户视图对象", description = "客户群关联客户视图对象")
public class TSanquanCustomerGroupCustomerVO {

    @Schema(name = "")
    private String id;

    @Schema(name = "客户群id")
    private String customerGroupId;

    @Schema(name = "客户id")
    private String customerId;

    @Schema(name = "客户名称")
    private String customerName;

    @Schema(name = "客户经理姓名")
    private String customerManagerName;

    @Schema(name = "地市")
    private String city;

    @Schema(name = "联系电话")
    private String telphone;

    @Schema(name = "关联时间")
    private String createTime;

    @Schema(name = "客户经理ID")
    private String customerManagerId;

    @Schema(name = "行业类别")
    private String industry;

}
