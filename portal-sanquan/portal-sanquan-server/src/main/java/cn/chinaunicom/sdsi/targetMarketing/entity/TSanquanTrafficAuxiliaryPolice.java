package cn.chinaunicom.sdsi.targetMarketing.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.http.server.reactive.ServerHttpRequest;

import java.io.Serializable;

/**
 * @Description: 交辅警业务发展数据报表明细对象 t_sanquan_traffic_auxiliary_police
 * @Author: hanruxiao
 * @Date: 2024-11-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "交辅警业务发展数据报表明细对象", description = "交辅警业务发展数据报表明细对象")
@TableName("t_sanquan_traffic_auxiliary_police")
public class TSanquanTrafficAuxiliaryPolice implements Serializable {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "地市")
    private String cityCode;

    @Schema(name = "责任人")
    private String zrr;

    @Schema(name = "本周新增")
    private Long newAdd;

    @Schema(name = "本年度累计")
    private Long yearSum;

    @Schema(name = "年度目标")
    private Long yearTarget;

    @Schema(name = "完成率")
    private Double finishPer;

    @Schema(name = "排名")
    private Long sort;

    @Schema(name = "累计发展户")
    private Long totalDevelop;

    @Schema(name = "辅警总人数(hu)")
    private Long auxiliaryPoliceNum;

    @Schema(name = "渗透率")
    private Double penetrate;

}
