package cn.chinaunicom.sdsi.targetMarketing.service;

import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanDZqztGemWorkOrderFeedbackInfo;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztGemWorkOrderFeedbackInfoVO;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanDZqztGemWorkOrderFeedbackInfoQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 靶向营销反馈表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-08-16
 */
public interface TSanquanDZqztGemWorkOrderFeedbackInfoService extends IService<TSanquanDZqztGemWorkOrderFeedbackInfo> {

    /**
     * 分页查询靶向营销反馈表
     * 
     * @param tSanquanDZqztGemWorkOrderFeedbackInfoQueryVO
     * @return IPage<TSanquanDZqztGemWorkOrderFeedbackInfoVO>
     */
    IPage<TSanquanDZqztGemWorkOrderFeedbackInfoVO> findPage(TSanquanDZqztGemWorkOrderFeedbackInfoQueryVO tSanquanDZqztGemWorkOrderFeedbackInfoQueryVO);

    /**
     * 查询靶向营销反馈表详细信息
     *
     * @param ID
     * @return TSanquanDZqztGemWorkOrderFeedbackInfoVO
     */
    TSanquanDZqztGemWorkOrderFeedbackInfoVO findInfo(String ID);

    /**
     * 新增靶向营销反馈表
     *
     * @param tSanquanDZqztGemWorkOrderFeedbackInfoVO
     * @return String
     */
    String add(TSanquanDZqztGemWorkOrderFeedbackInfoVO tSanquanDZqztGemWorkOrderFeedbackInfoVO);

    /**
     * 修改靶向营销反馈表
     *
     * @param tSanquanDZqztGemWorkOrderFeedbackInfoVO
     * @return Boolean
     */
    Boolean update(TSanquanDZqztGemWorkOrderFeedbackInfoVO tSanquanDZqztGemWorkOrderFeedbackInfoVO);

    /**
     * 删除靶向营销反馈表
     *
     * @param ID
     * @return Boolean
     */
    Boolean delete(String ID);

}
