package cn.chinaunicom.sdsi.customer.service.impl;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerIdc;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerIdcQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerIdcVO;
import cn.chinaunicom.sdsi.customer.dao.TSanquanCustomerIdcMapper;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerIdcService;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * IDC需求摸查-客户资料
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */
@Service
public class TSanquanCustomerIdcServiceImpl extends ServiceImpl<TSanquanCustomerIdcMapper, TSanquanCustomerIdc> implements TSanquanCustomerIdcService {
    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param tSanquanCustomerIdcQuery
     * @return IPage<TSanquanCustomerIdc>
     **/
    @Override
    public IPage<TSanquanCustomerIdc> findPage(TSanquanCustomerIdcQuery tSanquanCustomerIdcQuery) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanCustomerIdcQuery);
        return baseMapper.findPage(page, tSanquanCustomerIdcQuery);
    }

    /*
     * <AUTHOR>
     * @Description 根据id查询
     * @Date ${date}
     * @param id
     * @return TSanquanCustomerIdc
     **/
    @Override
    public TSanquanCustomerIdc findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 查询列表
     * @Date ${date}
     * @return List<TSanquanCustomerIdc>
     **/
    @Override
    public List<TSanquanCustomerIdc> findList(TSanquanCustomerIdcQuery query) {
        return baseMapper.findList(query);
    }

    /*
     * <AUTHOR>
     * @Description 新增
     * @Date ${date}
     * @param tSanquanCustomerIdc
     * @return int
     **/
    @Override
    public int add(TSanquanCustomerIdc tSanquanCustomerIdc) {
        return baseMapper.insert(tSanquanCustomerIdc);
    }

    /*
     * <AUTHOR>
     * @Description 修改
     * @Date ${date}
     * @param tSanquanCustomerIdc
     * @return int
     **/
    @Override
    public int update(TSanquanCustomerIdc tSanquanCustomerIdc) {
        return baseMapper.updateById(tSanquanCustomerIdc);
    }

    /*
     * <AUTHOR>
     * @Description 删除
     * @Date ${date}
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 根据客户id查询
     * @param id
     * @return
     */
    @Override
    public List<TSanquanCustomerIdcVO> getByCustomerIdList(String id) {
        return baseMapper.selectByCustomerIdList(id);
    }

}