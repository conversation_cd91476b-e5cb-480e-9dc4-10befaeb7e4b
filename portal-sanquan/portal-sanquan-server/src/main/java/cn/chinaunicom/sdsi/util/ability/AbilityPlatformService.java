package cn.chinaunicom.sdsi.util.ability;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.message.BasicHeader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 调用能力平台
 */
public class AbilityPlatformService {
    private static final Logger logger = LoggerFactory.getLogger(AbilityPlatformService.class);
    private static final String UNI_BSS_HEAD = "UNI_BSS_HEAD";
    private static final String UNI_BSS_BODY = "UNI_BSS_BODY";
    private static final String UNI_BSS_ATTACHED = "UNI_BSS_ATTACHED";
    private static final String APP_ID = "APP_ID";
    private static final String TIMESTAMP = "TIMESTAMP";
    private static final String TRANS_ID = "TRANS_ID";
    private static final String TOKEN = "TOKEN";
    private static final String RESERVED = "RESERVED";
    private static final String EMPTY_STRING = "";
    private static final char AND_CHAR = 0x26;
    private static final char EQUALS_CHAR = 0x3D;
    private static final char COMMA_CHAR = 0x2C;
    private static final char SLASH_CHAR = 0x2F;
    private static final char QUESTION_MARK_CHAR = 0x3F;
    private static final HttpHeaders DEFAULT_HTTP_HEADERS;
    private static final org.apache.http.Header[] DEFAULT_APACHE_HTTP_HEADERS;

    static {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.put("Content-Type", Collections.singletonList("application/json; charset=UTF-8"));
        httpHeaders.put("Accept", Collections.singletonList("application/json"));
        httpHeaders.put("Accept-Encoding", Collections.singletonList("utf-8"));
        DEFAULT_HTTP_HEADERS = httpHeaders;
        org.apache.http.Header[] headers = new org.apache.http.Header[3];
        headers[0] = new BasicHeader("Content-Type", "application/json; charset=UTF-8");
        headers[1] = new BasicHeader("Accept", "application/json");
        headers[2] = new BasicHeader("Accept-Encoding", "utf-8");
        DEFAULT_APACHE_HTTP_HEADERS = headers;
    }


    private final String appId;
    private final String secretKey;
    private final String prefix;
    private final RestTemplate restTemplate;

    public AbilityPlatformService(RestTemplate restTemplate, String appId, String secretKey, String url) {
        this.restTemplate = restTemplate;
        this.appId = appId;
        this.secretKey = secretKey;
        this.prefix = url;
    }

    public AbilityPlatformService(String appId, String secretKey, String url) {
        this.restTemplate = null;
        this.appId = appId;
        this.secretKey = secretKey;
        this.prefix = url;
    }

    public JSONObject doGet(String requestUrl) {
        return doGet(requestUrl, new StringBuilder());
    }


    private String getCommonDoGetUrl(String requestUrl, final StringBuilder stringBuilder) {
        final long millis = System.currentTimeMillis();
        final String timestamp = new Timestamp(millis).toString();
        final String transId = getTransId(millis);
        dealMD5(this.appId, timestamp, transId, this.secretKey, stringBuilder);
        final String md5Token = stringBuilder.toString();
        stringBuilder.setLength(0);
        final String urlString = stringBuilder.append(this.prefix).
                append(QUESTION_MARK_CHAR).append(APP_ID).append(EQUALS_CHAR).append(appId).
                append(AND_CHAR).append(TIMESTAMP).append(EQUALS_CHAR).append(timestamp).
                append(AND_CHAR).append(TRANS_ID).append(EQUALS_CHAR).append(transId).
                append(AND_CHAR).append(TOKEN).append(EQUALS_CHAR).append(md5Token).
                append(requestUrl).toString();
        logger.info("请求服务的url是:{}", urlString);
        return urlString;
    }

    private JSONObject doGet(String requestUrl, final StringBuilder stringBuilder) {
        final String urlString = getCommonDoGetUrl(requestUrl, stringBuilder);
        HttpEntity<String> httpEntity = new HttpEntity<>(DEFAULT_HTTP_HEADERS);
        JSONObject result = restTemplate.exchange(urlString, HttpMethod.GET, httpEntity, JSONObject.class).getBody();
        logger.info("请求服务返回的结果是:{}", result);
        return result;
    }

    private static void dealMD5(final String appId, final String timestamp, final String transId,
                                final String secretKey, final StringBuilder stringBuilder) {
        stringBuilder.setLength(0);
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(stringBuilder.append(APP_ID).append(appId).append(TIMESTAMP).append(timestamp).append(TRANS_ID).append(transId).append(secretKey).toString().getBytes(StandardCharsets.UTF_8));
            stringBuilder.setLength(0);
            byte[] b = md5.digest();
            int i;
            for (byte aByte : b) {
                i = aByte;
                if (i < 0) {
                    i += 256;
                }
                if (i < 16) {
                    stringBuilder.append("0");
                }
                stringBuilder.append(Integer.toHexString(i));
            }

        } catch (Exception e) {
            logger.error("执行MD5加密时出现异常:", e);
        }
    }

    private static String getTransId(Long millis) {
        StringBuffer stringBuilder = new StringBuffer();
        DateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        Random random = new Random();
        stringBuilder.append(dateFormat.format(new Date(millis))).append(String.format("%06d",
                random.nextInt(1000000)));
        return stringBuilder.toString();
    }


    public JSONObject doGet(Map<String, Object> params) {
        final StringBuilder stringBuilder = new StringBuilder();
        dealParam(stringBuilder, params);
        String url = stringBuilder.toString();
        return doGet(url, stringBuilder);
    }

    public JSONObject doGet(String[] keys, String[] values) {
        final StringBuilder stringBuilder = new StringBuilder();
        dealParam(stringBuilder, keys, values);
        String url = stringBuilder.toString();
        return doGet(url, stringBuilder);
    }

    public JSONObject doGet(Object[]... params) {
        final StringBuilder stringBuilder = new StringBuilder();
        dealParam(stringBuilder, params);
        String url = stringBuilder.toString();
        return doGet(url, stringBuilder);
    }

    private static void dealParam(final StringBuilder stringBuilder, final Map<String, Object> params) {
        stringBuilder.setLength(0);
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                final String key = entry.getKey();
                if (key == null || key.isEmpty()) {
                    continue;
                }
                stringBuilder.append(AND_CHAR).append(key);
                Object value = entry.getValue();
                stringBuilder.append(EQUALS_CHAR);
                dealValue(value, stringBuilder);
            }
        }
    }

    private static void dealParam(final StringBuilder stringBuilder, String[] keys, Object[] values) {
        stringBuilder.setLength(0);
        if (keys != null && keys.length != 0) {
            final int valueLength = values == null ? 0 : values.length;
            final int keyLength = keys.length;
            for (int i = 0; i < keyLength; ++i) {
                final String key = keys[i];
                if (key == null || key.isEmpty()) {
                    continue;
                }
                stringBuilder.append(AND_CHAR).append(key);
                stringBuilder.append(EQUALS_CHAR);
                Object value;
                if (i == keyLength - 1 && keyLength < valueLength) {
                    value = new Object[valueLength - keyLength];
                    System.arraycopy(values, i, value, 0, valueLength - keyLength);
                }
                value = i < valueLength ? values[i] : EMPTY_STRING;
                dealValue(value, stringBuilder);
            }
        }
    }

    private static void dealParam(final StringBuilder stringBuilder, Object[]... params) {
        stringBuilder.setLength(0);
        if (params != null) {
            for (Object[] param : params) {
                if (param != null && param.length != 0) {
                    String key = (String) param[0];
                    stringBuilder.append(AND_CHAR).append(key);
                    stringBuilder.append(EQUALS_CHAR);
                    Object[] value = new Object[param.length - 1];
                    System.arraycopy(param, 1, value, 0, value.length);
                    dealValue(value, stringBuilder);
                }
            }
        }
    }

    private static void dealValue(Object value, final StringBuilder stringBuilder) {
        if (value != null) {
            if (value instanceof List<?>) {
                for (Object ignored : (List<?>) value) {
                    stringBuilder.append(dealArrayValue(value));
                    stringBuilder.append(COMMA_CHAR);
                }
                if (!((Set<?>) value).isEmpty()) {
                    stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                }
            }
            if (value instanceof Object[]) {
                for (Object ignored : (Object[]) value) {
                    stringBuilder.append(dealArrayValue(value));
                    stringBuilder.append(COMMA_CHAR);
                }
                if (!((Set<?>) value).isEmpty()) {
                    stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                }
            } else if (value.getClass().isPrimitive() || value instanceof CharSequence) {
                stringBuilder.append(value);
            } else if (value instanceof Map<?, ?>) {
                stringBuilder.append(JSONObject.toJSONString(value));
            } else if (value instanceof Date) {
                stringBuilder.append(((Date) value).getTime());
            } else if (value instanceof Set<?>) {
                for (Object ignored : (Set<?>) value) {
                    stringBuilder.append(dealArrayValue(value));
                    stringBuilder.append(COMMA_CHAR);
                }
                if (!((Set<?>) value).isEmpty()) {
                    stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                }
            }
        }
    }

    private static String dealArrayValue(Object value) {
        if (value != null) {
            final Object result;
            if (value.getClass().isPrimitive() || value instanceof CharSequence) {
                result = value;
            } else if (value instanceof Map<?, ?>) {
                result = JSONObject.toJSONString(value);
            } else if (value instanceof Date) {
                result = ((Date) value).getTime();
            } else {
                result = EMPTY_STRING;
            }
            return result.toString();
        } else {
            return EMPTY_STRING;
        }
    }

    private static void convertPath(final StringBuilder stringBuilder, String... paths) {
        stringBuilder.setLength(0);
        if (paths != null) {
            for (String path : paths) {
                if (path.charAt(path.length() - 1) == SLASH_CHAR) {
                    stringBuilder.append(path);
                } else {
                    stringBuilder.append(path).append(SLASH_CHAR);
                }
            }
        }
    }

    private JSONObject getCommonDoPostBody(JSONArray reservedArray, JSONObject attached, JSONObject jsonBodyObject) {
        final StringBuilder stringBuilder = new StringBuilder();
        final long millis = System.currentTimeMillis();
        final String timestamp = new Timestamp(millis).toString();
        final String transId = getTransId(millis);
        dealMD5(this.appId, timestamp, transId, this.secretKey, stringBuilder);
        final String md5Token = stringBuilder.toString();
        JSONObject jsonObject = new JSONObject();
        JSONObject jsonHeadObject = new JSONObject();
        jsonHeadObject.put(APP_ID, appId);
        jsonHeadObject.put(TIMESTAMP, timestamp);
        jsonHeadObject.put(TRANS_ID, transId);
        jsonHeadObject.put(TOKEN, md5Token);
        jsonHeadObject.put(RESERVED, reservedArray == null ? new JSONArray() : reservedArray);
        jsonObject.put(UNI_BSS_HEAD, jsonHeadObject);
        jsonObject.put(UNI_BSS_BODY, jsonBodyObject == null ? new JSONObject() : jsonBodyObject);
        jsonObject.put(UNI_BSS_ATTACHED, attached == null ? new JSONObject() : attached);
        return jsonObject;
    }

    public JSONObject doPost(JSONArray reservedArray, JSONObject attached, JSONObject jsonBodyObject) {
        JSONObject result;
        JSONObject jsonObject = getCommonDoPostBody(reservedArray, attached, jsonBodyObject);
        JSONObject obj = new JSONObject();
        HttpEntity<String> httpEntity = new HttpEntity<>(jsonObject.toString(), DEFAULT_HTTP_HEADERS);
        logger.info("调用服务的url是:{}", this.prefix);
        logger.info("请求服务的json请求参数是:{}", httpEntity.getBody());
        result = restTemplate.postForEntity(this.prefix, httpEntity, JSONObject.class).getBody();
        logger.info("请求服务的json返回参数是:{}", result.toString());
        obj.put("reqJson",httpEntity.getBody());
        obj.put("rspJson",result.toString());
        return obj;
    }

    public JSONObject doPost(JSONObject attached, JSONObject jsonBodyObject) {
        return doPost(null, attached, jsonBodyObject);
    }

    public JSONObject doPost(JSONObject jsonBodyObject) {
        return doPost(null, null, jsonBodyObject);
    }

    /**
     * 支持自定义headers的doPost方法
     *
     * @param attached 附加数据
     * @param jsonBodyObject 请求体数据
     * @param customHeaders 自定义headers
     * @return 响应结果
     */
    public JSONObject doPostWithCustomHeaders(JSONObject attached, JSONObject jsonBodyObject, HttpHeaders customHeaders) {
        JSONObject result;
        JSONObject jsonObject = getCommonDoPostBody(null, attached, jsonBodyObject);
        JSONObject obj = new JSONObject();
        HttpHeaders mergedHeaders = new HttpHeaders();
        mergedHeaders.addAll(DEFAULT_HTTP_HEADERS);
        if (customHeaders != null) {
            mergedHeaders.addAll(customHeaders);
        }
        HttpEntity<String> httpEntity = new HttpEntity<>(jsonObject.toString(), mergedHeaders);
        logger.info("调用服务的url是:{}", this.prefix);
        logger.info("请求服务的json请求参数是:{}", httpEntity.getBody());
        logger.info("请求服务的headers是:{}", mergedHeaders);
        result = restTemplate.postForEntity(this.prefix, httpEntity, JSONObject.class).getBody();
        logger.info("请求服务的json返回参数是:{}", result.toString());
        obj.put("reqJson", httpEntity.getBody());
        obj.put("rspJson", result.toString());
        return obj;
    }
}
