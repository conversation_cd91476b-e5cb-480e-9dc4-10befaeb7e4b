package cn.chinaunicom.sdsi.customer.controller;

import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanDZqztJihezxProvinceMappingQueryVO;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanPersonnel;
import cn.chinaunicom.sdsi.customer.service.TSanquanDZqztJihezxProvinceMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

/**
 * 客户经理信息 控制器
 *
 * <AUTHOR>
 * @since 2024-05-16
 */
@RestController
@RequestMapping("/customer/mapping")
public class TSanquanDZqztJihezxProvinceMappingController extends BaseController {

    @Autowired
    private TSanquanDZqztJihezxProvinceMappingService tSanquanDZqztJihezxProvinceMappingService;

    /*
     * <AUTHOR>
     * @description 分页查询客户经理信息
     * @since 2024-05-16
     * @param tSanquanDZqztJihezxProvinceMappingQueryVO
     * @return BasePageResponse<TSanquanDZqztJihezxProvinceMappingVO>
     **/
    @Operation(summary = "分页查询客户经理信息", description = "分页查询客户经理信息数据")
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanDZqztJihezxProvinceMappingVO> findPage(TSanquanDZqztJihezxProvinceMappingQueryVO tSanquanDZqztJihezxProvinceMappingQueryVO) {
        return pageOk(tSanquanDZqztJihezxProvinceMappingService.findPage(tSanquanDZqztJihezxProvinceMappingQueryVO));
    }

    /*
     * <AUTHOR>
     * @description 查询客户经理信息详细信息
     * @since 2024-05-16
     * @param LOGIN
     * @return BaseResponse<TSanquanDZqztJihezxProvinceMappingVO>
     **/
    @Operation(summary = "查询客户经理信息详细信息", description = "查询客户经理信息详细信息")
    @GetMapping("/findInfo")
    public BaseResponse<TSanquanDZqztJihezxProvinceMappingVO> findInfo(String LOGIN) {
        return ok(tSanquanDZqztJihezxProvinceMappingService.findInfo(LOGIN));
    }

    /*
     * <AUTHOR>
     * @description 查询客户经理信息详细信息
     * @since 2024-05-16
     * @param LOGIN
     * @return BaseResponse<TSanquanDZqztJihezxProvinceMappingVO>
     **/
    @Operation(summary = "查询客户经理信息详细信息", description = "查询客户经理信息详细信息")
    @GetMapping("/findOne")
    public TSanquanDZqztJihezxProvinceMappingVO findOne(String LOGIN) {
        TSanquanDZqztJihezxProvinceMappingVO vo = tSanquanDZqztJihezxProvinceMappingService.findInfo(LOGIN);
        if (vo == null) {
            vo = new TSanquanDZqztJihezxProvinceMappingVO();
            vo.setCity("0000");
        }
        return vo;
    }

    @PostMapping("/findCustomerManager")
    @Operation(summary = "客户经理信息")
    public BaseResponse<List<TSanquanDZqztJihezxProvinceMappingVO>> findCustomerManager(@RequestBody TSanquanDZqztJihezxProvinceMappingQueryVO tSanquanDZqztJihezxProvinceMappingQueryVO) {
        return ok(tSanquanDZqztJihezxProvinceMappingService.findCustomerManager(tSanquanDZqztJihezxProvinceMappingQueryVO));
    }

    /*
     * <AUTHOR>
     * @description 新增客户经理信息
     * @since 2024-05-16
     * @param tSanquanDZqztJihezxProvinceMappingVO
     * @return BaseResponse<String>
     **/
    @Operation(summary = "新增客户经理信息", description = "新增客户经理信息")
    @PostMapping("/add")
    public BaseResponse<String> add(@RequestBody TSanquanDZqztJihezxProvinceMappingVO tSanquanDZqztJihezxProvinceMappingVO) {
        return ok(tSanquanDZqztJihezxProvinceMappingService.add(tSanquanDZqztJihezxProvinceMappingVO));
    }

    /*
     * <AUTHOR>
     * @description 修改客户经理信息
     * @since 2024-05-16
     * @param tSanquanDZqztJihezxProvinceMappingVO
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "修改客户经理信息", description = "修改客户经理信息")
    @PostMapping("/update")
    public BaseResponse<Boolean> update(@RequestBody TSanquanDZqztJihezxProvinceMappingVO tSanquanDZqztJihezxProvinceMappingVO) {
        return ok(tSanquanDZqztJihezxProvinceMappingService.update(tSanquanDZqztJihezxProvinceMappingVO));
    }

    /*
     * <AUTHOR>
     * @description 删除客户经理信息
     * @since 2024-05-16
     * @param LOGIN
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "删除客户经理信息", description = "删除客户经理信息")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String LOGIN) {
        return ok(tSanquanDZqztJihezxProvinceMappingService.delete(LOGIN));
    }

}
