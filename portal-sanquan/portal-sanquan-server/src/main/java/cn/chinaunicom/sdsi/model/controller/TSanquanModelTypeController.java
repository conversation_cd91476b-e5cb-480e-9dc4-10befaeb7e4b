package cn.chinaunicom.sdsi.model.controller;

import cn.chinaunicom.sdsi.model.vo.TSanquanModelTypeVO;
import cn.chinaunicom.sdsi.model.queryvo.TSanquanModelTypeQueryVO;
import cn.chinaunicom.sdsi.model.service.TSanquanModelTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;

import java.util.List;

/**
 * 模型分类 控制器
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@RestController
@RequestMapping("/modeltype")
public class TSanquanModelTypeController extends BaseController {

    @Autowired
    private TSanquanModelTypeService tSanquanModelTypeService;

    /*
     * <AUTHOR>
     * @description 分页查询模型分类
     * @since 2024-07-19
     * @param tSanquanModelTypeQueryVO
     * @return BasePageResponse<TSanquanModelTypeVO>
     **/
    @Operation(summary = "分页查询模型分类", description ="分页查询模型分类数据")
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanModelTypeVO> findPage(TSanquanModelTypeQueryVO tSanquanModelTypeQueryVO) {
        return pageOk(tSanquanModelTypeService.findPage(tSanquanModelTypeQueryVO));
    }
    @Operation(summary = "查询模型分类", description ="查询模型分类")
    @PostMapping("/findModelTypeList")
    public BaseResponse<List<TSanquanModelTypeVO>> findModelTypeList(@RequestBody TSanquanModelTypeQueryVO tSanquanModelTypeQueryVO) {
        return ok(tSanquanModelTypeService.findModelTypeList(tSanquanModelTypeQueryVO));
    }
    /*
     * <AUTHOR>
     * @description 查询模型分类详细信息
     * @since 2024-07-19
     * @param id
     * @return BaseResponse<TSanquanModelTypeVO>
     **/
    @Operation(summary = "查询模型分类详细信息", description ="查询模型分类详细信息")
    @GetMapping("/findInfo")
    public BaseResponse<TSanquanModelTypeVO> findInfo(String id) {
        return ok(tSanquanModelTypeService.findInfo(id));
    }

    /*
     * <AUTHOR>
     * @description 新增模型分类
     * @since 2024-07-19
     * @param tSanquanModelTypeVO
     * @return BaseResponse<String>
     **/
    @Operation(summary = "新增模型分类", description ="新增模型分类")
    @PostMapping("/add")
    public BaseResponse<String> add(@RequestBody TSanquanModelTypeVO tSanquanModelTypeVO) {
        return ok(tSanquanModelTypeService.add(tSanquanModelTypeVO));
    }

    /*
     * <AUTHOR>
     * @description 修改模型分类
     * @since 2024-07-19
     * @param tSanquanModelTypeVO
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "修改模型分类", description ="修改模型分类")
    @PostMapping("/update")
    public BaseResponse<Boolean> update(@RequestBody TSanquanModelTypeVO tSanquanModelTypeVO) {
        return ok(tSanquanModelTypeService. update(tSanquanModelTypeVO));
    }

    /*
     * <AUTHOR>
     * @description 删除模型分类
     * @since 2024-07-19
     * @param id
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "删除模型分类", description ="删除模型分类")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String id) {
        return ok(tSanquanModelTypeService.delete(id));
    }

    /****
     * 检查类型编码是否存在
     * @param id
     * @param code
     * @return
     */
    @PostMapping(value = "/checkModelTypeCode")
    public BaseResponse<Integer> checkModelTypeCode(String id, String code) {
        return ok(tSanquanModelTypeService.checkModelTypeCode(id, code));
    }

    /****
     * 检查类型名称是否存在
     * @param id
     * @param name
     * @return
     */
    @PostMapping(value = "/checkModelTypeName")
    public BaseResponse<Integer> checkModelTypeName(String id, String name) {
        return ok(tSanquanModelTypeService.checkModelTypeName(id, name));
    }
    /****
     * 检查该类型下是否已有模型
     * @param typeId
     * @return
     */
    @PostMapping(value = "/checkHasModel")
    public BaseResponse<Boolean> checkHasModel(String typeId) {
        return ok(tSanquanModelTypeService.checkHasModel(typeId));
    }
}
