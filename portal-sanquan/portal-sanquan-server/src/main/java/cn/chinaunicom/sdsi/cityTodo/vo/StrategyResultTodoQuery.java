package cn.chinaunicom.sdsi.cityTodo.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/11/1 10:32
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Schema(description = "待办表")
public class StrategyResultTodoQuery extends BaseQueryVO {
    private static final long serialVersionUID = 1L;

    private String id;

    private String todoCode;

    private String ownerSubject;

    private String dueTime;

    private String status;

    private String createUser;

    private String createTime;

    private String strategyResultId;

    private String strategyConfigId;

    /**
     * 执行人
     */
    private String currentPerson;

    /**
     * 执行人工号
     */
    private String currentPersonId;

    /**
     * 推送类型
     */
    private String pushType;

    @Schema(name = "产品名称")
    private String productName;

    /**
     * 关单时间
     */
    private String closeDate;

    /**
     * 标识
     */
    private String detailsType;

}
