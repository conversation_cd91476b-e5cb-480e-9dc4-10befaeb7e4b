package cn.chinaunicom.sdsi.chengxiao.service;

import cn.chinaunicom.sdsi.chengxiao.entity.ChengxiaoZhuanhua;
import cn.chinaunicom.sdsi.chengxiao.entity.ChengxiaoZhuanhuaVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 成效转化 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
public interface ChengxiaoZhuanhuaService extends IService<ChengxiaoZhuanhua> {

    List<String> getDataSourceList();

    String getMaxDate();

    List<String> getCityList();

    // 分页查询
    IPage<ChengxiaoZhuanhua> findPage(ChengxiaoZhuanhuaVO chengxiaoZhuanhuaVO);

    // 根据id查询
    ChengxiaoZhuanhua findOne(String id);

    // 查询列表
    List<ChengxiaoZhuanhua> findList();

    // 新增
    int add(ChengxiaoZhuanhua chTSanquanChengxiaoZhuanhuaResult);

    // 修改
    int update(ChengxiaoZhuanhua chTSanquanChengxiaoZhuanhuaResult);

    // 删除
    int delete(String id);

}
