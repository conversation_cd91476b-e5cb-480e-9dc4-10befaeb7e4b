package cn.chinaunicom.sdsi.rosterCustomer.service;

import cn.chinaunicom.sdsi.cloud.nature_customer.entity.NatureCoustomer;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.NatureCoustomerQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO;
import cn.chinaunicom.sdsi.cloud.rosterCustomer.entity.TSanquanRosterCustomerHierarchy;
import cn.chinaunicom.sdsi.cloud.rosterCustomer.query.TSanquanRosterCustomerHierarchyQuery;
import cn.chinaunicom.sdsi.cloud.rosterCustomer.vo.TSanquanRosterCustomerHierarchyTreeVO;
import cn.chinaunicom.sdsi.cloud.rosterCustomer.vo.TSanquanRosterCustomerHierarchyVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface TSanquanRosterCustomerHierarchyService extends IService<TSanquanRosterCustomerHierarchy> {

    /**
     * 分类查询
     * @param query
     * @return
     */
    List<TSanquanRosterCustomerHierarchyVO> findList(TSanquanRosterCustomerHierarchyQuery query);

    //树形结构
    TSanquanRosterCustomerHierarchyTreeVO findTreeById(String id);

    //查询全部的树形结构
    List<TSanquanRosterCustomerHierarchyTreeVO> findAllTree();

    //根据名单制客户名称查询树形结构
    TSanquanRosterCustomerHierarchyTreeVO findTreeByName(String rosterCustomerName);

    //根据查询条件生成树
    TSanquanRosterCustomerHierarchyTreeVO findTreeList(TSanquanRosterCustomerHierarchyQuery query);

    //根据省code查询所有的id集合
    List<String> selectByPovinceCode(String provinceCode);

    //根据市code查询所有的id集合
    List<String> selectByCityCode(String cityCode);

    //根据客户经理工号查询树形结构
    List<TSanquanRosterCustomerHierarchyTreeVO> findTreeByManagerId(String managerId);

    //根据城市查询树形结构
    List<TSanquanRosterCustomerHierarchyTreeVO> findTreeByCityName(String cityName);

    //地市接口人获取树形结构
    List<TSanquanRosterCustomerHierarchyTreeVO> findTreeByCityPerson();
}
