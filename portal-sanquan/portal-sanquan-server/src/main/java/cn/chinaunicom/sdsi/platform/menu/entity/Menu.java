package cn.chinaunicom.sdsi.platform.menu.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_sanquan_platform_menu")
public class Menu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;

    /**
     * 访问地址
     */

    private String path;

    /**
     * 菜单名称
     */
    private String title;

    private String icon;

    private String parentId;

    private String parentIdSet;
    /**
     * 加载文件
     */
    private String menuLoadFile;

    /**
     * 是否显示
     */
    private String showMenu;

    /**
     * 菜单排序
     */
    private String menuSort;

    /**
     * 0目录,1菜单,2权限
     */
    private String menuType;

    /**
     * 菜单权限编码
     */
    private String menuPermission;

//    /**
//     * 逻辑删除，normal表示正常，deleted表示删除
//     */
//    @TableLogic(value = "normal", delval = "deleted")
//    @Schema(name="逻辑删除，normal表示正常，deleted表示删除")
//    private String deleteFlag = "normal";

    // 菜单系统
    private String menuSystem;

}
