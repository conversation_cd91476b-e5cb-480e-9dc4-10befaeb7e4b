package cn.chinaunicom.sdsi.strategyResult.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 策略推荐结果表查询对象
 * @Author: han<PERSON><PERSON>o
 * @Date: 2024-04-16
 */
@Data
@Tag(name = "策略推荐结果表查询对象", description = "策略推荐结果表查询对象")
public class TSanquanStrategyResultQueryVO extends BaseQueryVO {

    @Schema(name = "策略配置id")
    private String strategyConfigId;

    @Schema(name = "策略名称")
    private String strategyName;

    @Schema(name = "产品id")
    private String productId;

    @Schema(name = "客户名称")
    private String customerName;

    @Schema(name = "产品名称")
    private String productName;

    @Schema(name = "策略执行时间")
    private Date executeTime;

    @Schema(name = "策略执行周期 每天,每周,每月")
    private String strategyExcuteCircle;

    @Schema(name = "潜在客户数")
    private Long potentialCustNum;

    @Schema(name = "潜在商机数")
    private Long opportunityNum;

    @Schema(name = "预留字段1")
    private String attribute1;

    @Schema(name = "预留字段2")
    private String attribute2;

    @Schema(name = "预留字段3")
    private String attribute3;

    @Schema(name = "创建人")
    private String createBy;

    @Schema(name = "创建时间")
    private Date createDate;

    @Schema(name = "开始时间")
    private String beginTime;

    @Schema(name = "结束时间")
    private String endTime;

    @Schema(name = "所属行业")
    private String industry;

    @Schema(description = "区域类型 P:省  C:地市")
    private String areaType;

    @Schema(description = "登录人角色")
    private String loginRole;

    @Schema(description = "创建人登录名")
    private String loginName;

    @Schema(description = "所属地市")
    private String city;
}
