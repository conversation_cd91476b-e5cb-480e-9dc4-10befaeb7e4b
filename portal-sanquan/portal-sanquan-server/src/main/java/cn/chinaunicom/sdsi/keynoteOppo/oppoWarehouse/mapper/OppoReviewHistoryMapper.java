package cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.mapper;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoReviewHistory;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoReviewHistoryQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 商机评审表(商机库表)(历史表) Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Mapper
public interface OppoReviewHistoryMapper extends BaseMapper<OppoReviewHistory> {

    // 分页查询
    IPage<OppoReviewHistory> findPage(@Param("page") IPage page, @Param("query") OppoReviewHistoryQueryVo oppoReviewHistoryVo);

    // 查询列表
    List<OppoReviewHistory> findList(@Param("query") OppoReviewHistoryQueryVo oppoReviewHistoryVo);
}
