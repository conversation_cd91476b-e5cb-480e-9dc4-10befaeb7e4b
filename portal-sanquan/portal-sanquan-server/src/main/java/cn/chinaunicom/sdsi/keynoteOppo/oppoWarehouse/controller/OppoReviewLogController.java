package cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.controller;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoReviewLogQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoReviewLogVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.service.OppoReviewLogService;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoReviewLog;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 商机评审流程日志表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@RestController
@RequestMapping("/oppoWarehouse/reviewLog")
public class OppoReviewLogController extends BaseController {

    @Autowired
    private OppoReviewLogService oppoReviewLogService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-06-26
     * @param oppoReviewLogVO
     * @return BasePageResponse<OppoReviewLog>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<OppoReviewLogVo> findPage(OppoReviewLogQueryVo oppoReviewLogVo){
        return pageOk(oppoReviewLogService.findPage(oppoReviewLogVo));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-06-26
     * @param id
     * @return BaseResponse<OppoReviewLog>
     **/
    @GetMapping("/findOne")
    public BaseResponse<OppoReviewLog> findOne(String id) {
        return ok(oppoReviewLogService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-06-26
     * @return BaseResponse<List<OppoReviewLog>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<OppoReviewLog>> findList(OppoReviewLogQueryVo oppoReviewLogVo) {
        return ok(oppoReviewLogService.findList(oppoReviewLogVo));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since 2025-06-26
     * @param oppoReviewLog
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody OppoReviewLog oppoReviewLog){
        return ok(oppoReviewLogService.add(oppoReviewLog));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since 2025-06-26
     * @param oppoReviewLog
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody OppoReviewLog oppoReviewLog) {
        return ok(oppoReviewLogService.update(oppoReviewLog));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since 2025-06-26
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(oppoReviewLogService.delete(id));
    }
}
