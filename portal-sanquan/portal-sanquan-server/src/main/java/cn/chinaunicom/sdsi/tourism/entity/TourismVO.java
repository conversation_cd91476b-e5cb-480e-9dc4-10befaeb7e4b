package cn.chinaunicom.sdsi.tourism.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-11-05
 */
@Data
public class TourismVO extends BaseQueryVO {

    private static final long serialVersionUID = 1L;

    private String id;
    private String userId;
    private String userName;
    private String roleId;
    private String cityId;
    private String countryId;
    private String roleName;
    private String city;
    private String configType;
    private String country;
    private String email;
    // 周期类型,1,文旅一周一次，2文旅二周一次，3文旅一月一次，4靶向合约到期/终端合约到期邮件推送,5等保测评片区经理
    private String cycleType;
    // 周期
    private String cycleName;
    private String status;

    @Schema(name="创建人")
    private String createBy;

    @Schema(name="创建时间")
    private Date createDate;

    @Schema(name="更新人")
    private String updateBy;

    @Schema(name="更新时间")
    private Date updateDate;

    /*业务类型*/
    private String businessType;
    private String businessName;
    private String templateName;
    /*所属行业*/
    private String industry;
    /*当前业务组织*/
    private String groupName;


}
