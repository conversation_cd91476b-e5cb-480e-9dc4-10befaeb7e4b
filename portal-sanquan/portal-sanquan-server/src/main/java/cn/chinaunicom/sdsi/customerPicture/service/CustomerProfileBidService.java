package cn.chinaunicom.sdsi.customerPicture.service;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileBid;
import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 三全客户画像-客户标讯信息-标讯信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
public interface CustomerProfileBidService extends IService<CustomerProfileBid> {

    //客户标讯总标书数
    int getProfileBid(CustomerPictureVo pictureVo);

    //客户标讯联通中标数
    int getProfileLiantongBid(CustomerPictureVo pictureVo);
}
