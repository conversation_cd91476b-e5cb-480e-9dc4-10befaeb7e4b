package cn.chinaunicom.sdsi.customer.service.impl;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerArithmeticIntelligence;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerArithmeticIntelligenceQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerArithmeticIntelligencePercentageVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerArithmeticIntelligenceVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.customer.dao.TSanquanCustomerArithmeticIntelligenceMapper;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerArithmeticIntelligenceService;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 算网数智-异网情况
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */
@Service
public class TSanquanCustomerArithmeticIntelligenceServiceImpl extends ServiceImpl<TSanquanCustomerArithmeticIntelligenceMapper, TSanquanCustomerArithmeticIntelligence> implements TSanquanCustomerArithmeticIntelligenceService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date ${date}
     * @param tSanquanCustomerArithmeticIntelligenceQuery
     * @return IPage<TSanquanCustomerArithmeticIntelligence>
     **/
    @Override
    public IPage<TSanquanCustomerArithmeticIntelligence> findPage(TSanquanCustomerArithmeticIntelligenceQuery tSanquanCustomerArithmeticIntelligenceQuery) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanCustomerArithmeticIntelligenceQuery);
        return baseMapper.findPage(page, tSanquanCustomerArithmeticIntelligenceQuery);
    }

    /*
     * <AUTHOR>
     * @Description 根据id查询
     * @Date ${date}
     * @param id
     * @return TSanquanCustomerArithmeticIntelligence
     **/
    @Override
    public TSanquanCustomerArithmeticIntelligence findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 查询列表
     * @Date ${date}
     * @return List<TSanquanCustomerArithmeticIntelligence>
     **/
    @Override
    public List<TSanquanCustomerArithmeticIntelligence> findList(TSanquanCustomerArithmeticIntelligenceQuery query) {
        return baseMapper.findList(query);
    }

    /*
     * <AUTHOR>
     * @Description 新增
     * @Date ${date}
     * @param tSanquanCustomerArithmeticIntelligence
     * @return int
     **/
    @Override
    public int add(TSanquanCustomerArithmeticIntelligence tSanquanCustomerArithmeticIntelligence) {
//        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
//                unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
//            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
//            tSanquanCustomerArithmeticIntelligence.setTenantId(tenantId);
//        }
        return baseMapper.insert(tSanquanCustomerArithmeticIntelligence);
    }

    /*
     * <AUTHOR>
     * @Description 修改
     * @Date ${date}
     * @param tSanquanCustomerArithmeticIntelligence
     * @return int
     **/
    @Override
    public int update(TSanquanCustomerArithmeticIntelligence tSanquanCustomerArithmeticIntelligence) {
        return baseMapper.updateById(tSanquanCustomerArithmeticIntelligence);
    }

    /*
     * <AUTHOR>
     * @Description 删除
     * @Date ${date}
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 根据客户id查询
     * @param id
     * @return
     */
    @Override
    public List<TSanquanCustomerArithmeticIntelligenceVO> getByCustomerIdList(String id) {
        return baseMapper.selectByCustomerIdList(id);
    }

    /**
     * 根据客户id查询占比（分类和运营商）
     * @param id
     * @return
     */
    @Override
    public List<TSanquanCustomerArithmeticIntelligencePercentageVO> findPercentage(String id) {
        if(!StringUtils.isNotEmpty(id)){
            throw new ServiceErrorException("客户id不能为空");
        }
        return baseMapper.findPercentage(id);
    }

}