package cn.chinaunicom.sdsi.private5GWorkflow.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 协调单信息查询响应实体类
 */
@Data
public class QryOsslBusiColbInfoByOrderIdResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 地市
     */
    private String areaId;

    /**
     * 协调单编码
     */
    private String busiColbNtfShtNo;

    /**
     * 申请时间
     */
    private String submitTime;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 业务类型
     */
    private String serviceType;

    /**
     * 主题
     */
    private String sheetTitle;

    /**
     * 业务需求内容
     */
    private String busiReqContent;

    /**
     * 处理时限要求
     */
    private String limitDate;

    /**
     * 要求反馈时间
     */
    private String responseTime;

    /**
     * 申请类型
     */
    private String applyType;

    /**
     * 客户经理
     */
    private String custMgrName;

    /**
     * 客户经理联系电话
     */
    private String custMgrTel;

    /**
     * 工单状态
     */
    private String sheetStatus;

    /**
     * 流程节点集合
     */
    private List<TodoLogInfo> todoLogInfoList;

    /**
     * 流程节点信息内部类
     */
    @Data
    public static class TodoLogInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 任务到达时间
         */
        private String createTime;

        /**
         * 任务结束时间
         */
        private String endTime;

        /**
         * 步骤名称
         */
        private String nodeName;

        /**
         * 处理人姓名
         */
        private String staffName;

        /**
         * 处理人工号
         */
        private String staffId;

        /**
         * 处理人电话
         */
        private String staffTel;

        /**
         * 处理结果
         */
        private String dealDesc;

        /**
         * 审批状态
         */
        private String dealResult;
    }

    /**
     * 从JSON数据解析为实体对象
     *
     * @param dataJson 数据JSON对象
     * @return 实体对象
     */
    public static QryOsslBusiColbInfoByOrderIdResponse fromJsonData(JSONObject dataJson) {
        QryOsslBusiColbInfoByOrderIdResponse info = new QryOsslBusiColbInfoByOrderIdResponse();
        try {
            if (dataJson != null) {
                // 解析协调单基本信息
                info.setAreaId(dataJson.getString("areaId"));
                info.setBusiColbNtfShtNo(dataJson.getString("busiColbNtfShtNo"));
                info.setSubmitTime(dataJson.getString("submitTime"));
                info.setCustName(dataJson.getString("custName"));
                info.setServiceType(dataJson.getString("serviceType"));
                info.setSheetTitle(dataJson.getString("sheetTitle"));
                info.setBusiReqContent(dataJson.getString("busiReqContent"));
                info.setLimitDate(dataJson.getString("limitDate"));
                info.setResponseTime(dataJson.getString("responseTime"));
                info.setApplyType(dataJson.getString("applyType"));
                info.setCustMgrName(dataJson.getString("custMgrName"));
                info.setCustMgrTel(dataJson.getString("custMgrTel"));
                info.setSheetStatus(dataJson.getString("sheetStatus"));

                // 解析流程节点信息列表
                JSONArray todoLogArray = dataJson.getJSONArray("todoLogInfoList");
                if (todoLogArray != null && todoLogArray.size() > 0) {
                    List<TodoLogInfo> todoLogInfoList = new ArrayList<>();
                    for (int i = 0; i < todoLogArray.size(); i++) {
                        JSONObject todoLogJson = todoLogArray.getJSONObject(i);
                        TodoLogInfo todoLogInfo = new TodoLogInfo();
                        todoLogInfo.setCreateTime(todoLogJson.getString("createTime"));
                        todoLogInfo.setEndTime(todoLogJson.getString("endTime"));
                        todoLogInfo.setNodeName(todoLogJson.getString("nodeName"));
                        todoLogInfo.setStaffName(todoLogJson.getString("staffName"));
                        todoLogInfo.setStaffId(todoLogJson.getString("staffId"));
                        todoLogInfo.setStaffTel(todoLogJson.getString("staffTel"));
                        todoLogInfo.setDealDesc(todoLogJson.getString("dealDesc"));
                        todoLogInfo.setDealResult(todoLogJson.getString("dealResult"));
                        todoLogInfoList.add(todoLogInfo);
                    }
                    info.setTodoLogInfoList(todoLogInfoList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return info;
    }
}
