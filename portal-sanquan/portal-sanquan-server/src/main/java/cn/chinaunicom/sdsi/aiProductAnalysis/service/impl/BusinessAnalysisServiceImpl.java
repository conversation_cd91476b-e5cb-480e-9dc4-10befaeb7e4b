package cn.chinaunicom.sdsi.aiProductAnalysis.service.impl;


import cn.chinaunicom.sdsi.aiProductAnalysis.dao.BusinessAnalysisMapper;
import cn.chinaunicom.sdsi.aiProductAnalysis.dto.BusinessAnalysisDTO;
import cn.chinaunicom.sdsi.aiProductAnalysis.entity.BusinessAnalysis;
import cn.chinaunicom.sdsi.aiProductAnalysis.service.IBusinessAnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业务分析服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Service
public class BusinessAnalysisServiceImpl implements IBusinessAnalysisService {

    @Autowired
    private BusinessAnalysisMapper businessAnalysisMapper;

    @Override
    public List<BusinessAnalysis> selectPendingBusinessAnalysis(BusinessAnalysisDTO param) {
        return businessAnalysisMapper.selectPendingBusinessAnalysis(param);
    }

    @Override
    public int updateBusinessAnalysis(BusinessAnalysisDTO param) {
        return businessAnalysisMapper.updateBusinessAnalysis(param);
    }

    @Override
    public int insertBusinessAnalysis(BusinessAnalysis businessAnalysis) {
        return businessAnalysisMapper.insertBusinessAnalysis(businessAnalysis);
    }

    @Override
    public BusinessAnalysis selectById(String id) {
        return businessAnalysisMapper.selectById(id);
    }
}