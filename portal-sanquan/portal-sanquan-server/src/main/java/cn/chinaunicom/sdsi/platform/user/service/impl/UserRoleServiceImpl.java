package cn.chinaunicom.sdsi.platform.user.service.impl;

import cn.chinaunicom.sdsi.platform.user.entity.UserRole;
import cn.chinaunicom.sdsi.platform.user.mapper.UserRoleMapper;
import cn.chinaunicom.sdsi.platform.user.service.IUserRoleService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Service
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRole> implements IUserRoleService {

    @Override
    public void insertBatch(List<UserRole> userRoles) {
        baseMapper.insertBatchSomeColumn(userRoles);
    }

    @Override
    public void deleteByUserId(String userId) {
        baseMapper.deleteByUserId(userId);
    }

    @Override
    public void deleteByRoleId(String roleId) {
        baseMapper.deleteByRoleId(roleId);
    }

    @Override
    public void deleteByRoleIdAndUserId(String roleId, String userId) {
        baseMapper.deleteByRoleIdAndUserId(roleId, userId);
    }
}
