package cn.chinaunicom.sdsi.customer.controller;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerArithmeticIntelligence;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerArithmeticIntelligenceQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerArithmeticIntelligenceVO;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerArithmeticIntelligenceService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
* 算网数智-异网情况
*
* <AUTHOR> 
* @since  2024-05-15
*/
@Slf4j
@RestController
@RequestMapping("/customer/arithmeticIntelligence")
@Tag(name="算网数智-异网情况")
public class TSanquanCustomerArithmeticIntelligenceController extends BaseController {

    @Autowired
    private TSanquanCustomerArithmeticIntelligenceService tSanquanCustomerArithmeticIntelligenceService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since ${date}
     * @param tSanquanCustomerArithmeticIntelligenceQuery
     * @return BasePageResponse<TSanquanCustomerArithmeticIntelligence>
     **/
    @GetMapping("/findPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<TSanquanCustomerArithmeticIntelligence> findPage(TSanquanCustomerArithmeticIntelligenceQuery tSanquanCustomerArithmeticIntelligenceQuery){
        return pageOk(tSanquanCustomerArithmeticIntelligenceService.findPage(tSanquanCustomerArithmeticIntelligenceQuery));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since ${date}
     * @param id
     * @return BaseResponse<TSanquanCustomerArithmeticIntelligence>
     **/
    @GetMapping("/findOne")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<TSanquanCustomerArithmeticIntelligence> findOne(String id) {
        return ok(tSanquanCustomerArithmeticIntelligenceService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since ${date}
     * @return BaseResponse<List<TSanquanCustomerArithmeticIntelligence>>
     **/
    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<TSanquanCustomerArithmeticIntelligence>> findList(TSanquanCustomerArithmeticIntelligenceQuery query) {
        return ok(tSanquanCustomerArithmeticIntelligenceService.findList(query));
    }

    /*
     * <AUTHOR>
     * @description 新增
     * @since ${date}
     * @param tSanquanCustomerInfo
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    @Operation(summary = "新增", description = "新增")
    public BaseResponse<Integer> add(@RequestBody TSanquanCustomerArithmeticIntelligence tSanquanCustomerArithmeticIntelligence){
        return ok(tSanquanCustomerArithmeticIntelligenceService.add(tSanquanCustomerArithmeticIntelligence));
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since ${date}
     * @param tSanquanCustomerInfo
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    @Operation(summary = "修改", description = "修改")
    public BaseResponse<Integer> update(@RequestBody TSanquanCustomerArithmeticIntelligence tSanquanCustomerArithmeticIntelligence) {
        return ok(tSanquanCustomerArithmeticIntelligenceService.update(tSanquanCustomerArithmeticIntelligence));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since ${date}
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    @Operation(summary = "删除", description = "删除")
    public BaseResponse<Integer> delete(String id) {
        return ok(tSanquanCustomerArithmeticIntelligenceService.delete(id));
    }
}