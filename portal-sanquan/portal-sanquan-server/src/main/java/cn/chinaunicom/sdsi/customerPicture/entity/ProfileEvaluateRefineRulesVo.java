package cn.chinaunicom.sdsi.customerPicture.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/9/18 13:55
 */
@Data
public class ProfileEvaluateRefineRulesVo {
    @TableId
    private String id;
    /*细化指标名称*/
    private String refineName;

    private String refineCode;

    private String scoreValue;
    /*细化指标口径*/
    private String caliber;

    private String maxValue;

    private String minValue;
    private String evaluateCode;
    /*评价指标*/
    private String evaluateName;
    private String dimensionCode;
    /* 评价维度 */
    private String dimensionName;

}
