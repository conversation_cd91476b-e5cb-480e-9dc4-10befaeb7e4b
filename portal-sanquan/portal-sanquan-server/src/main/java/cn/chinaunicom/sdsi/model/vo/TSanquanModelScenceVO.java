package cn.chinaunicom.sdsi.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description: 模型场景
 * @Author: han
 * @Date: 2024-07-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "模型场景", description = "模型场景")
public class TSanquanModelScenceVO {

    @Schema(name = "")
    private String id;

    @Schema(name = "模型分类id")
    private String modelTypeId;

    @Schema(name = "模型场景")
    private String scenceName;


}
