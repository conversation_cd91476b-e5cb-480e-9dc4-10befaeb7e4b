package cn.chinaunicom.sdsi.customer.dao;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerNetworkedCommunication;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerNetworkedCommunicationQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerNetworkedCommunicationPercentageVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerNetworkedCommunicationVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 联网通信-异网情况
*
* <AUTHOR> 
* @since  2024-05-14
*/
@Mapper
public interface TSanquanCustomerNetworkedCommunicationMapper extends BaseMapper<TSanquanCustomerNetworkedCommunication> {

    //根据客户id查询
    List<TSanquanCustomerNetworkedCommunicationVO> selectByCustomerIdList(@Param("id") String id);

    //分页
    IPage<TSanquanCustomerNetworkedCommunication> findPage(@Param("page") IPage page,@Param("query") TSanquanCustomerNetworkedCommunicationQuery tSanquanCustomerNetworkedCommunicationQuery);

    //列表
    List<TSanquanCustomerNetworkedCommunication> findList(@Param("query") TSanquanCustomerNetworkedCommunicationQuery query);

    //查询各个分类占比
    List<TSanquanCustomerNetworkedCommunicationPercentageVO> findPercentage(@Param("id") String id);
}