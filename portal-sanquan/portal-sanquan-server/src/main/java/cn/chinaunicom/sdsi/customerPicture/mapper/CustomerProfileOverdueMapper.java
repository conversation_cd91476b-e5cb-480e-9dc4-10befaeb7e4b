package cn.chinaunicom.sdsi.customerPicture.mapper;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOverdue;
import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 三全客户画像-回款信息-客户逾期表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Mapper
public interface CustomerProfileOverdueMapper extends BaseMapper<CustomerProfileOverdue> {

    CustomerProfileOverdue getMaxOverdueByCustomer(CustomerPictureVo pictureVo);
    CustomerProfileOverdue getArrearsInfo(CustomerPictureVo pictureVo);

}
