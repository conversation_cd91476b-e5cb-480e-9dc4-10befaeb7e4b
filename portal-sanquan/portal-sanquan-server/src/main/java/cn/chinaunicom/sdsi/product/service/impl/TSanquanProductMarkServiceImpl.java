package cn.chinaunicom.sdsi.product.service.impl;

import cn.chinaunicom.sdsi.cloud.product.entity.ProductEntity;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductMark;
import cn.chinaunicom.sdsi.cloud.product.vo.ProductMarkVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.product.dao.TSanquanProductMarkMapper;
import cn.chinaunicom.sdsi.product.service.ProductService;
import cn.chinaunicom.sdsi.product.service.TSanquanProductMarkService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.SerializationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.print.attribute.standard.PrinterURI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 1
 *
 * <AUTHOR> 
 * @since  2024-04-19
 */
@Service
public class TSanquanProductMarkServiceImpl extends ServiceImpl<TSanquanProductMarkMapper, TSanquanProductMark> implements TSanquanProductMarkService {

    @Autowired
    private ProductService productService;
    @Autowired
    private TSanquanProductMarkMapper tSanquanProductMarkMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String productMaking(ProductMarkVO vo) {
        TSanquanProductMark tSanquanProductMark = new TSanquanProductMark();
//        tSanquanProductMark.setMarkData(String.join(",", vo.getMarkData()));
        tSanquanProductMark.setProductId(vo.getId());
        ProductEntity byId = productService.getById(vo.getId());
        if(byId == null){
            throw new ServiceErrorException("产品id不能为空！");
        }
        //先删除原来的关联标签，再插入新的
        Map<String, Object> delMap = new HashMap<>();
        delMap.put("product_id",vo.getId());
        int delResult = tSanquanProductMarkMapper.deleteByMap(delMap);
        List<TSanquanProductMark> markList = vo.getMarkList();
        if(CollectionUtils.isNotEmpty(markList)){
            for(TSanquanProductMark mark: markList){
                System.err.println("markid============"+mark.getMarkDataId()+"==="+vo.getId());
                if(StringUtils.isNotBlank(mark.getMarkDataId())){
                    tSanquanProductMarkMapper.insert(mark);
                }
            }
        }
        //修改产品的打标状态
        productService.update(Wrappers.<ProductEntity>lambdaUpdate().eq(ProductEntity::getId,vo.getId()).set(ProductEntity::getIsMaking,1));
//        TSanquanProductMark one = this.getOne(Wrappers.<TSanquanProductMark>lambdaQuery().eq(TSanquanProductMark::getProductId, byId.getId()));
//        if(one != null){
//            tSanquanProductMark.setId(one.getId());
//            boolean save = this.updateById(tSanquanProductMark);
//        }else{
//            boolean save = this.save(tSanquanProductMark);
//        }
        return tSanquanProductMark.getId();
    }

    /**
     * 产品标签数量
     * @return
     */
    @Override
    public int getProductLabelNum(ProductMarkVO vo) {
        return baseMapper.selectProductLabelNum(vo);
    }
}