package cn.chinaunicom.sdsi.chengxiaoNew.vo;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2025/01/07 14:19
 */

@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class ChengXiaoDetailExcelVo implements Serializable {

    /*数据来源*/
    @ExcelIgnore
    String dataSource;

    /*地市*/
    @ColumnWidth(25)
    @ExcelProperty(value = "任务ID")
    String taskId;

    /*地市*/
    @ColumnWidth(10)
    @ExcelProperty(value = "地市")
    String cityName;

    @ColumnWidth(20)
    @ExcelProperty(value = "区县")
    String districtName;

    @ColumnWidth(30)
    @ExcelProperty(value = "营服")
    String gridName;

    @ColumnWidth(15)
    @ExcelProperty(value = "省分行业")
    String provinceIndustryName;

    @ColumnWidth(15)
    @ExcelProperty(value = "执行人")
    String executorName;

    @ColumnWidth(15)
    @ExcelProperty(value = "执行人OA工号")
    String executorOaId;

    @ColumnWidth(15)
    @ExcelProperty(value = "执行人手机")
    String executorPhone;

    @ColumnWidth(25)
    @ExcelProperty(value = "执行时间")
    String execTime;

    @ColumnWidth(15)
    @ExcelProperty(value = "自然客户ID")
    String natureCustId;

    @ColumnWidth(30)
    @ExcelProperty(value = "自然客户名称")
    String natureCustName;

    @ColumnWidth(12)
    @ExcelProperty(value = "执行情况")
    String status;

    @ColumnWidth(15)
    @ExcelProperty(value = "商机金额(万元)")
    String oppoAmount;

    @ColumnWidth(15)
    @ExcelProperty(value = "商机数量")
    String oppoNum;

    @ColumnWidth(20)
    @ExcelProperty(value = "商机编号")
    String oppoNumbers;

    @ColumnWidth(15)
    @ExcelProperty(value = "项目数量")
    String projectNum;

    @ColumnWidth(15)
    @ExcelProperty(value = "项目金额(万元)")
    String projectAmount;

    @ColumnWidth(20)
    @ExcelProperty(value = "项目编号")
    String projectNumbers;

    @ColumnWidth(20)
    @ExcelProperty(value = "派发时间")
    String dispatchTime;

    @ColumnWidth(15)
    @ExcelProperty(value = "转化情况")
    String isTransform;

    @ColumnWidth(15)
    @ExcelProperty(value = "新发展业务收入(元)")
    String newDevelopBusinessAmount;

//    @ColumnWidth(20)
//    @ExcelProperty(value = "新业务号码")
//    String deviceNumbers;

    @ColumnWidth(20)
    @ExcelProperty(value = "细化客户/项目")
    String detailCustName;

    @ColumnWidth(30)
    @ExcelProperty(value = "推送商机内容")
    String pushContent;

    @ColumnWidth(40)
    @ExcelProperty(value = "线索名称/标讯名称/策略名称")
    String marketingName;


    public String getStatus() {
        if(StringUtils.isEmpty(status)){
            return "-";
        }
        if ("1".equals(dataSource)) {
            // 线索状态值赋值
            switch (status) {
                case "90":
                    return "区县营销任务处理中";
                case "100":
                    return "客户经理处理中";
                case "110":
                    return "待处理";
                case "120":
                    return "持续跟踪";
                case "130":
                    return "转商机";
                case "140":
                    return "关联已有商机";
                case "150":
                    return "拜访关闭";
                case "160":
                    return "专家评审";
                case "170":
                    return "支撑经理处理";
                case "180":
                    return "线索关闭(集团审核)";
                case "190":
                    return "线索关闭(省分审核)";
                case "200":
                    return "推荐(到集团)";
                case "210":
                    return "推荐(到省分)";
                case "220":
                    return "客户经理工单关闭";
                case "230":
                    return "中标/签约";
                case "240":
                    return "线索关闭(地市审核)";
                case "250":
                    return "中标";
                case "260":
                    return "签约";
                case "330":
                    return "放弃";
                case "340":
                    return "丢标";
                case "350":
                    return "客户经理线索单关闭";
                default:
                    return status;  // 如果没有匹配的状态值，返回原状态
            }
        } else if ("2".equals(dataSource)) {
            // 标讯任务单状态值赋值
            switch (status) {
                case "501":
                    return "未执行";
                case "502":
                    return "关联商机审批";
                case "503":
                    return "关联商机";
                case "504":
                    return "弃标审批";
                case "505":
                    return "弃标";
                case "506":
                    return "已撤销";
                case "507":
                    return "关联商机-转录新商机";
                case "508":
                    return "客户经理废弃";
                case "509":
                    return "上级管理员审批";
                default:
                    return status;
            }

        }else if ("3".equals(dataSource)) {
            // 靶向营销状态值赋值
            // 靶向的，2已关单，4跟进中，改为已执行
            switch (status) {
                case "1":
                    return "待执行";
                case "2":
                    return "已执行";
                case "3":
                    return "待改派";
                case "4":
                    return "已执行";
                case "5":
                    return "到期未执行";
                default:
                    return status;
            }
        }else if ("4".equals(dataSource)) {
            // 靶向营销状态值赋值
            // 靶向的，2已关单，4跟进中，改为已执行
            switch (status) {
                case "1":
                    return "待执行";
                case "2":
                    return "已执行";
                case "3":
                    return "待改派";
                case "4":
                    return "已执行";
                case "5":
                    return "到期未执行";
                default:
                    return status;
            }
        }
        return status;
    }

    public String getExecTime() {
        if(StringUtils.isEmpty(execTime)){
            return "-";
        }
        if("null".equals(execTime)){
            return "-";
        }
        return execTime;
    }

    public String getIsTransform() {
        if(StringUtils.isEmpty(isTransform)){
            return "-";
        }
        if("1".equals(isTransform)){
            return "已转化";
        }else {
            return "未转化";
        }
    }



    /**
     * 根据数据源标识符返回对应的中文描述。
     * @return
     */
    public String getDataSource() {
        if (StringUtils.isNotEmpty(dataSource)) {
            if(dataSource.contains("合计")){
                return "合计";
            }
            switch (dataSource) {
                case "1":
                    return "线索";
                case "2":
                    return "标讯";
                case "3":
                    return "靶向-潜在商机";
                case "4":
                    return "靶向-存量价值提升";
                default:
                    return dataSource;
            }
        }
        return "未知"; // 如果输入为空也返回未知
    }
}
