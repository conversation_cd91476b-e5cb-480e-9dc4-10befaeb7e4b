package cn.chinaunicom.sdsi.dingding.mapper;

import cn.chinaunicom.sdsi.activityReport.dto.ActivityReportConfigDto;
import cn.chinaunicom.sdsi.dingding.entity.DayProductYXMarketingDS;
import cn.chinaunicom.sdsi.dingding.entity.DayProductYXMarketingManagerInfo;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXDetailVO;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingManagerInfoQueryVo;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingManagerInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Mapper
@Component
public interface DayProductYXMarketingManagerInfoMapper extends BaseMapper<DayProductYXMarketingDS> {

    IPage<Map<String, String>> findPageManager(@Param("page") IPage page, @Param("query") DayProductYXMarketingManagerInfoQueryVo chengXiaoDetail);

    IPage<Map<String, String>> findPageCity(@Param("page") IPage page, @Param("query") DayProductYXMarketingManagerInfoQueryVo chengXiaoDetail);

    /**
     * 钉钉日报，客户经理统计报表
     * @param chengXiaoDetail
     * @return
     */
    List<DayProductYXMarketingManagerInfoVo> findManagerTotalListDingDing(@Param("query") DayProductYXMarketingManagerInfoQueryVo chengXiaoDetail);

    List<DayProductYXMarketingManagerInfoVo> findManagerList(@Param("query") DayProductYXMarketingManagerInfoQueryVo chengXiaoDetail);

    List<DayProductYXMarketingManagerInfoVo> findCityList(@Param("query") DayProductYXMarketingManagerInfoQueryVo chengXiaoDetail);

    String getMaxDate();
    /**
     * 营销经理下拉列表
     * @return
     */
    List<DayProductYXMarketingManagerInfoVo> findManagerInfoList();

    /**
     * 查看策略下发工单个数
     * @return
     */
    List<ActivityReportConfigDto> selectOrderTotal();

    /**
     * 查询上周统计数据-营销经理
     * @return
     */
    List<DayProductYXMarketingManagerInfo> getLastWeekDataByManager(@Param("query") DayProductYXDetailVO detailVO);
    /**
     * 查询上周统计数据-地市
     * @return
     */
    List<DayProductYXMarketingDS> getLastWeekDataByCity(@Param("query") DayProductYXDetailVO detailVO);

    /**
     * 按策略进行日报统计
     */
    List<DayProductYXMarketingManagerInfoVo> getMarketingStatics(@Param("query") DayProductYXDetailVO detailVO);

}
