package cn.chinaunicom.sdsi.customer.service;



import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerMajor;
import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerMajor;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerMajorQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerMajorVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 重大事件-客户资料
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */
public interface TSanquanCustomerMajorService extends IService<TSanquanCustomerMajor> {
    // 分页查询
    IPage<TSanquanCustomerMajor> findPage(TSanquanCustomerMajorQuery tSanquanCustomerMajorQuery);

    // 根据id查询
    TSanquanCustomerMajor findOne(String id);

    // 查询列表
    List<TSanquanCustomerMajor> findList(TSanquanCustomerMajorQuery query);

    // 新增
    int add(TSanquanCustomerMajor tSanquanCustomerMajor);

    // 修改
    int update(TSanquanCustomerMajor tSanquanCustomerMajor);

    // 删除
    int delete(String id);

    //根据客户id查询
    List<TSanquanCustomerMajorVO> getByCustomerIdList(String id);
}