package cn.chinaunicom.sdsi.customerGroup.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 客户群标签对象 t_sanquan_customer_group_tag
 * @Author: hanruxiao
 * @Date: 2024-05-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "客户群标签对象", description = "客户群标签对象")
@TableName("t_sanquan_customer_group_tag")
public class TSanquanCustomerGroupTag implements Serializable {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "客户群id")
    private String customerGroupId;

    @Schema(name = "标签id")
    private String tagId;

    @Schema(name = "标签名称")
    private String tagName;

    @Schema(name = "标签值")
    private String tagValues;

    @Schema(name = "标签sql")
    private String tagSql;

    @Schema(name = "标签json")
    private String tagJson;

    @Schema(name = "创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;

    @Schema(name = "创建人")
    private String createBy;

    @Schema(name = "如果是字典类型,存字典值,逗号连接")
    @TableField(exist = false)
    private List<String> tagDictListId;

    @Schema(name = "第一个值")
    private String value1;

    @Schema(name = "第二个值")
    private String value2;

    @Schema(name = "第三个值")
    private String value3;

    @Schema(name = "第四个值")
    private String value4;


}
