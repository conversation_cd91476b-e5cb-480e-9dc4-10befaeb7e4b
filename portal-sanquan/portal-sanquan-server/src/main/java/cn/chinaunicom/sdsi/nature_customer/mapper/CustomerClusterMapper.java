package cn.chinaunicom.sdsi.nature_customer.mapper;

import cn.chinaunicom.sdsi.cloud.nature_customer.entity.CustomerCluster;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.ClusterPageQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.ClusterPageVO;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanUseScene;
import cn.chinaunicom.sdsi.cloud.product.query.TSanquanUseSceneQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: 王永凯
 * @Date: 2024/11/6 下午2:48
 */
@Mapper
public interface CustomerClusterMapper extends BaseMapper<CustomerCluster> {

    void insertBatchSomeColumn(@Param("list") List<CustomerCluster> list);

    List<CustomerCluster> findClusterByCustomerIdAndType(@Param("customerId") String customerId, @Param("customerType") String customerType);

    IPage<ClusterPageVO> findPage(@Param("page") IPage page, @Param("query") ClusterPageQuery query);

    IPage<ClusterPageVO> findAndCounterPage(@Param("page") IPage page, @Param("query") ClusterPageQuery query);
}
