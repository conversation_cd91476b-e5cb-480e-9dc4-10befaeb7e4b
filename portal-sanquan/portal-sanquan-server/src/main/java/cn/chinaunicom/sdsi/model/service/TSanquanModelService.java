package cn.chinaunicom.sdsi.model.service;

import cn.chinaunicom.sdsi.model.entity.TSanquanModel;
import cn.chinaunicom.sdsi.model.vo.ModelNode;
import cn.chinaunicom.sdsi.model.vo.TSanquanModelScenceVO;
import cn.chinaunicom.sdsi.model.vo.TSanquanModelVO;
import cn.chinaunicom.sdsi.model.queryvo.TSanquanModelQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 模型表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface TSanquanModelService extends IService<TSanquanModel> {

    /**
     * 分页查询模型表
     * 
     * @param tSanquanModelQueryVO
     * @return IPage<TSanquanModelVO>
     */
    IPage<TSanquanModelVO> findPage(TSanquanModelQueryVO tSanquanModelQueryVO);

    /**
     * 查询模型表详细信息
     *
     * @param id
     * @return TSanquanModelVO
     */
    TSanquanModelVO findInfo(String id);

    /**
     * 新增模型表
     *
     * @param tSanquanModelVO
     * @return String
     */
    String add(TSanquanModelVO tSanquanModelVO);

    /**
     * 修改模型表
     *
     * @param tSanquanModelVO
     * @return Boolean
     */
    Boolean update(TSanquanModelVO tSanquanModelVO);

    /**
     * 删除模型表
     *
     * @param id
     * @return Boolean
     */
    Boolean delete(String id);

    Integer checkModelName(String id, String name);

    List<TSanquanModelVO> findModelList(TSanquanModelQueryVO tSanquanModelQueryVO);

    List<TSanquanModelScenceVO> getSceneList(TSanquanModelQueryVO tSanquanModelQueryVO);

    List<ModelNode> findModelTree(TSanquanModelQueryVO tSanquanModelQueryVO);

    // 查询模型数量
    int getModelNum(TSanquanModelQueryVO tSanquanModelQueryVO);
}
