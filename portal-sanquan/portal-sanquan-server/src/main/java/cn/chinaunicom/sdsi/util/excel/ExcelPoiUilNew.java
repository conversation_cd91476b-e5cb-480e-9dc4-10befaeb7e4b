package cn.chinaunicom.sdsi.util.excel;

import cn.chinaunicom.sdsi.common.entity.PoiExcelEntity;
import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * poi组件渲染excel，工具类
 */
public class ExcelPoiUilNew {

    /**
     * 支持任意sheet页，创建任意个表格，及合并单元格
     * @param excelEntity
     * @return
     */
    public Sheet factoryPoiExcel(Workbook workbook,PoiExcelEntity excelEntity){
        int sheetNo = workbook.getNumberOfSheets();
        Sheet sheet = null;
        if(sheetNo > excelEntity.getSheetIndex()){
            sheet = workbook.getSheetAt(excelEntity.getSheetIndex());
        }else{
            sheet = workbook.createSheet(excelEntity.getSheetName());
        }
        this.fillSheetWithMergedHeader(sheet,excelEntity);

        return sheet;
    }

    public void fillSheetWithMergedHeader(Sheet sheet,PoiExcelEntity excelEntity) {

        // 创建样式
        CellStyle headerStyle = excelEntity.getHeaderStyle();
        if(headerStyle == null){
            createHeaderStyle(sheet.getWorkbook());
        }
        CellStyle dataStyle = excelEntity.getExcelStyle();
        if(dataStyle == null){
            dataStyle = createDataStyle(sheet.getWorkbook());
        }

        List<Row> headRowList = Lists.newArrayList();
        List<List<String>> headList = excelEntity.getHeadData();
        if(headList != null){
            for (int i=0;i<headList.size(); i++) {
                Row headerRow = sheet.createRow(excelEntity.getStartRow()+i);
                headerRow.setHeightInPoints(excelEntity.getHeadHeight());
//            headerRow.setHeight((short)(30 * 20)); // 等同于30点
                headRowList.add(headerRow);
            }
        }

        if(excelEntity.getMergeCellPos().size() > 0){
            excelEntity.getMergeCellPos().forEach(item->{
                sheet.addMergedRegion(new CellRangeAddress(excelEntity.getStartRow()+item[0], excelEntity.getStartRow()+item[1], excelEntity.getStartCol()+item[2], excelEntity.getStartCol()+item[3]));
            });
        }
        // 填充主标题行
        for(int i=0;i<headRowList.size();i++){
            Row row = headRowList.get(i);
            List<String> headListData = headList.get(i);
            for(int j=0;j<headListData.size();j++){
                Cell mainHeaderCell1 = row.createCell(j);
                mainHeaderCell1.setCellValue(headListData.get(j));
                if("2".equals(excelEntity.getType()) || "4".equals(excelEntity.getType())){
                    mainHeaderCell1.setCellStyle(headerStyle);
                }

            }
        }

        // 填充数据
        List<List<Object>> dataList = excelEntity.getData();
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(excelEntity.getStartRow()+headRowList.size()+i);
            row.setHeightInPoints(excelEntity.getRowHeight());
            for (int j = 0; j < dataList.get(i).size(); j++) {
                Cell cell = row.createCell(j);
                cell.setCellValue(String.valueOf(dataList.get(i).get(j)));
                cell.setCellStyle(dataStyle);
            }
        }
        // 自动调整列宽
        for (int i = 0; i < dataList.get(0).size(); i++) {
//            sheet.autoSizeColumn(i);
        }

        sheet.setDefaultColumnWidth(excelEntity.getColWidth());// 设置默认列宽

    }
    /**
     * 将实体类列表数据转换成poi需要的 List列表数据格式。
     * @param dataList
     * @return
     */
    public List<List<Object>> convertToExcelData(List<?> dataList) {
        List<List<Object>> result = new ArrayList<>();

        if (dataList == null || dataList.isEmpty()) {
            return result;
        }
        // 获取有序字段列表
        List<Field> orderedFields = getOrderedFields(dataList.get(0).getClass());

        // 添加数据行
        for (Object item : dataList) {
            result.add(getDataRow(item, orderedFields));
        }
        return result;
    }

    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        // 设置垂直居中（上下居中）
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }


    public static CellStyle createSubHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }




    /**
     * 获取按Java声明顺序排序的字段列表
     */
    private static List<Field> getOrderedFields(Class<?> clazz) {
        // 获取所有声明字段（包括私有字段）
        Field[] fields = clazz.getDeclaredFields();

        // 按字段在类中的声明顺序排序
        return Arrays.stream(fields)
                .filter(field -> !field.isSynthetic()) // 过滤编译器生成的字段
                .collect(Collectors.toList());
    }

    /**
     * 生成数据行
     */
    private static List<Object> getDataRow(Object obj, List<Field> fields) {
        List<Object> row = new ArrayList<>();

        for (Field field : fields) {
            try {
                field.setAccessible(true); // 允许访问私有字段
                Object value = field.get(obj);
                row.add(value != null ? value : ""); // 空值转为空字符串
            } catch (IllegalAccessException e) {
                row.add("");
                // 实际项目中建议使用日志记录异常
                e.printStackTrace();
            }
        }

        return row;
    }

}
