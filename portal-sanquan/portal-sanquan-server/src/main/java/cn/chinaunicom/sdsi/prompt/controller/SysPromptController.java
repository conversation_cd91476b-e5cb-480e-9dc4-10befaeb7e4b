package cn.chinaunicom.sdsi.prompt.controller;

import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.prompt.dto.PromptDTO;
import cn.chinaunicom.sdsi.prompt.entity.SysPrompt;
import cn.chinaunicom.sdsi.prompt.entity.SysPromptTemplate;
import cn.chinaunicom.sdsi.prompt.service.ISysPromptService;
import cn.hutool.core.util.RandomUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 提示词管理
 */
@RestController
@RequestMapping("/prompt")
public class SysPromptController  extends BaseController  {
    @Autowired
    private ISysPromptService promptService;
    /**
     * 获取提示词列表
     */
    @GetMapping("/list")
    public BasePageResponse<SysPrompt> list(PromptDTO param) {
        return pageOk(promptService.selectPromptList(param));
    }

    /**
     * 获取提示词详情
     */
    @GetMapping("/query/{id}")
    public BaseResponse<SysPrompt> getInfo(@PathVariable("id") Long id) {
        return ok(promptService.getPromptById(id));
    }

    /**
     * 新增提示词
     */
    @ResponseBody
    @PostMapping("/add")
    public BaseResponse add(@RequestBody PromptDTO promptDTO) {
        SysPrompt prompt = new SysPrompt();
        BeanUtils.copyProperties(promptDTO, prompt);
        if (promptDTO.getTemplates() != null) {
            List<SysPromptTemplate> templates = promptDTO.getTemplates().stream().map(templateDTO -> {
                    SysPromptTemplate template = new SysPromptTemplate();
                    template.setTemplateName(templateDTO.getName());
                    template.setTemplateContent(templateDTO.getContent());
                    template.setTemplateOrder(templateDTO.getOrder());
                    return template;
            }).collect(Collectors.toList());
            prompt.setTemplates(templates);
        }
        promptService.insertPrompt(prompt);
        return ok("操作成功");
    }

    /**
     * 修改提示词
     */
    @ResponseBody
    @PostMapping("/edit")
    public BaseResponse edit(@RequestBody PromptDTO promptDTO) {
        SysPrompt prompt = new SysPrompt();
        BeanUtils.copyProperties(promptDTO, prompt);
        if (promptDTO.getTemplates() != null) {
            List<SysPromptTemplate> templates = promptDTO.getTemplates().stream().map(templateDTO -> {
                    SysPromptTemplate template = new SysPromptTemplate();
                    template.setId(templateDTO.getId());
                    template.setTemplateName(templateDTO.getName());
                    template.setTemplateContent(templateDTO.getContent());
                    template.setTemplateOrder(templateDTO.getOrder());
                    return template;
            }).collect(Collectors.toList());
            prompt.setTemplates(templates);
        }
        promptService.updatePrompt(prompt);
        return ok("操作成功");
    }

    /**
     * 删除提示词
     */
    @GetMapping("/remove/{ids}")
    public BaseResponse remove(@PathVariable Long[] ids) {
        promptService.deletePromptByIds(ids);
        return ok("操作成功");
    }
}
