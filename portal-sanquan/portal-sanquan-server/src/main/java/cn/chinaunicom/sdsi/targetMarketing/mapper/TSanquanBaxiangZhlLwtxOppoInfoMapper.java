package cn.chinaunicom.sdsi.targetMarketing.mapper;

import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanBaxiangZhlLwtxOppoInfo;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanBaxiangZhlLwtxOppoInfoVO;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanBaxiangZhlLwtxOppoInfoQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 靶向营销联网通信拉动业务详情表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-05
 */
@Mapper
public interface TSanquanBaxiangZhlLwtxOppoInfoMapper extends BaseMapper<TSanquanBaxiangZhlLwtxOppoInfo> {

    /**
     * 分页查询靶向营销联网通信拉动业务详情表
     * 
     * @param page,tSanquanBaxiangZhlLwtxOppoInfoQueryVO
     * @return 靶向营销联网通信拉动业务详情表
     */
    IPage<TSanquanBaxiangZhlLwtxOppoInfoVO> findPage(@Param("page") IPage page, @Param("query") TSanquanBaxiangZhlLwtxOppoInfoQueryVO tSanquanBaxiangZhlLwtxOppoInfoQueryVO);

}
