package cn.chinaunicom.sdsi.dingtalkAbility.controller;

import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkContactService;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkContactQueryVo;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkContactVo;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/dingding/contact")
public class DingTalkContactController extends BaseController {

    @Autowired
    private DingTalkContactService contactService;


    /**
     * 查询人员列表
     * @param vo
     * @return
     */
    @GetMapping("/findPage")
    public BasePageResponse<DingTalkContactVo> findPage(DingTalkContactQueryVo vo) {
        return pageOk(contactService.findPage(vo));
    }


    /**
     * 更新人员信息
     * @param vo
     * @return
     */
    @PostMapping("/updateDingTalkContact")
    public BaseResponse<Boolean> updateDingTalkContact(@RequestBody DingTalkContactVo vo) {
        return ok(contactService.updateDingTalkContact(vo));
    }

    /**
     * 新增人员
     * @param vo
     * @return
     */
    @PostMapping("/insertDingTalkContact")
    public BaseResponse<Boolean> insertDingTalkContact(@RequestBody DingTalkContactVo vo) {
        if (StrUtil.isBlank(vo.getName())
                || StrUtil.isBlank(vo.getMobile())
                || StrUtil.isBlank(vo.getBusinessCategory())) {
            return notOk();
        }
        return ok(contactService.insertDingTalkContact(vo));
    }

    /**
     * 删除人员
     * @param id
     * @return
     */
    @PostMapping("/deleteDingTalkContact")
    public BaseResponse<Boolean> deleteDingTalkContact(String id) {
        return ok(contactService.deleteDingTalkContact(id));
    }



    /**
     * 启用/禁用人员
     * @param vo
     * @return
     */
    @PutMapping("/updateStatus")
    public BaseResponse<Boolean> updateStatus(@RequestBody DingTalkContactVo vo) {
        if (vo.getId() == null || vo.getStatus() == null) {
            return notOk();
        }
        return ok(contactService.updateStatus(vo));
    }

}
