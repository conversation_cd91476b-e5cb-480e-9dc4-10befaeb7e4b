package cn.chinaunicom.sdsi.customerGroup.service;

import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyProductQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyProductVO;
import cn.chinaunicom.sdsi.customerGroup.entity.TSanquanCustomerGroup;
import cn.chinaunicom.sdsi.customerGroup.queryvo.CustomerGroupVo;
import cn.chinaunicom.sdsi.customerGroup.queryvo.TSanquanCustomerGroupTagQueryVO;
import cn.chinaunicom.sdsi.customerGroup.vo.TSanquanCustomerGroupCustomerVO;
import cn.chinaunicom.sdsi.customerGroup.vo.TSanquanCustomerGroupVO;
import cn.chinaunicom.sdsi.customerGroup.queryvo.TSanquanCustomerGroupQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import cn.chinaunicom.sdsi.customerGroup.excel.CustomerGroupCustomerData;
import java.util.List;

/**
 * 客户群 服务层接口
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
public interface TSanquanCustomerGroupService extends IService<TSanquanCustomerGroup> {

    /**
     * 分页查询客户群
     * 
     * @param tSanquanCustomerGroupQueryVO
     * @return IPage<TSanquanCustomerGroupVO>
     */
    IPage<TSanquanCustomerGroupVO> findPage(TSanquanCustomerGroupQueryVO tSanquanCustomerGroupQueryVO);

    /**
     * 查询客户群详细信息
     *
     * @param id
     * @return TSanquanCustomerGroupVO
     */
    TSanquanCustomerGroupVO findInfo(String id);

    /**
     * 新增客户群
     *
     * @param tSanquanCustomerGroupVO
     * @return String
     */
    String add(TSanquanCustomerGroupVO tSanquanCustomerGroupVO);

    /**
     * 修改客户群
     *
     * @param tSanquanCustomerGroupVO
     * @return Boolean
     */
    Boolean update(TSanquanCustomerGroupVO tSanquanCustomerGroupVO);

    Boolean updateBaseInfo(TSanquanCustomerGroupVO tSanquanCustomerGroupVO);

    /**
     * 删除客户群
     *
     * @param id
     * @return Boolean
     */
    Boolean delete(String id);

    /**
     * 批量导入客户群所属的客户信息【excel导入】
     *
     * @param list
     * @return String
     */
    Integer  addBatchCustomerGroupCustomerList(List<CustomerGroupCustomerData> list,TSanquanCustomerGroup customerGroup);

    List<TSanquanCustomerGroupCustomerVO> freshGroupCustomerList(String groupSql);

    List<TSanquanCustomerGroupCustomerVO> freshGroupCustomerListByRule(List<TSanquanCustomerGroupTagQueryVO> groupTagList);

    // 刷新，查询客户群
    List<TSanquanCustomerGroupCustomerVO> freshCustomerGroupListByRule(List<TSanquanCustomerGroupTagQueryVO> groupTagList);

    // 刷新，查询客户群(分页)
    IPage<TSanquanCustomerGroupCustomerVO> freshCustomerGroupListByRule(CustomerGroupVo query);

    // 查询客户群列表
    List<TSanquanCustomerGroupVO> findList(TSanquanCustomerGroupQueryVO tSanquanCustomerGroupQueryVO);

    long freshCustomerGroupListBySql(TSanquanCustomerGroupVO groupVO);
}
