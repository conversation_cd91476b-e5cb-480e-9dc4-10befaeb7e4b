package cn.chinaunicom.sdsi.dbcp.service;


import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanRemindTask;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanRemindTaskQueryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
/**
 * <p>
 * 提醒任务表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface TSanquanRemindTaskService extends IService<TSanquanRemindTask> {

    int cancelTask(TSanquanRemindTask tSanquanRemindTaskQueryVo);

    int updateTask(TSanquanRemindTaskQueryVo tSanquanRemindTaskQueryVo);

    // 分页查询
    IPage<TSanquanRemindTask> findPage(TSanquanRemindTaskQueryVo tSanquanRemindTaskVO);

    // 根据id查询
    TSanquanRemindTask findOne(String id);

    // 查询列表
    List<TSanquanRemindTask> findList(TSanquanRemindTaskQueryVo tSanquanRemindTaskQueryVo);

    // 新增
    int add(TSanquanRemindTask tSanquanRemindTask);

    // 修改
    int update(TSanquanRemindTask tSanquanRemindTask);

    // 删除
    int delete(String id);

    // 发送短信
    void sendBatchSms(String test);
}
