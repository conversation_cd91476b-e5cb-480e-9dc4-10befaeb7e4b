package cn.chinaunicom.sdsi.dingtalkAbility.response;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class DingTalkBaseResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String code;
    private Boolean success;
    private String status;
    private String message;
    private T data;
}