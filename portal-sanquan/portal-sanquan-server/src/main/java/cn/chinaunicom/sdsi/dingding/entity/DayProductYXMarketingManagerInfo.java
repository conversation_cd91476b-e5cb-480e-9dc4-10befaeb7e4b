package cn.chinaunicom.sdsi.dingding.entity;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 产品营销-客户经理
 */
@Data
@TableName("t_sanquan_dm_d_cpyx_marketing_manager_info")
public class DayProductYXMarketingManagerInfo {

    private String dayId;          // 日期ID
    private String rhIndex;        // RH指标
    private String marketingManagerOa;
    private String marketingManagerName; // 营销经理

    private String targetGdCn; // 目标工单数
    private String finishGdCn; // 完成工单数
    private String wcl;        // 完成率
    private String oppoCn;     // 商机数
    private String oppoFee;    // 商机规模
    private String sendTime;    // 派单日期
    private String strategyId;     // 策略ID
    private String strategyName;   // 策略名称

    public String getOppoFee() {
        if(StringUtils.isEmpty(oppoFee) || "null".equals(oppoFee)){
            return "0.00";
        }
        return new BigDecimal(oppoFee).setScale(2).toPlainString();
    }

    public String getTargetGdCn() {
        if(StringUtils.isEmpty(targetGdCn) || "null".equals(targetGdCn)){
            return "0";
        }
        return targetGdCn;
    }

    public String getFinishGdCn() {
        if(StringUtils.isEmpty(finishGdCn) || "null".equals(finishGdCn)){
            return "0";
        }
        return finishGdCn;
    }

    public String getOppoCn() {
        if(StringUtils.isEmpty(oppoCn) || "null".equals(oppoCn)){
            return "0";
        }
        return oppoCn;
    }

    public String getMarketingManagerName() {
        if(StringUtils.isNotEmpty(marketingManagerName) && "总计".equals(marketingManagerName)){
            return "合计";
        }
        if("孙旋".equals(marketingManagerName)){
            return "巩卓";
        }
        return marketingManagerName;
    }

}
