package cn.chinaunicom.sdsi.dimProvCityCode.controller;


import cn.chinaunicom.sdsi.cloud.dimProvCityCode.entity.TSanquanDimProvCityCode;
import cn.chinaunicom.sdsi.dimProvCityCode.service.TSanquanDimProvCityCodeService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.stereotype.Controller;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@RestController
@RequestMapping("/dimProvCityCode")
public class TSanquanDimProvCityCodeContoller extends BaseController{

    @Autowired
    private TSanquanDimProvCityCodeService tSanquanDimProvCityCodeService;

    /**
     * 查询全部
     * @param query
     * @return
     */
    @Operation(summary = "查询列表", description = "查询列表")
    @GetMapping("/findList")
    public BaseResponse<List<TSanquanDimProvCityCode>> findList(TSanquanDimProvCityCode query) {
        return ok(tSanquanDimProvCityCodeService.findList(query));
    }

    /**
     * 根据省查询所有地市
     * @param query
     * @return
     */
    @Operation(summary = "根据省名称查询地市信息", description = "根据省名称查询地市信息")
    @GetMapping("/findListByProvName")
    public BaseResponse<List<Map<String,String>>> findListByProvName(TSanquanDimProvCityCode query) {
        return ok(tSanquanDimProvCityCodeService.findListByProvName(query));
    }

    /**
     * 根据地市查询所有区域
     * @param query
     * @return
     */
    @Operation(summary = "根据地市查询所有区域", description = "根据地市查询所有区域")
    @GetMapping("/findListByCityName")
    public BaseResponse<List<Map<String,String>>> findListByCityName(TSanquanDimProvCityCode query) {
        return ok(tSanquanDimProvCityCodeService.findListByCityName(query));
    }

}
