package cn.chinaunicom.sdsi.customerPicture.mapper;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileBaseinfo;
import cn.chinaunicom.sdsi.customerPicture.entity.ProfileEvaluateRefineRulesVo;
import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 三全客户画像-客户基本信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Mapper
public interface CustomerProfileBaseinfoMapper extends BaseMapper<CustomerProfileBaseinfo> {
    //客户基本信息
    CustomerProfileBaseinfo getBaseInfo(CustomerPictureVo pictureVo);

    List<ProfileEvaluateRefineRulesVo> getIndicatorScoreRules(@Param("modelId") String modelId);

}
