package cn.chinaunicom.sdsi.aiflow.dto;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.*;

/**
 * 客户洞察查询DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerInsightDTO extends BaseQueryVO  {
    /**
     * 主键Id
     */
    private String id;
    /**
     * 客户洞察编码
     */
    private String insightId;
    /**
     * 客户洞察名称，用于模糊查询
     */
    private String insightName;
    /**
     * 客户数量
     */
    private Integer customerCount;
    /**
     * 客户洞察标签
     */
    private String customerTags;


}
