package cn.chinaunicom.sdsi.strategy.controller;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrategyInterview;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrategyInterviewQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrategyInterviewVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.strategy.service.TSanquanStrategyInterviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 摸排信息表
*
* <AUTHOR> 
* @since  2024-04-25
*/
@RestController
@RequestMapping("/strategy/Interview")
@Tag(name="摸排信息表")
@AllArgsConstructor
public class TSanquanStrategyInterviewController extends BaseController {

    @Autowired
    private TSanquanStrategyInterviewService tSanquanStrategyInterviewService;

    /**
     * 分页查询摸排信息
     * @param query
     * @return
     */
    @GetMapping("/findPage")
    @Operation(summary = "分页")
    public BasePageResponse<TSanquanStrategyInterviewVO> findPage(TSanquanStrategyInterviewQuery query){
        return pageOk(tSanquanStrategyInterviewService.findPage(query));
    }

    /**
     * 获取详情信息
     * @param id
     * @return
     */
    @GetMapping("/findInfo/{id}")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanStrategyInterview> findById(@PathVariable("id") String id){
        return ok(tSanquanStrategyInterviewService.findById(id));
    }

    /**
     * 添加摸排信息
     * @param vo
     * @return
     */
    @PostMapping("/addInterview")
    @Operation(summary = "保存")
    public BaseResponse<String> save(@RequestBody TSanquanStrategyInterviewVO vo){
        return ok(tSanquanStrategyInterviewService.add(vo));
    }

    /**
     * 修改
     * @param vo
     * @return
     */
    @PostMapping("/updateInterview")
    @Operation(summary = "修改")
    public BaseResponse<Boolean> update(@RequestBody TSanquanStrategyInterviewVO vo){
        return ok(tSanquanStrategyInterviewService.updateInterview(vo));
    }

    /**
     * 删除
     * @param id
     * @return
     */
    @PostMapping("/deleteInterview/{id}")
    @Operation(summary = "删除")
    public BaseResponse<Boolean> delete(@RequestBody @PathVariable(value = "id") String id){
        return ok(tSanquanStrategyInterviewService.removeById(id));
    }

    /**
     * 获取详情信息(根据潜在机会查询)
     * @param id
     * @return
     */
    @GetMapping("/findInterviewInfo/{opportunityId}")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanStrategyInterviewVO> getInterview(@PathVariable("opportunityId") String id){
        return ok(tSanquanStrategyInterviewService.getInterview(id));
    }

    /**
     * 获取详情信息(根据todoCode查询)
     * @param todoOppoId
     * @return
     */
    @GetMapping("/findInterviewByTodoOppoId/{todoOppoId}")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanStrategyInterviewVO> getInterviewByTodoOppoId(@PathVariable("todoOppoId") String todoOppoId){
        return ok(tSanquanStrategyInterviewService.getInterviewByTodoOppoId(todoOppoId));
    }
}