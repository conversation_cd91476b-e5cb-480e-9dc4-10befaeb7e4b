package cn.chinaunicom.sdsi.customerVisit.service;

import cn.chinaunicom.sdsi.customerVisit.entity.TSanquanCustomerVisit;
import cn.chinaunicom.sdsi.customerVisit.vo.TSanquanCustomerVisitQuery;
import cn.chinaunicom.sdsi.customerVisit.vo.TSanquanCustomerVisitVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 客户拜访记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
public interface TSanquanCustomerVisitService extends IService<TSanquanCustomerVisit> {

    // 分页查询
    IPage<TSanquanCustomerVisit> findPage(TSanquanCustomerVisitQuery tSanquanCustomerVisitVO);

    List<TSanquanCustomerVisitVO> getYxFlagList();

    List<TSanquanCustomerVisitVO> getCityList(String dayId);

    List<TSanquanCustomerVisitVO> getUpNewDate();




    // 根据id查询
    TSanquanCustomerVisit findOne(String id);

    // 查询列表
    List<TSanquanCustomerVisit> findList();


}
