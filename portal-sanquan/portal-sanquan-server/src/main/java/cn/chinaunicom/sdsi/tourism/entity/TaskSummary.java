package cn.chinaunicom.sdsi.tourism.entity;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 文旅汇总实体
 */
@Data
public class TaskSummary {

    @ColumnWidth(30)
    @ExcelProperty(value="地市")
    String city;

    @ColumnWidth(30)
    @ExcelProperty(value="工单数")
    String orderNum;

    @ColumnWidth(30)
    @ExcelProperty(value="待执行数量")
    String unzhixingNum;

    @ExcelIgnore
    @ColumnWidth(30)
    @ExcelProperty(value="执行中数量")
    String zhixingNum;

    @ColumnWidth(30)
    @ExcelProperty(value="已关单")
    String endNum;

    @ColumnWidth(30)
    @ExcelProperty(value="执行率")
    String zhixingRate;

    @ColumnWidth(30)
    @ExcelProperty(value="匹配商机数量")
    String oppoNum;

    @ColumnWidth(30)
    @ExcelProperty(value="匹配商机金额")
    String oppoPrice;

    @ColumnWidth(30)
    @ExcelProperty(value="转化率")
    String transferRate;

    @ColumnWidth(30)
    @ExcelProperty(value="项目数量")
    String projectNum;

    @ColumnWidth(30)
    @ExcelProperty(value="项目金额")
    String projectPrice;

    /* 类型，1，区域，2行业 */
    @ExcelIgnore
    String taskType;

    /* 行业名称 */
    @ExcelIgnore
    String industry;

    @ExcelIgnore
    List<String> industrys;

    @ExcelIgnore
    List<String> citys;

    public String getOrderNum() {
        if(StringUtils.isNotEmpty(orderNum) && !orderNum.equals("-")){
            Double ddd = Double.parseDouble(orderNum);
            orderNum = String.valueOf(ddd.intValue());
        }
        return orderNum;
    }

    public String getUnzhixingNum() {
        if(StringUtils.isNotEmpty(unzhixingNum) && !unzhixingNum.equals("-")){
            Double ddd = Double.parseDouble(unzhixingNum);
            unzhixingNum = String.valueOf(ddd.intValue());
        }
        return unzhixingNum;
    }

    public String getZhixingNum() {
        if(StringUtils.isNotEmpty(zhixingNum) && !zhixingNum.equals("-")){
            Double ddd = Double.parseDouble(zhixingNum);
            zhixingNum = String.valueOf(ddd.intValue());
        }
        return zhixingNum;
    }

    public String getEndNum() {
        if(StringUtils.isNotEmpty(endNum) && !endNum.equals("-")){
            Double ddd = Double.parseDouble(endNum);
            endNum = String.valueOf(ddd.intValue());
        }
        return endNum;
    }

    public String getOppoNum() {
        if(StringUtils.isNotEmpty(oppoNum) && !oppoNum.equals("-")){
            Double ddd = Double.parseDouble(oppoNum);
            oppoNum = String.valueOf(ddd.intValue());
        }
        return oppoNum;
    }

    public String getProjectNum() {
        if(StringUtils.isNotEmpty(projectNum) && !projectNum.equals("-")){
            Double ddd = Double.parseDouble(projectNum);
            projectNum = String.valueOf(ddd.intValue());
        }
        return projectNum;
    }


    public String getZhixingRate() {
        if(StringUtils.isNotEmpty(zhixingRate)){
            BigDecimal bigDecimal = new BigDecimal(zhixingRate).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP);
            return bigDecimal.toPlainString()+"%";
        }
        return zhixingRate;
    }

    public void setZhixingRate(String zhixingRate) {
        this.zhixingRate = zhixingRate;
    }

    public String getTransferRate() {
        if(StringUtils.isNotEmpty(transferRate)){
            BigDecimal bigDecimal = new BigDecimal(transferRate).multiply(new BigDecimal("100")).setScale(2,RoundingMode.HALF_UP);
            return bigDecimal.toPlainString()+"%";
        }
        return transferRate;
    }

    public void setTransferRate(String transferRate) {
        this.transferRate = transferRate;
    }

    public String getOppoPrice() {
        if(StringUtils.isNotEmpty(oppoPrice)){
            BigDecimal bigDecimal = new BigDecimal(oppoPrice).setScale(2,RoundingMode.HALF_UP);
            return bigDecimal.toPlainString();
        }
        return oppoPrice;
    }

    public void setOppoPrice(String oppoPrice) {
        this.oppoPrice = oppoPrice;
    }

    public String getProjectPrice() {
        if(StringUtils.isNotEmpty(projectPrice)){
            BigDecimal bigDecimal = new BigDecimal(projectPrice).setScale(2,RoundingMode.HALF_UP);
            return bigDecimal.toPlainString();
        }
        return projectPrice;
    }

    public void setProjectPrice(String projectPrice) {
        this.projectPrice = projectPrice;
    }
}
