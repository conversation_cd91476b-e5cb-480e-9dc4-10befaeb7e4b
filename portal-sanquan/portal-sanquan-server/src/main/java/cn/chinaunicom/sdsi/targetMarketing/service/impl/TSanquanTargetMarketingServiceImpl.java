package cn.chinaunicom.sdsi.targetMarketing.service.impl;

import cn.chinaunicom.sdsi.cityTodo.entity.StrategyResultTodo;
import cn.chinaunicom.sdsi.cityTodo.entity.StrategyResultTodoOppotunity;
import cn.chinaunicom.sdsi.cityTodo.service.IStrategyResultTodoOppotunityService;
import cn.chinaunicom.sdsi.cityTodo.service.IStrategyResultTodoService;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanDZqztJihezxProvinceMappingQueryVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.dimProvCityCode.entity.TSanquanDimProvCityCode;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.NatureCoustomerQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.CustomerViewVO;
import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyConfiguration;
import cn.chinaunicom.sdsi.cloud.workFeedbackLog.entity.TSanquanWorkFeedbackLog;
import cn.chinaunicom.sdsi.common.service.YkService;
import cn.chinaunicom.sdsi.common.sms.SendSmsUtils;
import cn.chinaunicom.sdsi.customer.dao.TSanquanDZqztJihezxProvinceMappingMapper;
import cn.chinaunicom.sdsi.dimProvCityCode.dao.TSanquanDimProvCityCodeMapper;
import cn.chinaunicom.sdsi.framework.utils.*;
import cn.chinaunicom.sdsi.marketing.entity.TSanquanMarketingManager;
import cn.chinaunicom.sdsi.marketing.service.TSanquanMarketingManagerService;
import cn.chinaunicom.sdsi.nature_customer.mapper.NatureCoustomerMapper;
import cn.chinaunicom.sdsi.opportunity.entity.TSanquanPotentialOpportunity;
import cn.chinaunicom.sdsi.opportunity.mapper.TSanquanPotentialOpportunityMapper;
import cn.chinaunicom.sdsi.opportunity.queryvo.TSanquanPotentialOpportunityQueryVO;
import cn.chinaunicom.sdsi.opportunity.service.TSanquanPotentialOpportunityService;
import cn.chinaunicom.sdsi.opportunity.vo.TSanquanPotentialOpportunityVO;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.chinaunicom.sdsi.strategy.service.TSanquanStrtegyConfigurationService;
import cn.chinaunicom.sdsi.strategy.workFeedbackLog.service.TSanquanWorkFeedbackLogService;
import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanTargetMarketing;
import cn.chinaunicom.sdsi.targetMarketing.service.TSanquanTargetMarketingService;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanTargetMarketingVO;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanTargetMarketingQueryVO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.chinaunicom.sdsi.targetMarketing.mapper.TSanquanTargetMarketingMapper;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 靶向营销审核记录表业务实现类
 * 
 * <AUTHOR>
 * @date 2024-07-26
 */
@Service
@Slf4j
public class TSanquanTargetMarketingServiceImpl extends ServiceImpl<TSanquanTargetMarketingMapper, TSanquanTargetMarketing> implements TSanquanTargetMarketingService {

    @Autowired
    private UnifastContext unifastContext;
    @Resource
    private TSanquanPotentialOpportunityService tSanquanPotentialOpportunityService;
    @Autowired
    private TSanquanPotentialOpportunityMapper tSanquanPotentialOpportunityMapper;
    @Autowired
    private TSanquanDZqztJihezxProvinceMappingMapper sanquanDZqztJihezxProvinceMappingMapper;
    @Autowired
    private NatureCoustomerMapper natureCoustomerMapper;
    @Autowired
    private TSanquanDimProvCityCodeMapper sanquanDimProvCityCodeMapper;

    @Value("${unifast.cloud.interface.returnLink}")
    private String waitSolveUrl;
    @Value("${unifast.cloud.interface.dbLink}")
    private String requestLink;
    @Value("${unifast.cloud.interface.readLink}")
    private String readLink;
    @Value("${unifast.cloud.interface.day}")
    private int dayNum;

    @Autowired
    private IStrategyResultTodoService strategyResultTodoService;

    @Autowired
    private IStrategyResultTodoOppotunityService strategyResultTodoOppotunityService;

    @Autowired
    private TSanquanMarketingManagerService tSanquanMarketingManagerService;

    @Autowired
    private TSanquanWorkFeedbackLogService tSanquanWorkFeedbackLogService;

    @Autowired
    private SendSmsUtils sendSmsUtils;

    @Autowired
    private TSanquanStrtegyConfigurationService tSanquanStrtegyConfigurationService;

    /**
     * 分页查询靶向营销审核记录表
     * 
     * @param tSanquanTargetMarketingQueryVO
     * @return IPage<TSanquanTargetMarketingVO>
     */
    @Override
    public IPage<TSanquanTargetMarketingVO> findPage(TSanquanTargetMarketingQueryVO tSanquanTargetMarketingQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanTargetMarketingQueryVO);
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            tSanquanTargetMarketingQueryVO.setCreateBy(unifastContext.getUser().getStaffId());
        }
        return baseMapper.findPage(page, tSanquanTargetMarketingQueryVO);
    }

    /**
     * 查询靶向营销审核记录表详细信息
     * 
     * @param id
     * @return TSanquanTargetMarketingVO
     */
    @Override
    public TSanquanTargetMarketingVO findInfo(String id) {
        TSanquanTargetMarketing tSanquanTargetMarketing = baseMapper.selectById(id);
        TSanquanTargetMarketingVO tSanquanTargetMarketingVO = new TSanquanTargetMarketingVO();
        BeanUtils.copyProperties(tSanquanTargetMarketing, tSanquanTargetMarketingVO);
        return tSanquanTargetMarketingVO;
    }

    /**
     * 新增靶向营销审核记录表
     * 
     * @param tSanquanTargetMarketingVO
     * @return String
     */
    @Override
    public String add(TSanquanTargetMarketingVO tSanquanTargetMarketingVO) {
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            tSanquanTargetMarketingVO.setTenantId(tenantId);
        }
         TSanquanTargetMarketing tSanquanTargetMarketing = new TSanquanTargetMarketing();
         BeanUtils.copyProperties(tSanquanTargetMarketingVO, tSanquanTargetMarketing);
         baseMapper.insert(tSanquanTargetMarketing);
         return tSanquanTargetMarketing.getId();
    }

    /**
     * 修改靶向营销审核记录表
     * 
     * @param tSanquanTargetMarketingVO
     * @return Boolean
     */
    @Override
    public Boolean update(TSanquanTargetMarketingVO tSanquanTargetMarketingVO) {
        TSanquanTargetMarketing tSanquanTargetMarketing = new TSanquanTargetMarketing();
        BeanUtils.copyProperties(tSanquanTargetMarketingVO, tSanquanTargetMarketing);
        return baseMapper.updateById(tSanquanTargetMarketing) > 0;
    }

    /**
     * 删除靶向营销审核记录表
     * 
     * @param id
     * @return Boolean
     */
    @Override
    public Boolean delete(String id) {
        return baseMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String,String> submitAudit(TSanquanTargetMarketingQueryVO marketingQueryVO) {
        long begin = System.currentTimeMillis();
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("success","fail");
        TSanquanPotentialOpportunityQueryVO queryVO = new TSanquanPotentialOpportunityQueryVO();
        // 全部审核
        if ("0".equals(marketingQueryVO.getType())) {
            queryVO = new TSanquanPotentialOpportunityQueryVO();
            //根据策略结果查询
            queryVO.setStrategyResultId(marketingQueryVO.getStrategyResultId());

        } else if ("1".equals(marketingQueryVO.getType())) {
            //根据客户id,和 策略结果查询
            queryVO = new TSanquanPotentialOpportunityQueryVO();
            queryVO.setStrategyResultId(marketingQueryVO.getStrategyResultId());
            if (!CollectionUtils.isEmpty(marketingQueryVO.getCustomerIdList())) {
                queryVO.setCustomerIdList(marketingQueryVO.getCustomerIdList());
            } else {
                queryVO.setCustomerId(marketingQueryVO.getCustomerId());
            }
        } else if ("2".equals(marketingQueryVO.getType())) {
            queryVO = new TSanquanPotentialOpportunityQueryVO();
            //根据策略id推送
            if (!CollectionUtils.isEmpty(marketingQueryVO.getOpportunityIdList())) {
                queryVO.setOpportunityIdList(marketingQueryVO.getOpportunityIdList());
            } else {
                queryVO.setId(marketingQueryVO.getOpportunityId());
            }
        }
        queryVO.setIsAudit(0L);
        List<TSanquanPotentialOpportunityVO> queryList = tSanquanPotentialOpportunityService.findList(queryVO);
        int total = 0;
        int fail = 0;
        if(!CollectionUtils.isEmpty(queryList)){
            total = queryList.size();
            MallUser currentUser = UserUtils.getUser();

            //获取客户经理(login,telphone)登录名手机号MAP
            Map<String,String> khjlMap = getCustomerManagerMap();

            //获取客户经理(login,telphone)登录名手机号MAP
            Map<String,CustomerViewVO> customerViewMap = getCustomerViewMap();

            //获取地市map
            Map<String,String> cityMap = getCityMap();

            long middle = System.currentTimeMillis();
            System.err.println("中间耗时====================="+(middle - begin));
            List<TSanquanTargetMarketing> targetMarketingList = new ArrayList<>();

            for (TSanquanPotentialOpportunityVO _vo : queryList) {
                // 判断客户经理是否为空
                if (StringUtils.isBlank(_vo.getCustomerManagerId())) {
                    fail ++ ;
                    continue;
                }
                // 判断地市是否为空
                if (StringUtils.isBlank(_vo.getCityCode())) {
                    fail ++ ;
                    continue;
                }
                // 判断客户ID是否为空
                if (StringUtils.isBlank(_vo.getCustomerId())) {
                    fail ++ ;
                    continue;
                }
                // 判断省分行业是否为空
                if (StringUtils.isBlank(_vo.getIndustry())) {
                    fail ++ ;
                    continue;
                }
                TSanquanTargetMarketing marketing = createTargetMarketing(_vo,khjlMap,customerViewMap,cityMap,currentUser);
                baseMapper.insert(marketing);


                UpdateWrapper updateWrapper = new UpdateWrapper();
                updateWrapper.eq("id",_vo.getId());
                updateWrapper.set("is_audit",1);
                System.err.println("sql=====update===begin");
                tSanquanPotentialOpportunityMapper.update(null,updateWrapper);
                System.err.println("sql=====update===end");
            }
//            baseMapper.insertBatch(targetMarketingList);
        }
        long end = System.currentTimeMillis();
        System.err.println("一共耗时====================="+(end - begin));
        resultMap.put("success","success");
        resultMap.put("totalNum",String.valueOf(total));
        resultMap.put("failNum",String.valueOf(fail));
        resultMap.put("successNum",String.valueOf(total-fail));
        return resultMap;
    }
    private TSanquanTargetMarketing createTargetMarketing(TSanquanPotentialOpportunityVO _vo,
                                                          Map<String,String> khjlMap,
                                                          Map<String,CustomerViewVO> customerViewMap,
                                                          Map<String,String> cityMap,MallUser currentUser){
        TSanquanTargetMarketing marketing = new TSanquanTargetMarketing();
        BeanUtils.copyProperties(_vo,marketing);
        marketing.setOpportunityId(_vo.getId());
        marketing.setCustomerAttribute("0");//1:商企客户 0:政企要客
        marketing.setCityCode(cityMap.get(_vo.getCityCode()));
        marketing.setCityName(_vo.getCityCode());
        marketing.setCustomerDimen("1");
        marketing.setAcctId(_vo.getMonthId());
        marketing.setExecutorName(_vo.getCustomerManager());
        marketing.setExecutorOa(_vo.getCustomerManagerId());
        marketing.setMatchDate(new Date());
        if(StringUtils.isNotEmpty(_vo.getReferralBusinessType())){
            marketing.setExecutorPhone(_vo.getExecutorPhone());
        }else{
            marketing.setExecutorPhone(khjlMap.get(_vo.getCustomerManagerId())==null?"":khjlMap.get(_vo.getCustomerManagerId()));
        }
        marketing.setSubdivisionIndustry(_vo.getSubdivisionIndustry());
        marketing.setCreateBy(currentUser.getLoginName());
        marketing.setCreateDate(new Date());
        marketing.setCreateName(currentUser.getStaffName());
        marketing.setDistrictName(_vo.getCustomerDistrictsName());//区县名称
        marketing.setDistrictCode(_vo.getCustomerDistrictsCode());//区县编码

        marketing.setRecommendBusinessType(_vo.getReferralBusinessType());//推荐业务类型
        if(StringUtils.isNotEmpty(_vo.getHotSaleProductName())){
            marketing.setHotSaleProductName(_vo.getHotSaleProductName());//热销产品
        }else{
            marketing.setHotSaleProductName(_vo.getProductName());//热销产品
        }
        if(StringUtils.isNotEmpty(_vo.getIndustryPromotion())){
            marketing.setIndustryPromotion(_vo.getIndustryPromotion());//行业主推产品
        }else{
            marketing.setIndustryPromotion(_vo.getHotSaleProductName());//如果没有行业主推,赋值热销产品就行
        }
        if(StringUtils.isNotEmpty(_vo.getRosterCustomerName())){
            marketing.setRosterCustomerName(_vo.getRosterCustomerName());
        }else{
            marketing.setRosterCustomerName(customerViewMap.get(_vo.getCustomerId())==null?"无":customerViewMap.get(_vo.getCustomerId()).getRosterCustomerName());
        }
        if(StringUtils.isNotEmpty(_vo.getRosterCustomerId())){
            marketing.setRosterCustomerId(_vo.getRosterCustomerId());
        }else{
            marketing.setRosterCustomerId(customerViewMap.get(_vo.getCustomerId())==null?"无":customerViewMap.get(_vo.getCustomerId()).getRosterCustomerId());
        }
        marketing.setProjectName(_vo.getReferralBusinessType());//项目名称
        marketing.setRemark(_vo.getRemark());//备注
        System.err.println(marketing.toString());
        return marketing;
    }
    //获取客户经理(login,telphone)登录名手机号MAP
    private Map<String,String> getCustomerManagerMap(){
        List<TSanquanDZqztJihezxProvinceMappingVO> khjlList = sanquanDZqztJihezxProvinceMappingMapper.findCustomerManager(new TSanquanDZqztJihezxProvinceMappingQueryVO());
        Map<String,String> khjlMap = new HashMap<>();//客户经理手机号MAP
        if(khjlList!=null){
            for(TSanquanDZqztJihezxProvinceMappingVO khjl : khjlList){
                khjlMap.put(khjl.getLogin(),khjl.getNumbers());
            }
        }
        return khjlMap;
    }
    //获取客户经理(login,telphone)登录名手机号MAP
    private Map<String,CustomerViewVO> getCustomerViewMap(){
        Map<String,CustomerViewVO> customerViewMap = new HashMap<>();//自然客户对应名单制客户MAP
        List<CustomerViewVO> customerViewList = natureCoustomerMapper.findCustomerViewInfoList(new NatureCoustomerQuery());
        if(customerViewList!=null){
            for(CustomerViewVO cus : customerViewList){
                customerViewMap.put(cus.getNaturalCustomerId(),cus);
            }
        }
        return customerViewMap;
    }
    //获取地市MAP(cityName,cityCode)
    private Map<String,String> getCityMap(){
        Map<String,String> cityMap = new HashMap<>();//地市编码MAP(name,code)
        List<Map<String, String>> cityList = sanquanDimProvCityCodeMapper.selectByProvName(new TSanquanDimProvCityCode());
        if(cityList!=null){
            for(Map<String, String> map:cityList){
                cityMap.put(map.get("cityName"),map.get("cityCode"));
            }
        }
        return cityMap;
    }

    /**
     * 定时任务定期执行靶向营销的推送给营销经理
     */
    @Transactional(rollbackFor = Exception.class)
    public void targetMarketingPushYxjl(String test){
        System.err.println("靶向营销定时任务执行！！");
        // 批量查询相关数据，减少数据库访问
        List<TSanquanTargetMarketing> targetMarketingList = baseMapper.selectListIsAudit();
        if (CollectionUtil.isNotEmpty(targetMarketingList)) {
            // 地市营销经理信息
            Map<String, TSanquanMarketingManager> cityToMarketingManagerMap = new HashMap<>();
            // 存储策略配置查询过的不需要再查询
            Map<String,TSanquanStrtegyConfiguration> strtegyConfigurationMap = new HashMap<>();
            // 处理每个目标营销记录
            for (TSanquanTargetMarketing tSanquanTargetMarketing : targetMarketingList) {
                TSanquanPotentialOpportunity _vo = tSanquanPotentialOpportunityService.getById(tSanquanTargetMarketing.getOpportunityId());
                // 如果机会数据为空，跳过当前循环
                if (_vo == null) continue;
                // 判断其中的策略是否进行了查询
                TSanquanStrtegyConfiguration sanquanStrtegyConfiguration = strtegyConfigurationMap.computeIfAbsent(tSanquanTargetMarketing.getStrategyId(),k -> tSanquanStrtegyConfigurationService.getById(tSanquanTargetMarketing.getStrategyId()));
                // 是否开启推送营销经理
                if (sanquanStrtegyConfiguration == null || !"1".equals(sanquanStrtegyConfiguration.getIsPushMarketingManager())) {
                    // 如果策略配置为空或者不需要推送，跳过当前循环
                    continue;
                }
                // 根据城市查找营销经理
                TSanquanMarketingManager marketingManager =  cityToMarketingManagerMap.computeIfAbsent(_vo.getCityCode(),k -> tSanquanMarketingManagerService.getMarketingLeaderByCity("1",_vo.getCityCode()));
                if (marketingManager != null) {
                    // 推送给营销经理
                    pushYxMarketing(tSanquanTargetMarketing, sanquanStrtegyConfiguration, _vo, marketingManager.getMarketingManagerOa(),
                            marketingManager.getMarketingManagerName(), marketingManager.getTelphone());
                }
            }
        }
    }

    /**
     * 推送营销经理
     * @param tSanquanTargetMarketing 靶向营销信息
     * @param sanquanStrtegyConfiguration 策略信息
     * @param _vo 潜在机会信息
     * @param marketingId 推送的营销经理工号
     * @param marketingName 推送的营销经理名称
     */
    private synchronized void pushYxMarketing(TSanquanTargetMarketing tSanquanTargetMarketing ,TSanquanStrtegyConfiguration sanquanStrtegyConfiguration, TSanquanPotentialOpportunity _vo, String marketingId, String marketingName, String phone){
        SimpleDateFormat myFmt = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        int num = (int) ((Math.random() * 9 + 1) * 100000) + 1;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String todoCode = myFmt.format(new Date()) + num;
        String todoName = String.format("%s%s潜在商机支撑待办处理", _vo.getCustomerName(), (StringUtils.isNotEmpty(_vo.getProductName()) ? _vo.getProductName() : ""));
        String waitSolveUrlArr = waitSolveUrl + "/auth/sso/decide/unify/token?ssoType=4&todoCode=" + todoCode;
        System.err.println("推送营销经理：" + waitSolveUrlArr);
        System.err.println("营销经理待办地址返回:"+waitSolveUrlArr );
        // 推送
        String str = YkService.sendTodo(requestLink, waitSolveUrlArr, todoCode, todoName, marketingId, _vo.getCustomerId(), _vo.getCustomerName(), dayNum);
        cn.hutool.json.JSONObject obj = new cn.hutool.json.JSONObject(str);
        if ("Success".equals(obj.getStr("rspMsg"))) {
            // 靶向营销推送营销经理修改
            UpdateWrapper updateWrapper = new UpdateWrapper();
            updateWrapper.eq("id",tSanquanTargetMarketing.getId());
            updateWrapper.set("is_push_marketing",1);
            System.err.println("sql=====update===begin");
            this.update(updateWrapper);
            // 潜在机会修改状态
            TSanquanPotentialOpportunity s = new TSanquanPotentialOpportunity();
            s.setIsPushMarketing(1L);
            s.setId(_vo.getId());
            this.tSanquanPotentialOpportunityService.saveOrUpdate(s);
            // 生成唯一数据记录
            StrategyResultTodo strategyResultTodo = new StrategyResultTodo();
            strategyResultTodo.setId(todoCode);
            strategyResultTodo.setTodoCode(todoCode);
            strategyResultTodo.setCreateUser(tSanquanTargetMarketing.getCreateName());
            strategyResultTodo.setStrategyConfigId(sanquanStrtegyConfiguration.getId());
            strategyResultTodo.setStrategyResultId(_vo.getStrategyResultId());
            strategyResultTodo.setStatus("0");
            strategyResultTodo.setCreateTime(DateUtils.formatDateTime(new Date()));
            // 待处理人  ( 营销经理)
            strategyResultTodo.setCurrentPerson(marketingId);
            strategyResultTodo.setCurrentPersonId(marketingName);
            strategyResultTodoService.saveOrUpdate(strategyResultTodo);
            // 往t_sanquan_strategy_result_todo_oppotunity 表中插入  待办工单表（注意地市区分）
            StrategyResultTodoOppotunity strategyResultTodoOppotunity = new StrategyResultTodoOppotunity();
            strategyResultTodoOppotunity.setTodoCode(todoCode);
            strategyResultTodoOppotunity.setOpportunityId(_vo.getId());
            strategyResultTodoOppotunity.setIsPush("0");
            strategyResultTodoOppotunity.setPushType("yxjl");
            // 设置推送人
            strategyResultTodoOppotunity.setPushPerson(tSanquanTargetMarketing.getCreateName());
            strategyResultTodoOppotunity.setPushPersonId(tSanquanTargetMarketing.getCreateBy());
            // 待处理人  ( 营销经理)
            strategyResultTodoOppotunity.setCurrentPerson(marketingName);
            strategyResultTodoOppotunity.setCurrentPersonId(marketingId);
            // 设置营销经理工单状态
            strategyResultTodoOppotunity.setMarketingManagerFeedbackStatus("4");
            strategyResultTodoOppotunity.setPushTime(DateUtils.formatDateTime(new Date()));
            strategyResultTodoOppotunity.setMonitorStatus("0");
            strategyResultTodoOppotunityService.saveOrUpdate(strategyResultTodoOppotunity);
            String pushtype = "yxjl";
            String operation = "推送（营销经理）";
            // 添加工单操作记录
            addWorkFeedbackLog(tSanquanTargetMarketing,_vo.getId(), marketingId, marketingName, todoCode, strategyResultTodoOppotunity.getId(), pushtype, operation);
            if("1".equals(sanquanStrtegyConfiguration.getIsSms())){
                if(StringUtils.isNotEmpty(phone)){
                    // 发送短信
                    sendSmsUtils.sendSMS(phone,sanquanStrtegyConfiguration.getSmsTemplate());
                }
            }
        }
    }

    /**
     * // 添加工单操作记录
     * @param tSanquanTargetMarketing 靶向营销明细
     * @param oppoId 潜在机会Id
     * @param toById 推送给谁工号
     * @param toByName 推送给谁名称
     * @param todoCode 待办code
     * @param todoOppoId 工单待办表Id
     * @param pushtype 推送类型
     * @param operation 操作
     */
    private void addWorkFeedbackLog(TSanquanTargetMarketing tSanquanTargetMarketing,String oppoId, String toById, String toByName, String todoCode, String todoOppoId, String pushtype, String operation) {
        // 添加工单记录
        TSanquanWorkFeedbackLog tSanquanWorkFeedbackLog = new TSanquanWorkFeedbackLog();
        tSanquanWorkFeedbackLog.setOpportunityId(oppoId);
        tSanquanWorkFeedbackLog.setCreateBy(tSanquanTargetMarketing.getCreateBy());
        tSanquanWorkFeedbackLog.setCreateByName(tSanquanTargetMarketing.getCreateName());
        tSanquanWorkFeedbackLog.setCreateDate(new Date());
        tSanquanWorkFeedbackLog.setTodoCode(todoCode);
        tSanquanWorkFeedbackLog.setTodoOppoId(todoOppoId);
        tSanquanWorkFeedbackLog.setPushType(pushtype);
        tSanquanWorkFeedbackLog.setToByName(toByName);
        tSanquanWorkFeedbackLog.setToById(toById);
        tSanquanWorkFeedbackLog.setOperation(operation);
        tSanquanWorkFeedbackLogService.add(tSanquanWorkFeedbackLog);
    }
}
