package cn.chinaunicom.sdsi.nature_customer.service.impl;

import cn.chinaunicom.sdsi.cloud.nature_customer.bo.CustomerClusterBo;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.CustomerCluster;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.TSanquanCluster;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.TSanquanClusterScene;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.ClusterPageQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.CustomerClusterQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.ClusterPageVO;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanUseScene;
import cn.chinaunicom.sdsi.cloud.product.query.TSanquanUseSceneQuery;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.nature_customer.mapper.CustomerClusterMapper;
import cn.chinaunicom.sdsi.nature_customer.mapper.TSanquanClusterMapper;
import cn.chinaunicom.sdsi.nature_customer.mapper.TSanquanClusterSceneMapper;
import cn.chinaunicom.sdsi.nature_customer.service.CustomerClusterService;
import cn.chinaunicom.sdsi.product.dao.TSanquanUseSceneMapper;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: 王永凯
 * @Date: 2024/11/6 下午2:49
 */
@Service
@RequiredArgsConstructor
public class CustomerClusterServiceImpl extends ServiceImpl<CustomerClusterMapper, CustomerCluster> implements CustomerClusterService {

    private final CustomerClusterMapper customerClusterMapper;
    private final TSanquanClusterMapper clusterMapper;
    private final TSanquanClusterSceneMapper clusterSceneMapper;
    private final TSanquanUseSceneMapper useSceneMapper;

    /**
     * 通过客户Id和和客户类型获取聚类
     *
     * @param customerId   客户Id
     * @param customerType 客户类型
     * @return 查询结果
     */
    @Override
    public List<CustomerCluster> findClusterByCustomerIdAndType(String customerId, String customerType) {
        if (StrUtil.isBlank(customerId) || StrUtil.isBlank(customerType)) {
            return Collections.emptyList();
        }
        return customerClusterMapper.findClusterByCustomerIdAndType(customerId, customerType);
    }

    /**
     * 绑定客户及聚类
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveBatchCustomerCluster(CustomerClusterBo bo) {
        if (CollUtil.isNotEmpty(bo.getClusters())) {
            //  获取所有聚类值
            List<TSanquanCluster> clusterList = clusterMapper.selectList(null);
            Map<String, String> map = clusterList.stream().collect(Collectors.toMap(TSanquanCluster::getName, TSanquanCluster::getId));
            //  筛选未插入的聚类数据
            List<TSanquanCluster> clusterCheckList = new LinkedList<>();
            List<TSanquanCluster> insertList = new LinkedList<>();
            for (CustomerCluster customerCluster : bo.getClusterCheckList()) {
                TSanquanCluster cluster = new TSanquanCluster();
                cluster.setName(customerCluster.getClusterName());
                if (map.containsKey(customerCluster.getClusterName())) {
                    cluster.setId(map.get(customerCluster.getClusterName()));
                    clusterCheckList.add(cluster);
                } else {
                    insertList.add(cluster);
                }
            }
            //  批量插入聚类字典
            if (CollUtil.isNotEmpty(insertList)) {
                clusterMapper.insertBatchSomeColumn(insertList);
                clusterCheckList.addAll(insertList);
            }
            //  关联客户聚类
            List<CustomerCluster> list = new LinkedList<>();
            for (CustomerClusterQuery cluster : bo.getClusters()) {
                for (TSanquanCluster tSanquanCluster : clusterCheckList) {
                    CustomerCluster customerCluster = new CustomerCluster();
                    customerCluster.setCustomerId(cluster.getCustomerId());
                    customerCluster.setCustomerType(cluster.getCustomerType());
                    customerCluster.setClusterId(tSanquanCluster.getId());
                    customerCluster.setClusterName(tSanquanCluster.getName());
                    list.add(customerCluster);
                }
            }
            //  判读是批量打标还是单独编辑
            if(bo.getIsSeparate()){
                //  【单独编辑】删除原来的所有然后重新插入
                customerClusterMapper.delete(new QueryWrapper<CustomerCluster>().lambda()
                        .eq(CustomerCluster::getCustomerId, bo.getClusters().get(0).getCustomerId())
                        .eq(CustomerCluster::getCustomerType, bo.getClusters().get(0).getCustomerType()));
            }

            if (CollUtil.isNotEmpty(list)) {
                //  【批量打标】全部插入
                customerClusterMapper.insertBatchSomeColumn(list);
            }
        }
        return true;
    }

    /**
     * 分页列表
     */
    @Override
    public IPage<ClusterPageVO> findPage(ClusterPageQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return customerClusterMapper.findPage(page, query);
    }

    /**
     * 聚类关联场景显示分页列表
     */
    @Override
    public IPage<TSanquanUseScene> findScenePage(TSanquanUseSceneQuery query) {
        List<String> sceneIds = new ArrayList<>();
        if (StrUtil.isNotBlank(query.getClusterId())) {
            sceneIds = clusterSceneMapper.findSceneIdsByClusterId(query.getClusterId());
            query.setSceneIds(sceneIds);
        }
        IPage page = QueryVoToPageUtil.toPage(query);
        IPage<TSanquanUseScene> iPage = useSceneMapper.findPage(page, query);
        if (CollUtil.isNotEmpty(sceneIds)) {
            for (TSanquanUseScene record : iPage.getRecords()) {
                record.setAssociated(sceneIds.contains(record.getId()) ? "1" : "0");
            }
        }
        return iPage;
    }

    /**
     * 聚类配置场景关联或取消事件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean associatedScene(String clusterId, String sceneId, String associated) {
        if (StrUtil.isAllNotBlank(clusterId, sceneId, associated)) {
            if (StrUtil.equals(associated, "1")) {
                //  配置关联
                TSanquanClusterScene entity = new TSanquanClusterScene();
                entity.setClusterId(clusterId);
                entity.setSceneId(sceneId);
                clusterSceneMapper.insert(entity);
            } else {
                //  取消关联
                clusterSceneMapper.delete(new QueryWrapper<TSanquanClusterScene>()
                        .lambda()
                        .eq(TSanquanClusterScene::getClusterId, clusterId)
                        .eq(TSanquanClusterScene::getSceneId, sceneId));
            }
        }
        return true;
    }

    @Override
    public IPage<ClusterPageVO> findAndCounterPage(ClusterPageQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return customerClusterMapper.findAndCounterPage(page, query);
    }
}