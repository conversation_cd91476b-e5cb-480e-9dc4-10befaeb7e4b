package cn.chinaunicom.sdsi.cityCustListMark.dto;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

/**
 * @program: sanquan_server
 * @ClassName CityCustUserInfoDTO
 * @description:
 * @author: majian
 * @date: 2025-05-23 15:59
 * @Version 1.0
 **/
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class CityCustUserInfoDTO  extends BaseQueryVO {
    /**
     * 自然客户ID
     */
    private String  natureCustId;
}
