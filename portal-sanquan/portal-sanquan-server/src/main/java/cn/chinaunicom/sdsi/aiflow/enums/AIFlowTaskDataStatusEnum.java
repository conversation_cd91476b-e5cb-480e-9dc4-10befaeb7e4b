package cn.chinaunicom.sdsi.aiflow.enums;

/**
 * @program: sanquan_server
 * @ClassName AIFlowProcessStatusEnums
 * @description:
 * @author: ma<PERSON>an
 * @date: 2025-05-06 11:03
 * @Version 1.0
 **/
public enum AIFlowTaskDataStatusEnum {
    WAITING_STATUS("0", "等待执行"),
    EXECUTING_STATUS("1", "正在执行"),

    SUCCESS_STATUS("2", "执行成功"),
    FAILURE_STATUS("3", "执行失败");

    private final String code;
    private final String msg;

    AIFlowTaskDataStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
    public String getCode() {
        return code;
    }
    public String getMsg() {
        return msg;
    }
}
