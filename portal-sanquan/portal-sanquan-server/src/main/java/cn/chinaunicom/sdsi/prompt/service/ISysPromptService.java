package cn.chinaunicom.sdsi.prompt.service;

import cn.chinaunicom.sdsi.prompt.dto.PromptDTO;
import cn.chinaunicom.sdsi.prompt.entity.SysPrompt;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 提示词服务接口
 */
public interface ISysPromptService {
    /**
     * 查询提示词列表
     *
     * @param param 入参
     * @return 提示词列表
     */
    IPage<SysPrompt> selectPromptList(PromptDTO param);

    /**
     * 获取提示词详情
     *
     * @param id 提示词ID
     * @return 提示词信息
     */
    SysPrompt getPromptById(Long id);

    /**
     * 新增提示词
     *
     * @param prompt 提示词信息
     * @return 结果
     */
    int insertPrompt(SysPrompt prompt);

    /**
     * 修改提示词
     *
     * @param prompt 提示词信息
     * @return 结果
     */
    int updatePrompt(SysPrompt prompt);

    /**
     * 删除提示词
     *
     * @param ids 需要删除的提示词ID数组
     * @return 结果
     */
    int deletePromptByIds(Long[] ids);
}
