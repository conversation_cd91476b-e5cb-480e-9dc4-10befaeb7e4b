package cn.chinaunicom.sdsi.customerPicture.controller;

import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.customerPicture.service.CustomerProfileBidService;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 三全客户画像-客户标讯信息-标讯信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@RestController
@RequestMapping("/customerPictureBid")
public class CustomerProfileBidController extends BaseController {

    @Autowired
    private CustomerProfileBidService customerProfileBidService;


    /**
     * 客户标讯信息
     * @return
     */
    @GetMapping("/summary")
    public BaseResponse<Map> getBusinessYearTotalFee(CustomerPictureVo pictureVo){
        if(StringUtils.isEmpty(pictureVo.getNatureCustId())){
            return notOk();
        }
        Map res = new HashMap();
        int liantongBidNum = customerProfileBidService.getProfileLiantongBid( pictureVo);
        if(liantongBidNum != 0){
            int bidNum = customerProfileBidService.getProfileBid( pictureVo);
            if(bidNum != 0){
                res.put("zhongbiao",new BigDecimal(liantongBidNum/bidNum).multiply(new BigDecimal(100)).setScale(2));
            }else{
                res.put("zhongbiao",0);
            }
        }else{
            res.put("zhongbiao",0);
        }
        return ok(res);

    }

}
