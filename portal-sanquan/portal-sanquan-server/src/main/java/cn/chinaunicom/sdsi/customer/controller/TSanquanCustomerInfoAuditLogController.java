package cn.chinaunicom.sdsi.customer.controller;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerInfoAuditLog;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerInfoQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerInfoVO;
import cn.chinaunicom.sdsi.customer.service.TSanquanCustomerInfoAuditLogService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/7/8 14:50
 */
@Slf4j
@RestController
@RequestMapping("/customer/infoAuditLog")
@Tag(name="客户资料操作记录表")
public class TSanquanCustomerInfoAuditLogController extends BaseController {

    @Autowired
    private TSanquanCustomerInfoAuditLogService tSanquanCustomerInfoAuditLogService;

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since ${date}
     * @return BaseResponse<List<TSanquanCustomerInfo>>
     **/
    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<TSanquanCustomerInfoAuditLog>> findList(TSanquanCustomerInfoAuditLog query) {
        return ok(tSanquanCustomerInfoAuditLogService.findList(query));
    }

}
