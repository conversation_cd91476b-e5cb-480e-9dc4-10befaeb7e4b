package cn.chinaunicom.sdsi.dataexport.dto;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class DataExportQueryDTO extends BaseQueryVO {
    /**
     * 姓名
     */
    private String name;

    /**
     * 账期
     */
    private String timest;
    /**
     * 数据导入开始时间
     */
    private String beginImportTime;
    /**
     * 数据导入结束时间
     */
    private String endImportTime;
}
