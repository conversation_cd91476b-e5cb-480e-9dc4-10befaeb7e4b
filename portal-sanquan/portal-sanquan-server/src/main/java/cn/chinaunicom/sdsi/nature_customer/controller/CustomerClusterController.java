package cn.chinaunicom.sdsi.nature_customer.controller;

import cn.chinaunicom.sdsi.cloud.nature_customer.bo.CustomerClusterBo;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.CustomerCluster;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.ClusterPageQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.ClusterPageVO;
import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanUseScene;
import cn.chinaunicom.sdsi.cloud.product.query.TSanquanUseSceneQuery;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.nature_customer.service.CustomerClusterService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户聚类控制台
 *
 * @Author: 王永凯
 * @Date: 2024/11/6 下午2:24
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/nature/customer/cluster")
public class CustomerClusterController extends BaseController {

    private final CustomerClusterService customerClusterService;

    @Operation(summary = "通过客户Id和和客户类型获取聚类", description = "查询列表")
    @GetMapping("/findClusterByCustomerIdAndType")
    public BaseResponse<List<CustomerCluster>> findClusterByCustomerIdAndType(@RequestParam("customerId") String customerId,
                                                                              @RequestParam("customerType") String customerType) {
        return ok(customerClusterService.findClusterByCustomerIdAndType(customerId, customerType));
    }

    @Operation(summary = "绑定客户及聚类", description = "保存数据")
    @PostMapping("/saveBatch")
    public BaseResponse<Boolean> saveBatch(@RequestBody CustomerClusterBo bo) {
        return ok(customerClusterService.saveBatchCustomerCluster(bo));
    }
    
    @Operation(summary = "分页列表")
    @GetMapping("/findPage")
    public BasePageResponse<ClusterPageVO> findPage(ClusterPageQuery query) {
        return pageOk(customerClusterService.findPage(query));
    }

    @Operation(summary = "聚类分页列表（包含统计数量）")
    @GetMapping("/findAndCounterPage")
    public BasePageResponse<ClusterPageVO> findAndCounterPage(ClusterPageQuery query) {
        return pageOk(customerClusterService.findAndCounterPage(query));
    }

    @Operation(summary = "设置需求场景分页列表")
    @GetMapping("/findScenePage")
    public BasePageResponse<TSanquanUseScene> findScenePage(TSanquanUseSceneQuery query) {
        return pageOk(customerClusterService.findScenePage(query));
    }

    @Operation(summary = "关联聚类和场景", description = "保存数据")
    @PostMapping("/associatedScene")
    public BaseResponse<Boolean> associatedScene(@RequestParam("clusterId") String clusterId,
                                                 @RequestParam("sceneId") String sceneId,
                                                 @RequestParam("associated") String associated) {
        return ok(customerClusterService.associatedScene(clusterId, sceneId, associated));
    }

}
