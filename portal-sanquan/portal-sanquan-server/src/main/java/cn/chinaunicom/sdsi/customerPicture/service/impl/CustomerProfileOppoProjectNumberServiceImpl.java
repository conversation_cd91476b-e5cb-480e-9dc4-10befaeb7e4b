package cn.chinaunicom.sdsi.customerPicture.service.impl;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOppoProjectNumber;
import cn.chinaunicom.sdsi.customerPicture.mapper.CustomerProfileOppoProjectNumberMapper;
import cn.chinaunicom.sdsi.customerPicture.service.CustomerProfileOppoProjectNumberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 三全客户画像-客户商机信息-商机转项目数量表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Service
public class CustomerProfileOppoProjectNumberServiceImpl extends ServiceImpl<CustomerProfileOppoProjectNumberMapper, CustomerProfileOppoProjectNumber> implements CustomerProfileOppoProjectNumberService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-10-24
     * @param id
     * @return CustomerProfileOppoProjectNumber
     **/
    @Override
    public CustomerProfileOppoProjectNumber findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-10-24
     * @return List<CustomerProfileOppoProjectNumber>
     **/
    @Override
    public List<CustomerProfileOppoProjectNumber> findList() {
        return baseMapper.selectList(null);
    }


}
