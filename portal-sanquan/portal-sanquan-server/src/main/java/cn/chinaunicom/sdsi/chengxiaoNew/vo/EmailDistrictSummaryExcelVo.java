package cn.chinaunicom.sdsi.chengxiaoNew.vo;

import cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class EmailDistrictSummaryExcelVo implements Serializable {

    @ColumnWidth(40)
    @ExcelProperty(value = "地市")
    String districtName;

    @ExcelIgnore
    String dataType;

    @ExcelIgnore
    String gridName;

    @ColumnWidth(20)
    @ExcelProperty({"累计情况","任务数量"})
    String taskNum;

    @ColumnWidth(20)
    @ExcelProperty({"累计情况","执行数量"})
    String zhixingNum;

    @ColumnWidth(20)
    @ExcelProperty({"累计情况","未执行数量"})
    String weizhixingNum;

    @ColumnWidth(20)
    @ExcelProperty({"累计情况","执行率"})
    String zhixingRate;

    @ColumnWidth(20)
    @ExcelProperty(value = "本周派发")
    String sendNumWeek;

    @ColumnWidth(20)
    @ExcelProperty(value = "过期7天未执行")
    String expire7Day;

    public String getZhixingRate() {
        if(StringUtils.isNotEmpty(zhixingRate) && !zhixingRate.contains("%")){
            BigDecimal bigDecimal = new BigDecimal(zhixingRate).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            return bigDecimal.toString()+"%";
        }else if(StringUtils.isEmpty(zhixingRate)){
            return "";
        }
        return zhixingRate;
    }

    public String getDistrictName() {
        if("2".equals(dataType)){
            return gridName;
        }
        return districtName;
    }
}
