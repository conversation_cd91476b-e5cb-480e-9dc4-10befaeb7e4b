package cn.chinaunicom.sdsi.customerWorkSaturationStatistics.service.impl;

import cn.chinaunicom.sdsi.cloud.customerWorkSaturationStatistics.entity.CustGemWorkSaturationStatistics;
import cn.chinaunicom.sdsi.cloud.customerWorkSaturationStatistics.query.CustGemWorkSaturationStatisticsQueryVo;
import cn.chinaunicom.sdsi.customerWorkSaturationStatistics.mapper.CustGemWorkSaturationStatisticsMapper;
import cn.chinaunicom.sdsi.customerWorkSaturationStatistics.service.CustGemWorkSaturationStatisticsService;
import cn.chinaunicom.sdsi.customerWorkSaturationStatistics.vo.CustGemWorkSaturationStatisticsExcelVo;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
/**
 * <p>
 * 客户经理工单饱和度统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Service
public class CustGemWorkSaturationStatisticsServiceImpl extends ServiceImpl<CustGemWorkSaturationStatisticsMapper, CustGemWorkSaturationStatistics> implements CustGemWorkSaturationStatisticsService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-18
     * @param tSanquanCustGemWorkSaturationStatistics
     * @return IPage<TSanquanCustGemWorkSaturationStatistics>
     **/
    @Override
    public IPage<CustGemWorkSaturationStatistics> findPage(CustGemWorkSaturationStatisticsQueryVo tSanquanCustGemWorkSaturationStatisticsVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanCustGemWorkSaturationStatisticsVo);
        return baseMapper.findPage(page, tSanquanCustGemWorkSaturationStatisticsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-18
     * @param id
     * @return TSanquanCustGemWorkSaturationStatistics
     **/
    @Override
    public CustGemWorkSaturationStatistics findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-18
     * @return List<TSanquanCustGemWorkSaturationStatistics>
     **/
    @Override
    public List<CustGemWorkSaturationStatistics> findList(CustGemWorkSaturationStatisticsQueryVo tSanquanCustGemWorkSaturationStatisticsVo) {
        return baseMapper.findList(tSanquanCustGemWorkSaturationStatisticsVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-18
     * @param tSanquanCustGemWorkSaturationStatistics
     * @return int
     **/
    @Override
    public int add(CustGemWorkSaturationStatistics tSanquanCustGemWorkSaturationStatistics) {
        return baseMapper.insert(tSanquanCustGemWorkSaturationStatistics);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-18
     * @param tSanquanCustGemWorkSaturationStatistics
     * @return int
     **/
    @Override
    public int update(CustGemWorkSaturationStatistics tSanquanCustGemWorkSaturationStatistics) {
        return baseMapper.updateById(tSanquanCustGemWorkSaturationStatistics);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-18
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 导出数据
     * @param workSaturationStatisticsVo
     */
    @Override
    public void exportData(CustGemWorkSaturationStatisticsQueryVo workSaturationStatisticsVo) {
        List<CustGemWorkSaturationStatisticsExcelVo> list = baseMapper.exportExcelList(workSaturationStatisticsVo);
        try {
            ExcelUtils.exportExcel(list, CustGemWorkSaturationStatisticsExcelVo.class, "客户经理工单饱和度统计", "客户经理工单饱和度统计");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
