package cn.chinaunicom.sdsi.private5GWorkflow.enums;

/**
 * @program: sanquan_server
 * @ClassName ProcessStepEnums
 * @description:
 * @author: ma<PERSON>an
 * @date: 2025-04-09 18:38
 * @Version 1.0
 **/
public enum ProcessStepEnum {
    TZSQ("TZSQ","投资申请"),
    TZJC("TZJC","投资决策"),
    XMSS("XMSS","项目实施");

    String processCode;
    String processName;

    public String getProcessCode() {
        return processCode;
    }

    public String getProcessName() {
        return processName;
    }

    ProcessStepEnum(String processCode, String processName) {
        this.processCode = processCode;
        this.processName = processName;
    }
}
