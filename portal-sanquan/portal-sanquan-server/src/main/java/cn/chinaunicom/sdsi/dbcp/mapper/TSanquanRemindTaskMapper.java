package cn.chinaunicom.sdsi.dbcp.mapper;

import cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanRemindTask;
import cn.chinaunicom.sdsi.cloud.dbcp.query.TSanquanRemindTaskQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 提醒任务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Mapper
public interface TSanquanRemindTaskMapper extends BaseMapper<TSanquanRemindTask> {

    // 分页查询
    IPage<TSanquanRemindTask> findPage(@Param("page") IPage page,@Param("query") TSanquanRemindTaskQueryVo tSanquanRemindTaskQueryVo);

    // 查询列表
    List<TSanquanRemindTask> findList(@Param("query") TSanquanRemindTaskQueryVo tSanquanRemindTaskQueryVo);

    int updateTask(@Param("query") TSanquanRemindTaskQueryVo tSanquanRemindTaskQueryVo);

}
