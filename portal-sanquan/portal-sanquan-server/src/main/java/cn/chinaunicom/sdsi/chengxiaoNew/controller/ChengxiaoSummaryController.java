package cn.chinaunicom.sdsi.chengxiaoNew.controller;

import cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoDetailVO;
import cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary;
import cn.chinaunicom.sdsi.chengxiaoNew.service.ChengxiaoSummaryService;
import cn.chinaunicom.sdsi.chengxiaoNew.vo.LwtxDetailVo;
import cn.chinaunicom.sdsi.chengxiaoNew.vo.OppoAndProjectDetailVo;
import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.permissionConfig.entity.PermissionConfigVO;
import cn.chinaunicom.sdsi.permissionConfig.service.PermissionConfigService;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 成效转化 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Slf4j
@RestController
@RequestMapping("/chengxiaoSummary")
public class ChengxiaoSummaryController extends BaseController {

    @Autowired
    private ChengxiaoSummaryService summaryService;

    @Autowired
    PermissionConfigService permissionConfigService;

    /*
     * <AUTHOR>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<ChengXiaoSummary> findPage(ChengXiaoDetailVO detail){
        buildParam(detail);
        return pageOk(summaryService.getDataByParam(detail));
    }
    /*
     * 获取区县，营服列表
     **/
    @GetMapping("/getAreaList")
    public BaseResponse<List<ChengXiaoDetailVO>> getAreaList(ChengXiaoDetailVO detailVO){
        return ok(summaryService.getAreaList(detailVO));
    }

    /**
     * 导出数据
     * @param detail
     */
    @PostMapping("/exportData")
    @Operation(summary = "导出数据", description = "导出数据")
    public void exportData(@RequestBody ChengXiaoDetailVO detail) {
       /* if(StringUtils.isEmpty(detail.getIsShowTotal())){
            detail.setIsShowTotal("true");
        }
        if(StringUtils.isNotEmpty(detail.getCityName()) && detail.getCityName().contains("全部")){
            detail.setCityName(null);
        }*/
        buildParam(detail);
        if("6".equals(detail.getColumnType())){// 个人明细
            detail.setColumnType("60");
        }
//        detail.setIsShowDBCP("true"); // 导出时，明细不显示等保测评
        detail.setPageNum(-1);
        detail.setPageSize(Integer.MAX_VALUE);
        summaryService.exportData(detail);
    }

    /**
     * 导出，列表组装参数
     */
    private void buildParam(ChengXiaoDetailVO detail){
        if(StringUtils.isNotEmpty(detail.getEndTime())){
            if(!detail.getEndTime().contains("23:59:59")){
                detail.setEndTime(detail.getEndTime() + " 23:59:59");
            }
        }
        if(StringUtils.isNotEmpty(detail.getEndTimeContract())){
            if(!detail.getEndTimeContract().contains("23:59:59")){
                detail.setEndTimeContract(detail.getEndTimeContract() + " 23:59:59");
            }
        }
        if(StringUtils.isNotEmpty(detail.getEndTime1())){
            if(!detail.getEndTime1().contains("23:59:59")){
                detail.setEndTime1(detail.getEndTime1() + " 23:59:59");
            }
        }
        if(StringUtils.isEmpty(detail.getCreateTime())){
            String list = summaryService.getMaxDate();
            detail.setCreateTime(list);
        }
        if(StringUtils.isNotEmpty(detail.getCityName()) && detail.getCityName().contains("全部")){
            detail.setCityName(null);
        }
        if("全部".equals(detail.getProvinceIndustryName())){
            detail.setProvinceIndustryName(null);
        }
        if("全部".equals(detail.getProvinceDetailIndustryName())){
            detail.setProvinceDetailIndustryName(null);
        }
        if(StringUtils.isEmpty(detail.getIsShowTotal())){
            detail.setIsShowTotal("true");// 默认展示合计
        }
        if("7".equals(detail.getColumnType()) || "1".equals(detail.getColumnType())){
            // 工号级别为地市级的，不展示地市合计，其它地市也不显示，
            MallUser mallUser = UserUtils.getUser();
            List<PermissionConfigVO> list = permissionConfigService.getUserRolesOaLevel();
            if(list.size()>0 && "20".equals(list.get(0).getMainLevel()) && StringUtils.isNotEmpty(mallUser.getCity())){
                detail.setCityName(mallUser.getCity());
                detail.setIsShowTotal("false");// 地市级别，默认不展示合计
            }
        }
        if("1".equals(detail.getColumnType())){ // 本市
            detail.setCityCodeOrder("cityCode");
            if("1".equals(detail.getDataSource()) && 1==2){ // 线索不显示待转派了
                detail.setColumn("city_name,dispatch_source");
                detail.setOrderColumn("b.city_id,data_source,dispatch_source");
                detail.setColumnTotal("CITY_NAME");
                if(!detail.getIsShowTotal().equals("false")){
                    detail.setColumnProvTotal("dispatch_source");
                }
                if(detail.getIsShowTotal().equals("false")){
                    // 派发，合计层对应全省，不展示全省。但地市合计还要展示
                    detail.setIsShowTotal("true");
                }
            }else{
                detail.setColumn("city_name");
                detail.setOrderColumn("data_source,b.city_id");
            }
        }else if("2".equals(detail.getColumnType())){ // 行业
            // 地市统筹，必须有 city_name。省公司city_name 为空
            detail.setColumn("province_industry_name");
            detail.setOrderColumn("data_source,province_industry_name");
        }else if("3".equals(detail.getColumnType())){ // 区县
            // 必须有 city_name
            detail.setColumn("district_name");
            detail.setOrderColumn("data_source,district_name");
        }else if("4".equals(detail.getColumnType())){ // 营服
            // 必须有 city_name，district_name
            detail.setColumn("grid_name");
            detail.setOrderColumn("data_source,grid_name");
        }else if("5".equals(detail.getColumnType())){ // 个人(客户经理)
            detail.setColumn("executor_name");
            detail.setOrderColumn("data_source,executor_name");
        }else if("7".equals(detail.getColumnType())){ // 策略
            if(StringUtils.isNotEmpty(detail.getDataSource()) && detail.getDataSource().contains("4")){// 靶向-存量价值
                detail.setCityCodeOrder("cityCode");
                detail.setColumn("city_name,detail_scene");
                if(!detail.getIsShowTotal().equals("false")){ // 不展示全省
                    detail.setColumnProvTotal("detail_scene");
                }
                detail.setOrderColumn("b.city_id,data_source,detail_scene");
            }else{
                detail.setCityCodeOrder("cityCode");
                detail.setColumn("city_name,marketing_number");
                if(!detail.getIsShowTotal().equals("false")){
                    detail.setColumnProvTotal("marketing_number");
                }
                detail.setOrderColumn("b.city_id,data_source,marketing_number");
            }
            if(detail.getIsShowTotal().equals("false")){
                // 策略类型，合计层对应全省，不展示全省。但地市合计还要展示
                detail.setIsShowTotal("true");
            }
            detail.setColumnTotal("city_name");
        }else{
            // 默认的分组（6类型为展示详情信息）
            detail.setColumn("data_source");
            detail.setOrderColumn("data_source");
        }
    }

    /**
     * 导出明细数据
     * @param detail
     */
    @PostMapping("/exportDataDetails")
    @Operation(summary = "导出明细数据", description = "导出明细数据")
    public void exportDataDetails(@RequestBody ChengXiaoDetailVO detail) {
        if(StringUtils.isNotEmpty(detail.getCityName()) && detail.getCityName().contains("全部")){
            detail.setCityName(null);
        }
        summaryService.exportDataDetails(detail);
    }

    /**
     * 查询商机、项目、合同明细信息
     * @param detail
     * @return
     */
    @GetMapping("/oppoAndProjectChengXiaoList")
    public BasePageResponse<OppoAndProjectDetailVo> oppoAndProjectList(ChengXiaoDetailVO detail){
        if(StringUtils.isEmpty(detail.getCreateTime())){
            String list = summaryService.getMaxDate();
            detail.setCreateTime(list);
        }
        if(StringUtils.isNotEmpty(detail.getCityName())){
           if("全部".equals(detail.getCityName())){
               detail.setCityName(null);
           }
            if(StringUtils.isNotEmpty(detail.getDistrictName())){
                // 区县都带有地市，无需地市条件。有老数据当地区县挂在另一个地市上，所以暂去掉地市条件
                detail.setCityName(null);
            }
        }
        return pageOk(summaryService.getOppoAndProjectList(detail));
    }

    /**
     * 计算合计金额
     * @param detail
     * @return
     */
    @GetMapping("/oppoAndProjectChengXiaoListSumPrice")
    public BaseResponse<OppoAndProjectDetailVo> oppoAndProjectChengXiaoListSumPrice( ChengXiaoDetailVO detail){
        if(StringUtils.isEmpty(detail.getCreateTime())){
            String list = summaryService.getMaxDate();
            detail.setCreateTime(list);
        }
        if(StringUtils.isNotEmpty(detail.getCityName())){
            if("全部".equals(detail.getCityName())){
                detail.setCityName(null);
            }
            if(StringUtils.isNotEmpty(detail.getDistrictName())){
                // 区县都带有地市，无需地市条件。有老数据当地区县挂在另一个地市上，所以暂去掉地市条件
                detail.setCityName(null);
            }
        }
        OppoAndProjectDetailVo detailVo = new OppoAndProjectDetailVo();
        detail.setQueryType("1");
        OppoAndProjectDetailVo detailVo1 = summaryService.oppoAndProjectChengXiaoListSumPrice(detail);
        detailVo.setContractAmount(detailVo1 != null ? detailVo1.getContractAmount() : "0");
        detail.setQueryType("2");
        detail.setDataType("project");
        OppoAndProjectDetailVo detailVo2 = summaryService.oppoAndProjectChengXiaoListSumPrice(detail);
        detailVo.setProjectNumber(detailVo2 != null ? detailVo2.getProjectNumber() : "0");
        return ok(detailVo);
    }

    /**
     * 联网通信明细查询
     * @param detail
     * @return
     */
    @GetMapping("/lwtxChengXiaoList")
    public BasePageResponse<LwtxDetailVo> lwtxChengXiaoList(ChengXiaoDetailVO detail){
        if(StringUtils.isNotEmpty(detail.getEndTimeContract())){
            if(!detail.getEndTimeContract().contains("23:59:59")){
                detail.setEndTimeContract(detail.getEndTimeContract() + " 23:59:59");
            }
        }
        return pageOk(summaryService.getLwtxChengXiaoList(detail));
    }

    /**
     * 商机、项目导出
     * @param detail
     * @return
     */
    @PostMapping("/oppoAndProjectChengXiaoExport")
    public void oppoAndProjectChengXiaoExport(@RequestBody ChengXiaoDetailVO detail){
        detail.setPageSize(Integer.MAX_VALUE);
        if(StringUtils.isEmpty(detail.getCreateTime())){
            String list = summaryService.getMaxDate();
            detail.setCreateTime(list);
        }
        if(StringUtils.isNotEmpty(detail.getEndTimeContract())){
            if(!detail.getEndTimeContract().contains("23:59:59")){
                detail.setEndTimeContract(detail.getEndTimeContract() + " 23:59:59");
            }
        }
        summaryService.oppoAndProjectChengXiaoExport(detail);
    }

    /**
     * 联网通信明细导出
     * @param detail
     * @return
     */
    @PostMapping("/lwtxChengXiaoExport")
    public void lwtxChengXiaoExport(@RequestBody ChengXiaoDetailVO detail){
        detail.setPageSize(Integer.MAX_VALUE);
        if(StringUtils.isNotEmpty(detail.getEndTimeContract())){
            if(!detail.getEndTimeContract().contains("23:59:59")){
                detail.setEndTimeContract(detail.getEndTimeContract() + " 23:59:59");
            }
        }
        summaryService.lwtxChengXiaoExport(detail);
    }

    /**
     * 查询角色跳转页面控制
     * @param type 角色类型
     * @return
     */
    @GetMapping("/roleJumpPage")
    public BaseResponse<List<Map<String,String>>> roleJumpPage(String type){
        return ok(summaryService.roleJumpPage(type));
    }

    /**
     * 一键催办
     * @param detail
     * @return
     */
    @GetMapping("/urgingTask")
    public BaseResponse<Map<String, String> > urgingTask(ChengXiaoDetailVO detail){
        return ok(summaryService.urgingTask(detail));
    }


    /**
     * 一键催办、查询已执行的数量和未执行的数量
     * @param detail
     * @return
     */
    @GetMapping("/urgingTaskNum")
    public BaseResponse<Map<String, Object>> urgingTaskNum(ChengXiaoDetailVO detail){
        return ok(summaryService.urgingTaskNum(detail));
    }

    /**
     * 获取最新日期
     * @return
     */
    @GetMapping("/getUpNewDate")
    public BaseResponse<String> getMaxDate(){
        String list = summaryService.getMaxDate();
        return ok(list);
    }

}
