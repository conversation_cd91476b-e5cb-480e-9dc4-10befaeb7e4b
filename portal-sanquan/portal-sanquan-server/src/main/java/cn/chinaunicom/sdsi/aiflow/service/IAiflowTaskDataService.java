package cn.chinaunicom.sdsi.aiflow.service;

import cn.chinaunicom.sdsi.aiflow.dto.AiflowTaskDataDTO;
import cn.chinaunicom.sdsi.aiflow.entity.AiflowTaskData;
import cn.chinaunicom.sdsi.aiflow.entity.CustomerInsightData;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface IAiflowTaskDataService extends IService<AiflowTaskData> {

    /**
     * 获取模型结果数据
     * @param param
     * @return
     */
    IPage<AiflowTaskData> selectAiflowTaskData(AiflowTaskDataDTO param);

    /**
     * 导出模型结果列表
     * @param param
     */
    void exportAiFlowData(AiflowTaskDataDTO param);

    /**
     * 获取模型数据账期
     * @param param
     * @return
     */
    List<String> selectAiflowTaskDataTimest(AiflowTaskDataDTO param);

    /**
     * 根据任务ID查询等待处理数据列表
     *
     * @param taskId 任务ID
     * @return 等待处理数据列表
     */
    List<AiflowTaskData> getPendingDataByTaskId(String taskId);

    /**
     * 根据ID查询任务数据基本信息
     *
     * @param id 主键ID
     * @return 任务数据信息
     */
    AiflowTaskData getTaskDataById(Long id);

    /**
     * 根据模型ID删除数据
     *
     * @param modelCode 模型ID
     * @return 是否成功
     */
    boolean deleteByModelCode(String modelCode);

    /**
     * 批量插入任务数据
     *
     * @param taskDataList 任务数据列表
     * @return 是否成功
     */
    boolean batchInsertTaskData(List<AiflowTaskData> taskDataList);

    /**
     * 根据ID更新任务数据
     *
     * @param taskData 任务数据
     * @return 是否成功
     */
    boolean updateTaskDataById(AiflowTaskData taskData);

    /**
     * 根据modelCode和账期删除任务数据
     * @param modelCode
     * @param timest
     * @return
     */
    int deleteTaskDataByModelCodeAndTimest(@Param("modelCode") String modelCode, @Param("timest") String timest);
}
