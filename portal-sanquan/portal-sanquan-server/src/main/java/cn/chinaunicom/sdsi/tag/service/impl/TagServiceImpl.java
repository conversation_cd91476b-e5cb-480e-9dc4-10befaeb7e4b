package cn.chinaunicom.sdsi.tag.service.impl;

import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.chinaunicom.sdsi.tag.entity.TagDb;
import cn.chinaunicom.sdsi.tag.entity.TagDbQuery;
import cn.chinaunicom.sdsi.tag.entity.TagForm;
import cn.chinaunicom.sdsi.tag.mapper.TagMapper;
import cn.chinaunicom.sdsi.tag.service.TagDbService;
import cn.chinaunicom.sdsi.tag.service.TagService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略推荐结果表业务实现类
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
@Slf4j
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {

    @Autowired
    private UnifastContext unifastContext;
    @Autowired
    private TagDbService tagDbService;

    @Override
    public IPage<TagVO> findPage(TagQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return baseMapper.findPage(page, query);
    }

    @Override
    public List<TagVO> findList(TagQuery query) {
        return baseMapper.findList(query);
    }

    @Override
    public int findByTotalCount(TagQuery query) {
        return baseMapper.findByTotalCount(query);
    }

    @Override
    public int findByMonthCount(TagQuery query) {
        return baseMapper.findByMonthCount(query);
    }

    @Override
    public TagVO findOneById(TagQuery query) {
        return baseMapper.findOneById(query);
    }

    @Override
    public TagForm findTagForm(TagQuery query) {
        Tag tag = this.getById(query.getId());
        TagDbQuery tq = new TagDbQuery();
        tq.setTagId(query.getId());
        List<TagDb> list = tagDbService.findList(tq);
        TagForm tagForm = new TagForm();
        tagForm.setTagDbs(list);
        tagForm.setTag(tag);
        return tagForm;
    }

    @Override
    public List<Map<String, Object>> findDbTest(Map<String, Object> params) {
        return this.baseMapper.findDbTest(params);
    }

    @Override
    public void insertColumn(String column) {
        baseMapper.insertColumn(column);
    }

    @Override
    public void insertCustomerId(String date) {
        this.baseMapper.insertCustomerId(date);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean addAll(TagForm tagFrom) {
        Tag entity = tagFrom.getTag();
        //创建大宽表列
        if (StringUtils.isEmpty(entity.getId())) {
            if("1".equals(entity.getCollectionType())){
                // 客户大宽表
                // 判断大宽表是否已有当前列名
                if (baseMapper.tagColumnExist(tagFrom.getTag().getTagName()) == 0) {
                    baseMapper.insertColumn(tagFrom.getTag().getTagName());
                }
            }else {
                // 产品大宽表
                // 判断大宽表是否已有当前列名
                if (baseMapper.tagProductColumnExist(tagFrom.getTag().getTagName()) == 0) {
                    baseMapper.insertProductColumn(tagFrom.getTag().getTagName());
                }
            }
        }
        boolean b = super.saveOrUpdate(entity);
        //查询已有的数据源
        System.err.println(entity.getId() + "<==============================>");
        tagDbService.deleteByTagId(entity.getId());
        //新增数据源
        List<TagDb> list = tagFrom.getTagDbs().stream().filter((item) -> {
            item.setTagId(entity.getId());
            return true;
        }).collect(Collectors.toList());
        tagDbService.saveBatch(list);
        return b;
    }

    @Override
    public void updateSQL(String sql) {
        baseMapper.updateSQL(sql);
    }

    @Override
    public boolean checkExist(TagQuery query) {
        QueryWrapper<Tag> qw = Wrappers.query();
        if (StringUtils.isNotEmpty(query.getId())) {
            qw.lambda().ne(Tag::getId, query.getId());
        }
        if ("code".equals(query.getExistType())) {
            qw.lambda().eq(Tag::getTagCode, query.getTagCode());
        } else {
            qw.lambda().eq(Tag::getTagName, query.getTagName());
        }
        List<Tag> list = baseMapper.selectList(qw);
        return list.isEmpty();
    }

    /**
     * 根据传递的标签的code查询包含的所有标签
     * @param query
     * @return
     */
    @Override
    public int getTagNum(TagVO query) {
        return baseMapper.selectTagNum(query);
    }

    /**
     * 插入新的产品id
     * @param date
     */
    @Override
    public void insertProductId(String date) {
        this.baseMapper.insertProductId(date);
    }


}
