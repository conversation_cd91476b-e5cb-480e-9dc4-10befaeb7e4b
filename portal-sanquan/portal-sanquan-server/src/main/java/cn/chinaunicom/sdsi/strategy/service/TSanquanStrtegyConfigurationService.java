package cn.chinaunicom.sdsi.strategy.service;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyConfiguration;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyConfigurationQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyConfigurationVO;
import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.strategy.entity.StrategyConfig;
import cn.chinaunicom.sdsi.strategy.vo.StrategyNumVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 策略推荐结果表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface TSanquanStrtegyConfigurationService extends IService<TSanquanStrtegyConfiguration> {


    IPage<TSanquanStrtegyConfigurationVO> findPage(TSanquanStrtegyConfigurationQuery query);

    List<TSanquanStrtegyConfigurationVO> findList(TSanquanStrtegyConfigurationQuery query);

    int findByTotalCount(TagQuery query);

    int findByMonthCount(TagQuery query);

    TSanquanStrtegyConfigurationVO findOneById(TSanquanStrtegyConfigurationQuery query);

    boolean addConfig(StrategyConfig config);

    StrategyConfig findByConfigId(String id);

    /***
     * 执行策略配置
     * @param strategyConfigId
     * @return
     */
    boolean execStrategyConfig(String strategyConfigId);

    // 策略数量
    StrategyNumVO getStrategyInfo(TSanquanStrtegyConfigurationQuery query);

    /***
     * 检查营销经理是否符合
     * @param entity
     * @return
     */
    Map<String,Object> checkSubmit(StrategyConfig entity);

    String getDataBaseCron();
}
