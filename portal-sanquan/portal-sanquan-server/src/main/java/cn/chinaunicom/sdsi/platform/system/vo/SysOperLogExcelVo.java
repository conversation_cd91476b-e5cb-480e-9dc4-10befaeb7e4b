package cn.chinaunicom.sdsi.platform.system.vo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 操作日志记录表 oper_log
 * 
 * <AUTHOR>
 */
@Data

public class SysOperLogExcelVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "序号")
    private Long operId;

    /** 操作模块 */
    @ExcelProperty(value = "操作模块")
    private String title;

    /** 业务类型（0其它 1新增 2修改 3删除） */
    //@Excel(name = "业务类型", readConverterExp = "0=其它,1=新增,2=修改,3=删除,4=授权,5=导出,6=导入,7=强退,8=生成代码,9=清空数据")
    @ExcelProperty(value = "业务类型")
    private String businessType;

//    /** 业务类型数组 */
//    @ExcelProperty(value = "业务类型数组")
//    private Integer[] businessTypes;

    /** 请求方法 */
    //@Excel(name = "请求方法")
    @ExcelProperty(value = "请求方法")
    private String method;

    /** 请求方式 */
    //@Excel(name = "请求方式")
    @ExcelProperty(value = "请求方式")
    private String requestMethod;

    /** 操作人员 */
    //@Excel(name = "操作人员")
    @ExcelProperty(value = "操作人员")
    private String operName;

    /** 请求url */
    //@Excel(name = "请求地址")
    @ExcelProperty(value = "请求地址")
    private String operUrl;

    /** 操作地址 */
    //@Excel(name = "操作地址")
    @ExcelProperty(value = "操作地址")
    private String operIp;

    /** 操作地点 */
    //@Excel(name = "操作地点")
    @ExcelProperty(value = "操作地点")
    private String operLocation;

    /** 请求参数 */
    //@Excel(name = "请求参数")
    @ExcelProperty(value = "请求参数")
    private String operParam;

    /** 操作状态（0正常 1异常） */
    //@Excel(name = "状态", readConverterExp = "0=正常,1=异常")
    @ExcelProperty(value = "状态")
    private String status;

    /** 错误消息 */
    //@Excel(name = "错误消息")
    @ExcelProperty(value = "错误消息")
    private String errorMsg;

    /** 操作时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    //@Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "操作时间")
    private String operTime;

    /** 消耗时间 */
    //@Excel(name = "消耗时间", suffix = "毫秒")
    @ExcelProperty(value = "消耗时间")
    private String costTime;

    public String getCostTime()
    {
        return costTime + "毫秒";
    }

    public String getBusinessType(){
        if("0".equals(businessType)){
            return "其他";
        }else if("1".equals(businessType)){
            return "新增";
        }else if("2".equals(businessType)){
            return "修改";
        }else if("3".equals(businessType)){
            return "删除";
        }else if("4".equals(businessType)){
            return "授权";
        }else if("5".equals(businessType)){
            return "导出";
        }else if("6".equals(businessType)){
            return "导入";
        }else if("7".equals(businessType)){
            return "强退";
        }else if("8".equals(businessType)){
            return "生成代码";
        }else if("9".equals(businessType)){
            return "清空数据";
        }
        return businessType;
    }

    public String getStatus(){
        if("0".equals(status)){
            return "正常";
        }else if("1".equals(status)){
            return "异常";
        }
        return status;
    }
}
