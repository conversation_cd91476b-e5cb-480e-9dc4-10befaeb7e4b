package cn.chinaunicom.sdsi.tag.service;

import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.tag.entity.TagDb;
import cn.chinaunicom.sdsi.tag.entity.TagDbQuery;
import cn.chinaunicom.sdsi.tag.entity.TagDbVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 策略推荐结果表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface TagDbService extends IService<TagDb> {

    List<TagDb> findList(@Param("query") TagDbQuery query);

    void deleteByTagId(String tagId);

}
