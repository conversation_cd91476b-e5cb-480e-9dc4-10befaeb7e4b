package cn.chinaunicom.sdsi.common.service;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoReviewCommonExcelVo;
import cn.chinaunicom.sdsi.common.entity.PoiExcelEntity;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

@Service
@Slf4j
public abstract class PoiExcelService {

    private String filePath;

    private Workbook workbook;

    private PoiExcelEntity excelEntity;

    private PoiExcelService poiExcelService;

    /**
     * 子类实现具体渲染sheet页面逻辑
     */
    public abstract void renderWorkbook(Workbook workbook,PoiExcelEntity excelEntity,List... data);

    /**
     * 子类实现特殊参数情况处理
     */
    public abstract void renderWorkbookCell(Workbook workbook,PoiExcelEntity excelEntity,List... data);

    /**
     *  @param poiService
     * @param poiExcelEntity
     * @param data
     */
    public void startExcel(PoiExcelService poiService, PoiExcelEntity poiExcelEntity, List... data) {
        try {
            if(!StringUtils.isNotEmpty(poiExcelEntity.getType())){
                log.error("未设置需要处理的类型");
                return;
            }
            this.poiExcelService = poiService;
            this.excelEntity = poiExcelEntity;
            if("1".equals(poiExcelEntity.getType())){
                this.startType1(poiExcelEntity,data);
            }else if("2".equals(poiExcelEntity.getType())){
                this.startType2(poiExcelEntity,data);
            }else if("3".equals(poiExcelEntity.getType())){

            }else if("4".equals(poiExcelEntity.getType())){

            }

        } finally {
            close();
        }
    }

    public void close(){
        try {
            if (workbook != null) {
                workbook.close();
            }
        } catch (Exception e) {
            log.error("系统错误，poi：",e);
            e.printStackTrace();
        }
    }



    public void startType1(PoiExcelEntity poiExcelEntity,List... data){
        if(!StringUtils.isNotEmpty(poiExcelEntity.getResourceFilePath())){
            log.error("请输入正确的模板路径");
            return;
        }
        ResourceLoader resourceLoader = new DefaultResourceLoader();
        org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:"+poiExcelEntity.getResourceFilePath());// oppo/商机简报数据模板.xlsx
        InputStream is = null;
        try {
            is = resource.getInputStream();

            Workbook workbook = WorkbookFactory.create(is);

            // 渲染数据
            poiExcelService.renderWorkbook(workbook,poiExcelEntity,data);
            poiExcelService.renderWorkbookCell(workbook,poiExcelEntity,data);

            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(poiExcelEntity.getFileName(), "UTF-8"));
            OutputStream out = response.getOutputStream();
            // 写入响应流
            workbook.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("poi 系统错误：" ,e);
            e.printStackTrace();
        }
    }

    public void startType2(PoiExcelEntity poiExcelEntity,List... data){
        try {
            Workbook workbook = new SXSSFWorkbook();

            // 渲染数据
            poiExcelService.renderWorkbook(workbook,poiExcelEntity,data);
            poiExcelService.renderWorkbookCell(workbook,poiExcelEntity,data);

            HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            response.setCharacterEncoding("utf-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(poiExcelEntity.getFileName(), "UTF-8"));
            OutputStream out = response.getOutputStream();
            // 写入响应流
            workbook.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("poi 系统错误：" ,e);
            e.printStackTrace();
        }
    }







}
