package cn.chinaunicom.sdsi.dsbusinessopportunity.controller;


import cn.chinaunicom.sdsi.dsbusinessopportunity.constant.BusinessOpportunityConstant;
import cn.chinaunicom.sdsi.dsbusinessopportunity.entity.DsBusinessAnalyse;
import cn.chinaunicom.sdsi.dsbusinessopportunity.entity.DsOpportunityAnalyse;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.DsBusinessAnalyseService;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.DsOpportunityAnalyseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/dsbusinessopportunity")
public class DsBusinessOpportunityController {
    private static final Logger logger = LoggerFactory.getLogger(DsBusinessOpportunityController.class);

    @Autowired
    private DsBusinessAnalyseService dsBusinessAnalyseService;

    @Autowired
    private DsOpportunityAnalyseService dsOpportunityAnalyseService;

    /**
     * 查询未处理的商机任务并提交异步处理
     */
    @PostMapping("/processbusinesss")
    public ResponseEntity<String> processPendingBusinessAnalyses() {
        List<DsBusinessAnalyse> pendingTasks = dsBusinessAnalyseService.findByStatus(BusinessOpportunityConstant.STATUS_PENDING);
        if (pendingTasks != null && !pendingTasks.isEmpty()) {
            CompletableFuture.runAsync(() -> {
                try {
                    dsBusinessAnalyseService.batchProcessTasksAsync(pendingTasks);
                } catch (Exception e) {
                    logger.error("商机任务处理异常", e);
                }
            });
            return ResponseEntity.ok("提交 " + pendingTasks.size() + " 个待处理商机任务成功");
        } else {
            return ResponseEntity.ok("未查询到待处理商机任务");
        }
    }

    /**
     * 查询未处理的标讯任务并提交异步处理
     */
    @PostMapping("/processopportunities")
    public ResponseEntity<String> processPendingOpportunityAnalyses() {
        List<DsOpportunityAnalyse> pendingTasks = dsOpportunityAnalyseService.findByStatus(BusinessOpportunityConstant.STATUS_PENDING);
        if (pendingTasks != null && !pendingTasks.isEmpty()) {
            CompletableFuture.runAsync(() -> {
                try {
                    dsOpportunityAnalyseService.batchProcessMarkDigitalStatus(pendingTasks);
                } catch (Exception e) {
                    logger.error("标讯任务处理异常", e);
                }
            });
            return ResponseEntity.ok("提交 " + pendingTasks.size() + " 个待处理标讯任务成功");
        } else {
            return ResponseEntity.ok("未查询到待处理标讯任务");
        }
    }
}
