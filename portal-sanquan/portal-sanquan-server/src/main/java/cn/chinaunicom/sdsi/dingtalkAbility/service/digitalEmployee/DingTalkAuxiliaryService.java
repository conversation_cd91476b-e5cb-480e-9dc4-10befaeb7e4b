package cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee;

import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig;
import cn.chinaunicom.sdsi.dingtalkAbility.request.MobileQueryRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.UserMessageRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.response.*;
import cn.chinaunicom.sdsi.dingtalkAbility.util.DingHttpUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.File;


@Service
public class DingTalkAuxiliaryService {


    private final DingHttpUtil httpUtil;

    private final DingTalkConfigCenter configCenter;


    public DingTalkAuxiliaryService(DingHttpUtil httpUtil, DingTalkConfigCenter configCenter) {
        this.httpUtil = httpUtil;
        this.configCenter = configCenter;
    }

    /**
     * 资源上传接口
     * @param group 钉钉分组
     * @param fileType 资源类型(image/voice/video/file)
     * @param file 上传的文件
     * @return 上传结果 错误返回null
     */
    public ResponseEntity<DingTalkBaseResponse<UploadResourceResponse>> uploadResource(String group, String fileType, File file) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/upload/resource";
        return httpUtil.postRequestWithFile(url, config, fileType, file);
    }

//    /**
//     * 网址生成图片并生成资源（接口测试不可用）
//     * @param group 钉钉分组
//     * @param url 要生成图片的网址
//     * @return 生成结果
//     */
//    public ResponseEntity<DingTalkBaseResponse<String>> createHtmlPic(String group, String url) {
//        DingTalkConfig config = configCenter.getConfig(group)
//                .orElseThrow(() -> new RuntimeException("配置不存在"));
//        HtmlPicRequest request = new HtmlPicRequest(url);
//        String apiUrl = configCenter.getBaseUrl() + "/dingtalk/createHtmlPic";
//        return httpUtil.postRequest(apiUrl, config, request, String.class);
//    }

    /**
     * 通过userid收取信息
     * @param group
     * @param request
     * @return
     */
    public ResponseEntity<DingTalkBaseListResponse<UserMessageResponse>> getMessageByUserId(String group, UserMessageRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/message/get";
        return httpUtil.postListRequest(url, config, request, UserMessageResponse.class);
    }

    /**
     * 根据手机号查询钉钉userId
     * @param group
     * @param mobile
     * @return
     */
    public ResponseEntity<DingTalkBaseResponse<MobileQueryResponse>> queryUserIdByMobile(String group, String mobile) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        MobileQueryRequest request = new MobileQueryRequest(mobile);
        String url = configCenter.getBaseUrl() + "/dingtalk/queryUserIdByMobile";
        return httpUtil.postRequest(url, config, request, MobileQueryResponse.class);
    }
}