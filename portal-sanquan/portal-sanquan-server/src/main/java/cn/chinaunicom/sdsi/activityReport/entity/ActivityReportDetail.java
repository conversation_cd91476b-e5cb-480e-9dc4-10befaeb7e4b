package cn.chinaunicom.sdsi.activityReport.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.*;

/**
 * 专项活动报表明细实体类
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ActivityReportDetail {

    @ColumnWidth(30)
    @ExcelProperty(value = "工单编号")
    private String todoCode;
    @ExcelIgnore
    @ExcelProperty(value = "潜在商机id")
    private String opportunityId;
    @ColumnWidth(15)
    @ExcelProperty(value = "自然客户ID")
    private String customerId;
    @ColumnWidth(30)
    @ExcelProperty(value = "自然客户名称")
    private String customerName;
    @ExcelProperty(value = "总部行业室")
    private String industryDept;
    @ColumnWidth(10)
    @ExcelProperty(value = "地市")
    private String cityCode;
    @ColumnWidth(10)
    @ExcelProperty(value = "客户经理执行人")
    private String customerManager;
    @ColumnWidth(10)
    @ExcelProperty(value = "执行人OA工号")
    private String customerManagerId;
    @ColumnWidth(10)
    @ExcelProperty(value = "客户经理执行人手机号码")
    private String executorPhone;
    @ColumnWidth(30)
    @ExcelProperty(value = "策略ID")
    private String strategyId;
    @ColumnWidth(30)
    @ExcelProperty(value = "策略名称")
    private String strategyName;
    @ColumnWidth(20)
    @ExcelProperty(value = "创建时间")
    private String gmtCreate;

    @ExcelIgnore
    private String visitTime;
    @ColumnWidth(20)
    @ExcelProperty(value = {"客户经理反馈","拜访时间"})
    private String gemVisitTime;
    @ColumnWidth(10)
    @ExcelProperty(value = {"客户经理反馈","参访人员角色/姓名"})
    private String visitPerson;
    @ColumnWidth(30)
    @ExcelProperty(value = {"客户经理反馈","访谈内容"})
    private String visitContent;
    @ColumnWidth(30)
    @ExcelProperty(value = {"客户经理反馈","拜访总结"})
    private String visitSummarize;
    @ExcelIgnore
    private String isSignIn;
    @ExcelProperty(value = {"客户经理反馈","是否签到"})
    private String gemIsSignIn;
    @ExcelProperty(value = {"客户经理反馈","签到时间"})
    private String gemSignInStartTime;
    @ExcelIgnore
    private String hasPhoto;
    @ExcelIgnore
    private String isBusiOppWilling;
    @ExcelProperty(value = {"客户经理反馈","是否转换商机"})
    private String gemIsOppo;
    @ExcelIgnore
    private String businessOpportunityAmount;
    @ExcelProperty(value = {"客户经理反馈","商机金额"})
    private String gemEstimatTotalContratAmount;
    @ExcelIgnore
    private String customerDemandSummary;
    @ExcelProperty(value = {"客户经理反馈","客户需求简介"})
    private String gemCustReq;
    @ExcelIgnore
    private String isSigned;
    @ExcelProperty(value = {"客户经理反馈","是否签约"})
    private String gemIsProject;
    @ExcelProperty(value = {"客户经理反馈","反馈状态"})
    private String status;

    @ColumnWidth(30)
    @ExcelProperty(value = {"营销经理反馈","营销经理姓名"})
    private String marketingManager;
    @ColumnWidth(30)
    @ExcelProperty(value = {"营销经理反馈","摸排方式"})
    private String visitMode;
    @ColumnWidth(20)
    @ExcelProperty(value = {"营销经理反馈","走访时间"})
    private String visitDate;
    @ExcelProperty(value = {"营销经理反馈","联系人"})
    private String contactName;
    @ColumnWidth(30)
    @ExcelProperty(value = {"营销经理反馈","反馈内容"})
    private String feedbackContent;
    @ColumnWidth(30)
    @ExcelProperty(value = {"营销经理反馈","摸排反馈内容"})
    private String interviewFeedback;
    @ColumnWidth(30)
    @ExcelProperty(value = {"营销经理反馈","有无意向"})
    private String feedbackType;
    @ExcelProperty(value = {"营销经理反馈","项目所处阶段"})
    private String executionStatus;
    @ExcelProperty(value = {"营销经理反馈","项目金额(万元)"})
    private String projectScale;
    @ExcelProperty(value = {"营销经理反馈","软件金额(万元)"})
    private String softwareScale;
    @ExcelProperty(value = {"营销经理反馈","毛利润(%)"})
    private String grossProfit;
    @ExcelProperty(value = {"营销经理反馈","是否方案支撑"})
    private String isBrace;
    @ExcelProperty(value = {"营销经理反馈","支撑人"})
    private String solutionSupporter;
    @ExcelProperty(value = {"营销经理反馈","支撑时间"})
    private String supportTime;
    @ExcelProperty(value = {"营销经理反馈","支撑方式"})
    private String supportForms;
    @ExcelProperty(value = {"营销经理反馈","补充说明"})
    private String supplementaryExplanation;

    @ExcelIgnore
    @ExcelProperty(value = "产品名称")
    private String productName;

    @ExcelIgnore
    @ExcelProperty(value = "营销经理id")
    private String marketingManagerId;

    @ExcelIgnore
    @ExcelProperty(value = "营销经理反馈状态")
    private String marketingManagerFeedbackStatus;

    @ExcelIgnore
    @ExcelProperty(value = "联系人类别")
    private String contactType;

    @ExcelIgnore
    @ExcelProperty(value = "反馈时间")
    private String updateDate;

    @ExcelIgnore
    @ExcelProperty(value = "关单时间")
    private String closeTime;

    @ExcelIgnore
    @ExcelProperty(value = "客户经理编码")
    private String actualManagerNo;

    @ExcelIgnore
    @ExcelProperty(value = "客户经理名称")
    private String actualManagerName;

    @ExcelIgnore
    @ExcelProperty(value = "客户经理手机号")
    private String actualManagerMobile;



    @ExcelIgnore
    @ExcelProperty(value = "商机数")
    private String businessNum;

    @ExcelIgnore
    @ExcelProperty(value = "战略签约数")
    private String signNum;

    @ExcelIgnore
    @ExcelProperty(value = "预期收入")
    private String expectedIncome;

    @ExcelIgnore
    @ExcelProperty(value = "发展用户数")
    private String developNum;

    @ExcelIgnore
    @ExcelProperty(value = "任务开始时间")
    private String startDate;

    @ExcelIgnore
    @ExcelProperty(value = "业务场景")
    private String businessScene;

    @ExcelIgnore
    @ExcelProperty(value = "反馈触点ID")
    private String touchId;

    @ExcelIgnore
    @ExcelProperty(value = "客户中心拜访ID")
    private String visitId;

    @ExcelIgnore
    @ExcelProperty(value = "集客工号")
    private String userCode;

    @ExcelIgnore
    @ExcelProperty(value = "反馈结果")
    private String visitResult;

    @ExcelIgnore
    @ExcelProperty(value = "反馈时间")
    private String backTime;

    @ExcelIgnore
    @ExcelProperty(value = "是否继续拜访")
    private String isContinueVisit;

    @ExcelIgnore
    @ExcelProperty(value = "无法执行原因")
    private String noVisitReason;

    @ExcelIgnore
    @ExcelProperty(value = "下次执行时间")
    private String nextTime;

    @ExcelIgnore
    @ExcelProperty(value = "拜访主题")
    private String visitTheme;

    @ExcelIgnore
    @ExcelProperty(value = "拜访形式")
    private String visitWay;

    @ExcelIgnore
    @ExcelProperty(value = "拜访地点")
    private String visitSite;

    @ExcelIgnore
    @ExcelProperty(value = "访谈人联系方式")
    private String visitPhone;

    @ExcelIgnore
    @ExcelProperty(value = "重点推荐产品")
    private String keyRecommendedProduct;

    @ExcelIgnore
    @ExcelProperty(value = "是否需要支撑")
    private String isNeedSupport;

    @ExcelIgnore
    @ExcelProperty(value = "客户中心商机ID")
    private String busiOppt;

    @ExcelIgnore
    @ExcelProperty(value = "商机类型")
    private String busiOppType;

    @ExcelIgnore
    @ExcelProperty(value = "商机名称")
    private String busiOpptName;

    @ExcelIgnore
    @ExcelProperty(value = "是否录入异网信息")
    private String isInsertDiff;

    @ExcelIgnore
    @ExcelProperty(value = "运营商")
    private String operator;

    @ExcelIgnore
    @ExcelProperty(value = "业务类型")
    private String businessType;

    @ExcelIgnore
    @ExcelProperty(value = "异网到期时间")
    private String endDate;

    @ExcelIgnore
    @ExcelProperty(value = "异网业务信息描述")
    private String diffBusinessInfo;

    @ExcelIgnore
    @ExcelProperty(value = "附件链接")
    private String pictureUrls;

    @ExcelIgnore
    @ExcelProperty(value = "客户意愿")
    private String customerWillingness;

    @ExcelIgnore
    @ExcelProperty(value = "是否完成续约")
    private String isRenewal;

    @ExcelIgnore
    @ExcelProperty(value = "预计收回时间")
    private String estimatedRecoveryTime;

    @ExcelIgnore
    @ExcelProperty(value = "是否催缴成功")
    private String isPaymentSuccess;

    @ExcelIgnore
    @ExcelProperty(value = "是否销账")
    private String isWriteOff;

    @ExcelIgnore
    @ExcelProperty(value = "是否续约成功")
    private String isRenewalSuccess;

    @ExcelIgnore
    @ExcelProperty(value = "续约内容")
    private String renewalContent;

    @ExcelIgnore
    @ExcelProperty(value = "是否存在离网倾向")
    private String isOffLineTendency;

    @ExcelIgnore
    @ExcelProperty(value = "业务到期预警反馈_客户意愿")
    private String customerWillingnessForBusiness;

    @ExcelIgnore
    @ExcelProperty(value = "业务到期预警反馈_是否完成续约")
    private String isCompleteRenewalForBusiness;

    @ExcelIgnore
    @ExcelProperty(value = "收入波动反馈_是否存在离网倾向")
    private String isOffLineTendencyForIncome;

    @ExcelIgnore
    @ExcelProperty(value = "零产原因反馈")
    private String zeroProductionReason;

    @ExcelIgnore
    @ExcelProperty(value = "下一步动作")
    private String nextStep;

    @ExcelIgnore
    @ExcelProperty(value = "是否本楼客户")
    private String isBuildingCustomer;

    @ExcelIgnore
    @ExcelProperty(value = "是否修改客户入位信息")
    private String isModifyCustomerCheckInfo;

    @ExcelIgnore
    @ExcelProperty(value = "是否录入拜访记录")
    private String isVisitId;

    @ExcelIgnore
    @ExcelProperty(value = "是否有效")
    private String isValid;

    @ExcelIgnore
    @ExcelProperty(value = "无效原因")
    private String invalidReason;

    @ExcelIgnore
    @ExcelProperty(value = "签到位置")
    private String signInPlace;

    @ExcelIgnore
    @ExcelProperty(value = "签到开始时间")
    private String signInStartTime;

    @ExcelIgnore
    @ExcelProperty(value = "签到结束时间")
    private String signInEndTime;

    @ExcelIgnore
    @ExcelProperty(value = "反馈额外信息关联ID")
    private String extraInfoLink;

    @ExcelIgnore
    @ExcelProperty(value = "创建人ID")
    private String createUserId;

    @ExcelIgnore
    @ExcelProperty(value = "创建人姓名")
    private String createUserName;

    @ExcelIgnore
    @ExcelProperty(value = "修改时间")
    private String gmtModified;

    @ExcelIgnore
    @ExcelProperty(value = "修改人姓名")
    private String updateUserName;

    @ExcelIgnore
    @ExcelProperty(value = "修改人ID")
    private String updateUserId;
}
