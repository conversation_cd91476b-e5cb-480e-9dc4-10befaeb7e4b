package cn.chinaunicom.sdsi.leaderVisit.custVisit.mapper;

import cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity.LeaderVisitCust;
import cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.query.LeaderVisitCustQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 大客户经理拜访表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@Mapper
public interface LeaderVisitCustMapper extends BaseMapper<LeaderVisitCust> {

    // 分页查询
    IPage<LeaderVisitCust> findPage(@Param("page") IPage page, @Param("query") LeaderVisitCustQueryVo leaderVisitCustVo);

    // 查询列表
    List<LeaderVisitCust> findList(@Param("query") LeaderVisitCustQueryVo leaderVisitCustVo);
}
