package cn.chinaunicom.sdsi.private5GWorkflow.utils;

import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.SM4;
import org.springframework.util.Base64Utils;

/**
 * @program: sanquan_server
 * @ClassName WyjCryptUtils
 * @description:  沃易建加密解密工具
 * @author: majian
 * @date: 2025-04-16 17:53
 * @Version 1.0
 **/
public class WyjCryptUtils {
    private static volatile WyjCryptUtils instance;
    private String appId;
    private String requestPublicKey;
    private String requestPrivateKey;

    private String responsePublicKey;
    private String responsePrivateKey;
    private WyjCryptUtils(String appId, String requestPublicKey, String requestPrivateKey,String responsePublicKey,String responsePrivateKey) {
        this.appId = appId;
        this.requestPublicKey = requestPublicKey;
        this.requestPrivateKey = requestPrivateKey;
        this.responsePublicKey = responsePublicKey;
        this.responsePrivateKey = responsePrivateKey;
    }
    public static WyjCryptUtils getInstance(String appId, String requestPublicKey, String requestPrivateKey,String responsePublicKey,String responsePrivateKey) {
        if (instance == null) {
            synchronized (WyjCryptUtils.class) {
                if (instance == null) {
                    instance = new WyjCryptUtils(appId, requestPublicKey, requestPrivateKey, responsePublicKey, responsePrivateKey);
                }
            }
        }
        return instance;
    }

    /**
     * 加密方法
     * @param data
     * @return
     */
    public WyjCryptEntity encrypt(String data) {
        // 获取密钥明文
        String securityKey = SmUtils.generateSecurityKey();
        // SM4 工具
        SM4 sm4 = new SM4(securityKey.getBytes());
        // 使用sm4 + securityKey 生成数据密文
        String securityData = Base64Utils.encodeToString(sm4.encrypt(data.getBytes()));
        // 创建SM2 工具
        SM2 sm = new SM2(SmUtils.base642Key(requestPrivateKey), SmUtils.base642Key(requestPublicKey));
        // sm2 加密密钥
        byte[] encrypt = sm.encrypt(securityKey.getBytes(), KeyType.PublicKey);
        String securityKeyBase64 = Base64Utils.encodeToString(encrypt);
        WyjCryptEntity entity = new WyjCryptEntity().setEncryptData(securityData).setEncryptSecurityKey(securityKeyBase64);
        return entity;
    }

    /**
     * 解密方法
     * @param encryptData
     * @param securityKey
     * @return
     */
    public WyjCryptEntity decrypt(String encryptData,String securityKey) {
        byte[] securityKeyBytes = Base64Utils.decodeFromString(securityKey);
        SM2 sm = new SM2(SmUtils.base642Key(responsePrivateKey), SmUtils.base642Key(responsePublicKey));
        byte[] clearSecurityKeyBytes = sm.decrypt(securityKeyBytes,KeyType.PrivateKey);
        String decryptSecurityKey = new String(clearSecurityKeyBytes);
        SM4 sm4 = new SM4(decryptSecurityKey.getBytes());
        String decryptData = new String(sm4.decrypt(Base64Utils.decodeFromString(encryptData)));
        WyjCryptEntity entity = new WyjCryptEntity().setDecryptData(decryptData).setDecryptSecurityKey(decryptSecurityKey);
        return entity;
    }
}
