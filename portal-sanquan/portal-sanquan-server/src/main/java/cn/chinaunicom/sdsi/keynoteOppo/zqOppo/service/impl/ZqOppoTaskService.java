package cn.chinaunicom.sdsi.keynoteOppo.zqOppo.service.impl;

import cn.chinaunicom.sdsi.businessOppoPushBrief.service.BusinessOppoPushBriefService;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoTrackQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoTrackVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.query.ZqOppoPoolsQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.vo.ZqOppoPoolsVo;
import cn.chinaunicom.sdsi.common.sms.SendSmsUtils;
import cn.chinaunicom.sdsi.dingding.util.DingDingPushMes;
import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkContact;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkApiFactory;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkApiService;
import cn.chinaunicom.sdsi.dingtalkAbility.service.DingTalkContactService;
import cn.chinaunicom.sdsi.framework.utils.OsUtil;
import cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.service.OppoTrackService;
import cn.chinaunicom.sdsi.keynoteOppo.zqOppo.service.ZqOppoPoolsService;
import cn.chinaunicom.sdsi.marketing.entity.TSanquanMarketingManager;
import cn.chinaunicom.sdsi.marketing.queryvo.TSanquanMarketingManagerQueryVO;
import cn.chinaunicom.sdsi.marketing.service.TSanquanMarketingManagerService;
import cn.chinaunicom.sdsi.platform.sysConfig.SysConfigNum;
import cn.chinaunicom.sdsi.platform.sysConfig.service.TSanquanSysConfigService;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.platform.user.service.IUserService;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("zqOppoTaskService")
@Slf4j
public class ZqOppoTaskService {

    @Autowired
    ZqOppoPoolsService poolsService;

    @Autowired
    private SendSmsUtils sendSmsUtils;

    @Autowired
    private OppoTrackService trackService;

    @Autowired
    private IUserService userService;

    @Autowired
    private TSanquanSysConfigService sysConfigService;

    @Autowired
    private TSanquanMarketingManagerService marketingManagerService;

    @Autowired
    private BusinessOppoPushBriefService businessOppoPushBriefService;

    @Resource
    private DingTalkApiFactory dingTalkApiFactory;

    @Autowired
    DingDingPushMes pushMesUtil;

    String errorStr = "";

    String groupName = "businessOppo"; // 商机支撑营销经理

    String bizName = "oppoZhiCheng";// 商机支撑业务，标识

    String groupOppoContract = "oppoLeaderContract";// 商机支撑营销经理领导 （赵志超，张茜）

    String groupOppoJc = "oppoDeptJc";// 商机支撑 集成交付部门
    String groupOppoYy = "oppoDeptYy";// 商机支撑 运营服务部门
    String groupOppoCy = "oppoDeptCy";// 商机支撑 产品研发部门

    /**
     * 推送营销经理（下午2,4,6），或领导（下午3点），未处理任务
     * @return
     */
    public String pushCustomerMsg(){
        log.info("产品营销营销经理未处理提醒任务进来了，pushCustomerMsg");
        if(OsUtil.isWindows()){
            groupName = "sanquan";
        }else{
            groupName = "businessOppo";
        }
        Boolean isExpire = false;
        LocalTime currentTime = LocalTime.now();
        if (currentTime.getHour() == 15 ) {
            isExpire = true;
        }
        DingTalkApiService dingTalkApiService = dingTalkApiFactory.getBaseService(false);

        ZqOppoPoolsQueryVo queryVo = new ZqOppoPoolsQueryVo();
        queryVo.setStatus("0");
        List<ZqOppoPoolsVo> poolsVos = poolsService.findList(queryVo);
        if(poolsVos.size()>0){
            List<String> expireCity = Lists.newArrayList();
            Map<String, List<ZqOppoPoolsVo>> cityToPoolsMap = poolsVos.stream()
                    .collect(Collectors.groupingBy(vo -> vo.getCity() != null ? vo.getCity() : "未知城市"
                    ));
            Boolean finalIsExpire = isExpire;
            cityToPoolsMap.forEach((city, poolsList) -> {
                if(poolsList.size()>0){
                    if(!finalIsExpire){// 推送营销经理，提醒任务
                        String str = city+"市今日尚有"+poolsList.size()+"个商机未处理，请您及时处理！";
                        if(poolsList.size()>0){
                            String yesterday = LocalDate.now().minusDays(2).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                            List<ZqOppoPoolsVo> expirePools = poolsList.stream().filter(vo -> yesterday.equals(vo.getDayId())).collect(Collectors.toList());
                            StringBuilder expireStr = new StringBuilder();
                            expireStr.append("其中昨日未处理商机有：");
                            if(expirePools.size() > 0){
                                for (ZqOppoPoolsVo expirePool : expirePools) {
                                    expireStr.append(expirePool.getOppoName()+",");
                                }
                                String sss = expireStr.toString().substring(0,expireStr.length()-1);
                                str += sss;
                            }
                        }
                        String msgRes = pushMsg(city,str);
                    }
                    expireCity.add(city+"("+poolsList.size()+"个)");
                }else{
                    log.info("地市{}今日没有未处理的商机",city);
                }
            });
            if(isExpire){
                // 13点未处理的任务，给领导 赵志超 发送短信+钉钉提醒。发一次
                String cityStr = StringUtils.join(expireCity, ", ");
                String str = "您好，"+cityStr+", 今日尚有未处理的商机已超时，请关注！";


                String batchId = UUID.fastUUID().toString();
                List<String> mobileList = this.getDeptPhone(groupOppoContract);// 营销经理领导
//                    mobileList.add("15624569654");// 张志超
//                    mobileList.add("18615310188"); // 张茜
                if(mobileList.size() > 0){
                    if(OsUtil.isWindows()){
                        String value = sysConfigService.getValueByKey(SysConfigNum.KEY_NODE_LEADER.getTypeCode());
                        pushMesUtil.pushUserMsg("商机提醒",str,StringUtils.isNotEmpty(value) ? value : "***********",groupName);
                        pushMesUtil.closeToken();
                    }else{
                        int len = dingTalkApiService.sendTextMessageByMobile(groupOppoContract, mobileList, str, batchId, bizName, "产品商机超时提醒", true);
                    }
                }else{
                    log.error("营销领导未配置，code：" + groupOppoContract);
                }
                mobileList.forEach(item->{
                    sendSmsUtils.sendSMSNotStoreShuangxian(item , str ,"产品商机支撑(领导)");
                });

            }
        }else{
            log.info("非常好，山东省未处理的商机任务为空");
        }
        return "响应成功";
    }

    /**
     * 业务部门晚上8点提醒
     * @return
     */
    public String pushDepartmentMsg(){
        if(OsUtil.isWindows()){
            groupName = "sanquan";
        }else{
            groupName = "businessOppo";
        }
        log.info("产品营销三部门未处理提醒任务进来了，pushDepartmentMsg");
        String resStr = null;

        DingTalkApiService dingTalkApiService = dingTalkApiFactory.getBaseService(false);
        String batchId = UUID.fastUUID().toString();

        List<ZqOppoPoolsVo> poolsVos = this.pushDeptTask("jc");
        List<String> jcPhoneList = this.getDeptPhone(groupOppoJc);
        if(poolsVos.size()>0 && jcPhoneList.size() > 0){
            String str = "您好，您今日尚有"+poolsVos.size()+"个未处理的商机评审任务，请及时处理";
            if(OsUtil.isWindows()){
                jcPhoneList.forEach(item->{
                    String msgRes = pushMesUtil.pushUserMsg("商机评审",str,"***********",groupName);
                });
            }else{
                int len = dingTalkApiService.sendTextMessageByMobile(groupName, jcPhoneList, str, batchId, bizName, "产品商机支撑集成交付未处理提醒", true);
                jcPhoneList.forEach(item->{
                    sendSmsUtils.sendSMSNotStoreShuangxian(item,str,"产品商机支撑(集成交付)");
                });
            }
        }else{
            resStr = "集成部门都已处理";
        }

        poolsVos = this.pushDeptTask("yy");
        List<String> yyPhoneList = this.getDeptPhone(groupOppoYy);
        if(poolsVos.size()>0 && yyPhoneList.size()>0){
            String str = "您好，您今日尚有"+poolsVos.size()+"个未处理的商机评审任务，请及时处理";
            if(OsUtil.isWindows()){
                for (String s : yyPhoneList) {
                    String msgRes = pushMesUtil.pushUserMsg("商机评审",str,"***********",groupName);
                }
            }else{
                dingTalkApiService.sendTextMessageByMobile(groupName, yyPhoneList, str, batchId, bizName, "产品商机支撑运营服务未处理提醒", true);
                for (String s : yyPhoneList) {
                    sendSmsUtils.sendSMSNotStoreShuangxian(s,str,"产品商机支撑(运营服务)");
                }
            }
        }else{
            resStr += " | 运营部门都已处理";
        }

        poolsVos = this.pushDeptTask("cy");
        List<String> cyPhoneList = this.getDeptPhone(groupOppoCy);
        if(poolsVos.size()>0 && cyPhoneList.size()>0){
            String str = "您好，您今日尚有"+poolsVos.size()+"个未处理的商机评审任务，请及时处理";
            if(OsUtil.isWindows()){
                cyPhoneList.forEach(item->{
                    String msgRes = pushMesUtil.pushUserMsg("商机评审",str,"***********",groupName);
                });
            }else{
                dingTalkApiService.sendTextMessageByMobile(groupName, cyPhoneList, str, batchId, bizName, "产品商机支撑产品研发未处理提醒", true);
                cyPhoneList.forEach(item->{
                    sendSmsUtils.sendSMSNotStoreShuangxian(item,str,"产品商机支撑(产品研发)");
                });
            }
        }else{
            resStr += " | 产研部门都已处理";
        }
        return resStr;
    }

    /**
     * 上周五到本周四未进行跟踪的任务，进行营销经理提醒
     * @return
     */
    public String pushCustomerTrackMsg(){
        if(OsUtil.isWindows()){
            groupName = "sanquan";
        }else{
            groupName = "businessOppo";
        }
        log.info("产品营销商机跟踪提醒任务进来了，pushCustomerTrackMsg");
        String[] errorMsg = {""};
        LocalDate lastFriday = LocalDate.now().with(DayOfWeek.FRIDAY);
        if (!lastFriday.isBefore(LocalDate.now())) { // 如果计算出的周五是今天或之后
            lastFriday = lastFriday.minusWeeks(1); // 减去一周
        }
        // 计算本周四
        LocalDate thisThursday = lastFriday.plusDays(7);
        OppoTrackQueryVo vo = new OppoTrackQueryVo();
        vo.setStartTime(lastFriday.toString());
        vo.setEndTime(thisThursday.toString() + " 23:59:59");
        List<OppoTrackVo> list = trackService.getLastWeekUnTrack(vo);

        DingTalkApiService dingTalkApiService = dingTalkApiFactory.getBaseService(false);
        if(list.size()>0){
            Map<String, List<OppoTrackVo>> cityResultMap = list.stream()
                    .collect(Collectors.groupingBy(OppoTrackVo::getCity));
            cityResultMap.forEach((city, trackList) -> {

                TSanquanMarketingManagerQueryVO queryVO = new TSanquanMarketingManagerQueryVO();
                queryVO.setCity(city);
                List<TSanquanMarketingManager> managerList = marketingManagerService.getManagerList(queryVO);

                String msg = "";
                int total = 0;
                for (OppoTrackVo oppoTrackVo : trackList) {
                    total += Integer.parseInt(oppoTrackVo.getTotal());
                }
                if(total > 0){
                    msg = ""+city+"市有"+total+"个有效商机需要您跟踪，其中";
                    Map<String, List<OppoTrackVo>> resultMap = trackList.stream()
                            .filter(track -> track.getSjgjlxResult() != null && !track.getSjgjlxResult().isEmpty())
                            .collect(Collectors.groupingBy(OppoTrackVo::getSjgjlxResult));
                    System.out.println("结果：" + resultMap.get("重点跟踪"));
                    msg += "重点跟踪" + (StringUtils.isEmpty(resultMap.get("重点跟踪")) ? 0 : resultMap.get("重点跟踪").size())  + "个，";
                    msg += "继续跟进" + (StringUtils.isEmpty(resultMap.get("继续跟进")) ? 0 : resultMap.get("继续跟进").size())  + "个，";
                    msg += "摸查需求" + (StringUtils.isEmpty(resultMap.get("摸查需求")) ? 0 : resultMap.get("摸查需求").size())  + "个，";

                    List<String> cyPhoneList = Lists.newArrayList();
                    for (TSanquanMarketingManager tSanquanMarketingManager : managerList) {
                        if(OsUtil.isWindows()){
                            String mmm = pushMesUtil.pushUserMsg("商机跟踪提醒",msg,"***********",groupName);
                        }else{
                            cyPhoneList.add(tSanquanMarketingManager.getTelphone());
                            sendSmsUtils.sendSMSNotStoreShuangxian(tSanquanMarketingManager.getTelphone(),msg,"营销经理(跟踪)");
                        }
                    }
                    if(cyPhoneList.size()>0){
                        String batchId = UUID.fastUUID().toString();
                        dingTalkApiService.sendTextMessageByMobile(groupName, cyPhoneList, msg, batchId, bizName, "产品商机支撑营销经理未跟踪提醒", true);
                    }else{
                        log.error("地市"+city+",上周没有未跟踪的任务");
                    }
                }else{
                    log.info("地市"+city+"没有需要跟踪的商机");
                }

            });
        }
        return null;
    }

    private List<String> getDeptPhone(String groupName){
        DingTalkContactService dingTalkContactService = dingTalkApiFactory.getContactService();
        List<DingTalkContact> contactList = dingTalkContactService.getContactByGroup(groupName);
        List<String> mobileList = Lists.newArrayList();
        contactList.forEach(item->{
            mobileList.add(item.getMobile());
        });
        return mobileList;
    }

    private String pushMsg(String city,String msg){
        DingTalkApiService dingTalkApiService = dingTalkApiFactory.getBaseService(false);
        DingTalkContactService dingTalkContactService = dingTalkApiFactory.getContactService();
        String batchId = UUID.fastUUID().toString();

        TSanquanMarketingManagerQueryVO queryVO = new TSanquanMarketingManagerQueryVO();
        queryVO.setCity(city);
        String[] errorStr = {""};
        List<TSanquanMarketingManager> managerList = marketingManagerService.getManagerList(queryVO);
        if(managerList.size()>0){
            managerList.forEach(item->{
                if(StringUtils.isNotEmpty(item.getTelphone())){
                    if(OsUtil.isWindows()){
                        String res = pushMesUtil.pushUserMsg("商机评审提醒",msg,"***********",groupName);
                        pushMesUtil.closeToken();
                    }else{
                        int len = dingTalkApiService.sendTextMessageByMobile(groupName, Arrays.asList(item.getTelphone()), msg, batchId, bizName, "产品商机支撑营销经理未处理提醒", true);
                        sendSmsUtils.sendSMSNotStoreShuangxian(item.getTelphone(),msg,"产品商机支撑(营销经理)");
                    }
                }else{
                    log.error("营销经理{}未查询到手机号", JSON.toJSONString(item));
                    errorStr[0] = "营销经理"+item.getMarketingManagerOa()+"未查询到手机号";
                }
            });
        }else{
            log.error("地市{}未查询到营销经理，请排查",city);
            errorStr[0] = "地市"+city+"未查询到营销经理";
        }
        return errorStr[0];
    }


    private  List<ZqOppoPoolsVo> pushDeptTask(String statusType){
        ZqOppoPoolsQueryVo queryVo = new ZqOppoPoolsQueryVo();
        queryVo.setStatus("1");
        if("jc".equals(statusType)){
            queryVo.setStatusJC("0");
        }else if("yy".equals(statusType)){
            queryVo.setStatusYY("0");
        }else if("cy".equals(statusType)){
            queryVo.setStatusCY("0");
        }
        List<ZqOppoPoolsVo> poolsVos = poolsService.findList(queryVo);
        return poolsVos;
    }

    /*private  String getDeptPhone(String statusType){
        String jobNum = "";
        if("jc".equals(statusType)){
            jobNum = sysConfigService.getValueByKey(SysConfigNum.KEY_NODE_JC.getTypeCode());
        }else if("yy".equals(statusType)){
            jobNum = sysConfigService.getValueByKey(SysConfigNum.KEY_NODE_YY.getTypeCode());
        }else if("cy".equals(statusType)){
            jobNum = sysConfigService.getValueByKey(SysConfigNum.KEY_NODE_CY.getTypeCode());
        }
        if(StringUtils.isNotEmpty(jobNum)){
            UserVo userVo = userService.findByJobNum(jobNum);
            return userVo.getPhone();
        }else{
            return null;
        }
    }*/

}
