package cn.chinaunicom.sdsi.holiday.service.impl;

import cn.chinaunicom.sdsi.cloud.holiday.entity.Holiday;
import cn.chinaunicom.sdsi.cloud.holiday.query.HolidayQueryVo;
import cn.chinaunicom.sdsi.holiday.mapper.HolidayMapper;
import cn.chinaunicom.sdsi.holiday.service.HolidayService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 节假日表（包含周末、法定假日、调休等） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-23
 */
@Service
public class HolidayServiceImpl extends ServiceImpl<HolidayMapper, Holiday> implements HolidayService {

    private static final ZoneId SHANGHAI_ZONE = ZoneId.of("Asia/Shanghai");

    @Autowired
    public UnifastContext unifastContext;

    /**
     * 计算两个 LocalDateTime 之间的实际工作时间（排除休息日）
     * @param start 开始时间
     * @param end 结束时间
     * @param holidayVo 节假日查询条件
     * @return 工作小时数（如 3.5 小时）
     */
    @Override
    public double calculateWorkingHours(LocalDateTime start, LocalDateTime end, HolidayQueryVo holidayVo) {
        if (start.isAfter(end)) {
            return 0.0;
        }
        // 1. 计算总小时数（包括所有时间）
        double totalHours = Duration.between(start, end).toMinutes() / 60.0;
        // 2. 获取节假日列表（带空值保护）
        Set<LocalDate> holidayDates = Optional.ofNullable(baseMapper.findList(holidayVo))
                .orElse(Collections.emptyList())
                .stream()
                .map(Holiday::getHolidayDate)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 3. 计算包含的节假日天数
        long holidayDays = 0;
        LocalDate currentDate = start.toLocalDate();
        LocalDate endDate = end.toLocalDate();
        while (!currentDate.isAfter(endDate)) {
            if (holidayDates.contains(currentDate)) {
                holidayDays++;
            }
            currentDate = currentDate.plusDays(1);
        }
        // 4. 扣除节假日对应的小时数（每个节假日按24小时计算）
        totalHours -= holidayDays * 24;
        // 确保结果不为负数
        return Math.max(0, totalHours);
    }

    /**
     * 从开始时间累计增加指定天数（自动跳过节假日）
     * @param start 开始时间
     * @param addDays 需要增加的基础天数
     * @param holidayVo 节假日查询条件
     * @return 累计增加指定天数后的最终日期（已跳过所有节假日）
     */
    @Override
    public Date calculateSkipHoliday(Date start, int addDays, HolidayQueryVo holidayVo) {
        // 边界条件处理：开始时间为空或增加天数小于等于0，直接返回开始时间
        if (start == null || addDays <= 0) {
            return start;
        }
        // 1. 查询节假日并转换为LocalDate集合（上海时区）
        Set<LocalDate> holidayDates = Optional.ofNullable(baseMapper.findList(holidayVo))
                .orElse(Collections.emptyList())
                .stream()
                .map(Holiday::getHolidayDate)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 2. 将开始时间转换为上海时区的LocalDate（保留原始时间信息）
        LocalDateTime currentDateTime = LocalDateTime.ofInstant(start.toInstant(), SHANGHAI_ZONE);
        LocalDate currentDate = currentDateTime.toLocalDate();
        // 3. 累计增加天数，遇到节假日则额外加1天
        int accumulatedDays = 0;
        while (accumulatedDays < addDays) {
            currentDate = currentDate.plusDays(1);
            if (holidayDates.contains(currentDate)) {
                continue;
            }
            accumulatedDays++;
        }
        // 4. 构建最终日期（保留原始时间信息）
        LocalDateTime resultDateTime = currentDate.atTime(
                currentDateTime.getHour(),
                currentDateTime.getMinute(),
                currentDateTime.getSecond(),
                currentDateTime.getNano()
        );
        return Date.from(resultDateTime.atZone(SHANGHAI_ZONE).toInstant());
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-23
     * @param holidayVo
     * @return IPage<Holiday>
     **/
    @Override
    public IPage<Holiday> findPage(HolidayQueryVo holidayVo) {
        IPage page = QueryVoToPageUtil.toPage(holidayVo);
        return baseMapper.findPage(page, holidayVo);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-23
     * @param id
     * @return Holiday
     **/
    @Override
    public Holiday findOne(String id) {
        return baseMapper.selectById(id);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-23
     * @return List<Holiday>
     **/
    @Override
    public List<Holiday> findList(HolidayQueryVo holidayVo) {
        return baseMapper.findList(holidayVo);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-23
     * @param holiday
     * @return int
     **/
    @Override
    public int add(Holiday holiday) {
        return baseMapper.insert(holiday);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-23
     * @param holiday
     * @return int
     **/
    @Override
    public int update(Holiday holiday) {
        return baseMapper.updateById(holiday);
    }

    /**
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-23
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
