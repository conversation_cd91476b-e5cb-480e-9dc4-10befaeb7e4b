package cn.chinaunicom.sdsi.policeStationStatistics.entity;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_sanquan_m_police_station_statistics")
public class PoliceStationStatistics implements Serializable {

    @ExcelProperty(title = "派出所编号", cellSort = 0, width = 5000)
    private String customerId;
    @ExcelProperty(title = "派出所名称", cellSort = 1, width = 5000)
    private String customerName;
    private String cityCode;
    @ExcelProperty(title = "地市", cellSort = 2, width = 5000)
    private String cityName;
    private String districtCode;
    @ExcelProperty(title = "区县", cellSort = 3, width = 5000)
    private String districtName;
    @ExcelProperty(title = "年累收入", cellSort = 4, width = 5000)
    private String yearIncome;
    @ExcelProperty(title = "业务发展数量", cellSort = 5, width = 5000)
    private String businessDevelopCount;
    @ExcelProperty(title = "业务号码数量", cellSort = 6, width = 5000)
    private String businessNumberCount;
    @ExcelProperty(title = "ICT项目收入", cellSort = 7, width = 5000)
    private String ictIncome;
    private String monthId;
    private String dayId;

}
