package cn.chinaunicom.sdsi.private5GWorkflow.controller;

import cn.chinaunicom.sdsi.private5GWorkflow.entity.FiveGOrderData;
import cn.chinaunicom.sdsi.private5GWorkflow.entity.FiveGOrderQueryDTO;
import cn.chinaunicom.sdsi.private5GWorkflow.entity.SyncOrderInfoRequest;
import cn.chinaunicom.sdsi.private5GWorkflow.entity.SyncOrderInfoResponse;
import cn.chinaunicom.sdsi.private5GWorkflow.service.FiveGOrderService;
import cn.chinaunicom.sdsi.private5GWorkflow.service.OrderSyncService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 5G端到端需求对外开放的接口
 */
@RestController
@RequestMapping("/custOperation")
public class Private5GApiController {
    private static final Logger logger = LoggerFactory.getLogger(Private5GApiController.class);
    @Autowired
    private FiveGOrderService fiveGOrderService;

    @Autowired
    private OrderSyncService orderSyncService;

    /**
     * 专线开通列表查询接口
     * @param queryDTO 查询参数
     * @return 查询结果
     */
    @PostMapping("/qryLineOpenList")
    public JSONObject queryLineOpenList(@RequestBody FiveGOrderQueryDTO queryDTO) {
        JSONObject response = new JSONObject(true);
        try {
            IPage<FiveGOrderData> result = fiveGOrderService.queryFiveGOrderList(queryDTO);
            JSONObject data = new JSONObject();
            data.put("totalRecords", result.getTotal());
            data.put("currentPage", result.getCurrent());
            data.put("pageSize", result.getSize());
            List<JSONObject> records = new ArrayList<>();
            for (FiveGOrderData order : result.getRecords()) {
                JSONObject record = new JSONObject(true);
                record.put("area", order.getArea());
                record.put("lineOpenOrderId", order.getLineOpenOrderId());
                record.put("lineAcceptTime", order.getLineAcceptTime());
                record.put("customerName", order.getCustomerName());
                record.put("installationAddress", order.getInstallationAddress());
                record.put("serviceNumber", order.getServiceNumber());
                record.put("productName", order.getProductName());
                records.add(record);
            }
            data.put("records", records);
            response.put("rspCode", "0000");
            response.put("rspInfo", "查询成功");
            response.put("data", data);
            logger.info("[Private5GApi] 专线开通列表查询成功，总记录数：{}", result.getTotal());
        } catch (Exception e) {
            logger.error("[Private5GApi] 专线开通列表查询异常", e);
            response.put("rspCode", "9999");
            response.put("rspInfo", "系统异常：" + e.getMessage());
        }
        return response;
    }

    /**
     * 订单信息同步接口
     */
    @PostMapping("/syncOrderInfo")
    public JSONObject syncOrderInfo(@RequestBody SyncOrderInfoRequest request) {
        JSONObject response = new JSONObject();
        try {
            SyncOrderInfoResponse syncResponse = orderSyncService.syncOrderInfo(request);
            response.put("rspCode", syncResponse.getStatus());
            response.put("rspInfo", syncResponse.getMessage());
            response.put("data", null);
            logger.info("订单信息同步成功，订单号：{}", request.getOrderId());
        } catch (Exception e) {
            logger.error("订单信息同步异常", e);
            response.put("rspCode", "9999");
            response.put("rspInfo", "系统异常");
        }
        return response;
    }
}
