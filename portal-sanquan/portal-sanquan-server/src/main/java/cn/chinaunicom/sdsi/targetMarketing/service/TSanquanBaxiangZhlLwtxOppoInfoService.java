package cn.chinaunicom.sdsi.targetMarketing.service;

import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanBaxiangZhlLwtxOppoInfo;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanBaxiangZhlLwtxOppoInfoVO;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanBaxiangZhlLwtxOppoInfoQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 靶向营销联网通信拉动业务详情表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
public interface TSanquanBaxiangZhlLwtxOppoInfoService extends IService<TSanquanBaxiangZhlLwtxOppoInfo> {

    /**
     * 分页查询靶向营销联网通信拉动业务详情表
     * 
     * @param tSanquanBaxiangZhlLwtxOppoInfoQueryVO
     * @return IPage<TSanquanBaxiangZhlLwtxOppoInfoVO>
     */
    IPage<TSanquanBaxiangZhlLwtxOppoInfoVO> findPage(TSanquanBaxiangZhlLwtxOppoInfoQueryVO tSanquanBaxiangZhlLwtxOppoInfoQueryVO);

    /**
     * 查询靶向营销联网通信拉动业务详情表详细信息
     *
     * @param superiorPolicyCode
     * @return TSanquanBaxiangZhlLwtxOppoInfoVO
     */
    TSanquanBaxiangZhlLwtxOppoInfoVO findInfo(String superiorPolicyCode);

    /**
     * 新增靶向营销联网通信拉动业务详情表
     *
     * @param tSanquanBaxiangZhlLwtxOppoInfoVO
     * @return String
     */
    String add(TSanquanBaxiangZhlLwtxOppoInfoVO tSanquanBaxiangZhlLwtxOppoInfoVO);

    /**
     * 修改靶向营销联网通信拉动业务详情表
     *
     * @param tSanquanBaxiangZhlLwtxOppoInfoVO
     * @return Boolean
     */
    Boolean update(TSanquanBaxiangZhlLwtxOppoInfoVO tSanquanBaxiangZhlLwtxOppoInfoVO);

    /**
     * 删除靶向营销联网通信拉动业务详情表
     *
     * @param superiorPolicyCode
     * @return Boolean
     */
    Boolean delete(String superiorPolicyCode);

}
