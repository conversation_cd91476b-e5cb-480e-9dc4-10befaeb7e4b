package cn.chinaunicom.sdsi.dsbusinessopportunity.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import cn.chinaunicom.sdsi.aiability.constant.InternalDeepSeekConstant;
import cn.chinaunicom.sdsi.aiability.utils.AITextRegexUtils;
import cn.chinaunicom.sdsi.aiability.utils.InternalDeepSeekUtils;
import cn.chinaunicom.sdsi.dsbusinessopportunity.constant.BusinessOpportunityConstant;
import cn.chinaunicom.sdsi.dsbusinessopportunity.dao.DsOpportunityAnalyseMapper;
import cn.chinaunicom.sdsi.dsbusinessopportunity.entity.DsOpportunityAnalyse;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.AsyncProcessingService;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.DsOpportunityAnalyseService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 标讯数据服务实现类
 */
@Service
public class DsOpportunityAnalyseServiceImpl implements DsOpportunityAnalyseService {
    private static final Logger logger = LoggerFactory.getLogger(DsOpportunityAnalyseServiceImpl.class);
    @Autowired
    private DsOpportunityAnalyseMapper dsOpportunityAnalyseMapper;
    @Autowired
    private AsyncProcessingService asyncProcessingService;
    @Autowired
    private InternalDeepSeekUtils internalDeepSeekUtils;
    private static final int MARK_DIGITAL_BATCH_SIZE = 20;

    /**
     * 根据ID查询标讯信息
     */
    @Override
    public DsOpportunityAnalyse findById(Long id) {
        return dsOpportunityAnalyseMapper.findById(id);
    }

    /**
     * 根据状态查询标讯列表
     */
    @Override
    public List<DsOpportunityAnalyse> findByStatus(String status) {
        return dsOpportunityAnalyseMapper.findByStatus(status);
    }

    /**
     * 根据ID更新标讯信息
     */
    @Override
    public boolean updateMatchInfo(DsOpportunityAnalyse dsOpportunityAnalyse) {
        return dsOpportunityAnalyseMapper.updateMatchInfo(dsOpportunityAnalyse) > 0;
    }

    /**
     * 根据ID列表批量更新digital_flag
     */
    @Override
    public boolean batchUpdateDigitalFlagByIds(List<Long> ids, String digitalFlag) {
        return dsOpportunityAnalyseMapper.batchUpdateDigitalFlagByIds(ids, digitalFlag) > 0;
    }

    /**
     * 根据ID列表批量更新status
     */
    @Override
    public boolean batchUpdateStatusByIds(List<Long> ids, String status) {
        return dsOpportunityAnalyseMapper.batchUpdateStatusByIds(ids, status) > 0;
    }

    /**
     * 批量打标是否数字化项目
     * 分批处理，减轻系统压力，避免卡住
     *
     * @param tasks
     */
    @Override
    public void batchProcessMarkDigitalStatus(List<DsOpportunityAnalyse> tasks) {
        // 大批次处理，每批处理100个任务
        int batchSize = 100;
        int totalTasks = tasks.size();
        logger.info("开始处理标讯任务，总任务数: {}", totalTasks);
        for (int batchStart = 0; batchStart < totalTasks; batchStart += batchSize) {
            int batchEnd = Math.min(batchStart + batchSize, totalTasks);
            List<DsOpportunityAnalyse> currentBatch = tasks.subList(batchStart, batchEnd);
            logger.info("处理批次 {}/{} ({}%), 当前批次任务数: {}",
                    (batchStart / batchSize) + 1,
                    (totalTasks + batchSize - 1) / batchSize,
                    String.format("%.2f", (batchEnd * 100.0 / totalTasks)),
                    currentBatch.size());
            // 处理当前批次
            processBatch(currentBatch);
            // 等待一段时间再处理下一批
            if (batchEnd < totalTasks) {
                try {
                    logger.info("批次处理完成，等待3秒后处理下一批");
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.error("任务处理被中断", e);
                    break;
                }
            }
        }
        logger.info("所有标讯任务处理完成，总数: {}", totalTasks);
    }

    /**
     * 处理单个批次的数据
     *
     * @param tasks 当前批次的任务
     */
    private void processBatch(List<DsOpportunityAnalyse> tasks) {
        List<DsOpportunityAnalyse> waitSubmitTasks = new ArrayList<>();
        int length = tasks.size();
        int requestLength = 0;
        for (int i = 0; i < length; i++) {
            try {
                DsOpportunityAnalyse analyseTask = tasks.get(i);
                this.updateMatchInfo(new DsOpportunityAnalyse().setId(analyseTask.getId()).setStatus(BusinessOpportunityConstant.STATUS_PROCESSING));
                waitSubmitTasks.add(analyseTask);
                String opportunityTitle = analyseTask.getOpportunityTitle();
                String opportunityContent = analyseTask.getOpportunityContent();
                requestLength = requestLength + (StringUtils.isNotEmpty(opportunityTitle)?opportunityTitle.length()+1:0) + (StringUtils.isNotEmpty(opportunityContent)?opportunityContent.length()+1:0);
                if ((length == 1 || i != 0) && (i % MARK_DIGITAL_BATCH_SIZE == 0 || i == length - 1 || requestLength > InternalDeepSeekConstant.MAX_REQUEST_LENGTH)) {
                    JSONArray array = new JSONArray();
                    waitSubmitTasks.stream().forEach(item -> {
                        array.add(new JSONObject() {{
                            put("数据ID", item.getId());
                            put("标讯标题", item.getOpportunityTitle());
                            put("标讯内容", item.getOpportunityContent());
                        }});
                    });
                    StringBuffer querySb = new StringBuffer("请根据标讯标题、标讯内容判断标讯信息是否为数字化项目，返回结果为json，key为数据ID，value为是或否");
                    querySb.append(JSONArray.toJSONString(array));
                    try {
                        long startTime = System.currentTimeMillis();
                        String answer = internalDeepSeekUtils.ask(querySb.toString());
                        String jsonStr = AITextRegexUtils.extractJson(answer);
                        JSONObject resultJson = JSONObject.parseObject(jsonStr);
                        List<Long> digitalIds = new ArrayList<>();
                        List<Long> nonDigitalIds = new ArrayList<>();
                        for (Map.Entry<String, Object> entry : resultJson.entrySet()) {
                            Long dataId = Long.parseLong(entry.getKey());
                            String result = entry.getValue().toString();
                            if ("是".equals(result)) {
                                digitalIds.add(dataId);
                            } else {
                                nonDigitalIds.add(dataId);
                            }
                        }
                        long processTime = System.currentTimeMillis() - startTime;
                        logger.info("小批次处理完成，处理数量: {}，用时: {} ms", waitSubmitTasks.size(), processTime);
                        if (!digitalIds.isEmpty()) {
                            List<DsOpportunityAnalyse> dataList = waitSubmitTasks.stream().filter(item -> digitalIds.contains(item.getId())).collect(Collectors.toList());
                            batchUpdateDigitalFlagByIds(digitalIds, BusinessOpportunityConstant.DIGITAL_FLAG);
                            // 这里改为逐个提交任务，避免并发过高
                            processDigitalTasksOneByOne(dataList);
                        }
                        if (!nonDigitalIds.isEmpty()) {
                            batchUpdateDigitalFlagByIds(nonDigitalIds, BusinessOpportunityConstant.NOT_DIGITAL_FLAG);
                            batchUpdateStatusByIds(nonDigitalIds, BusinessOpportunityConstant.STATUS_SUCCESS);
                        }
                    } catch (Exception e) {
                        logger.error("处理批次数据失败", e);
                        List<Long> failIds = waitSubmitTasks.stream().map(item -> item.getId()).collect(Collectors.toList());
                        batchUpdateStatusByIds(failIds, BusinessOpportunityConstant.STATUS_FAIL);
                    } finally {
                        array.clear();
                        waitSubmitTasks.clear();
                        requestLength = 0;
                    }
                }
            }catch (Exception e){
                logger.error("处理批次数据失败", e);
            }
        }
    }

    /**
     * 逐个处理数字化标讯任务，避免并发过高
     *
     * @param tasks 待处理的任务
     */
    private void processDigitalTasksOneByOne(List<DsOpportunityAnalyse> tasks) {
        logger.info("开始处理数字化标讯任务，数量: {}", tasks.size());
        int count = 0;

        for (DsOpportunityAnalyse task : tasks) {
            asyncProcessingService.processOpportunityAnalyseTask(task);
            count++;

            // 每处理5个任务输出一次日志
            if (count % 5 == 0 || count == tasks.size()) {
                logger.info("数字化标讯处理进度: {}/{}", count, tasks.size());
            }

            // 每提交一个任务等待一小段时间，避免瞬间提交过多
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.error("数字化标讯处理被中断", e);
                break;
            }
        }
    }

    /**
     * 多线程批量处理提取标讯关键词任务
     * 该方法保留但不再直接使用，由processDigitalTasksOneByOne代替
     */
    @Override
    public void batchProcessDigitalTasksAsync(List<DsOpportunityAnalyse> tasks) {
        for (DsOpportunityAnalyse task : tasks) {
            asyncProcessingService.processOpportunityAnalyseTask(task);
        }
    }
}
