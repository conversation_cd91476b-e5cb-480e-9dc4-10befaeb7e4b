package cn.chinaunicom.sdsi.model.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo;
import cn.chinaunicom.sdsi.model.service.DRosterNatureCustomerInfoService;
import cn.chinaunicom.sdsi.model.mapper.DRosterNatureCustomerInfoMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 针对表【t_sanquan_d_roster_nature_customer_info】的数据库操作Service实现
 * @createDate 2024-09-24 16:44:58
 */
@Service
public class DRosterNatureCustomerInfoServiceImpl extends ServiceImpl<DRosterNatureCustomerInfoMapper, DRosterNatureCustomerInfo>
        implements DRosterNatureCustomerInfoService {

    @Override
    public DRosterNatureCustomerInfo findByOrgCode(String orgCode) {
        return baseMapper.findByOrgCode(orgCode);
    }
    @Override
    public DRosterNatureCustomerInfo findByTydm(String tydm) {
        return baseMapper.findByTydm(tydm);
    }

    @Override
    public DRosterNatureCustomerInfo findByNatrueCustId(String natureCustomerId) {
        return baseMapper.findByNatrueCustId(natureCustomerId);
    }
    @Override
    public DRosterNatureCustomerInfo findByNatrueCustName(String natureCustomerName) {
        return baseMapper.findByNatrueCustName(natureCustomerName);
    }
}




