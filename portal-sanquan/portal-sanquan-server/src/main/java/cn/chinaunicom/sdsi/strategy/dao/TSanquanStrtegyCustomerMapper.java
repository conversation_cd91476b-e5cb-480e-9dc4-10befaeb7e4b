package cn.chinaunicom.sdsi.strategy.dao;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyCustomer;
import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyProduct;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyCustomerQuery;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyProductQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyCustomerVO;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyProductVO;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 *
 */
@Mapper
public interface TSanquanStrtegyCustomerMapper extends BaseMapper<TSanquanStrtegyCustomer> {

    IPage<TSanquanStrtegyCustomerVO> findPage(@Param("page") IPage page, @Param("query") TSanquanStrtegyCustomerQuery query);

    List<TSanquanStrtegyCustomerVO> findList( @Param("query") TSanquanStrtegyCustomerQuery query);

    int findByTotalCount( @Param("query") TagQuery query);
    int findByMonthCount( @Param("query") TagQuery query);
    TSanquanStrtegyCustomerVO findOneById( @Param("query") TSanquanStrtegyCustomerQuery query);
}
