package cn.chinaunicom.sdsi.opportunity.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;

/**
 * @Description: 潜在机会(商机)表对象 t_sanquan_potential_opportunity
 * @Author: hanruxiao
 * @Date: 2024-04-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "潜在机会(商机)表对象", description = "潜在机会(商机)表对象")
@TableName("t_sanquan_potential_opportunity")
public class TSanquanPotentialOpportunity extends BaseEntity {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "策略id")
    private String strategyId;

    @Schema(name = "策略推荐结果id")
    private String strategyResultId;

    @Schema(name = "客户id")
    private String customerId;

    @Schema(name = "客户名称")
    private String customerName;

    @Schema(name = "产品id")
    private String productId;

    @Schema(name = "产品名称")
    private String productName;

    @Schema(name = "产品名称2")
    private String productName2;

    @Schema(name = "所属行业")
    private String industry;

    @Schema(name = "是否已读 0:未读 1:已读")
    private Long isRead;

    @Schema(name = "是否推送 0:未推送 1:已推送")
    private Long isPush;

    @Schema(name = "是否推送地市接口人 0:未推送 1:已推送")
    private Long isPushCity;

    @Schema(name = "是否已审核 0:未审核 1:已审核")
    private Long isAudit;

    @Schema(name = "推送时间")
    private Date pushTime;

    @Schema(name = "查看时间")
    private Date viewTime;

    @Schema(name = "预留字段1")
    private String attr1;

    @Schema(name = "预留字段2")
    private String attr2;

    @Schema(name = "预留字段3")
    private String attr3;

    @Schema(name = "产品类型")
    private String productType;

    @Schema(name = "推荐描述")
    private String pushDescribe;

    @Schema(name = "产品支撑人名称")
    private String productBraceUserName;

    @Schema(name = "产品支撑人电话")
    private String productBraceUserPhone;

    @Schema(name = "客户经理")
    private String customerManager;

    @Schema(name = "地市")
    private String cityCode;

    @Schema(name = "账期")
    private String monthId;

    @Schema(name = "客户经理ID")
    private String customerManagerId;

    @Schema(name = "推送编码保证唯一")
    private String pushTodoCode;

    @Schema(name = "地市接口人")
    private String cityInterfacePerson;

    @Schema(name = "0未执行 1执行中  2已反馈  9退回")
    private String status;

    @Schema(name = "模型类别")
    private String modelType;

    @Schema(name = "模型id")
    private String modelId;

    @Schema(name = "模型业务分类")
    private String modelBusinessType;

    @Schema(name = "主推产品id")
    private String mainPushProductId;

    @Schema(name = "主推产品")
    private String mainPushProductName;

    @Schema(name = "热销产品id")
    private String hotSaleProductId;

    @Schema(name = "热销产品")
    private String hotSaleProductName;

    @Schema(name = "辅推产品id")
    private String assistPushProductId;

    @Schema(name = "辅推产品")
    private String assistPushProductName;

    @Schema(description = "推荐业务类型")
    private String referralBusinessType;

    @Schema(description = "行业主推")
    private String industryPromotion;

    @Schema(description = "客户维度 1-自然客户；2-实体客户；3-用户；4-楼宇；5-未知类型")
    private String customerDimension;

    @Schema(description = "客户属性 1:商企客户 0:政企要客")
    private String customerAttributes;

    @Schema(description = "省分行业")
    private String provincialBranch;

    @Schema(description = "执行人")
    private String executor;

    @Schema(description = "执行人电话")
    private String executorPhone;

    @Schema(description = "执行人OA账号（工号）")
    private String executorOaId;

    @Schema(description = "名单制客户ID")
    private String rosterCustomerId;

    @Schema(description = "名单客户名称")
    private String rosterCustomerName;

    @Schema(description = "客户地市编码")
    private String customerCityCode;

    @Schema(description = "客户区县编码")
    private String customerDistrictsCode;

    @Schema(description = "客户地市名称")
    private String customerCityName;

    @Schema(description = "客户区县名称")
    private String customerDistrictsName;

    @Schema(description = "预留字段1")
    private String mobo1;

    @Schema(description = "预留字段2")
    private String mobo2;

    @Schema(description = "预留字段3")
    private String mobo3;

    @Schema(description = "预留字段4")
    private String mobo4;

    @Schema(description = "预留字段5")
    private String mobo5;

    @Schema(description = "预留字段6")
    private String mobo6;

    @Schema(description = "任务（0、不是，1、是）")
    private String isTask;

    @Schema(description = "任务名称")
    private String taskName;

    @Schema(description = "任务Id")
    private String taskId;

    @Schema(name = "监控状态（0、正常，1、预警，2、催办，3、督办,4、关单）")
    private String monitorStatus;

    @Schema(name = "客户类型：0-自然客户；1-派出所")
    private String customerType;

    //图片
    private String imgText;

    //来源
    private String source;

    private String industryCustomerName;

    private String isWeight;

    private String importFlag;

    @Schema(description = "行业细分")
    private String subdivisionIndustry;

    @Schema(description = "备注")
    private String remark;

    @TableField(exist = false)
    @Schema(description = "客户组织机构编码")
    private String customerOrgCode;

    @Schema(name = "是否推送营销经理：0：未推送、1：已推送")
    private Long isPushMarketing;

    @Schema(name = "产品详情")
    private String productDetail;

    @Schema(name = "挖掘类型：ai:ai挖掘、self:自己挖掘")
    private String dagType;
}
