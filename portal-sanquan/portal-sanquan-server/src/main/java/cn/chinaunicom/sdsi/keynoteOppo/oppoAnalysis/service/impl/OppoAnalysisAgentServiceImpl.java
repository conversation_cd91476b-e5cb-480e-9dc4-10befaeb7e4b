package cn.chinaunicom.sdsi.keynoteOppo.oppoAnalysis.service.impl;

import cn.chinaunicom.sdsi.aiability.config.AIThreadPoolConfig;
import cn.chinaunicom.sdsi.aiability.service.BishengAgentService;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.constant.OppoAnalysisConstant;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.dto.OppoMarkDto;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.dto.OppoReviewAnalysisDto;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.entity.OppoMarkPools;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoAnalysis.entity.OppoReviewAnalysis;
import cn.chinaunicom.sdsi.keynoteOppo.oppoAnalysis.service.IOppoAnalysisAgentService;
import cn.chinaunicom.sdsi.keynoteOppo.oppoAnalysis.service.IOppoMarkService;
import cn.chinaunicom.sdsi.keynoteOppo.oppoAnalysis.service.IOppoReviewAnalysisService;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;

/**
 * @program: sanquan_server
 * @ClassName OppoAnalysisAgentServiceImpl
 * @description: 商机研判相关智能体调用
 * @author: majian
 * @date: 2025-06-30 16:52
 * @Version 1.0
 **/
@Service
public class OppoAnalysisAgentServiceImpl implements IOppoAnalysisAgentService {
    @Autowired
    private BishengAgentService bishengAgentService;
    @Autowired
    private IOppoReviewAnalysisService oppoReviewAnalysisService;
    @Autowired
    private IOppoMarkService oppoMarkService;

    /**
     * 调用商机研判-总结商机建议智能体
     */
    @Override
    @Async(AIThreadPoolConfig.TASK_EXECUTOR_NAME)
    public void handleOppoReviewAnalysis(OppoReviewAnalysis param) {
        JSONObject request = new JSONObject() {{
                put("sjgjjyJc", param.getSjgjjyJc());
                put("sjgjjyYy", param.getSjgjjyYy());
                put("sjgjjyCy", param.getSjgjjyCy());
        }};
        String query = JSONObject.toJSONString(request);
        JSONObject response = invokeOppoReviewAnalysisAgent(query);
        String sjgjlxResult = response.getString("sjgjlxResult");
        OppoReviewAnalysisDto analisysDto = new OppoReviewAnalysisDto().setId(param.getId()).setSjgjjyResult(sjgjlxResult);
        oppoReviewAnalysisService.updateOppoReview(analisysDto);
    }

    /**
     * 调用商机标记AI分析智能体
     */
    @Override
    public void handleOppoMarkAnalysis(OppoMarkPools param) {
        JSONObject request = new JSONObject() {{
                put("oppoNumber", param.getOppoNumber());
                put("oppoName", param.getOppoName());
                put("khxqjj", param.getKhxqjj());
        }};
        String query = JSONObject.toJSONString(request);
        JSONObject response = invokeOppoMarkAnalysisAgent(query);
        String markType = response.getString("markType");
        String reqKeyword = response.getString("reqKeyword");
        String productAi = response.getString("productAi");
        OppoMarkDto updateDto = new OppoMarkDto();
        updateDto.setOppoNumber(param.getOppoNumber());
        updateDto.setMarkType(markType);
        updateDto.setReqKeyword(reqKeyword);
        updateDto.setProductAi(productAi);
        if("纯软件商机".equals(markType)||"软硬结合商机".equals(markType)){
            updateDto.setFirstAuditPass("1");
        }else {
            updateDto.setFirstAuditPass("0");
        }
        updateDto.setMarkStatus("1");
        oppoMarkService.updateOppoMarkStatus(updateDto);
    }

    /**
     * 调用商机研判-总结商机建议智能体
     *
     * @param query
     * @return
     */
    private JSONObject invokeOppoReviewAnalysisAgent(String query) {
        JSONObject inputNode = new JSONObject();
        inputNode.put("user_input", query);
        JSONObject input = new JSONObject();
        input.put("input_676de", inputNode);
        Map<String, Object> result = bishengAgentService.invokeWorkflow(OppoAnalysisConstant.OPPO_REVIEW_ANALYSIS_FLOW_ID, input);
        String content = (String) result.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        Set<String> keys = contentJSON.keySet();
        JSONObject analysisResult = new JSONObject();
        for (String key : keys) {
            String str = contentJSON.getString(key);
            JSONObject obj = JSONObject.parseObject(str);
            analysisResult.putAll(obj);
        }
        return analysisResult;
    }

    /**
     * 调用商机标记AI分析智能体
     *
     * @param query
     * @return
     */
    private JSONObject invokeOppoMarkAnalysisAgent(String query) {
        JSONObject inputNode = new JSONObject();
        inputNode.put("user_input", query);
        JSONObject input = new JSONObject();
        input.put("input_ea98f", inputNode);
        Map<String, Object> result = bishengAgentService.invokeWorkflow(OppoAnalysisConstant.OPPO_MARK_ANALYSIS_FLOW_ID, input);
        String content = (String) result.get("content");
        JSONObject contentJSON = JSONObject.parseObject(content);
        Set<String> keys = contentJSON.keySet();
        JSONObject analysisResult = new JSONObject();
        for (String key : keys) {
            String str = contentJSON.getString(key);
            JSONObject obj = JSONObject.parseObject(str);
            analysisResult.putAll(obj);
        }
        return analysisResult;
    }
}
