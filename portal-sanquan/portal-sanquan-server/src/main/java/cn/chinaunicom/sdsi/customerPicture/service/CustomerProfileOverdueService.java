package cn.chinaunicom.sdsi.customerPicture.service;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOverdue;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 三全客户画像-回款信息-客户逾期表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
public interface CustomerProfileOverdueService extends IService<CustomerProfileOverdue> {

    // 根据id查询
    CustomerProfileOverdue findOne(String id);

    // 查询列表
    List<CustomerProfileOverdue> findList();


}
