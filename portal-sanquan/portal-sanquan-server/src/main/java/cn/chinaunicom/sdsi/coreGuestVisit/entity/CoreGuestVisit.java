package cn.chinaunicom.sdsi.coreGuestVisit.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 三全维度拜访名单表 - 数据表实体类
 */
@Data
@TableName("t_sanquan_DIM_MDZ_BFLIST1")
public class CoreGuestVisit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 地市
     */
    @TableField("AREA_NAME")
    private String areaName;

    /**
     * 名单制ID
     */
    @TableField("ROSTER_CUSTOMER_ID")
    private String rosterCustomerId;

    /**
     * 名单制名称
     */
    @TableField("ROSTER_CUSTOMER_NAME")
    private String rosterCustomerName;

    /**
     * 名单制等级
     */
    @TableField("FLAG")
    private String flag;

    /**
     * 自然客户ID
     */
    @TableField("NATURE_CUST_ID")
    private String natureCustId;

    /**
     * 自然客户名称
     */
    @TableField("NATURE_CUST_NAME")
    private String natureCustName;

    /**
     * 拜访ID
     */
    @TableField("RECORD_ID")
    private String recordId;

    /**
     * 拜访时间
     */
    @TableField("VISIT_TIME")
    private String visitTime;

    /**
     * 拜访月份
     */
    @TableField("VISIT_TIME_NEW")
    private String visitTimeNew;

    /**
     * 客户经理姓名
     */
    @TableField("CUST_MANAGER_NAME")
    private String custManagerName;

    /**
     * 客户经理工号
     */
    @TableField("CUST_MANAGER_CODE")
    private String custManagerCode;

    /**
     * 商机编码
     */
    @TableField("OPPO_NUMBER")
    private String oppoNumber;

    /**
     * 商机金额
     */
    @TableField("ESTIMAT_TOTAL_CONTRAT_AMOUNT")
    private String estimatTotalContratAmount;

    /**
     * 商机创建时间
     */
    @TableField("OPPO_CREATETIME")
    private String oppoCreatetime;

    /**
     * 账期
     */
    @TableField("DAY_ID")
    private String dayId;
}