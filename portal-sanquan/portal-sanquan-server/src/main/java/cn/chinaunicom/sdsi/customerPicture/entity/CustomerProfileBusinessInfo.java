package cn.chinaunicom.sdsi.customerPicture.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * 三全客户画像-客户业务信息-收入金额表
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@TableName("t_sanquan_customer_profile_business_info")
public class CustomerProfileBusinessInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;
    private String natureCustId;
    private String natureCustName;
    private String accountFee;
    private String accountPeriod;

    public String getAccountFee() {
        if(StringUtils.isNotBlank(accountFee)){
            return new BigDecimal(accountFee).setScale(2, RoundingMode.HALF_UP).toPlainString();
        }
        return accountFee;
    }

    public void setAccountFee(String accountFee) {
        this.accountFee = accountFee;
    }
}
