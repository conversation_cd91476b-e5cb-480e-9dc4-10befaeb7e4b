package cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.controller;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoReviewHistory;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoReviewHistoryQueryVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.service.OppoReviewHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import cn.chinaunicom.sdsi.framework.base.BaseController;
/**
 * <p>
 * 商机评审表(商机库表)(历史表) 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@RequestMapping("/oppoWarehouse/reviewHistory")
public class OppoReviewHistoryController extends BaseController {

    @Autowired
    private OppoReviewHistoryService oppoReviewHistoryService;

    /**
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-07-24
     * @param oppoReviewHistoryVo
     * @return BasePageResponse<OppoReviewHistory>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<OppoReviewHistory> findPage(OppoReviewHistoryQueryVo oppoReviewHistoryVo){
        return pageOk(oppoReviewHistoryService.findPage(oppoReviewHistoryVo));
    }

    /**
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-07-24
     * @param id
     * @return BaseResponse<OppoReviewHistory>
     **/
    @GetMapping("/findOne")
    public BaseResponse<OppoReviewHistory> findOne(String id) {
        return ok(oppoReviewHistoryService.findOne(id));
    }

    /**
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-07-24
     * @return BaseResponse<List<OppoReviewHistory>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<OppoReviewHistory>> findList(OppoReviewHistoryQueryVo oppoReviewHistoryVo) {
        return ok(oppoReviewHistoryService.findList(oppoReviewHistoryVo));
    }

    /**
     * <AUTHOR>
     * @description 新增
     * @since 2025-07-24
     * @param oppoReviewHistory
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody OppoReviewHistory oppoReviewHistory){
        return ok(oppoReviewHistoryService.add(oppoReviewHistory));
    }

    /**
     * <AUTHOR>
     * @description 修改
     * @since 2025-07-24
     * @param oppoReviewHistory
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody OppoReviewHistory oppoReviewHistory) {
        return ok(oppoReviewHistoryService.update(oppoReviewHistory));
    }

    /**
     * <AUTHOR>
     * @description 删除
     * @since 2025-07-24
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(oppoReviewHistoryService.delete(id));
    }
}
