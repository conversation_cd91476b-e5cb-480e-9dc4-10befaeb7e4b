package cn.chinaunicom.sdsi.chengxiaoNew.vo;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class EmailDistrictSummaryMarketExcelVo implements Serializable {

    @ColumnWidth(20)
    @ExcelProperty(value = "策略")
    String sendMarket;

    @ColumnWidth(20)
    @ExcelProperty(value = "任务数量")
    String sendNum;

}
