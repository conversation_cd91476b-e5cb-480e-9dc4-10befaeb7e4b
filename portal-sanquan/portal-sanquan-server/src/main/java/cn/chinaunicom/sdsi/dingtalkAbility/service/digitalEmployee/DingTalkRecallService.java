package cn.chinaunicom.sdsi.dingtalkAbility.service.digitalEmployee;

import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig;
import cn.chinaunicom.sdsi.dingtalkAbility.request.BatchRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.GroupRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.PrivateChatRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.RecallWorkMessageRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.response.RecallCommonResponse;
import cn.chinaunicom.sdsi.dingtalkAbility.response.DingTalkBaseResponse;
import cn.chinaunicom.sdsi.dingtalkAbility.util.DingHttpUtil;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
public class DingTalkRecallService {

    private final DingHttpUtil httpUtil;

    private final DingTalkConfigCenter configCenter;

    public DingTalkRecallService(DingHttpUtil httpUtil, DingTalkConfigCenter configCenter) {
        this.configCenter = configCenter;
        this.httpUtil = httpUtil;
    }

    /**
     * 撤回工作通知消息
     */
    public ResponseEntity<DingTalkBaseResponse<RecallWorkMessageRequest>> recallWorkMessage(String group, String msgTaskId) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        RecallWorkMessageRequest request = new RecallWorkMessageRequest(msgTaskId);
        String url = configCenter.getBaseUrl() + "/dingtalk/recall/work/message";
        return httpUtil.postRequest(url, config, request, RecallWorkMessageRequest.class);
    }

    /**
     * 批量撤回人与机器人会话消息
     */
    public ResponseEntity<DingTalkBaseResponse<RecallCommonResponse>> batchRecallRobotMessages(String group, BatchRecallRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/batchRecall";
        if (request.getRobotCode() == null || request.getRobotCode().isEmpty()) {
            request.setRobotCode(config.getRobotCode());
        }
        return httpUtil.postRequest(url, config, request, RecallCommonResponse.class);
    }

    /**
     * 撤回人与人会话中的机器人消息
     */
    public ResponseEntity<DingTalkBaseResponse<RecallCommonResponse>> recallPrivateChatMessages(String group, PrivateChatRecallRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/privateChatMessagesBatchRecall";
        if (request.getRobotCode() == null || request.getRobotCode().isEmpty()) {
            request.setRobotCode(config.getRobotCode());
        }
        if (request.getOpenConversationId() == null || request.getOpenConversationId().isEmpty()) {
            request.setOpenConversationId(config.getOpenConversationId());
        }
        return httpUtil.postRequest(url, config, request, RecallCommonResponse.class);
    }

    /**
     * 撤回内部群消息
     */
    public ResponseEntity<DingTalkBaseResponse<RecallCommonResponse>> recallGroupMessages(String group, GroupRecallRequest request) {
        DingTalkConfig config = configCenter.getConfig(group)
                .orElseThrow(() -> new RuntimeException("配置不存在"));
        String url = configCenter.getBaseUrl() + "/dingtalk/groupMessagesBatchRecall";
        if (request.getRobotCode() == null || request.getRobotCode().isEmpty()) {
            request.setRobotCode(config.getRobotCode());
        }
        if (request.getOpenConversationId() == null || request.getOpenConversationId().isEmpty()) {
            request.setOpenConversationId(config.getOpenConversationId());
        }
        return httpUtil.postRequest(url, config, request, RecallCommonResponse.class);
    }

}