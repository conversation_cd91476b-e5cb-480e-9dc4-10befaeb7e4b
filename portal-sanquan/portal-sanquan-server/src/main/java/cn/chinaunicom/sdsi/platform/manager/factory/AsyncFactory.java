package cn.chinaunicom.sdsi.platform.manager.factory;

import cn.chinaunicom.sdsi.framework.utils.SpringUtils;
import cn.chinaunicom.sdsi.framework.utils.ip.AddressUtils;
import cn.chinaunicom.sdsi.platform.service.entity.SysOperLog;
import cn.chinaunicom.sdsi.platform.system.service.ISysOperLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TimerTask;

/**
 * 异步工厂（产生任务用）
 *
 * <AUTHOR>
 */
public class AsyncFactory
{
    private static final Logger sys_user_logger = LoggerFactory.getLogger("sys-user");

    /**
     * 操作日志记录
     *
     * @param operLog 操作日志信息
     * @return 任务task
     */
    public static TimerTask recordOper(final SysOperLog operLog)
    {
        return new TimerTask()
        {
            @Override
            public void run()
            {
                // 远程查询操作地点
                operLog.setOperLocation(AddressUtils.getOnlineRealAddressByIP(operLog.getOperIp()));
                SpringUtils.getBean(ISysOperLogService.class).insertOperlog(operLog);
            }
        };
    }
}
