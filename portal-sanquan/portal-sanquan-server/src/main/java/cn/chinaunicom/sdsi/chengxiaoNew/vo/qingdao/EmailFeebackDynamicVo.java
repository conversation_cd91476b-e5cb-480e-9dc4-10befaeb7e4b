package cn.chinaunicom.sdsi.chengxiaoNew.vo.qingdao;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/26 11:12
 */
@Data
public class EmailFeebackDynamicVo implements Serializable {

    @ExcelIgnore
    String dataSource;

    @ExcelIgnore
    String feedbackId;

    /*地市*/
    @ColumnWidth(20)
    String fieldCode;

    /*任务数量*/
    @ColumnWidth(20)
    String fieldName;

    /*执行数量*/
    @ColumnWidth(20)
    String fieldValue;


}
