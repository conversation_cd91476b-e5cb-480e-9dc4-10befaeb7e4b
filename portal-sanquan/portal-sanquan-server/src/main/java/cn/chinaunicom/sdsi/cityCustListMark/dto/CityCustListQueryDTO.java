package cn.chinaunicom.sdsi.cityCustListMark.dto;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;

import java.io.Serializable;
import java.util.List;

/**
 * 客户列表查询DTO
 *
 * <AUTHOR>
 */
public class CityCustListQueryDTO extends BaseQueryVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 名单制客户经理OA
     */
    private String managerOa;
    /**
     * 自然客户名称
     */
    private String natureCustName;
    /**
     * 自然客户ID列表
     */
    private List<String> natureCustIds;
    /**
     * 地市名称
     */
    private String city;

    public String getManagerOa() {
        return managerOa;
    }

    public CityCustListQueryDTO setManagerOa(String managerOa) {
        this.managerOa = managerOa;
        return this;
    }

    public String getNatureCustName() {
        return natureCustName;
    }

    public CityCustListQueryDTO setNatureCustName(String natureCustName) {
        this.natureCustName = natureCustName;
        return this;
    }

    public List<String> getNatureCustIds() {
        return natureCustIds;
    }

    public CityCustListQueryDTO setNatureCustIds(List<String> natureCustIds) {
        this.natureCustIds = natureCustIds;
        return this;
    }

    public String getCity() {
        return city;
    }

    public CityCustListQueryDTO setCity(String city) {
        this.city = city;
        return this;
    }
}
