package cn.chinaunicom.sdsi.dingding.controller;

import cn.chinaunicom.sdsi.activityReport.dto.ActivityReportConfigDto;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportConfig;
import cn.chinaunicom.sdsi.dingding.service.DayProductYXMarketingManagerInfoService;
import cn.chinaunicom.sdsi.dingding.util.DingDingUtil;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingManagerInfoQueryVo;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingManagerInfoVo;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.alibaba.excel.ExcelWriter;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.io.*;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 产品营销日报-营销经理
 */
@RestController
@RequestMapping("/productDay/manager")
@Slf4j
public class DayProductYXMarketingManagerInfoController extends BaseController {

    @Autowired
    DayProductYXMarketingManagerInfoService managerInfoService;

    @Autowired
    DingDingUtil util;

    String dingdingMsg = "";

    @GetMapping("/findManagerPage")
    public BasePageResponse<Map<String, String>> findManagerPage(DayProductYXMarketingManagerInfoQueryVo detail){
        detail.setCityName(null);
        return pageOk(managerInfoService.findManagerPage(detail));
    }

    @GetMapping("/findCityPage")
    public BasePageResponse<Map<String, String>> findCityPage(DayProductYXMarketingManagerInfoQueryVo detail){
        detail.setMarketingManagerOa(null);
        if(StringUtils.isNotEmpty(detail.getCityName()) && "全部".equals(detail.getCityName())){
           detail.setCityName(null);
        }
        return pageOk(managerInfoService.findCityPage(detail));
    }



    @PostMapping("/exportData")
    public void exportData(@RequestBody DayProductYXMarketingManagerInfoQueryVo queryVo) {
        queryVo.setPageNum(0);
        queryVo.setPageSize(10000);
        ExcelWriter excelWriter = null;
        try{
            List<ActivityReportConfig> headMarketing = managerInfoService.selectActivityZqytReportList();
            List<List<Object>> managerList = this.getExcelData(queryVo,headMarketing,true);// 渲染excel的组装数据
            List<List<Object>> dishiList = this.getExcelData(queryVo,headMarketing,false);// 渲染excel的组装数据

            String dateStr = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            excelWriter = ExcelUtils.initExcelWriter(Map.class,"产品营销日报"+dateStr);
            ExcelUtils.exportExcelMoreSheet(excelWriter,managerList, "按营销经理统计",0,getHeader(headMarketing,true));
            ExcelUtils.exportExcelMoreSheet(excelWriter,dishiList, "按地市统计",1,getHeader(headMarketing,false));
            ExcelUtils.closeExcelWriter(excelWriter);

        }catch (Exception e){
            log.error("系统异常:",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }

    }

//    @GetMapping("/handleExportFile")



    /**
     * 表头列表
     * @return
     */
    @GetMapping("/selectActivityZqytReportList")
    public BaseResponse<List<ActivityReportConfig>> selectActivityZqytReportList(){
        return ok(managerInfoService.selectActivityZqytReportList());
    }

    /**
     * 营销经理列表
     * @return
     */
    @GetMapping("/findManagerInfoList")
    public BaseResponse<List<DayProductYXMarketingManagerInfoVo>> findManagerInfoList(){
        return ok(managerInfoService.findManagerInfoList());
    }

    public String getDescription(String strategyId,List<ActivityReportConfigDto> orderTotalList){
        StringBuilder desStr = new StringBuilder();
        orderTotalList.forEach(item->{
            if(item.getStrategyId().equals(strategyId)){
                String dateStr = item.getSendTime();
                LocalDate date = LocalDate.parse(dateStr);
                String formattedDate = date.getMonthValue() + "月" + date.getDayOfMonth() + "号";
                String total = item.getTotal();

                long workdays = calculateWorkdaysBetween(date, LocalDate.now());
                desStr.append("(").append(formattedDate).append("下发").append(total).append("个工单，已派单"+workdays).append("天)");
            }
        });
        return desStr.toString();
    }

    public static long calculateWorkdaysBetween(LocalDate startDate, LocalDate endDate) {
        // 确保开始日期不晚于结束日期
        if (startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("开始日期不能晚于结束日期");
        }

        long totalDays = ChronoUnit.DAYS.between(startDate, endDate);
        long workdays = 0;

        LocalDate date = startDate;
        for (int i = 0; i <= totalDays; i++) {
            DayOfWeek dayOfWeek = date.getDayOfWeek();
            if (dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY) {
                workdays++;
            }
            date = date.plusDays(1);
        }

        return workdays;
    }

    /**
     * 页面导出，横排，表头
     */
    public List<List<String>> getHeader(List<ActivityReportConfig> headMarketing,Boolean boo){
        List<ActivityReportConfigDto> orderTotalList = managerInfoService.selectOrderTotal();
        List<List<String>> headList = Lists.newArrayList();
        if(boo){
            headList.add(Lists.newArrayList("客户经理"));
        }else{
            headList.add(Lists.newArrayList("地市"));
        }
        headList.add(Lists.newArrayList(  managerInfoService.weekDateStr(),"完成工单"));
        headList.add(Lists.newArrayList(managerInfoService.weekDateStr(),"商机个数"));
        headList.add(Lists.newArrayList(managerInfoService.weekDateStr(),"商机规模"));

        headList.add(Lists.newArrayList("累计数据","目标工单"));
        headList.add(Lists.newArrayList("累计数据","完成工单"));
        headList.add(Lists.newArrayList("累计数据","完成率"));
        headList.add(Lists.newArrayList("累计数据","商机个数"));
        headList.add(Lists.newArrayList("累计数据","商机率"));
        headList.add(Lists.newArrayList("累计数据","商机规模"));
        headMarketing.forEach(item->{
            headList.add(Lists.newArrayList(item.getStrategyName()+getDescription(item.getStrategyId(),orderTotalList),"目标工单"));
            headList.add(Lists.newArrayList(item.getStrategyName()+getDescription(item.getStrategyId(),orderTotalList),"完成工单"));
            headList.add(Lists.newArrayList(item.getStrategyName()+getDescription(item.getStrategyId(),orderTotalList),"完成率"));
            headList.add(Lists.newArrayList(item.getStrategyName()+getDescription(item.getStrategyId(),orderTotalList),"商机个数"));
            headList.add(Lists.newArrayList(item.getStrategyName()+getDescription(item.getStrategyId(),orderTotalList),"商机率"));
            headList.add(Lists.newArrayList(item.getStrategyName()+getDescription(item.getStrategyId(),orderTotalList),"商机规模"));
        });
        return headList;
    }
    /**
     * 页面导出，横排，数据
     */
    public List<List<Object>> getExcelData(DayProductYXMarketingManagerInfoQueryVo queryVo, List<ActivityReportConfig> headMarketing, Boolean boo){
        queryVo.setPageSize(10000);
        List<Map<String, String>> dataList = null;
        List<List<Object>> resultList = Lists.newArrayList();
        if(boo){
            BasePageResponse<Map<String, String>> managerPageList = this.findManagerPage(queryVo);
            dataList = managerPageList.getData().getRecords();
        }else{
            BasePageResponse<Map<String, String>> managerPageList = this.findCityPage(queryVo);
            dataList = managerPageList.getData().getRecords();
        }
        if(dataList == null){
            return resultList;
        }
        dataList.forEach(item->{
            List<Object> tableRow = Lists.newArrayList();
            if(boo){
                tableRow.add((item.get("marketingManagerName")));
            }else{
                tableRow.add((item.get("cityName")));
            }
            tableRow.add((item.get("weekFinishGdCn")));
            tableRow.add((item.get("weekOppoCn")));
            tableRow.add((item.get("weekOppoFee")));

            tableRow.add((item.get("targetGdCnTotal")));
            tableRow.add((item.get("finishGdCnTotal")));
            tableRow.add((item.get("wclTotal")));
            tableRow.add((item.get("oppoCnTotal")));
            tableRow.add((item.get("oppoRateTotal")));
            tableRow.add((item.get("oppoFeeTotal")));
            headMarketing.forEach(market->{
                tableRow.add((item.get("targetGdCn_"+market.getStrategyId()) != null ? item.get("targetGdCn_"+market.getStrategyId()) : "" ) );
                tableRow.add((item.get("finishGdCn_"+market.getStrategyId()) != null ? item.get("finishGdCn_"+market.getStrategyId()) : "" ) );
                tableRow.add((item.get("wcl_"+market.getStrategyId()) != null ? item.get("wcl_"+market.getStrategyId()) : "" ) );
                tableRow.add((item.get("oppoCn_"+market.getStrategyId()) != null ? item.get("oppoCn_"+market.getStrategyId()) : "" ) );
                tableRow.add((item.get("oppoRate_"+market.getStrategyId()) != null ? item.get("oppoRate_"+market.getStrategyId()) : "" ) );
                tableRow.add((item.get("oppoFee_"+market.getStrategyId()) != null ? item.get("oppoFee_"+market.getStrategyId()) : "" ) );
            });
            resultList.add(tableRow);
        });
        return resultList;
    }


}
