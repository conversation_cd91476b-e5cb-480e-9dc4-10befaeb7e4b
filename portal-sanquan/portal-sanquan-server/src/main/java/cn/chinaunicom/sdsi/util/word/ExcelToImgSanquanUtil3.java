package cn.chinaunicom.sdsi.util.word;

import cn.chinaunicom.sdsi.tourism.util.TemplateFacory;
import cn.hutool.core.io.FileUtil;
import com.aspose.cells.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.avalon.framework.service.ServiceException;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;


@Slf4j
public class ExcelToImgSanquanUtil3 {

    /**
     * excel to image
     *
     * @param excelFileName {@link java.lang.String} excel文件名
     * @param imageFileName {@link java.lang.String} 图片文件名
     * <AUTHOR>
     * @date 2024/10/18 上午10:03
     */

    public static String convertExcelToImage( String excelFileName, String imageFileName,int sheetIndex) {
        return convertExcelToImage(excelFileName, imageFileName, sheetIndex, null);
    }

    /**
     *
     * @param excelFileName
     * @param imageFileName
     * @param sheetIndex
     * @param params {"FitToPagesWide" Integer 宽度缩放尺寸, "FitToPagesTall" Integer 高度缩放尺寸, "dpiX" Integer 水平DPI, "dpiY" Integer 垂直DPI}
     * @return
     */
    public static String convertExcelToImage(String excelFileName, String imageFileName, int sheetIndex, Map<String, Object> params) {
        new TemplateFacory();
        String basePath = TemplateFacory.UPLOAD_PATCH;
        try (FileInputStream fis = new FileInputStream(basePath + File.separator + excelFileName)) {
            // 加载Excel文件
            com.aspose.cells.Workbook workbook = new com.aspose.cells.Workbook(fis);
            // 获取第一个工作表
            Worksheet worksheet = workbook.getWorksheets().get(sheetIndex);
            PageSetup pageSetup = worksheet.getPageSetup();
            pageSetup.setPaperSize(PaperSizeType.PAPER_A_3);
            pageSetup.setOrientation(PageOrientationType.LANDSCAPE);
            if (params != null && params.containsKey("FitToPagesTall")) {
                pageSetup.setFitToPagesTall((Integer) params.get("FitToPagesTall"));  // 图片缩放高度
            }
            if (params != null && params.containsKey("FitToPagesWide")) {
                pageSetup.setFitToPagesWide((Integer) params.get("FitToPagesWide")); // 图片缩放宽度
            }


            pageSetup.setTopMargin(0.5);
            pageSetup.setLeftMargin(0.5);
            // 自动调整行高
            for (int i = 0; i <= worksheet.getCells().getMaxDataRow() + 1; i++) {
                worksheet.autoFitRow(i);
            }
            // 创建一个ImageOrPrintOptions对象
            ImageOrPrintOptions imgOptions = new ImageOrPrintOptions();
            imgOptions.setDefaultFont("SimSun");

            imgOptions.setOnePagePerSheet(true);
            imgOptions.setCellAutoFit(true);  // 关键设置：启用单元格自动适应高度

            if (params != null && params.containsKey("dpiX")) {
                imgOptions.setHorizontalResolution((Integer) params.get("dpiX")); // 水平DPI ，值越大越清晰，显示越清晰。会影响图片转换速率
            }
            if (params != null && params.containsKey("dpiY")) {
                imgOptions.setVerticalResolution((Integer) params.get("dpiY"));
            }

            // 创建一个SheetRender对象
            SheetRender sr = new SheetRender(worksheet, imgOptions);
            // 中间图片路径
            String midImagePath = basePath +File.separator + "midImage.png";
            // 将工作表渲染为图片
            sr.toImage(0, midImagePath);

            try {
                // 读取原始图片
                BufferedImage originalImage = ImageIO.read(new File(midImagePath));

                Rectangle bounds = new ExcelToImgSanquanUtil3().calculateContentBounds(originalImage);
                // 剪切图片
                BufferedImage croppedImage = originalImage.getSubimage(bounds.x, bounds.y, bounds.width, bounds.height);
                // 保存原始的图片
                ImageIO.write(croppedImage, "png", new File(basePath + File.separator + imageFileName));
                // 删除中间图片
                FileUtil.del(new File(midImagePath));
                return basePath + File.separator + imageFileName;
            } catch (Exception e) {
                throw new ServiceException("9999", "剪切图片时发生错误");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("剪切图片系统错误",e);
            return "error";
        }

    }

    /**
     * 将多张图片合并为一张图片
     */
    public static String mergeImage(List<String> imageUrls,String fileNameNew) {
        try {
            // 加载两张图片
            BufferedImage image1 = ImageIO.read(new File(imageUrls.get(0)));
            BufferedImage image2 = ImageIO.read(new File(imageUrls.get(1)));

            // 确定拼接的方向（水平或垂直）
            boolean horizontal = false; // 水平拼接设置为true，垂直拼接设置为false

            int width, height;
            if (horizontal) {
                width = image1.getWidth() + image2.getWidth();
                height = Math.max(image1.getHeight(), image2.getHeight());
            } else {
                width = Math.max(image1.getWidth(), image2.getWidth());
                height = image1.getHeight() + image2.getHeight();
            }

            // 创建一个新的BufferedImage来存储结果
            BufferedImage combinedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics g = combinedImage.createGraphics();

            // 绘制两张图片到新的BufferedImage上
            g.drawImage(image1, 0, 0, null);
            g.drawImage(image2, horizontal ? image1.getWidth() : 0, horizontal ? 0 : image1.getHeight(), null);

            g.dispose(); // 释放图形上下文占用的系统资源

            // 保存结果图片
            new TemplateFacory();
            ImageIO.write(combinedImage, "png", new File(TemplateFacory.UPLOAD_PATCH + File.separator + fileNameNew + ".png"));
            return TemplateFacory.UPLOAD_PATCH + File.separator + fileNameNew + ".png";
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 计算图片边界大小
     * @param image
     * @return
     */
    private Rectangle calculateContentBounds(BufferedImage image) {
        int minX = image.getWidth();
        int minY = image.getHeight();
        int maxX = 0;
        int maxY = 0;

        // 扫描图像边界
        for (int y = 0; y < image.getHeight(); y++) {
            for (int x = 0; x < image.getWidth(); x++) {
                int rgb = image.getRGB(x, y);
                // 如果不是背景色(白色)
                if (rgb != Color.WHITE.getRGB()) {
                    minX = Math.min(minX, x);
                    minY = Math.min(minY, y);
                    maxX = Math.max(maxX, x);
                    maxY = Math.max(maxY, y);
                }
            }
        }

        // 添加一些边距
        int margin = 10;
        return new Rectangle(
                Math.max(0, minX - margin),
                Math.max(0, minY - margin),
                Math.min(image.getWidth() - minX, maxX - minX + 2 * margin),
                Math.min(image.getHeight() - minY, maxY - minY + 2 * margin)
        );
    }











}
