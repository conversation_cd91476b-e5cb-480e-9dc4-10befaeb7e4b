package cn.chinaunicom.sdsi.keynoteOppo.zqOppo.mapper;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoMark;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.query.ZqOppoMarkQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 商机打标表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Mapper
public interface ZqOppoMarkMapper extends BaseMapper<ZqOppoMark> {

    // 分页查询
    IPage<ZqOppoMark> findPage(@Param("page") IPage page, @Param("query") ZqOppoMarkQueryVo zqOppoMarkVo);

    // 查询列表
    List<ZqOppoMark> findList(@Param("query") ZqOppoMarkQueryVo zqOppoMarkVo);

    int updateOppoMark(@Param("query") ZqOppoMarkQueryVo queryVo);
}
