package cn.chinaunicom.sdsi.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * @program: sanquan_server
 * @ClassName RestTemplateConfig
 * @description:
 * @author: ma<PERSON><PERSON>
 * @date: 2025-04-09 15:20
 * @Version 1.0
 **/
@Configuration
public class RestTemplateConfig {
    @Bean
    RestTemplate restTemplate(){
        return new RestTemplate();
    }
}
