package cn.chinaunicom.sdsi.platform.sysConfig.service;

import cn.chinaunicom.sdsi.platform.sysConfig.entity.TSanquanSysConfig;
import cn.chinaunicom.sdsi.platform.sysConfig.vo.TSanquanSysConfigQueryVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 三全系统参数配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface TSanquanSysConfigService extends IService<TSanquanSysConfig> {

    // 分页查询
    IPage<TSanquanSysConfig> findPage(TSanquanSysConfigQueryVO tSanquanSysConfigVO);

    // 根据id查询
    TSanquanSysConfig findOne(String id);

    // 查询列表
    List<TSanquanSysConfig> findList(TSanquanSysConfigQueryVO tSanquanSysConfigVO);

    // 新增
    int add(TSanquanSysConfig tSanquanSysConfig);

    // 修改
    int update(TSanquanSysConfig tSanquanSysConfig);

    // 删除
    int delete(String id);

    String getValueByKey(String key);

}
