package cn.chinaunicom.sdsi.todo.service;

import cn.chinaunicom.sdsi.todo.entity.TSanquanTodo;
import cn.chinaunicom.sdsi.todo.vo.TSanquanTodoVO;
import cn.chinaunicom.sdsi.todo.queryvo.TSanquanTodoQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 三全项目内部待办 服务层接口
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
public interface TSanquanTodoService extends IService<TSanquanTodo> {

    /**
     * 分页查询三全项目内部待办
     * 
     * @param tSanquanTodoQueryVO
     * @return IPage<TSanquanTodoVO>
     */
    IPage<TSanquanTodoVO> findPage(TSanquanTodoQueryVO tSanquanTodoQueryVO);

    /**
     * 查询三全项目内部待办详细信息
     *
     * @param id
     * @return TSanquanTodoVO
     */
    TSanquanTodoVO findInfo(String id);

    /**
     * 新增三全项目内部待办
     *
     * @param tSanquanTodoVO
     * @return String
     */
    String add(TSanquanTodoVO tSanquanTodoVO);

    /**
     * 修改三全项目内部待办
     *
     * @param tSanquanTodoVO
     * @return Boolean
     */
    Boolean update(TSanquanTodoVO tSanquanTodoVO);

    /**
     * 删除三全项目内部待办
     *
     * @param id
     * @return Boolean
     */
    Boolean delete(String id);

}
