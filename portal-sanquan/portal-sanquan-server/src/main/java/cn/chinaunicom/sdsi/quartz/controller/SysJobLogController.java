package cn.chinaunicom.sdsi.quartz.controller;

import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.quartz.domain.SysJobLog;
import cn.chinaunicom.sdsi.quartz.domain.SysJobLogQueryVo;
import cn.chinaunicom.sdsi.quartz.service.ISysJobLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调度日志操作处理
 *
 */
@RestController
@RequestMapping("/monitor/jobLog")
public class SysJobLogController extends BaseController
{
    @Autowired
    private ISysJobLogService jobLogService;
    /**
     * 查询定时任务调度日志列表
     */
    @Operation(summary = "分页查询潜在机会(商机)表", description = "分页查询潜在机会(商机)表数据")
    @GetMapping("/findPage")
    public BasePageResponse<SysJobLog> findPage(SysJobLogQueryVo sysJobLogQueryVo) {
        IPage<SysJobLog> ipage = jobLogService.findPage(sysJobLogQueryVo);
        return pageOk(ipage);
    }
    /**
     * 查询定时任务调度日志列表
     */
    @GetMapping("/list")
    public BaseResponse<List<SysJobLog>> list(SysJobLog sysJobLog)
    {
        List<SysJobLog> list = jobLogService.selectJobLogList(sysJobLog);
        return ok(list);
    }

    /**
     * 导出定时任务调度日志列表
     */
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SysJobLog sysJobLog)
//    {
//        List<SysJobLog> list = jobLogService.selectJobLogList(sysJobLog);
//        ExcelUtil<SysJobLog> util = new ExcelUtil<SysJobLog>(SysJobLog.class);
//        util.exportExcel(response, list, "调度日志");
//    }

    /**
     * 根据调度编号获取详细信息
     */
    @GetMapping(value = "/{jobLogId}")
    public BaseResponse<SysJobLog> getInfo(@PathVariable Long jobLogId)
    {
        return ok(jobLogService.selectJobLogById(jobLogId));
    }


    /**
     * 删除定时任务调度日志
     */
    @GetMapping("/remove/{jobLogIds}")
    public BaseResponse<Integer> remove(@PathVariable Long[] jobLogIds)
    {
        return ok(jobLogService.deleteJobLogByIds(jobLogIds));
    }

    /**
     * 清空定时任务调度日志
     */
    @GetMapping("/clean")
    public BaseResponse<String> clean()
    {
        jobLogService.cleanJobLog();
        return ok("success");
    }
}
