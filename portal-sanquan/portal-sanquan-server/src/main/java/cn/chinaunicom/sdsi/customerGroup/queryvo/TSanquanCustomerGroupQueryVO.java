package cn.chinaunicom.sdsi.customerGroup.queryvo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Description: 客户群查询对象
 * @Author: han<PERSON><PERSON><PERSON>
 * @Date: 2024-05-20
 */
@Data
@Tag(name = "客户群查询对象", description = "客户群查询对象")
public class TSanquanCustomerGroupQueryVO extends BaseQueryVO {

    @Schema(name = "客户群名称")
    private String customerGroupName;

    @Schema(name = "创建方式 1：模型匹配 2:文件导入")
    private String createType;

    @Schema(name = "创建人")
    private String createBy;

    @Schema(name = "开始时间")
    private String beginTime;

    @Schema(name = "结束时间")
    private String endTime;

    @Schema(name = "客户群id集合")
    private List<String> customerList;
}
