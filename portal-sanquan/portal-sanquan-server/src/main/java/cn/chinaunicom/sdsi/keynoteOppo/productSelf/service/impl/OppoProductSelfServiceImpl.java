package cn.chinaunicom.sdsi.keynoteOppo.productSelf.service.impl;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.productSelf.entity.OppoProductSelf;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.productSelf.query.OppoProductSelfQueryVo;
import cn.chinaunicom.sdsi.keynoteOppo.productSelf.mapper.OppoProductSelfMapper;
import cn.chinaunicom.sdsi.keynoteOppo.productSelf.service.OppoProductSelfService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Service
public class OppoProductSelfServiceImpl extends ServiceImpl<OppoProductSelfMapper, OppoProductSelf> implements OppoProductSelfService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-09
     * @param oppoProductSelf
     * @return IPage<OppoProductSelf>
     **/
    @Override
    public IPage<OppoProductSelf> findPage(OppoProductSelfQueryVo oppoProductSelfVo) {
        IPage page = QueryVoToPageUtil.toPage(oppoProductSelfVo);
        return baseMapper.findPage(page, oppoProductSelfVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-09
     * @param id
     * @return OppoProductSelf
     **/
    @Override
    public OppoProductSelf findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-09
     * @return List<OppoProductSelf>
     **/
    @Override
    public List<OppoProductSelf> findList(OppoProductSelfQueryVo oppoProductSelfVo) {
        return baseMapper.findList(oppoProductSelfVo);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-09
     * @param oppoProductSelf
     * @return int
     **/
    @Override
    public int add(OppoProductSelf oppoProductSelf) {
        return baseMapper.insert(oppoProductSelf);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-09
     * @param oppoProductSelf
     * @return int
     **/
    @Override
    public int update(OppoProductSelf oppoProductSelf) {
        return baseMapper.updateById(oppoProductSelf);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-07-09
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
