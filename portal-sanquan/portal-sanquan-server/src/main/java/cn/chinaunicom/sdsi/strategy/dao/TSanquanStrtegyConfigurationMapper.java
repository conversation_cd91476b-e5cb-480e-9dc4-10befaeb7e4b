package cn.chinaunicom.sdsi.strategy.dao;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyConfiguration;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyConfigurationQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyConfigurationVO;
import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *
 *
 */
@Mapper
public interface TSanquanStrtegyConfigurationMapper extends BaseMapper<TSanquanStrtegyConfiguration> {

    IPage<TSanquanStrtegyConfigurationVO> findPage(@Param("page") IPage page, @Param("query") TSanquanStrtegyConfigurationQuery query);

    List<TSanquanStrtegyConfigurationVO> findList( @Param("query") TSanquanStrtegyConfigurationQuery query);

    int findByTotalCount( @Param("query") TagQuery query);
    int findByMonthCount( @Param("query") TagQuery query);
    TSanquanStrtegyConfigurationVO findOneById( @Param("query") TSanquanStrtegyConfigurationQuery query);

    int updateJobId(@Param("jobId") String jobId,@Param("id") String id);

    // 查询策略数量
    Map<String, String> selectStrategyNum(@Param("query") TSanquanStrtegyConfigurationQuery query);

    // 根据策略查询策略的行业信息
    List<String> selectStrtegyIndustry(@Param("id") String id);
}
