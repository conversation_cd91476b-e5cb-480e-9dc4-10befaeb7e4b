package cn.chinaunicom.sdsi.targetMarketing.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.dataScope.annotation.DataScope;
import cn.chinaunicom.sdsi.dataScope.annotation.DataScopeQuery;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.platform.user.service.IUserService;
import cn.chinaunicom.sdsi.targetMarketing.entity.*;
import cn.chinaunicom.sdsi.targetMarketing.mapper.TSanquanDMrtZqztGemFdyinfoMapper;
import cn.chinaunicom.sdsi.targetMarketing.mapper.TSanquanDZqztGemWorkOrderFeedbackInfoMapper;
import cn.chinaunicom.sdsi.targetMarketing.mapper.TSanquanTargetMarketingMapper;
import cn.chinaunicom.sdsi.targetMarketing.service.TSanquanDZqztGemWorkOrderService;
import cn.chinaunicom.sdsi.targetMarketing.vo.*;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanDZqztGemWorkOrderQueryVO;
import org.apache.catalina.util.ServerInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.chinaunicom.sdsi.targetMarketing.mapper.TSanquanDZqztGemWorkOrderMapper;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;

import java.util.List;
import java.util.Map;

/**
 * 靶向营销任务信息表业务实现类
 * 
 * <AUTHOR>
 * @date 2024-07-30
 */
@Service
public class TSanquanDZqztGemWorkOrderServiceImpl extends ServiceImpl<TSanquanDZqztGemWorkOrderMapper, TSanquanDZqztGemWorkOrder> implements TSanquanDZqztGemWorkOrderService {

    @Autowired
    private UnifastContext unifastContext;
    @Autowired
    private TSanquanDZqztGemWorkOrderFeedbackInfoMapper sanquanDZqztGemWorkOrderFeedbackInfoMapper;
    @Autowired
    private TSanquanDMrtZqztGemFdyinfoMapper sanquanDMrtZqztGemFdyinfoMapper;
    @Autowired
    private TSanquanTargetMarketingMapper targetMarketingMapper;
    @Autowired
    private IUserService userService;
    /**
     * 分页查询靶向营销任务信息表
     * 
     * @param tSanquanDZqztGemWorkOrderQueryVO
     * @return IPage<TSanquanDZqztGemWorkOrderVO>
     */
    @Override
    @DataScope(
            cityQuery=@DataScopeQuery(required = true,column="cd.city_name"), // 地市
            industryQuery=@DataScopeQuery(required = true,column="cd.province_industry_name"), // 行业
            xiansuoQuery=@DataScopeQuery(required = true,column="cd.data_source"), // 地市(地市领导，标讯管理员，线索管理员，)
            biaoxunQuery=@DataScopeQuery(required = true,column="cd.data_source"), // 地市(地市领导，标讯管理员，线索管理员，)
            detailIndustryQuery=@DataScopeQuery(required = true,column="cd.province_detail_industry_name"), // 细分行业
            districtQuery=@DataScopeQuery(required = true,column="cd.district_name"), // 区县
            gridQuery=@DataScopeQuery(required = true,column="cd.grid_name"), // 营服
            userQuery=@DataScopeQuery(required = true,column="cd.EXECUTOR_OA_ID") // 个人
    )
    public IPage<TSanquanDZqztGemWorkOrderVO> findPage(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDZqztGemWorkOrderQueryVO);
        return baseMapper.findPage(page, tSanquanDZqztGemWorkOrderQueryVO);
    }
    /**
     * 查询靶向营销任务列表的反馈信息-按策略查询，导出
     */
    @DataScope(
            cityQuery=@DataScopeQuery(required = true,column="cd.city_name"), // 地市
            industryQuery=@DataScopeQuery(required = true,column="cd.province_industry_name"), // 行业
            xiansuoQuery=@DataScopeQuery(required = true,column="cd.data_source"), // 地市(地市领导，标讯管理员，线索管理员，)
            biaoxunQuery=@DataScopeQuery(required = true,column="cd.data_source"), // 地市(地市领导，标讯管理员，线索管理员，)
            detailIndustryQuery=@DataScopeQuery(required = true,column="cd.province_detail_industry_name"), // 细分行业
            districtQuery=@DataScopeQuery(required = true,column="cd.district_name"), // 区县
            gridQuery=@DataScopeQuery(required = true,column="cd.grid_name"), // 营服
            userQuery=@DataScopeQuery(required = true,column="cd.EXECUTOR_OA_ID") // 个人
    )
    public List<Map<String,Object>> findOrderList(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO) {
        return baseMapper.findOrderList(tSanquanDZqztGemWorkOrderQueryVO);
    }

    @Override
    public IPage<TSanquanDZqztGemWorkOrderVO> findTaskPage(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanDZqztGemWorkOrderQueryVO);
        // 添加地市查询条件
        addCurrentCity(tSanquanDZqztGemWorkOrderQueryVO);
        IPage<TSanquanDZqztGemWorkOrderVO> iPage = baseMapper.findTaskPage(page, tSanquanDZqztGemWorkOrderQueryVO);
        return iPage;
    }

    // 添加地市查询条件
    private void addCurrentCity(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO) {
        // 获取当前用户和地市 只查询当前地市的
        MallUser mallUser = UserUtils.getUser();
        String userCityName = mallUser.getCity();
        // 检查cityName是否为空，如果为空则不设置数据范围过滤条件
        if (userCityName == null || userCityName.trim().isEmpty() || "0000".equals(userCityName)) {
            // 如果cityName为空，不设置过滤条件
            tSanquanDZqztGemWorkOrderQueryVO.setDataScopeSqlFilter("");
        } else {
            // 设置地市查询条件
            tSanquanDZqztGemWorkOrderQueryVO.setCity(userCityName);
        }
        // 添加细分行业的条件
        UserVo byJobNum = userService.findByJobNum(mallUser.getStaffId());
        if(byJobNum != null){
            if (byJobNum.getSegments() != null) {
                tSanquanDZqztGemWorkOrderQueryVO.setSegments(byJobNum.getSegments());
            }
        }
    }

    /**
     * 查询靶向营销任务信息表详细信息
     * 
     * @param ID
     * @return TSanquanDZqztGemWorkOrderVO
     */
    @Override
    public TSanquanDZqztGemWorkOrderVO findInfo(String ID,String superiorPolicyCode,String maxDate) {
        System.err.println(ServerInfo.getServerInfo());
//        TSanquanDZqztGemWorkOrder tSanquanDZqztGemWorkOrder = baseMapper.selectById(ID);
        TSanquanDZqztGemWorkOrder tSanquanDZqztGemWorkOrder = baseMapper.selectOneByIdAndTime(ID,maxDate);
        if(tSanquanDZqztGemWorkOrder == null){
            return new TSanquanDZqztGemWorkOrderVO();
        }
        TSanquanDZqztGemWorkOrderVO tSanquanDZqztGemWorkOrderVO = new TSanquanDZqztGemWorkOrderVO();
        BeanUtils.copyProperties(tSanquanDZqztGemWorkOrder, tSanquanDZqztGemWorkOrderVO);
        //文旅体育行业潜在商机走访策略,的反馈的走的是动态表,P17202412180024:创新产品前置商机走访策略 的反馈也走动态表
        if(this.isDynamicFeeback(superiorPolicyCode)) {
            TSanquanDMrtZqztGemFdyinfo fdyinfo = new TSanquanDMrtZqztGemFdyinfo();
            fdyinfo.setWorkOrderCode(tSanquanDZqztGemWorkOrder.getCode());
            List<TSanquanDMrtZqztGemFdyinfo> fdyinfoList = sanquanDMrtZqztGemFdyinfoMapper.findList(fdyinfo);
            tSanquanDZqztGemWorkOrderVO.setFdyinfoList(fdyinfoList);


            List<TSanquanZqztOrderTypePropInfoVO> propInfoList = sanquanDZqztGemWorkOrderFeedbackInfoMapper.selectOrderPropInfo(tSanquanDZqztGemWorkOrder.getCode(),maxDate);
            if(propInfoList.size() > 0){
                tSanquanDZqztGemWorkOrderVO.setTargetMarketing(propInfoList);
            }

        }else{
            List<TSanquanDZqztGemWorkOrderFeedbackInfoVO> tSanquanDZqztGemWorkOrderFeedbackInfoVO = sanquanDZqztGemWorkOrderFeedbackInfoMapper.selectByWorkOrderId(tSanquanDZqztGemWorkOrder.getCode());
            tSanquanDZqztGemWorkOrderVO.setTSanquanDZqztGemWorkOrderFeedbackInfoVO(tSanquanDZqztGemWorkOrderFeedbackInfoVO);

            List<TSanquanZqztOrderTypePropInfoVO> propInfoList = sanquanDZqztGemWorkOrderFeedbackInfoMapper.selectOrderPropInfo(tSanquanDZqztGemWorkOrder.getCode(),maxDate);
            if(propInfoList.size() > 0){
                tSanquanDZqztGemWorkOrderVO.setTargetMarketing(propInfoList);
            }

        }
        // 查询业务信息
        return tSanquanDZqztGemWorkOrderVO;
    }

    /**
     * 查询指定策略的动态反馈单字段数据
     */
    public List<TSanquanDMrtZqztGemFdyinfo> findFeebackDynamicsList(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO){
        return sanquanDMrtZqztGemFdyinfoMapper.findFeebackDynamicsList(tSanquanDZqztGemWorkOrderQueryVO);
    }

    public List<TSanquanDMrtZqztGemFdyinfo> findFeebackDengBao(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO){
        return sanquanDMrtZqztGemFdyinfoMapper.findFeebackDengBao(tSanquanDZqztGemWorkOrderQueryVO);
    }
    /**
     * 查询指定策略的动态反馈单字段名称
     */
    public List<Map<String,String>> getDynamicFeebackColumn( TSanquanDZqztGemWorkOrderQueryVO tSanquanDMrtZqztGemFdyinfo){
        return sanquanDMrtZqztGemFdyinfoMapper.getDynamicFeebackColumn(tSanquanDMrtZqztGemFdyinfo);
    }

    /**
     * 导出靶向，查询反馈信息(静态表，t_sanquan_d_zqzt_gem_work_order_feedback_info)
     * @return
     */
    public List<Map<String,Object>> getFeekInfoStatic(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO) {
        List<Map<String,Object>> list = sanquanDZqztGemWorkOrderFeedbackInfoMapper.getFeekInfoStatic(tSanquanDZqztGemWorkOrderQueryVO);
        return list;
    }

    /**
     * 新增靶向营销任务信息表
     * 
     * @param tSanquanDZqztGemWorkOrderVO
     * @return String
     */
    @Override
    public String add(TSanquanDZqztGemWorkOrderVO tSanquanDZqztGemWorkOrderVO) {
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            tSanquanDZqztGemWorkOrderVO.setTenantId(tenantId);
        }
         TSanquanDZqztGemWorkOrder tSanquanDZqztGemWorkOrder = new TSanquanDZqztGemWorkOrder();
         BeanUtils.copyProperties(tSanquanDZqztGemWorkOrderVO, tSanquanDZqztGemWorkOrder);
         baseMapper.insert(tSanquanDZqztGemWorkOrder);
         return tSanquanDZqztGemWorkOrder.getId();
    }

    /**
     * 修改靶向营销任务信息表
     * 
     * @param tSanquanDZqztGemWorkOrderVO
     * @return Boolean
     */
    @Override
    public Boolean update(TSanquanDZqztGemWorkOrderVO tSanquanDZqztGemWorkOrderVO) {
        TSanquanDZqztGemWorkOrder tSanquanDZqztGemWorkOrder = new TSanquanDZqztGemWorkOrder();
        BeanUtils.copyProperties(tSanquanDZqztGemWorkOrderVO, tSanquanDZqztGemWorkOrder);
        return baseMapper.updateById(tSanquanDZqztGemWorkOrder) > 0;
    }

    /**
     * 删除靶向营销任务信息表
     * 
     * @param ID
     * @return Boolean
     */
    @Override
    public Boolean delete(String ID) {
        return baseMapper.deleteById(ID) > 0;
    }

    /**
     * 根据潜在机会查询靶向任务信息
     * @param opportunityId
     * @return
     */
    @Override
    public TSanquanDZqztGemWorkOrder selectInfoByOpportunityId(String opportunityId) {
        return baseMapper.selectInfoByOpportunityId(opportunityId);
    }

    public List<TSanquanEmailEntity> selectExpireCustomerOrder(TSanquanEmailVO order){
        return baseMapper.selectExpireCustomerOrder(order);
    }

    /* 查询最新账期*/
    public String getMaxDate(TSanquanDZqztGemWorkOrderQueryVO orderQueryVO){
        orderQueryVO.setCreateTime("1111");
        String md = baseMapper.getMaxDate(orderQueryVO);
        if(StringUtils.isNotEmpty(md)){
            orderQueryVO.setCreateTime(null);// 防止跨年时查不到数
            return baseMapper.getMaxDate(orderQueryVO);
        }
        return md;
    }

    /**
     * 判断是否是动态反馈字段的策略
     * @return
     */
    public Boolean isDynamicFeeback(String superiorPolicyCode){
        if("P17202410080027".equals(superiorPolicyCode)||"P17202412180024".equals(superiorPolicyCode)
                ||"P17202502070024".equals(superiorPolicyCode)||"P17202501170237".equals(superiorPolicyCode)
                ||"P17202502190012".equals(superiorPolicyCode)||"P17202502190043".equals(superiorPolicyCode)
                ||"P17202505120025".equals(superiorPolicyCode)

        ){
            return true;
        }
        return false;
    }

}
