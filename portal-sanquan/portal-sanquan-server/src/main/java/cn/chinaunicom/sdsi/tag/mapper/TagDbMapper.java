package cn.chinaunicom.sdsi.tag.mapper;

import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.tag.entity.TagDb;
import cn.chinaunicom.sdsi.tag.entity.TagDbQuery;
import cn.chinaunicom.sdsi.tag.entity.TagDbVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 *
 */
@Mapper
public interface TagDbMapper extends BaseMapper<TagDb> {

    List<TagDb> findList(@Param("query") TagDbQuery query);

    void deleteByTagId(String tagId);
}
