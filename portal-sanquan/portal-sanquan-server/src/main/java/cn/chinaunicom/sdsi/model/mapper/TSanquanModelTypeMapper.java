package cn.chinaunicom.sdsi.model.mapper;

import cn.chinaunicom.sdsi.model.entity.TSanquanModelType;
import cn.chinaunicom.sdsi.model.vo.TSanquanModelTypeVO;
import cn.chinaunicom.sdsi.model.queryvo.TSanquanModelTypeQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 模型分类Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-19
 */
@Mapper
public interface TSanquanModelTypeMapper extends BaseMapper<TSanquanModelType> {

    /**
     * 分页查询模型分类
     * 
     * @param page,tSanquanModelTypeQueryVO
     * @return 模型分类
     */
    IPage<TSanquanModelTypeVO> findPage(@Param("page") IPage page, @Param("query") TSanquanModelTypeQueryVO tSanquanModelTypeQueryVO);

    List<TSanquanModelTypeVO> findModelTypeList(@Param("query") TSanquanModelTypeQueryVO tSanquanModelTypeQueryVO);

    TSanquanModelType selectOneNew(@Param("query") TSanquanModelType tSanquanModelTypeQueryVO);
}
