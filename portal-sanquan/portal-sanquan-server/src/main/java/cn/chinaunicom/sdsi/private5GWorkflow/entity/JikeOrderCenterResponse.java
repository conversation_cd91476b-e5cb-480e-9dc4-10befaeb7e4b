package cn.chinaunicom.sdsi.private5GWorkflow.entity;

import lombok.Data;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONArray;
import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

/**
 * 订单详细查询响应实体类
 */
@Data
public class JikeOrderCenterResponse implements Serializable {

    /** 责任人信息 */
    private ResponsibleInfo responsible;
    /** 电路交付信息 */
    private CircuitDeliveryInfo circuitDeliveryInfo;
    /** 配送信息 */
    private List<DeliveryInfo> delivery;
    /** 请求信息 */
    private RequestInfo request;
    /** 支付信息 */
    private List<PaymentInfo> payments;
    /** 合同信息 */
    private List<ContractInfo> contract;
    /** 回执信息 */
    private List<FlowDataInfo> flowData;
    /** 群组信息 */
    private List<GroupInfo> groups;
    /** 折扣审批 */
    private List<DiscountInfo> discount;
    /** 商品信息 */
    private List<GoodsInfo> goods;
    /** 流程信息 */
    private List<FlowInfo> flowInfo;
    /** 经办人信息 */
    private OperatorInfo operator;
    /** 联系人信息 */
    private LinkmanInfo linkman;
    /** 用户信息 */
    private List<UserInfo> users;
    /** 受理信息 */
    private AcceptInfo accept;
    /** 发展人信息 */
    private DeveloperInfo developer;
    /** 使用客户信息 */
    private List<UseCustomerInfo> useCustomer;
    /** 账户信息 */
    private List<AccountInfo> accounts;
    /** 发票信息 */
    private InvoiceInfo invoice;
    /** 用户群组关系 */
    private List<RelationInfo> relations;
    /** 订单信息 */
    private OrderInfo order;
    /** 客户信息 */
    private CustomerInfo customer;
    /** 责任客户经理 */
    private SupportManagerInfo supportManager;
    /** 前评估节点 */
    private PreEvaluateInfo preEvaluate;
    /** 流程变量 */
    private List<FlowVariableInfo> flowVariable;
    /** OA受理信息 */
    private OaAcceptInfo oaAccept;
    /** 订单关系 */
    private OrderRelationInfo orderRelation;

    /**
     * 从DATA节点JSON数据转换为实体类
     *
     * @param dataJson DATA节点的JSON数据
     * @return JikeOrderCenterResponse实例
     */
    public static JikeOrderCenterResponse fromJsonData(JSONObject dataJson) {
        JikeOrderCenterResponse response = new JikeOrderCenterResponse();

        if (dataJson == null) {
            return response;
        }

        response.setResponsible(ResponsibleInfo.fromJson(dataJson.getJSONObject("RESPONSIBLE")));
        response.setCircuitDeliveryInfo(CircuitDeliveryInfo.fromJson(dataJson.getJSONObject("CIRCUIT_DELIVERY_INFO")));
        response.setDelivery(DeliveryInfo.fromJsonArray(dataJson.getJSONArray("DELIVERY")));
        response.setRequest(RequestInfo.fromJson(dataJson.getJSONObject("REQUEST")));
        response.setPayments(PaymentInfo.fromJsonArray(dataJson.getJSONArray("PAYMENTS")));
        response.setContract(ContractInfo.fromJsonArray(dataJson.getJSONArray("CONTRACT")));
        response.setFlowData(FlowDataInfo.fromJsonArray(dataJson.getJSONArray("FLOW_DATA")));
        response.setGroups(GroupInfo.fromJsonArray(dataJson.getJSONArray("GROUPS")));
        response.setDiscount(DiscountInfo.fromJsonArray(dataJson.getJSONArray("DISCOUNT")));
        response.setGoods(GoodsInfo.fromJsonArray(dataJson.getJSONArray("GOODS")));
        response.setFlowInfo(FlowInfo.fromJsonArray(dataJson.getJSONArray("FLOW_INFO")));
        response.setOperator(OperatorInfo.fromJson(dataJson.getJSONObject("OPERATOR")));
        response.setLinkman(LinkmanInfo.fromJson(dataJson.getJSONObject("LINKMAN")));
        response.setUsers(UserInfo.fromJsonArray(dataJson.getJSONArray("USERS")));
        response.setAccept(AcceptInfo.fromJson(dataJson.getJSONObject("ACCEPT")));
        response.setDeveloper(DeveloperInfo.fromJson(dataJson.getJSONObject("DEVELOPER")));
        response.setUseCustomer(UseCustomerInfo.fromJsonArray(dataJson.getJSONArray("USE_CUSTOMER")));
        response.setAccounts(AccountInfo.fromJsonArray(dataJson.getJSONArray("ACCOUNTS")));
        response.setInvoice(InvoiceInfo.fromJson(dataJson.getJSONObject("INVOICE")));
        response.setRelations(RelationInfo.fromJsonArray(dataJson.getJSONArray("RELATIONS")));
        response.setOrder(OrderInfo.fromJson(dataJson.getJSONObject("ORDER")));
        response.setCustomer(CustomerInfo.fromJson(dataJson.getJSONObject("CUSTOMER")));
        response.setSupportManager(SupportManagerInfo.fromJson(dataJson.getJSONObject("SUPPORT_MANAGER")));
        response.setPreEvaluate(PreEvaluateInfo.fromJson(dataJson.getJSONObject("PRE_EVALUATE")));
        response.setFlowVariable(FlowVariableInfo.fromJsonArray(dataJson.getJSONArray("FLOW_VARIABLE")));
        response.setOaAccept(OaAcceptInfo.fromJson(dataJson.getJSONObject("OA_ACCEPT")));
        response.setOrderRelation(OrderRelationInfo.fromJson(dataJson.getJSONObject("ORDER_RELATION")));

        return response;
    }

    @Data
    public static class ResponsibleInfo implements Serializable {
        /** 责任人证件号码 */
        private String idNo;
        /** 责任人邮编 */
        private String postcode;
        /** 责任人电话 */
        private String phone;
        /** 责任人地址 */
        private String address;
        /** 责任人证件类型 */
        private String idType;
        /** 责任人证件地址 */
        private String idAddr;
        /** 责任人邮箱 */
        private String email;
        /** 责任人备注 */
        private String remark;
        /** 责任人姓名 */
        private String name;
        /** 责任人英文姓名 */
        private String enName;
        /** 责任人新版证件号码 */
        private String newIdno;
        /** 责任人证件有效期 */
        private String inDate;
        /** 责任人性别 */
        private String sex;
        /** 责任人国籍 */
        private String nation;
        /** 责任人英文国籍 */
        private String enNation;
        /** 责任人出生日期 */
        private String birthDate;
        /** 对端系统主键 */
        private String destitemid;
        /** 开始时间 */
        private String startTime;
        /** 结束时间 */
        private String endTime;
        /** 修改标记 */
        private String changeType;
        /** 附件信息 */
        private List<AttachmentInfo> attachments;
        /** 实名制信息 */
        private List<RealnameInfo> realname;

        public static ResponsibleInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            ResponsibleInfo info = new ResponsibleInfo();
            info.setIdNo(json.getString("ID_NO"));
            info.setPostcode(json.getString("POSTCODE"));
            info.setPhone(json.getString("PHONE"));
            info.setAddress(json.getString("ADDRESS"));
            info.setIdType(json.getString("ID_TYPE"));
            info.setIdAddr(json.getString("ID_ADDR"));
            info.setEmail(json.getString("EMAIL"));
            info.setRemark(json.getString("REMARK"));
            info.setName(json.getString("NAME"));
            info.setEnName(json.getString("EN_NAME"));
            info.setNewIdno(json.getString("NEW_IDNO"));
            info.setInDate(json.getString("IN_DATE"));
            info.setSex(json.getString("SEX"));
            info.setNation(json.getString("NATION"));
            info.setEnNation(json.getString("EN_NATION"));
            info.setBirthDate(json.getString("BIRTH_DATE"));
            info.setDestitemid(json.getString("DESTITEMID"));
            info.setStartTime(json.getString("START_TIME"));
            info.setEndTime(json.getString("END_TIME"));
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setAttachments(AttachmentInfo.fromJsonArray(json.getJSONArray("ATTACHMENTS")));
            info.setRealname(RealnameInfo.fromJsonArray(json.getJSONArray("REALNAME")));

            return info;
        }
    }

    @Data
    public static class AttachmentInfo implements Serializable {
        /** 修改标记 */
        private String changeType;
        /** 附件类型 */
        private String catagory;
        /** 上传时间 */
        private String uploadTime;
        /** URL文件ID */
        private String url;
        /** 附件名称 */
        private String name;
        /** 对端系统主键 */
        private String attDestitemid;
        /** 开始时间 */
        private String startTime;
        /** 结束时间 */
        private String endTime;
        /** 附件条形码 */
        private String barCode;
        /** 批次号 */
        private String batchCode;
        /** 来源 */
        private String source;

        public static AttachmentInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            AttachmentInfo info = new AttachmentInfo();
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setCatagory(json.getString("CATAGORY"));
            info.setUploadTime(json.getString("UPLOAD_TIME"));
            info.setUrl(json.getString("URL"));
            info.setName(json.getString("NAME"));
            info.setAttDestitemid(json.getString("ATT_DESTITEMID"));
            info.setStartTime(json.getString("START_TIME"));
            info.setEndTime(json.getString("END_TIME"));
            info.setBarCode(json.getString("BAR_CODE"));
            info.setBatchCode(json.getString("BATCH_CODE"));
            info.setSource(json.getString("SOURCE"));

            return info;
        }

        public static List<AttachmentInfo> fromJsonArray(JSONArray jsonArray) {
            List<AttachmentInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    AttachmentInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class RealnameInfo implements Serializable {
        /** 修改标记 */
        private String changeType;
        /** 认证类型 */
        private String authType;
        /** 认证结果 */
        private String authResult;
        /** 认证值，如相似度 */
        private String similarity;
        /** 开始时间 */
        private String startTime;
        /** 结束时间 */
        private String endTime;
        /** 认证时间 */
        private String authTime;

        public static RealnameInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            RealnameInfo info = new RealnameInfo();
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setAuthType(json.getString("AUTH_TYPE"));
            info.setAuthResult(json.getString("AUTH_RESULT"));
            info.setSimilarity(json.getString("SIMILARITY"));
            info.setStartTime(json.getString("START_TIME"));
            info.setEndTime(json.getString("END_TIME"));
            info.setAuthTime(json.getString("AUTH_TIME"));

            return info;
        }

        public static List<RealnameInfo> fromJsonArray(JSONArray jsonArray) {
            List<RealnameInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    RealnameInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class OrderInfo implements Serializable {
        /** 订单ID */
        private String orderId;
        /** 订单名称 */
        private String name;
        /** 订单状态 */
        private String status;
        /** 订单状态名称 */
        private String statusName;
        /** 归属地市 */
        private String eparchyCode;
        /** 归属省分 */
        private String provinceCode;
        /** 订单时间 */
        private String orderDate;
        /** 开始时间 */
        private String startTime;
        /** 起租时间 */
        private String startRentDate;
        /** 竣工时间 */
        private String completeDate;
        /** 客户地址 */
        private String address;
        /** 商品编码 */
        private String goodsCode;
        /** 商品名称 */
        private String goodsNames;
        /** 标准价 */
        private String listPrice;
        /** 实际价格 */
        private String realPrice;
        /** 折扣 */
        private String discount;
        /** 订单备注 */
        private String remark;
        /** 5G专网类型：0-人网、1-物网 */
        private String ord10565;
        /** 5G专网组网方式：1-人网虚拟专网、2-人网混合专网、3-物网虚拟专网、4-物网混合/独立专网 */
        private String ord10566;
        /** 专线开通订单号 */
        private String ord10567;
        /** 投资申请实例号 */
        private String ord10568;

        public static OrderInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            OrderInfo info = new OrderInfo();
            info.setOrderId(json.getString("ORDER_ID"));
            info.setName(json.getString("NAME"));
            info.setStatus(json.getString("STATUS"));
            info.setStatusName(json.getString("STATUS_NAME"));
            info.setEparchyCode(json.getString("EPARCHY_CODE"));
            info.setProvinceCode(json.getString("PROVINCE_CODE"));
            info.setOrderDate(json.getString("ORDER_DATE"));
            info.setStartTime(json.getString("START_TIME"));
            info.setStartRentDate(json.getString("START_RENT_DATE"));
            info.setCompleteDate(json.getString("COMPLETE_DATE"));
            info.setAddress(json.getString("ADDRESS"));
            info.setGoodsCode(json.getString("GOODS_CODE"));
            info.setGoodsNames(json.getString("GOODS_NAMES"));
            info.setListPrice(json.getString("LIST_PRICE"));
            info.setRealPrice(json.getString("REAL_PRICE"));
            info.setDiscount(json.getString("DISCOUNT"));
            info.setRemark(json.getString("REMARK"));

            // 解析订单属性字段
            info.setOrd10565(json.getString("ORD10565"));
            info.setOrd10566(json.getString("ORD10566"));
            info.setOrd10567(json.getString("ORD10567"));
            info.setOrd10568(json.getString("ORD10568"));

            return info;
        }
    }

    @Data
    public static class CustomerInfo implements Serializable {
        /** 客户名称 */
        private String name;
        /** 客户地址 */
        private String address;
        /** 客户联系人名称 */
        private String contactName;
        /** 客户联系人联系方式 */
        private String contactPhone;
        /** BSS客户ID */
        private String bssId;
        /** CBSS客户ID */
        private String cbssId;
        /** 集团客户编码 */
        private String groupId;
        /** 集中集客客户id */
        private String custId;
        /** 客户类型 */
        private String custType;
        /** 客户状态 */
        private String custStatus;
        /** 是否新客户 */
        private String isNewCustomer;
        /** 归属业务省 */
        private String eparchyProvince;
        /** 归属业务市 */
        private String eparchyCity;
        /** 归属业务区 */
        private String eparchyArea;
        /** 客户一级行业分类 */
        private String firstCallingTypeCode;

        public static CustomerInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            CustomerInfo info = new CustomerInfo();
            info.setName(json.getString("NAME"));
            info.setAddress(json.getString("ADDRESS"));
            info.setContactName(json.getString("CONTACT_NAME"));
            info.setContactPhone(json.getString("CONTACT_PHONE"));
            info.setBssId(json.getString("BSS_ID"));
            info.setCbssId(json.getString("CBSS_ID"));
            info.setGroupId(json.getString("GROUP_ID"));
            info.setCustId(json.getString("CUST_ID"));
            info.setCustType(json.getString("CUST_TYPE"));
            info.setCustStatus(json.getString("CUST_STATUS"));
            info.setIsNewCustomer(json.getString("IS_NEW_CUSTOMER"));
            info.setEparchyProvince(json.getString("EPARCHY_PROVINCE"));
            info.setEparchyCity(json.getString("EPARCHY_CITY"));
            info.setEparchyArea(json.getString("EPARCHY_AREA"));
            info.setFirstCallingTypeCode(json.getString("FIRST_CALLING_TYPE_CODE"));

            return info;
        }
    }

    @Data
    public static class UserInfo implements Serializable {
        /** 用户实例ID */
        private String instanceId;
        /** 用户ID */
        private String userId;
        /** 用户名称 */
        private String name;
        /** 服务号码 */
        private String serialNumber;
        /** 用户性质 */
        private String userDiffCode;
        /** 用户开户时间 */
        private String openDate;
        /** 省份编码 */
        private String provinceCode;
        /** 城市编码 */
        private String cityCode;
        /** 区域编码 */
        private String districtCode;
        /** 所属群组实例ID */
        private String groupInstId;
        /** 使用客户实例ID */
        private String useCustInstId;
        /** 备注 */
        private String remark;
        /** 修改标识 */
        private String changeType;

        public static UserInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            UserInfo info = new UserInfo();
            info.setInstanceId(json.getString("INSTANCE_ID"));
            info.setUserId(json.getString("USER_ID"));
            info.setName(json.getString("NAME"));
            info.setSerialNumber(json.getString("SERIAL_NUMBER"));
            info.setUserDiffCode(json.getString("USER_DIFF_CODE"));
            info.setOpenDate(json.getString("OPEN_DATE"));
            info.setProvinceCode(json.getString("PROVINCE_CODE"));
            info.setCityCode(json.getString("CITY_CODE"));
            info.setDistrictCode(json.getString("DISTRICT_CODE"));
            info.setGroupInstId(json.getString("GROUP_INST_ID"));
            info.setUseCustInstId(json.getString("USE_CUST_INST_ID"));
            info.setRemark(json.getString("REMARK"));
            info.setChangeType(json.getString("CHANGE_TYPE"));

            return info;
        }

        public static List<UserInfo> fromJsonArray(JSONArray jsonArray) {
            List<UserInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    UserInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class GoodsInfo implements Serializable {
        /** 商品编码 */
        private String code;
        /** 商品实例ID */
        private String instanceId;
        /** 商品名称 */
        private String name;
        /** 商品数量 */
        private String count;
        /** 商品分类编码 */
        private String catagoryId;
        /** 父商品实例ID */
        private String parentInstId;
        /** 用户实例ID */
        private String userInstId;
        /** 合同实例ID */
        private String contractInstId;
        /** 账户实例ID */
        private String accountInstId;
        /** 配送实例ID */
        private String deliveryInstId;
        /** 支付实例ID */
        private String paymentInstId;
        /** 折扣审批单实例ID */
        private String discountInstId;
        /** 修改标记 */
        private String changeType;
        /** 对端主键 */
        private String destItemId;

        public static GoodsInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            GoodsInfo info = new GoodsInfo();
            info.setCode(json.getString("CODE"));
            info.setInstanceId(json.getString("INSTANCE_ID"));
            info.setName(json.getString("NAME"));
            info.setCount(json.getString("COUNT"));
            info.setCatagoryId(json.getString("CATAGORY_ID"));
            info.setParentInstId(json.getString("PARENT_INST_ID"));
            info.setUserInstId(json.getString("USER_INST_ID"));
            info.setContractInstId(json.getString("CONTRACT_INST_ID"));
            info.setAccountInstId(json.getString("ACCOUNT_INST_ID"));
            info.setDeliveryInstId(json.getString("DELIVERY_INST_ID"));
            info.setPaymentInstId(json.getString("PAYMENT_INST_ID"));
            info.setDiscountInstId(json.getString("DISCOUNT_INST_ID"));
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setDestItemId(json.getString("DEST_ITEM_ID"));

            return info;
        }

        public static List<GoodsInfo> fromJsonArray(JSONArray jsonArray) {
            List<GoodsInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    GoodsInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class CircuitDeliveryInfo implements Serializable {
        public static CircuitDeliveryInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class DeliveryInfo implements Serializable {
        public static List<DeliveryInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class RequestInfo implements Serializable {
        public static RequestInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class PaymentInfo implements Serializable {
        public static List<PaymentInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class ContractInfo implements Serializable {
        /** 实例ID */
        private String instanceId;
        /** 订单ID */
        private String orderId;
        /** 合同类型 */
        private String contractType;
        /** 修改类型 */
        private String changeType;
        /** 批次号 */
        private String batchCode;
        /** 合同名称 */
        private String name;
        /** 合同编码 */
        private String contractCode;
        /** 附件信息 */
        private List<AttachmentInfo> attachments;
        /** 属性信息 */
        private List<AttrInfo> attrs;
        /** 层级属性 */
        private LevelAttrsInfo levelAttrs;

        public static ContractInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            ContractInfo info = new ContractInfo();
            info.setInstanceId(json.getString("INSTANCE_ID"));
            info.setOrderId(json.getString("ORDER_ID"));
            info.setContractType(json.getString("CONTRACT_TYPE"));
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setBatchCode(json.getString("BATCH_CODE"));
            info.setName(json.getString("NAME"));
            info.setContractCode(json.getString("CONTRACT_CODE"));
            info.setAttachments(AttachmentInfo.fromJsonArray(json.getJSONArray("ATTACHMENTS")));
            info.setAttrs(AttrInfo.fromJsonArray(json.getJSONArray("ATTRS")));
            info.setLevelAttrs(LevelAttrsInfo.fromJson(json.getJSONObject("LEVEL_ATTRS")));
            return info;
        }

        public static List<ContractInfo> fromJsonArray(JSONArray jsonArray) {
            List<ContractInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    ContractInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class FlowDataInfo implements Serializable {
        /** 流程值 */
        private String flowValue;
        /** 流程数据ID */
        private String flowDataId;
        /** 订单ID */
        private String orderId;
        /** 流程键 */
        private String flowKey;
        /** 任务ID */
        private String taskId;

        public static FlowDataInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            FlowDataInfo info = new FlowDataInfo();
            info.setFlowValue(json.getString("FLOW_VALUE"));
            info.setFlowDataId(json.getString("FLOW_DATA_ID"));
            info.setOrderId(json.getString("ORDER_ID"));
            info.setFlowKey(json.getString("FLOW_KEY"));
            info.setTaskId(json.getString("TASK_ID"));
            return info;
        }

        public static List<FlowDataInfo> fromJsonArray(JSONArray jsonArray) {
            List<FlowDataInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    FlowDataInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class GroupInfo implements Serializable {
        public static List<GroupInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class DiscountInfo implements Serializable {
        /** 实例ID */
        private String instanceId;
        /** 折扣编码 */
        private String discountCode;
        /** 订单ID */
        private String orderId;
        /** 修改类型 */
        private String changeType;
        /** 名称 */
        private String name;
        /** 类型 */
        private String type;
        /** 目标项ID */
        private String destItemId;
        /** 附件信息 */
        private List<AttachmentInfo> attachments;

        public static DiscountInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            DiscountInfo info = new DiscountInfo();
            info.setInstanceId(json.getString("INSTANCE_ID"));
            info.setDiscountCode(json.getString("DISCOUNT_CODE"));
            info.setOrderId(json.getString("ORDER_ID"));
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setName(json.getString("NAME"));
            info.setType(json.getString("TYPE"));
            info.setDestItemId(json.getString("DEST_ITEM_ID"));
            info.setAttachments(AttachmentInfo.fromJsonArray(json.getJSONArray("ATTACHMENTS")));
            return info;
        }

        public static List<DiscountInfo> fromJsonArray(JSONArray jsonArray) {
            List<DiscountInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    DiscountInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class FlowInfo implements Serializable {
        public static List<FlowInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class OperatorInfo implements Serializable {
        public static OperatorInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class LinkmanInfo implements Serializable {
        public static LinkmanInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class AcceptInfo implements Serializable {
        public static AcceptInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class DeveloperInfo implements Serializable {
        public static DeveloperInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class UseCustomerInfo implements Serializable {
        public static List<UseCustomerInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class AccountInfo implements Serializable {
        public static List<AccountInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class InvoiceInfo implements Serializable {
        public static InvoiceInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class RelationInfo implements Serializable {
        public static List<RelationInfo> fromJsonArray(JSONArray jsonArray) {
            return new ArrayList<>();
        }
    }

    @Data
    public static class SupportManagerInfo implements Serializable {
        public static SupportManagerInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class PreEvaluateInfo implements Serializable {
        public static PreEvaluateInfo fromJson(JSONObject json) {
            return null;
        }
    }

    @Data
    public static class FlowVariableInfo implements Serializable {
        /** 变量名称 */
        private String name;
        /** 变量值 */
        private String value;

        public static FlowVariableInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            FlowVariableInfo info = new FlowVariableInfo();
            info.setName(json.getString("NAME"));
            info.setValue(json.getString("VALUE"));
            return info;
        }

        public static List<FlowVariableInfo> fromJsonArray(JSONArray jsonArray) {
            List<FlowVariableInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    FlowVariableInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class OaAcceptInfo implements Serializable {
        /** 区县编码 */
        private String countyCode;
        /** 订单ID */
        private String orderId;
        /** 省份编码 */
        private String provinceCode;
        /** 姓名 */
        private String name;
        /** 省份名称 */
        private String provinceName;
        /** 地市名称 */
        private String eparchyName;
        /** 根订单ID */
        private String rootOrderId;
        /** 标记 */
        private String mark;
        /** OA编码 */
        private String oaCode;
        /** 地市编码 */
        private String eparchyCode;
        /** 区县名称 */
        private String countyName;

        public static OaAcceptInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            OaAcceptInfo info = new OaAcceptInfo();
            info.setCountyCode(json.getString("COUNTY_CODE"));
            info.setOrderId(json.getString("ORDER_ID"));
            info.setProvinceCode(json.getString("PROVINCE_CODE"));
            info.setName(json.getString("NAME"));
            info.setProvinceName(json.getString("PROVINCE_NAME"));
            info.setEparchyName(json.getString("EPARCHY_NAME"));
            info.setRootOrderId(json.getString("ROOT_ORDER_ID"));
            info.setMark(json.getString("MARK"));
            info.setOaCode(json.getString("OA_CODE"));
            info.setEparchyCode(json.getString("EPARCHY_CODE"));
            info.setCountyName(json.getString("COUNTY_NAME"));
            return info;
        }
    }

    @Data
    public static class OrderRelationInfo implements Serializable {
        /** 属性列表 */
        private List<AttrInfo> attrs;

        public static OrderRelationInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            OrderRelationInfo info = new OrderRelationInfo();
            info.setAttrs(AttrInfo.fromJsonArray(json.getJSONArray("ATTRS")));
            return info;
        }
    }

    @Data
    public static class AttrInfo implements Serializable {
        /** 属性编码 */
        private String code;
        /** 失效日期 */
        private String endDate;
        /** 变更类型 */
        private String changeType;
        /** 值类型 */
        private String type;
        /** 属性值 */
        private String value;
        /** 生效日期 */
        private String startDate;
        /** 原属性值 */
        private String originValue;

        public static AttrInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            AttrInfo info = new AttrInfo();
            info.setCode(json.getString("CODE"));
            info.setEndDate(json.getString("END_DATE"));
            info.setChangeType(json.getString("CHANGE_TYPE"));
            info.setType(json.getString("TYPE"));
            info.setValue(json.getString("VALUE"));
            info.setStartDate(json.getString("START_DATE"));
            info.setOriginValue(json.getString("ORIGIN_VALUE"));
            return info;
        }

        public static List<AttrInfo> fromJsonArray(JSONArray jsonArray) {
            List<AttrInfo> list = new ArrayList<>();
            if (jsonArray != null) {
                for (int i = 0; i < jsonArray.size(); i++) {
                    AttrInfo info = fromJson(jsonArray.getJSONObject(i));
                    if (info != null) {
                        list.add(info);
                    }
                }
            }
            return list;
        }
    }

    @Data
    public static class LevelAttrsInfo implements Serializable {
        /** 执行开始时间 */
        private String executorStart;
        /** 省份编码 */
        private String provinceCode;
        /** 合同金额 */
        private String contractAmount;
        /** 合同状态编码 */
        private String contractStatusCode;
        /** 合同生命周期 */
        private String contractLifecycle;
        /** 执行结束时间 */
        private String executorEnd;

        public static LevelAttrsInfo fromJson(JSONObject json) {
            if (json == null)
                return null;

            LevelAttrsInfo info = new LevelAttrsInfo();
            info.setExecutorStart(json.getString("EXECUTOR_START"));
            info.setProvinceCode(json.getString("PROVINCE_CODE"));
            info.setContractAmount(json.getString("CONTRACT_AMOUNT"));
            info.setContractStatusCode(json.getString("CONTRACT_STATUS_CODE"));
            info.setContractLifecycle(json.getString("CONTRACT_LIFECYCLE"));
            info.setExecutorEnd(json.getString("EXECUTOR_END"));
            return info;
        }
    }
}
