package cn.chinaunicom.sdsi.score.evaluate.service;

import cn.chinaunicom.sdsi.score.demension.entity.ScoreDimensionEvaluate;
import cn.chinaunicom.sdsi.score.evaluate.entity.Evaluate;
import cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateQuery;
import cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【t_sanquan_score_model】的数据库操作Service
 * @createDate 2024-09-14 10:12:53
 */
public interface IEvaluateService extends IService<Evaluate> {
    /**
     * 保存/修改數據
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    boolean insertOrUpdate(EvaluateVo entityVo);


    /**
     * 列表查询
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    List<EvaluateVo> findList(EvaluateQuery query);

    /**
     * 分页查询
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    IPage<EvaluateVo> findPage(EvaluateQuery query);

    /**
     * 根据主键查询
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    EvaluateVo findOne(String id);

    /**
     * 根据主键删除
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    Integer delete(String id);

    List<ScoreDimensionEvaluate>findListByDimensionId(String dimensionId);

}
