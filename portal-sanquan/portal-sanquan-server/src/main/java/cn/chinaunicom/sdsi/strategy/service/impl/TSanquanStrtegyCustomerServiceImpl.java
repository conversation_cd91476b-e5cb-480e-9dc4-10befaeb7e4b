package cn.chinaunicom.sdsi.strategy.service.impl;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyCustomer;
import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyProduct;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyCustomerQuery;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyProductQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyCustomerVO;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyProductVO;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import cn.chinaunicom.sdsi.strategy.dao.TSanquanStrtegyCustomerMapper;
import cn.chinaunicom.sdsi.strategy.dao.TSanquanStrtegyProductMapper;
import cn.chinaunicom.sdsi.strategy.service.TSanquanStrtegyCustomerService;
import cn.chinaunicom.sdsi.strategy.service.TSanquanStrtegyProductService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 策略推荐结果表业务实现类
 *
 * <AUTHOR>
 * @date 2024-04-16
 */
@Service
public class TSanquanStrtegyCustomerServiceImpl extends ServiceImpl<TSanquanStrtegyCustomerMapper, TSanquanStrtegyCustomer> implements TSanquanStrtegyCustomerService {

    @Autowired
    private UnifastContext unifastContext;

    @Override
    public IPage<TSanquanStrtegyCustomerVO> findPage(TSanquanStrtegyCustomerQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return baseMapper.findPage(page, query);
    }

    @Override
    public List<TSanquanStrtegyCustomerVO> findList(TSanquanStrtegyCustomerQuery query) {
        return baseMapper.findList(query);
    }

    @Override
    public int findByTotalCount(TagQuery query) {
        return baseMapper.findByTotalCount(query);
    }

    @Override
    public int findByMonthCount(TagQuery query) {
        return baseMapper.findByMonthCount(query);
    }

    @Override
    public TSanquanStrtegyCustomerVO findOneById(TSanquanStrtegyCustomerQuery query) {
        return baseMapper.findOneById(query);
    }


}
