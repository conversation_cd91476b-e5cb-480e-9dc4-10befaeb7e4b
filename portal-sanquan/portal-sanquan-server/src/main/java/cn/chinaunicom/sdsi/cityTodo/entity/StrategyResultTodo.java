package cn.chinaunicom.sdsi.cityTodo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 策略推送至地市记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_sanquan_strategy_result_todo")
public class StrategyResultTodo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private String id;


    private String todoCode;

    private String ownerSubject;

    private String dueTime;

    private String status;

    private String createUser;

    private String createTime;

    private String strategyResultId;

    private String strategyConfigId;

    /**
     * 执行人
     */
    private String currentPerson;

    /**
     * 执行人工号
     */
    private String currentPersonId;

    /**
     * 推送类型
     */
    private String pushType;


}
