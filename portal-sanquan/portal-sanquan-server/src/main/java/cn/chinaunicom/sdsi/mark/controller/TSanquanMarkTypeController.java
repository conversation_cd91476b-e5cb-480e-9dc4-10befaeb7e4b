package cn.chinaunicom.sdsi.mark.controller;

import cn.chinaunicom.sdsi.cloud.mark.entity.TSanquanMarkType;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.mark.service.TSanquanMarkTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 打标-标签类型
*
* <AUTHOR>
* @since  2024-04-12
*/
@RestController
@RequestMapping("/markType")
@Tag(name="打标-标签类型")
public class TSanquanMarkTypeController extends BaseController {

    @Autowired
    private TSanquanMarkTypeService tSanquanMarkTypeService;

//    @GetMapping("page")
//    @Operation(summary = "分页")
//    public Result<PageResult<TSanquanMarkTypeVO>> page(@ParameterObject @Valid TSanquanMarkTypeQuery query){
//        PageResult<TSanquanMarkTypeVO> page = tSanquanMarkTypeService.page(query);
//
//        return Result.ok(page);
//    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanMarkType> get(@PathVariable("id") Long id){
        TSanquanMarkType entity = tSanquanMarkTypeService.getById(id);
        return ok(entity);
    }

    /**
     * 新增标签分类
     * @param vo
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "保存")
    public BaseResponse<String> save(@RequestBody TSanquanMarkType vo){
        tSanquanMarkTypeService.save(vo);
        return ok(vo.getId());
    }

    /**
     * 修改标签分类
     * @param vo
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "修改")
    public BaseResponse<Boolean> update(@RequestBody TSanquanMarkType vo){
        return ok(tSanquanMarkTypeService.saveOrUpdate(vo));
    }

    /**
     * 删除标签分类
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @Operation(summary = "删除")
    public BaseResponse<Boolean> delete(@RequestBody String id){
        return ok(tSanquanMarkTypeService.removeById(id));
    }
}