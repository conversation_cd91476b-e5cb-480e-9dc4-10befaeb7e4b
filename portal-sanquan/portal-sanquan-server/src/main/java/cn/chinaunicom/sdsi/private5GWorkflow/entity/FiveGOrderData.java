package cn.chinaunicom.sdsi.private5GWorkflow.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * 5G订单数据实体类
 * 对应表：t_sanquan_fiveg_order_data
 */
public class FiveGOrderData implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单类型（专线开通、5G专网订购）
     */
    private String orderType;

    /**
     * 5G专网类型（0-人网、1-物网）
     */
    private String networkType;

    /**
     * 5G专网组网方式（1-人网虚拟专网、2-人网混合专网、3-物网虚拟专网、4-物网混合/独立专网）
     */
    private String networkMode;

    /**
     * 投资申请实例号
     */
    private String investmentOrderId;

    /**
     * 订单状态（正常、作废）
     */
    private String orderStatus;

    /**
     * 地市
     */
    private String area;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 服务号码
     */
    private String serviceNumber;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 装机地址
     */
    private String installationAddress;

    /**
     * 专线受理时间
     */
    private String lineAcceptTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 专线开通订单号
     */
    private String lineOpenOrderId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }

    public String getNetworkMode() {
        return networkMode;
    }

    public void setNetworkMode(String networkMode) {
        this.networkMode = networkMode;
    }

    public String getInvestmentOrderId() {
        return investmentOrderId;
    }

    public void setInvestmentOrderId(String investmentOrderId) {
        this.investmentOrderId = investmentOrderId;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getServiceNumber() {
        return serviceNumber;
    }

    public void setServiceNumber(String serviceNumber) {
        this.serviceNumber = serviceNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getInstallationAddress() {
        return installationAddress;
    }

    public void setInstallationAddress(String installationAddress) {
        this.installationAddress = installationAddress;
    }

    public String getLineAcceptTime() {
        return lineAcceptTime;
    }

    public void setLineAcceptTime(String lineAcceptTime) {
        this.lineAcceptTime = lineAcceptTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getLineOpenOrderId() {
        return lineOpenOrderId;
    }

    public void setLineOpenOrderId(String lineOpenOrderId) {
        this.lineOpenOrderId = lineOpenOrderId;
    }

    @Override
    public String toString() {
        return "FiveGOrderData{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", orderType='" + orderType + '\'' +
                ", networkType='" + networkType + '\'' +
                ", networkMode='" + networkMode + '\'' +
                ", investmentOrderId='" + investmentOrderId + '\'' +
                ", orderStatus='" + orderStatus + '\'' +
                ", area='" + area + '\'' +
                ", customerName='" + customerName + '\'' +
                ", serviceNumber='" + serviceNumber + '\'' +
                ", productName='" + productName + '\'' +
                ", installationAddress='" + installationAddress + '\'' +
                ", lineAcceptTime='" + lineAcceptTime + '\'' +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", lineOpenOrderId='" + lineOpenOrderId + '\'' +
                '}';
    }
}
