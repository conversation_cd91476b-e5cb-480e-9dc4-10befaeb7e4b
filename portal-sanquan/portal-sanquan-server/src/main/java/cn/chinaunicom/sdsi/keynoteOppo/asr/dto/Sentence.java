package cn.chinaunicom.sdsi.keynoteOppo.asr.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: dzk
 * @Date: 2025/07/07 14:13
 * @ClassName: Sentence
 * @Description: TODO
 * @Version: 1.0
 **/
@Data
public class Sentence {
    // 左声道0，右声道1
    @JsonProperty("channel_id")
    private int channelId;

    // 开始毫秒
    @JsonProperty("begin_time")
    private int beginTime;

    // 结束毫秒
    @JsonProperty("end_time")
    private int endTime;

    // 同一声道上下句空闲时间（秒）
    @JsonProperty("silence_duration")
    private int silenceDuration;

    @JsonProperty("speech_rate")
    private float speechRate;

    @JsonProperty("emotion_value")
    private float emotionValue;

    // 每秒几个字
    private float speed;

    // 每个字对应的分贝大小
    private List<Float> dbList;

    // 平均分贝的大小
    private float dbAvg;

    // 识别信息
    private String text;

    // 开启汉字转化阿拉伯数据的对比数据。
    private String cnText;

    // 对应识别时间 毫秒
    private List<Float> timestamps;

    // 对应识别字
    private List<String> tokens;

    // 原始asr识别返回数据
    private List<String> oldTokens;
}
