package cn.chinaunicom.sdsi.customerGroup.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.ibatis.type.JdbcType;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 客户群关联客户对象 t_sanquan_customer_group_customer
 * @Author: hanruxiao
 * @Date: 2024-05-18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "客户群关联客户对象", description = "客户群关联客户对象")
@TableName("t_sanquan_customer_group_customer")
public class TSanquanCustomerGroupCustomer implements Serializable {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "客户群id")
    private String customerGroupId;

    @Schema(name = "客户id")
    private String customerId;

    @Schema(name = "客户名称")
    private String customerName;

    @Schema(name = "关联时间")
    private String createTime;

}
