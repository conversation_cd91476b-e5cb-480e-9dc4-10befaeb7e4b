package cn.chinaunicom.sdsi.score.evaluate.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/9/18 14:16
 */
@Data
public class EvaluateQuery extends BaseQueryVO {
    @TableId
    private String id;
    private String evaluateName;
    private String evaluateCode;
    private String mobo;
    private String creator;
    private String createTime;
    private String deleted;

}
