package cn.chinaunicom.sdsi.score.model.service.impl;

import cn.chinaunicom.sdsi.framework.utils.BeanHelper;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.score.ValidEntity;
import cn.chinaunicom.sdsi.score.demension.entity.ScoreModelDimension;
import cn.chinaunicom.sdsi.score.demension.mapper.ScoreModelDimensionMapper;
import cn.chinaunicom.sdsi.score.model.entity.ScoreModel;
import cn.chinaunicom.sdsi.score.model.entity.ScoreModelQuery;
import cn.chinaunicom.sdsi.score.model.entity.ScoreModelVo;
import cn.chinaunicom.sdsi.score.model.mapper.ScoreModelMapper;
import cn.chinaunicom.sdsi.score.model.service.IScoreModelService;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_sanquan_score_model】的数据库操作Service实现
 * @createDate 2024-09-14 10:12:53
 */
@Service
public class ScoreModelServiceImpl extends ServiceImpl<ScoreModelMapper, ScoreModel>
        implements IScoreModelService {

    @Autowired
    private ScoreModelDimensionMapper scoreModelDimensionMapper;

    @Override
    public boolean insertOrUpdate(ScoreModelVo entityVo) {
        try {
            ScoreModel scoreModel = new ScoreModel();
            BeanHelper.copyProperties(entityVo, scoreModel);
            boolean ok = super.saveOrUpdate(scoreModel);
            scoreModelDimensionMapper.deleteByModelId(scoreModel.getId());
            List<ScoreModelDimension> list = entityVo.getDimensionArray().stream().peek(item -> item.setModelId(scoreModel.getId())).collect(Collectors.toList());
            scoreModelDimensionMapper.insertBatchSomeColumn(list);
            return ok;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public boolean checkExist(ValidEntity query) {
        LambdaUpdateWrapper<ScoreModel> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        if ("modelName".equals(query.getType())) {
            lambdaUpdateWrapper.eq(ScoreModel::getModelName, query.getCode());
        } else if ("modelCode".equals(query.getType())) {
            lambdaUpdateWrapper.eq(ScoreModel::getModelCode, query.getCode());
        }
        lambdaUpdateWrapper.eq(ScoreModel::getDeleted, 0);
        if (query.getId() != null) {
            lambdaUpdateWrapper.ne(ScoreModel::getId, query.getId());
        }
        List<ScoreModel> list = baseMapper.selectList(lambdaUpdateWrapper);
        return list == null || list.isEmpty();
    }

    @Override
    public List<ScoreModelVo> findList(ScoreModelQuery query) {
        return this.baseMapper.findList(query);
    }

    @Override
    public IPage<ScoreModelVo> findPage(ScoreModelQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return baseMapper.findPage(page, query);
    }

    @Override
    public ScoreModelVo findOne(String id) {
        return baseMapper.findOne(id);
    }

    @Override
    public Integer delete(String id) {
        return baseMapper.deleteById(id);
    }
}




