package cn.chinaunicom.sdsi.ls.entity;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelTitle;
import cn.chinaunicom.sdsi.plugins.office.excel.annotation.TitleStyle;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @Description: 地市接口人对象
 * @Author: dzk
 * @Date: 2024-05-31
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@TitleStyle(backgroundColor = 10)
public class LsQueryExcel {

    @ExcelProperty(title = "序号", width = 2000, cellSort = 0, rowSpan = 2)
    private String rowNum;
    /**
     * 客户名称
     */
    @ExcelProperty(title = "客户名称", width = 8000, cellSort = 1, rowSpan = 2)
    private String custName;

    private Integer custLsNum;
    /**
     * 客户六数收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 3)
    private String custLsIncome;
    /**
     * 细化客户名称
     */
    @ExcelProperty(title = "名单制客户", width = 10000,cellSort = 4,rowSpan = 2, fillChildrenValue = true)
    private String rosterCustomerName;
    /**
     * 细化客户六数数量
     */
    private Integer rosterCustomerLsNum;
    /**
     * 细化客户六数收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 6, fillChildrenValue = true)
    private String rosterCustomerLsIncome;
    /**
     * 数采数量
     */

    private Integer scNum;
    /**
     * 数采收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 18, fillChildrenValue = true)
    private String scIncome;
    /**
     * 数传数量
     */
    private Integer schuanNum;
    /**
     * 数传收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 8, fillChildrenValue = true)
    private String schuanIncome;

    private Integer scunNum;
    /**
     * 数存收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 16, fillChildrenValue = true)
    private String scunIncome;
    /**
     * 数管数量
     */
    private Integer sgNum;
    /**
     * 数管收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 10, fillChildrenValue = true)
    private String sgIncome;
    /**
     * 数安数量
     */
    private Integer saNum;
    /**
     * 数安收入
     */
    @ColumnWidth(20)
    @ExcelProperty(title = "收入(元)", cellSort = 12, fillChildrenValue = true)
    private String saIncome;
    /**
     * 数用数量
     */
    private Integer syNum;
    /**
     * 数用收入
     */
    @ExcelProperty(title = "收入(元)", cellSort = 14, fillChildrenValue = true)
    private String syIncome;
    /**
     * 月
     */
    private String monthId;
    @Schema(description = "下级管理的")
    private List<LsQueryExcel> children;
    @Schema(description = "序号")
    private String orderNum;
    @ExcelProperty(title = "涵盖", cellSort = 2,
            firstRowCell = @ExcelTitle(title = "客户六数", colSpan = 2))
    @Schema(description = "'客户包含六数种类'")
    private String lsCustTypeNum;
    @ExcelProperty(title = "涵盖", cellSort = 5, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "名单客户六数", colSpan = 2))
    @Schema(description = "'名单客户六数种数'")
    private String lsCustomerTypeNum;
    @ExcelProperty(title = "是否涵盖", cellSort = 17, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "数采", colSpan = 2))
    @Schema(description = "'是否数采'")
    private String isShucai;
    @ExcelProperty(title = "是否涵盖", cellSort = 7, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "数传", colSpan = 2))
    @Schema(description = "''是否数传''")
    private String isShuchuan;
    @ExcelProperty(title = "是否涵盖", cellSort = 15, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "数存", colSpan = 2))
    @Schema(description = "'是否数存'")
    private String isShucun;
    @ExcelProperty(title = "是否涵盖", cellSort = 9, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "数管", colSpan = 2))
    @Schema(description = "''是否数管''")
    private String isShuguan;
    @ExcelProperty(title = "是否涵盖", cellSort = 11, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "数安", colSpan = 2))
    @Schema(description = "'''是否数安'''")
    private String isShuan;
    @ExcelProperty(title = "是否涵盖", cellSort =13, fillChildrenValue = true,
            firstRowCell = @ExcelTitle(title = "数用", colSpan = 2))
    @Schema(description = "''''是否数用''''")
    private String isShuyong;
}
