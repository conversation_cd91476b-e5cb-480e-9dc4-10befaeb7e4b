package cn.chinaunicom.sdsi.statistics.controller;

import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerInfoQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerInfoVO;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.statistics.entity.StatisticsDetailResult;
import cn.chinaunicom.sdsi.statistics.entity.StatisticsQuery;
import cn.chinaunicom.sdsi.statistics.entity.StatisticsResult;
import cn.chinaunicom.sdsi.statistics.service.StatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @Date 2024/5/25 17:51
 */
@RestController
@RequestMapping("/statistic/customer")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;


    @GetMapping("/findPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<StatisticsResult> findStatistics(StatisticsQuery query) {
        if (UserUtils.hasPermission("ROLE_CITY_INTERFACE")) {
            query.setCity(UserUtils.getUser().getCity());
        }
        return new BasePageResponse<>(statisticsService.findStatistics(query));
    }

    @GetMapping("/findDetailsPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<StatisticsDetailResult> findDetailsPage(StatisticsQuery query) {
        return new BasePageResponse<>(statisticsService.findStatisticsDetails(query));
    }

    @GetMapping("/findCityGroup")
    @Operation(summary = "查询地市", description = "查询地市")
    public BaseResponse<List<String>> findCityGroup() {
        return new BaseResponse<>(statisticsService.findCityGroup());
    }

    @GetMapping("/findCityGroupAndAllOrder")
    @Operation(summary = "查询地市", description = "查询地市及全部，并排序")
    public BaseResponse<List<String>> findCityGroupAndAllOrder() {
        return new BaseResponse<>(statisticsService.findCityGroupAndAllOrder());
    }

    @GetMapping("/findCityGroupAndAllOrderNo")
    @Operation(summary = "查询地市", description = "查询地市及全部，并排序(无权限)")
    public BaseResponse<List<String>> findCityGroupAndAllOrderNo() {
        return new BaseResponse<>(statisticsService.findCityGroupAndAllOrderNo());
    }

    @GetMapping("/findDistrictGroup")
    @Operation(summary = "查询区县", description = "查询区县")
    public BaseResponse<List<String>> findCityGroup(StatisticsQuery query) {
        return new BaseResponse<>(statisticsService.findDistrictGroup(query));
    }

    /**
     * 导出客户统计信息
     * @param query 查询条件
     */
    @Operation(summary = "根据条件导出客户统计信息", description = "根据条件导出客户统计信息")
    @PostMapping("/exportCustomerStatisticsDataByQuery")
    public void exportCustomerStatisticsDataByQuery(@RequestBody StatisticsQuery query) {
        statisticsService.exportCustomerStatisticsDataByQuery(query);
    }
}
