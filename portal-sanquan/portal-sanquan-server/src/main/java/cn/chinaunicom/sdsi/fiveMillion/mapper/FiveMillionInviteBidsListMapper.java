package cn.chinaunicom.sdsi.fiveMillion.mapper;

import cn.chinaunicom.sdsi.fiveMillion.entity.FiveMillionInviteBidsList;
import cn.chinaunicom.sdsi.fiveMillion.entity.FiveMillionOppoList;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FiveMillionInviteBidsListMapper extends BaseMapper<FiveMillionInviteBidsList> {
    List<FiveMillionInviteBidsList> findList(@Param("dayId") String dayId);

}
