package cn.chinaunicom.sdsi.strategy.service;


import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrategyInterview;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrategyInterviewQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrategyInterviewVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 摸排信息表
 *
 * <AUTHOR> 
 * @since  2024-04-25
 */
public interface TSanquanStrategyInterviewService extends IService<TSanquanStrategyInterview> {

    //分页数据
    IPage<TSanquanStrategyInterviewVO> findPage(TSanquanStrategyInterviewQuery query);

    /**
     * 添加摸排信息
     * @param vo
     * @return
     */
    String add(TSanquanStrategyInterviewVO vo);

    /**
     * 修改
     * @param vo
     * @return
     */
    Boolean updateInterview(TSanquanStrategyInterviewVO vo);

    /**
     * 根据潜在机会id查询走访信息
     * @param id
     * @return
     */
    TSanquanStrategyInterviewVO getInterview(String id);

    //根据id查询
    TSanquanStrategyInterview findById(String id);

    // 获取详情信息(根据todoCode查询)
    TSanquanStrategyInterviewVO getInterviewByTodoOppoId(String todoOppoId);
}