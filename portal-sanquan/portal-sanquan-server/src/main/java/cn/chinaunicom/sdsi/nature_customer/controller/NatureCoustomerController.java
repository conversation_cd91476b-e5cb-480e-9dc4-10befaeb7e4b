package cn.chinaunicom.sdsi.nature_customer.controller;

import cn.chinaunicom.sdsi.cloud.nature_customer.entity.NatureCoustomer;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.NatureCoustomerQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO;
import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.nature_customer.service.NatureCoustomerService;
import cn.chinaunicom.sdsi.shuangxianProtocolExpiration.controller.ShuangxianProtocolExpirationMsgController;
import cn.chinaunicom.sdsi.tag.service.TagService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标签 控制器
 */
@RestController
@RequestMapping("/nature/customer/")
public class NatureCoustomerController extends BaseController {

    @Autowired
    private NatureCoustomerService baseService;

    @Operation(summary = "分页", description = "分页")
    @GetMapping("/findPage")
    public BasePageResponse<NatureCoustomerVO> findPage(NatureCoustomerQuery query) {
        return pageOk(baseService.findPage(query));
    }

    @Operation(summary = "查询列表", description = "查询列表")
    @GetMapping("/findList")
    public BaseResponse<List<NatureCoustomerVO>> findList(NatureCoustomerQuery query) {
        return ok(baseService.findList(query));
    }

    @Operation(summary = "根据id查询", description = "根据id查询")
    @GetMapping("/findOne")
    public BaseResponse<NatureCoustomerVO> findOne(String id) {
        NatureCoustomerQuery query = new NatureCoustomerQuery();
        query.setRosterCustomerId(id);
        return ok(baseService.findOneById(query));
    }


    @Operation(summary = "保存数据", description = "保存数据")
    @PostMapping("/save")
    public BaseResponse<Boolean> add(@RequestBody NatureCoustomer entity) {
        return ok(baseService.saveOrUpdate(entity));
    }

    @Operation(summary = "查询地市", description = "查询地市")
    @PostMapping("/findCity")
    public BaseResponse<List<String>> findCity() {
        return ok(baseService.findCityByCityName());
    }

    @Operation(summary = "删除", description = "删除")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String id) {
        return ok(baseService.removeById(id));
    }

}
