package cn.chinaunicom.sdsi.dsbusinessopportunity.service.impl;

import cn.chinaunicom.sdsi.aiability.config.AIThreadPoolConfig;
import cn.chinaunicom.sdsi.aiability.service.RagRecommendProductService;
import cn.chinaunicom.sdsi.aiability.utils.AITextRegexUtils;
import cn.chinaunicom.sdsi.aiability.utils.InternalDeepSeekUtils;
import cn.chinaunicom.sdsi.dsbusinessopportunity.constant.BusinessOpportunityConstant;
import cn.chinaunicom.sdsi.dsbusinessopportunity.entity.DsBusinessAnalyse;
import cn.chinaunicom.sdsi.dsbusinessopportunity.entity.DsOpportunityAnalyse;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.AsyncProcessingService;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.DsBusinessAnalyseService;
import cn.chinaunicom.sdsi.dsbusinessopportunity.service.DsOpportunityAnalyseService;
import com.alibaba.fastjson2.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;
/**
 * 异步任务处理服务
 */
@Service
public class AsyncProcessingServiceImpl implements AsyncProcessingService {
    private static final Logger logger = LoggerFactory.getLogger(AsyncProcessingServiceImpl.class);
    @Lazy
    @Autowired
    private DsOpportunityAnalyseService dsOpportunityAnalyseService;
    @Lazy
    @Autowired
    private DsBusinessAnalyseService dsBusinessAnalyseService;
    @Autowired
    private InternalDeepSeekUtils internalDeepSeekUtils;
    @Autowired
    private RagRecommendProductService ragRecommendProductService;
    @Override
    @Async(AIThreadPoolConfig.TASK_EXECUTOR_BUS_NAME)
    public void processBusinessAnalyseTask(DsBusinessAnalyse task) {
        /*StringBuffer prompt = new StringBuffer("假设现在你是一名联通公司的客情分析师，根据下面的商机名称、商机描述，提取联通可切入的需求方向关键词。")
                .append("分析要求：提出联通可切入的业务方向，并给出优先级。")
                .append("输出要求：只需要输出与联通业务相关的关键字，并按照优先级排名，请按json形式输出。")
                .append("输出格式：```json\n" +
                        "{\n" +
                        "  \"keywords\": [\n" +
                        "    \"5G网络建设\",\n" +
                        "    \"物联网（IoT）\",\n" +
                        "    \"大数据分析\",\n" +
                        "    \"云计算服务\",\n" +
                        "    \"智慧农业\",\n" +
                        "    \"硬件集成\",\n" +
                        "    \"智能监控系统\",\n" +
                        "    \"数据传输\",\n" +
                        "    \"农业信息化\"\n" +
                        "  ]\n" +
                        "}\n" +
                        "```");*/
        StringBuffer prompt = new StringBuffer();
        prompt.append("你现在是一名资深的中国联通客情分析师。\n\n");
        prompt.append("**任务目标：**\n");
        prompt.append("根据提供的“商机名称”和“商机描述”，深入分析客户的**信息化需求**、**数字化转型痛点**，并从中提取出中国联通可切入的、**与信息化技术、平台及服务强相关**的、具有商业价值的业务方向关键词。\n\n");
        prompt.append("**分析原则：**\n");
        prompt.append("1.  **核心能力导向与业务需求驱动相结合：** 您分析的重点是客户商机描述中的**具体需求点**。提取的关键词必须源自这些具体需求，并确保其所代表的业务方向能够与中国联通的核心业务能力领域相契合。下方的核心能力列表旨在阐明联通可提供服务的**宏观领域**，作为您判断客户具体需求是否属于联通服务范畴的**指导性框架**。联通的核心能力领域包括：\n");
        prompt.append("    *   **基础通信网络与连接：** 提供全面的基础网络接入、数据传输和组网服务，涵盖移动通信（如5G）、固定通信、物联网专用连接等技术领域。\n");
        prompt.append("    *   **物联网（IoT）解决方案：** 聚焦物联网连接管理、设备管理、数据采集与处理，提供支持各类行业应用的物联网平台及相关服务。\n");
        prompt.append("    *   **云计算服务：** 依托联通云，提供从基础设施（IaaS）、平台（PaaS）到软件（SaaS）的各类云计算资源、服务以及边缘计算能力。\n");
        prompt.append("    *   **大数据与人工智能（AI）：** 围绕数据价值的挖掘与应用，提供数据处理、分析、治理、可视化服务，以及人工智能算法、模型、算力与智能化应用平台相关的技术与服务。\n");
        prompt.append("    *   **行业数字化解决方案：** 面向政府及各行各业的数字化转型需求，提供融合云、网、数、智、安等技术能力的综合性解决方案设计、开发、集成与实施服务。\n");
        prompt.append("    *   **网络与数据安全：** 构建端到端的安全防护体系，提供网络安全、数据安全、云安全、应用安全等领域的安全产品、服务与咨询。\n");
        prompt.append("    *   **IT服务与集成：** 围绕企业IT基础设施和应用系统，提供规划咨询、系统集成、软件开发、运维外包等全生命周期IT服务。\n");
        prompt.append("    *   **企业通信与协作：** 为企业提供高效便捷的内外沟通与协同办公能力，包括融合通信、视频会议、呼叫中心等解决方案。\n");
        prompt.append("2.  **识别深层信息化需求：** 尝试从描述中识别出客户表面需求背后对数据处理、系统集成、平台建设、智能应用、网络保障等深层信息化、数字化需求。\n");
        prompt.append("3.  **精准提炼具体关键词，严禁过度联想与宽泛匹配：**\n");
        prompt.append("    *   提取的每一个关键词都**必须**是客户商机描述中**明确提及或能够直接且唯一推断出**的、代表客户具体需求的**特定信息化技术、平台、产品或服务名称**。\n");
        prompt.append("    *   **严禁**将核心能力列表中的大类方向（例如，直接使用“云计算服务”、“物联网解决方案”或“行业数字化解决方案”）作为关键词输出。\n");
        prompt.append("    *   关键词应精确反映客户对某一具体解决方案或技术的实际诉求。例如，若客户描述需要“建设一套用于工厂设备远程监控和故障预警的系统”，并且这属于联通物联网解决方案的范畴，那么您应该提取如“设备远程监控系统”、“工业设备故障预警平台”或“物联网监控预警平台”等具体的关键词，而非笼统的“物联网解决方案”或“物联网平台”。\n");
        prompt.append("    *   **严禁**进行过度联想、引申或创造商机描述中**未直接或明确暗示**的业务方向。\n");
        prompt.append("4.  **商业价值评估：** 优先考虑那些能为联通带来较高潜在业务价值、且联通具有竞争优势的需求方向。\n\n");
        prompt.append("**优先级排序依据：**\n");
        prompt.append("1.  **与联通核心信息化业务的契合度：** 越是联通的基础、优势信息化业务，优先级越高。\n");
        prompt.append("2.  **潜在业务规模/价值：** 预计能为联通带来较大收入或战略意义的需求。\n");
        prompt.append("3.  **需求紧迫性/明确性：** 客户需求越明确、越紧迫，优先级越高。\n\n");
        prompt.append("**输入格式示例：**\n");
        prompt.append("请等待我提供以下格式的商机信息：\n");
        prompt.append("```\n");
        prompt.append("{\n");
        prompt.append("  \"商机名称\": \"示例商机名称\",\n");
        prompt.append("  \"商机描述\": \"这是关于示例商机的详细描述，包含客户的痛点、目标和现有情况等。\"\n");
        prompt.append("}\n");
        prompt.append("```\n\n");
        prompt.append("**输出要求：**\n");
        prompt.append("请严格按照以下JSON格式输出，只包含与联通业务相关的、**明确指向具体信息化技术、平台或服务**的关键字，并按照优先级从高到低排列。关键字应为具体的业务方向或解决方案类型，**优先考虑能够直接体现为联通可提供的成熟产品、解决方案或特定服务能力的名称，这些名称应主要源自客户的直接描述或紧密推断。**\n\n");
        prompt.append("**输出格式示例：**\n");
        prompt.append("```json\n");
        prompt.append("{\n");
        prompt.append("  \"keywords\": [\n");
        prompt.append("    \"5G行业虚拟专网\",\n");
        prompt.append("    \"AI质检平台\",\n");
        prompt.append("    \"设备远程监控系统\",\n");
        prompt.append("    \"联通云IaaS服务\",\n");
        prompt.append("    \"工业大数据分析平台\",\n");
        prompt.append("    \"边缘计算节点部署\",\n");
        prompt.append("    \"智慧工厂MES系统集成\",\n");
        prompt.append("    \"数据防泄漏（DLP）解决方案\",\n");
        prompt.append("    \"IT系统定制开发服务\",\n");
        prompt.append("    \"高清视频会议系统\"\n");
        prompt.append("  ]\n");
        prompt.append("}\n");
        prompt.append("```");
        JSONObject opportunityInfo = new JSONObject() {{
            put("商机名称", task.getBusinessName());
            put("商机描述", task.getBusinessContent());
        }};
        prompt.append(opportunityInfo.toJSONString());
        String answer = AITextRegexUtils.extractJson(internalDeepSeekUtils.ask(prompt.toString()));
        JSONObject answerObj = JSONObject.parseObject(answer);
        DsBusinessAnalyse updateParam = new DsBusinessAnalyse();
        updateParam.setId(task.getId()).setStatus(BusinessOpportunityConstant.STATUS_SUCCESS);
        if(answerObj.containsKey("keywords") && answerObj.getJSONArray("keywords") != null){
            List<String> keywords = answerObj.getJSONArray("keywords").toJavaList(String.class);
            LinkedHashSet<String> productSet = ragRecommendProductService.queryRecommendProductByKeywords(keywords);

            logger.info("过滤前产品：{}",productSet.stream().collect(Collectors.joining(",")));
            String description = new StringBuffer("商机名称：").append(task.getBusinessName()).append("，商机描述：").append(task.getBusinessContent()).toString();
            productSet = ragRecommendProductService.filterProductsByDS(description, keywords, productSet);
            logger.info("过滤后产品：{}",productSet.stream().collect(Collectors.joining(",")));

            String keywordsStr = keywords.stream().collect(Collectors.joining(","));
            String productStr = productSet.stream().collect(Collectors.joining(","));
            updateParam.setKeywords(keywordsStr);
            updateParam.setProduct(productStr);
        }
        dsBusinessAnalyseService.updateMatchInfo(updateParam);
    }

    /**
     * 分析标讯任务需求关键词
     * @param task
     */
    @Override
    @Async(AIThreadPoolConfig.TASK_EXECUTOR_NAME)
    public void processOpportunityAnalyseTask(DsOpportunityAnalyse task) {
        StringBuffer prompt = new StringBuffer("假设现在你是一名联通公司的客情分析师，根据下面的标讯标题、标讯内容，提取联通可切入的需求方向关键词。")
                .append("分析要求：提出联通可切入的业务方向，并给出优先级。")
                .append("输出要求：只需要输出与联通业务相关的关键字，并按照优先级排名，请按json形式输出。")
                .append("输出格式：```json\n" +
                        "{\n" +
                        "  \"keywords\": [\n" +
                        "    \"5G网络建设\",\n" +
                        "    \"物联网（IoT）\",\n" +
                        "    \"大数据分析\",\n" +
                        "    \"云计算服务\",\n" +
                        "    \"智慧农业\",\n" +
                        "    \"硬件集成\",\n" +
                        "    \"智能监控系统\",\n" +
                        "    \"数据传输\",\n" +
                        "    \"农业信息化\"\n" +
                        "  ]\n" +
                        "}\n" +
                        "```");
        JSONObject opportunityInfo = new JSONObject() {{
                put("标讯标题", task.getOpportunityTitle());
                put("标讯内容", task.getOpportunityContent());
        }};
        prompt.append(opportunityInfo.toJSONString());
        String answer = AITextRegexUtils.extractJson(internalDeepSeekUtils.ask(prompt.toString()));
        JSONObject answerObj = JSONObject.parseObject(answer);
        DsOpportunityAnalyse updateParam = new DsOpportunityAnalyse();
        updateParam.setId(task.getId()).setStatus(BusinessOpportunityConstant.STATUS_SUCCESS);
        if(answerObj.containsKey("keywords") && answerObj.getJSONArray("keywords") != null){
            List<String> keywords = answerObj.getJSONArray("keywords").toJavaList(String.class);
            LinkedHashSet<String> productSet = ragRecommendProductService.queryRecommendProductByKeywords(keywords);

            String keywordsStr = keywords.stream().collect(Collectors.joining(","));
            String productStr = productSet.stream().collect(Collectors.joining(","));
            updateParam.setKeywords(keywordsStr);
            updateParam.setProduct(productStr);
        }
        dsOpportunityAnalyseService.updateMatchInfo(updateParam);
    }
}
