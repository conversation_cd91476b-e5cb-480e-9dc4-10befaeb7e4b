package cn.chinaunicom.sdsi.cityCustListMark.controller;

import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserAccessParamDTO;
import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserInfoDTO;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserAccessParam;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserInfo;
import cn.chinaunicom.sdsi.cityCustListMark.service.ICityCustUserInfoService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @program: sanquan_server
 * @ClassName CityCustUserInfoController
 * @description:
 * @author: majian
 * @date: 2025-05-23 16:12
 * @Version 1.0
 **/
@RestController
@RequestMapping("/cityCustUserInfo")
public class CityCustUserInfoController extends BaseController {
    @Autowired
    private ICityCustUserInfoService cityCustUserInfoService;
    /**
     * 分页获取用户策略数据列表
     */
    @PostMapping("/list")
    @ResponseBody
    public BasePageResponse<CityCustUserInfo> list(@RequestBody CityCustUserInfoDTO cityCustUserInfoDTO) {
        IPage<CityCustUserInfo> page = cityCustUserInfoService.findPage(cityCustUserInfoDTO);
        return pageOk(page);
    }
}
