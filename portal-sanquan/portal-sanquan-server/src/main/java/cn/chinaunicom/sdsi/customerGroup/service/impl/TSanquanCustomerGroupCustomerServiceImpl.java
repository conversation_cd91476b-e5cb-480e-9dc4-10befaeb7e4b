package cn.chinaunicom.sdsi.customerGroup.service.impl;

import cn.chinaunicom.sdsi.customerGroup.entity.TSanquanCustomerGroupCustomer;
import cn.chinaunicom.sdsi.customerGroup.service.TSanquanCustomerGroupCustomerService;
import cn.chinaunicom.sdsi.customerGroup.vo.TSanquanCustomerGroupCustomerVO;
import cn.chinaunicom.sdsi.customerGroup.queryvo.TSanquanCustomerGroupCustomerQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.chinaunicom.sdsi.customerGroup.mapper.TSanquanCustomerGroupCustomerMapper;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;

/**
 * 客户群关联客户业务实现类
 * 
 * <AUTHOR>
 * @date 2024-05-20
 */
@Service
public class TSanquanCustomerGroupCustomerServiceImpl extends ServiceImpl<TSanquanCustomerGroupCustomerMapper, TSanquanCustomerGroupCustomer> implements TSanquanCustomerGroupCustomerService {

    @Autowired
    private UnifastContext unifastContext;

    /**
     * 分页查询客户群关联客户
     * 
     * @param tSanquanCustomerGroupCustomerQueryVO
     * @return IPage<TSanquanCustomerGroupCustomerVO>
     */
    @Override
    public IPage<TSanquanCustomerGroupCustomerVO> findPage(TSanquanCustomerGroupCustomerQueryVO tSanquanCustomerGroupCustomerQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanCustomerGroupCustomerQueryVO);
//        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
//            tSanquanCustomerGroupCustomerQueryVO.setCreateBy(unifastContext.getUser().getStaffId());
//        }
        return baseMapper.findPage(page, tSanquanCustomerGroupCustomerQueryVO);
    }
    @Override
    public IPage<TSanquanCustomerGroupCustomerVO> findPageCustomerView(TSanquanCustomerGroupCustomerQueryVO tSanquanCustomerGroupCustomerQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanCustomerGroupCustomerQueryVO);
        return baseMapper.findPageCustomerView(page, tSanquanCustomerGroupCustomerQueryVO);
    }

    /**
     * 查询客户群关联客户详细信息
     * 
     * @param id
     * @return TSanquanCustomerGroupCustomerVO
     */
    @Override
    public TSanquanCustomerGroupCustomerVO findInfo(String id) {
        TSanquanCustomerGroupCustomer tSanquanCustomerGroupCustomer = baseMapper.selectById(id);
        TSanquanCustomerGroupCustomerVO tSanquanCustomerGroupCustomerVO = new TSanquanCustomerGroupCustomerVO();
        BeanUtils.copyProperties(tSanquanCustomerGroupCustomer, tSanquanCustomerGroupCustomerVO);
        return tSanquanCustomerGroupCustomerVO;
    }

    /**
     * 新增客户群关联客户
     * 
     * @param tSanquanCustomerGroupCustomerVO
     * @return String
     */
    @Override
    public String add(TSanquanCustomerGroupCustomerVO tSanquanCustomerGroupCustomerVO) {
         TSanquanCustomerGroupCustomer tSanquanCustomerGroupCustomer = new TSanquanCustomerGroupCustomer();
         BeanUtils.copyProperties(tSanquanCustomerGroupCustomerVO, tSanquanCustomerGroupCustomer);
         baseMapper.insert(tSanquanCustomerGroupCustomer);
         return tSanquanCustomerGroupCustomer.getId();
    }

    /**
     * 修改客户群关联客户
     * 
     * @param tSanquanCustomerGroupCustomerVO
     * @return Boolean
     */
    @Override
    public Boolean update(TSanquanCustomerGroupCustomerVO tSanquanCustomerGroupCustomerVO) {
        TSanquanCustomerGroupCustomer tSanquanCustomerGroupCustomer = new TSanquanCustomerGroupCustomer();
        BeanUtils.copyProperties(tSanquanCustomerGroupCustomerVO, tSanquanCustomerGroupCustomer);
        return baseMapper.updateById(tSanquanCustomerGroupCustomer) > 0;
    }

    /**
     * 删除客户群关联客户
     * 
     * @param id
     * @return Boolean
     */
    @Override
    public Boolean delete(String id) {
        return baseMapper.deleteById(id) > 0;
    }

}
