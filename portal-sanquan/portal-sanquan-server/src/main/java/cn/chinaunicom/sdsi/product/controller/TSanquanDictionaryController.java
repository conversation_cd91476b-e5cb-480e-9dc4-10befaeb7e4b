package cn.chinaunicom.sdsi.product.controller;

import cn.chinaunicom.sdsi.cloud.product.vo.DictionaryVO;
import cn.chinaunicom.sdsi.product.service.TSanquanDictionaryService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;

import java.util.List;

/**
 * <p>
 * 字典表（下拉框数据） 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@RestController
@RequestMapping("/dictionary")
public class TSanquanDictionaryController extends BaseController {

    @Autowired
    private TSanquanDictionaryService tSanquanDictionaryService;

    /**
     * 添加字典表信息
     * @return
     */
    @PostMapping("/addDictionary")
    @Operation(summary = "数据字典信息")
    public BaseResponse<String> addDictionary(@RequestBody DictionaryVO query){
        return ok(tSanquanDictionaryService.addDictionary(query));
    }

    /**
     * 查询字典类型
     * @return
     */
    @GetMapping("/deleteDictionary/{id}")
    @Operation(summary = "数据字典信息")
    public BaseResponse<Boolean> deleteDictionary(@PathVariable(value = "id") String id){
        return ok(tSanquanDictionaryService.deleteDictionary(id));
    }


}
