package cn.chinaunicom.sdsi.chengxiao.service.impl;

import cn.chinaunicom.sdsi.chengxiao.entity.ChengxiaoZhuanhua;
import cn.chinaunicom.sdsi.chengxiao.entity.ChengxiaoZhuanhuaVO;
import cn.chinaunicom.sdsi.chengxiao.mapper.ChengxiaoZhuanhuaMapper;
import cn.chinaunicom.sdsi.chengxiao.service.ChengxiaoZhuanhuaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 成效转化 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-13
 */
@Service
public class ChengxiaoZhuanhuaServiceImpl extends ServiceImpl<ChengxiaoZhuanhuaMapper, ChengxiaoZhuanhua> implements ChengxiaoZhuanhuaService {

    @Autowired
    public UnifastContext unifastContext;

    @Autowired
    public ChengxiaoZhuanhuaMapper chengxiaoZhuanhuaMapper;

    public List<String> getDataSourceList(){
        return chengxiaoZhuanhuaMapper.getDataSourceList();
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-13
     * @param chTSanquanChengxiaoZhuanhuaResult
     * @return IPage<ChTSanquanChengxiaoZhuanhuaResult>
     **/
    @Override
    public IPage<ChengxiaoZhuanhua> findPage(ChengxiaoZhuanhuaVO chengxiaoZhuanhuaVO) {
        IPage page = QueryVoToPageUtil.toPage(chengxiaoZhuanhuaVO);
        if("country".equals(chengxiaoZhuanhuaVO.getDataType())){
            IPage<ChengxiaoZhuanhua> pageList = baseMapper.selectPageCountry(page, chengxiaoZhuanhuaVO);
            List<ChengxiaoZhuanhua> list = pageList.getRecords();
            for (ChengxiaoZhuanhua item : list) {
                if(item.getCityName() != null && !item.getCityName().equals("-")){
                    break;
                }
                if(item.getBusScene()!=null && (item.getBusScene().contains("潜在商机挖掘") || item.getBusScene().contains("存量价值") )){
                    item.setBusScene("全省合计");
                    item.setCityName("全省"); // 合计列
                }else if((item.getCityName() == null || item.getCityName().equals("-")) && !item.getBusScene().equals("合计")
                         && ("线索".equals(item.getDataSource()) || "标讯".equals(item.getDataSource()))){
                    item.setCityName("全省"); // 合计列
                }else if((item.getCityName() == null || item.getCityName().equals("-")) && !item.getBusScene().equals("合计") && item.getDataSource() != null
                        && (item.getDataSource().contains("靶向任务") || item.getDataSource().contains("靶向营销")
                            || item.getDataSource().contains("潜在商机") )){
                    item.setCityName("全省"); // 合计列
                }
            }
            return pageList;
        }else{
            return baseMapper.selectPageProv(page, chengxiaoZhuanhuaVO);
        }

    }

    public String getMaxDate(){
        return baseMapper.getMaxDate();
    }

    public List<String> getCityList(){
        return baseMapper.getCityList();
    }


    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-13
     * @param id
     * @return ChTSanquanChengxiaoZhuanhuaResult
     **/
    @Override
    public ChengxiaoZhuanhua findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-13
     * @return List<ChTSanquanChengxiaoZhuanhuaResult>
     **/
    @Override
    public List<ChengxiaoZhuanhua> findList() {
        return baseMapper.selectList(null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-13
     * @param chTSanquanChengxiaoZhuanhuaResult
     * @return int
     **/
    @Override
    public int add(ChengxiaoZhuanhua chengxiaoZhuanhua) {
        /*if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
        unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            chengxiaoZhuanhua.setTenantId(tenantId);
        }*/
        return baseMapper.insert(chengxiaoZhuanhua);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-13
     * @param chTSanquanChengxiaoZhuanhuaResult
     * @return int
     **/
    @Override
    public int update(ChengxiaoZhuanhua chTSanquanChengxiaoZhuanhuaResult) {
        return baseMapper.updateById(chTSanquanChengxiaoZhuanhuaResult);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-13
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
