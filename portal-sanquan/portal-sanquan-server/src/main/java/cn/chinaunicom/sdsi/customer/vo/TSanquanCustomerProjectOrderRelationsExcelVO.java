package cn.chinaunicom.sdsi.customer.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 客户-项目或订单关系表(导出表)
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class TSanquanCustomerProjectOrderRelationsExcelVO {

	@ColumnWidth(20)
	@ExcelProperty(value = "项目或订单ID")
	private String projectOrderId;

	@ColumnWidth(20)
	@ExcelProperty(value = "项目或订单名称")
	private String projectOrderName;

	@ColumnWidth(20)
	@ExcelProperty(value = "项目金额")
	private String projectAmount;

	@ColumnWidth(20)
	@ExcelProperty(value = "业务类型")
	private String businessName;

}