package cn.chinaunicom.sdsi.ssoPath.service.impl;

import cn.chinaunicom.sdsi.cloud.ssoPath.vo.TSanquanSsoPathVo;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.cloud.ssoPath.entity.TSanquanSsoPath;
import cn.chinaunicom.sdsi.ssoPath.mapper.TSanquanSsoPathMapper;
import cn.chinaunicom.sdsi.ssoPath.service.TSanquanSsoPathService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 单点登录地址 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Service
public class TSanquanSsoPathServiceImpl extends ServiceImpl<TSanquanSsoPathMapper, TSanquanSsoPath> implements TSanquanSsoPathService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-09-29
     * @param tSanquanSsoPath
     * @return IPage<TSanquanSsoPath>
     **/
    @Override
    public IPage<TSanquanSsoPath> findPage(TSanquanSsoPathVo tSanquanSsoPathVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanSsoPathVO);
        return baseMapper.selectPage(page, null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-09-29
     * @param id
     * @return TSanquanSsoPath
     **/
    @Override
    public TSanquanSsoPath findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-09-29
     * @return List<TSanquanSsoPath>
     **/
    @Override
    public List<TSanquanSsoPath> findList() {
        return baseMapper.selectList(null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-09-29
     * @param tSanquanSsoPath
     * @return int
     **/
    @Override
    public boolean add(TSanquanSsoPath tSanquanSsoPath) {
        if(!StringUtils.isNotEmpty(tSanquanSsoPath.getSsoType())){
            throw new ServiceErrorException("类型不能为空");
        }
        TSanquanSsoPath ssoPath = this.getOne(
                Wrappers.<TSanquanSsoPath>lambdaQuery()
                        .eq(TSanquanSsoPath::getSsoType, tSanquanSsoPath.getSsoType())
        );
        if(ssoPath != null){
            tSanquanSsoPath.setId(ssoPath.getId());
        }
        return this.saveOrUpdate(tSanquanSsoPath);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-09-29
     * @param tSanquanSsoPath
     * @return int
     **/
    @Override
    public int update(TSanquanSsoPath tSanquanSsoPath) {
        return baseMapper.updateById(tSanquanSsoPath);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-09-29
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

    /**
     * 根据类型查询
     * @param ssoType
     * @return
     */
    @Override
    public TSanquanSsoPath findByType(String ssoType) {
        if(!StringUtils.isNotEmpty(ssoType)){
            throw new ServiceErrorException("类型不能为空");
        }
        TSanquanSsoPath ssoPath = this.getOne(
                Wrappers.<TSanquanSsoPath>lambdaQuery()
                        .eq(TSanquanSsoPath::getSsoType, ssoType)
        );
        return ssoPath;
    }

    /**
     * 检查该类型是否已经存在
     * @param ssoType
     * @param id
     * @return
     */
    @Override
    public Integer verifyType(String ssoType, String id) {
        return baseMapper.verifyType(ssoType,id);
    }

}
