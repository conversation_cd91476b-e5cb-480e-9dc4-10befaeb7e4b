package cn.chinaunicom.sdsi.strategy.workFeedbackHistory.service.impl;

import cn.chinaunicom.sdsi.strategy.workFeedbackHistory.entity.TSanquanWorkFeedbackHistory;
import cn.chinaunicom.sdsi.strategy.workFeedbackHistory.mapper.TSanquanWorkFeedbackHistoryMapper;
import cn.chinaunicom.sdsi.strategy.workFeedbackHistory.service.TSanquanWorkFeedbackHistoryService;
import cn.chinaunicom.sdsi.strategy.workFeedbackHistory.vo.TSanquanWorkFeedbackHistoryVo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 工单反馈表（潜在机会推送） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
@Service
public class TSanquanWorkFeedbackHistoryServiceImpl extends ServiceImpl<TSanquanWorkFeedbackHistoryMapper, TSanquanWorkFeedbackHistory> implements TSanquanWorkFeedbackHistoryService {

    @Autowired
    public UnifastContext unifastContext;

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-01
     * @param tSanquanWorkFeedbackHistory
     * @return IPage<TSanquanWorkFeedbackHistory>
     **/
    @Override
    public IPage<TSanquanWorkFeedbackHistory> findPage(TSanquanWorkFeedbackHistoryVo tSanquanWorkFeedbackHistoryVo) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanWorkFeedbackHistoryVo);
        return baseMapper.selectPage(page, null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-01
     * @param id
     * @return TSanquanWorkFeedbackHistory
     **/
    @Override
    public TSanquanWorkFeedbackHistory findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-01
     * @return List<TSanquanWorkFeedbackHistory>
     **/
    @Override
    public List<TSanquanWorkFeedbackHistory> findList() {
        return baseMapper.selectList(null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-01
     * @param tSanquanWorkFeedbackHistory
     * @return int
     **/
    @Override
    public int add(TSanquanWorkFeedbackHistory tSanquanWorkFeedbackHistory) {
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
        unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
        String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
        tSanquanWorkFeedbackHistory.setTenantId(tenantId);
        }
        return baseMapper.insert(tSanquanWorkFeedbackHistory);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-01
     * @param tSanquanWorkFeedbackHistory
     * @return int
     **/
    @Override
    public int update(TSanquanWorkFeedbackHistory tSanquanWorkFeedbackHistory) {
        return baseMapper.updateById(tSanquanWorkFeedbackHistory);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2024-11-01
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}
