package cn.chinaunicom.sdsi.private5GWorkflow.mapper;

import cn.chinaunicom.sdsi.private5GWorkflow.entity.FiveGOrderQueryDTO;
import cn.chinaunicom.sdsi.private5GWorkflow.entity.FiveGOrderData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 5G订单数据Mapper接口
 */
@Mapper
public interface FiveGOrderMapper extends BaseMapper<FiveGOrderData> {

    /**
     * 查询5G订单数据列表
     *
     * @param page     分页参数
     * @param queryDTO 查询条件
     * @return 5G订单数据列表
     */
    IPage<FiveGOrderData> selectFiveGOrderList(@Param("page") IPage<FiveGOrderData> page,
            @Param("query") FiveGOrderQueryDTO queryDTO);

    /**
     * 查询5G订单数据列表
     *
     * @param queryDTO 查询条件
     * @return 5G订单数据列表
     */
    List<FiveGOrderData> selectFiveGOrderList(@Param("query") FiveGOrderQueryDTO queryDTO);

    /**
     * 新增5G订单数据
     *
     * @param fiveGOrderData 5G订单数据
     * @return 影响行数
     */
    int insertFiveGOrder(FiveGOrderData fiveGOrderData);

    /**
     * 根据订单号更新5G订单数据
     *
     * @param fiveGOrderData 5G订单数据
     * @return 影响行数
     */
    int updateFiveGOrderByOrderId(FiveGOrderData fiveGOrderData);

    /**
     * 根据订单号查询5G订单数据
     *
     * @param orderId 订单号
     * @return 5G订单数据
     */
    FiveGOrderData selectByOrderId(@Param("orderId") String orderId);

    /**
     * 根据订单号统计数量
     *
     * @param orderId 订单号
     * @return 数量
     */
    int countByOrderId(@Param("orderId") String orderId);

    /**
     * 根据订单号删除5G订单数据
     *
     * @param orderId 订单号
     * @return 影响行数
     */
    int deleteByOrderId(@Param("orderId") String orderId);
}
