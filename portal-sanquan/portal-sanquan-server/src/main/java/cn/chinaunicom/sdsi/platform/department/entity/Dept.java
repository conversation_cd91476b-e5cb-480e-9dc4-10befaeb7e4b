package cn.chinaunicom.sdsi.platform.department.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 部门表
 * </p>
 * <AUTHOR>
 * @since 2024-10-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_sanquan_platform_dept")
public class Dept implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @TableId
    private String id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 父级名称
     */
    private String parentName;

    /**
     * 地市
     */
    private String city;

    /**
     * 区县
     */
    private String county;

    /**
     * 父级id集合
     */
    private String parentIdList;

    /**
     * 父级名称集合
     */
    private String parentNameList;

    /**
     * 逻辑删除，normal表示正常，deleted表示删除
     */
    private String deleted = "normal";

}
