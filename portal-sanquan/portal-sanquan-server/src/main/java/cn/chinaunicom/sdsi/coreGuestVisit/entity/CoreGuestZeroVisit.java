package cn.chinaunicom.sdsi.coreGuestVisit.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 三全维度拜访名单表 - 数据表实体类
 */
@Data
@TableName("t_sanquan_TMP_MDZ_ZERO1")
public class CoreGuestZeroVisit implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 地市
     */
    @TableField("ROSTER_TYPE")
    private String rosterType;

    /**
     * 地市
     */
    @TableField("AREA_NAME")
    private String areaName;

    /**
     * 名单制ID
     */
    @TableField("ROSTER_CUSTOMER_ID")
    private String rosterCustomerId;

    /**
     * 名单制名称
     */
    @TableField("ROSTER_CUSTOMER_NAME")
    private String rosterCustomerName;

    /**
     * 是否长期零拜访 (1-是  0-否)
     */
    @TableField("FLAG_ZF")
    private String flagZf;

    /**
     * 账期
     */
    @TableField("DAY_ID")
    private String dayId;
}