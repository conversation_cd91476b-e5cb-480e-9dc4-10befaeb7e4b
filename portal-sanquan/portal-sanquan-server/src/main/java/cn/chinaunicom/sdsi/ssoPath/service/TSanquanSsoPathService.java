package cn.chinaunicom.sdsi.ssoPath.service;

import cn.chinaunicom.sdsi.cloud.ssoPath.entity.TSanquanSsoPath;
import cn.chinaunicom.sdsi.cloud.ssoPath.vo.TSanquanSsoPathVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * 单点登录地址 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
public interface TSanquanSsoPathService extends IService<TSanquanSsoPath> {

    // 分页查询
    IPage<TSanquanSsoPath> findPage(TSanquanSsoPathVo tSanquanSsoPathVO);

    // 根据id查询
    TSanquanSsoPath findOne(String id);

    // 查询列表
    List<TSanquanSsoPath> findList();

    // 新增
    boolean add(TSanquanSsoPath tSanquanSsoPath);

    // 修改
    int update(TSanquanSsoPath tSanquanSsoPath);

    // 删除
    int delete(String id);

    // 根据类型查询
    TSanquanSsoPath findByType(String ssoType);

    // 检查该类型是否已经存在
    Integer verifyType(String ssoType, String id);
}
