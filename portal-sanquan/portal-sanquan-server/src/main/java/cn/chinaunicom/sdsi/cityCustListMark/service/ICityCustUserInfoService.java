package cn.chinaunicom.sdsi.cityCustListMark.service;

import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserAccessParamDTO;
import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserInfoDTO;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserAccessParam;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface ICityCustUserInfoService extends IService<CityCustUserInfo> {
    /**
     * 分页查询用户策略信息
     *
     * @param cityCustUserInfoDTO 查询参数
     * @return 分页结果
     */
    IPage<CityCustUserInfo> findPage(CityCustUserInfoDTO cityCustUserInfoDTO);

    /**
     * 新增用户访问参数
     *
     * @param saveDTO 新增参数
     * @return 新增结果
     */
    int saveUserAccessParam(CityCustUserAccessParamDTO saveDTO);

    /**
     * 根据UUID查询用户访问参数
     *
     * @param uuid UUID
     * @return 用户访问参数实体
     */
    CityCustUserAccessParam findUserAccessParamByUuid(String uuid);
}
