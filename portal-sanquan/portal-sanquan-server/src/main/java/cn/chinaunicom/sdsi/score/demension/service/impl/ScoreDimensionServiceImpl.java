package cn.chinaunicom.sdsi.score.demension.service.impl;

import cn.chinaunicom.sdsi.framework.utils.BeanHelper;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.score.demension.entity.*;
import cn.chinaunicom.sdsi.score.demension.mapper.ScoreDimensionEvaluateMapper;
import cn.chinaunicom.sdsi.score.demension.mapper.ScoreDimensionMapper;
import cn.chinaunicom.sdsi.score.demension.service.IScoreDimensionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【t_sanquan_score_dimension】的数据库操作Service实现
 * @createDate 2024-09-14 11:00:58
 */
@Service
public class ScoreDimensionServiceImpl extends ServiceImpl<ScoreDimensionMapper, ScoreDimension>
        implements IScoreDimensionService {

    @Autowired
    private ScoreDimensionEvaluateMapper scoreDimensionEvaluateMapper;

    @Override
    public boolean insertOrUpdate(ScoreDimensionVo vo) {
        vo.setDeleted("normal");
        ScoreDimension scoreModel = new ScoreDimension();
        BeanHelper.copyProperties(vo, scoreModel);
        boolean ok = super.saveOrUpdate(scoreModel);
        scoreDimensionEvaluateMapper.deleteByDimensionId(scoreModel.getId());
        List<ScoreDimensionEvaluate> list = vo.getEvaluates().stream().peek(item -> item.setDimensionId(scoreModel.getId())).collect(Collectors.toList());
        scoreDimensionEvaluateMapper.insertBatchSomeColumn(list);
        return ok;
    }

    @Override
    public List<ScoreDimensionVo> findList(ScoreDimensionQuery query) {
        return baseMapper.findList(query);
    }

    @Override
    public IPage<ScoreDimensionVo> findPage(ScoreDimensionQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return baseMapper.findPage(page, query);
    }

    @Override
    public List<ScoreModelDimension> findPageByModelId(ScoreDimensionQuery query) {
        return baseMapper.findPageByModelId(query);
    }

    @Override
    public ScoreDimensionVo findOne(String id) {
        return baseMapper.findOne(id);
    }

    @Override
    public Integer deleteById(String id) {
        return baseMapper.deleteById(id);
    }
}