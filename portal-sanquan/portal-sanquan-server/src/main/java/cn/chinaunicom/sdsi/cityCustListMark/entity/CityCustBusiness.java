package cn.chinaunicom.sdsi.cityCustListMark.entity;

import java.io.Serializable;

/**
 * 客户业务表 实体类
 *
 * <AUTHOR>
 */
public class CityCustBusiness implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自然客户ID
     */
    private String natureCustId;

    /**
     * 自然客户名称
     */
    private String natureCustName;

    /**
     * 业务类型名称
     */
    private String serviceKindName;

    public String getNatureCustId() {
        return natureCustId;
    }

    public CityCustBusiness setNatureCustId(String natureCustId) {
        this.natureCustId = natureCustId;
        return this;
    }

    public String getNatureCustName() {
        return natureCustName;
    }

    public CityCustBusiness setNatureCustName(String natureCustName) {
        this.natureCustName = natureCustName;
        return this;
    }

    public String getServiceKindName() {
        return serviceKindName;
    }

    public CityCustBusiness setServiceKindName(String serviceKindName) {
        this.serviceKindName = serviceKindName;
        return this;
    }
}
