package cn.chinaunicom.sdsi.deepSeek.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.deepSeek.entity.DeepSeekEntity;
import cn.chinaunicom.sdsi.deepSeek.entity.DeepSeekMessageEntity;
import cn.chinaunicom.sdsi.deepSeek.mapper.DeepSeekMapper;
import cn.chinaunicom.sdsi.deepSeek.service.DeepSeekService;
import cn.chinaunicom.sdsi.deepSeek.vo.RemoteDeepSeekChoices;
import cn.chinaunicom.sdsi.deepSeek.vo.RemoteDeepSeekRes;
import cn.chinaunicom.sdsi.deepSeek.vo.RemoteKnowRes;
import cn.chinaunicom.sdsi.deepSeek.vo.RemoteRes;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import cn.chinaunicom.sdsi.util.HttpRequest;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * deepSeek
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
@Service
@Slf4j
public class DeepSeekServiceImpl extends ServiceImpl<DeepSeekMapper, DeepSeekEntity> implements DeepSeekService {

    static String knowUrl = "http://10.72.12.114:28080/sdai/activity/requestforwarding/e9146a53-63f3-47af-b5bb-b0b94efd191d";


    // ------------------  deepSeek配置参数  -------------------

    static String deepSeekUrl = "http://10.72.12.114:28080/sdai/activity/requestforwarding/ad2ef86f-8cda-463b-ae90-b510b1591ffb";

    static String userIdDeep = "shuzihua";// deepSeek用户

    static Boolean stream = false;// 是否流式响应，默认false

    static String model = "qwen2";

    /**
     * 读取知识库接口
     * 必传参数
     */
    public String remoteKnow(Map map){
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("user_id", map.get("userId"));
        jsonMap.put("knowledge_base_name", "1894337739579932674");
        jsonMap.put("query", map.get("query"));
        jsonMap.put("top_k", 10);// 评分级别前十
        jsonMap.put("scene", map.get("scene"));
        jsonMap.put("power", Lists.newArrayList("1894337739579932674"));
        String res = HttpRequest.post(knowUrl,jsonMap);
        if(StringUtils.isNotEmpty(res)){
            System.out.println(">>>>>>>>>>>>>>");
            System.out.println(res);

            ObjectMapper objectMapper = new ObjectMapper();
            try {
                if(StringUtils.isNotEmpty(res) && res.contains("iNum")){
                    res = res.replaceAll("iNum","numSeq");
                }
                // 使用 JavaType 明确指定泛型类型
                JavaType javaType = TypeFactory.defaultInstance()
                        .constructParametricType(RemoteRes.class, RemoteKnowRes.class);
                RemoteRes<RemoteKnowRes> responseBase = objectMapper.readValue(res, javaType);
                List<RemoteKnowRes> data = responseBase.getData();
                StringBuilder chanpinMes = new StringBuilder();
                StringBuilder kehuMes = new StringBuilder();
                data.forEach(resKnow -> {
                    RemoteKnowRes item = objectMapper.convertValue(resKnow, RemoteKnowRes.class);

                    String fileName = item.getFileName();
                    if(StringUtils.isNotEmpty(fileName) && fileName.contains("产品")){
                        chanpinMes.append(item.getPageContent());
                    }else if(StringUtils.isNotEmpty(fileName) && fileName.contains("客户")){
                        kehuMes.append(item.getPageContent());
                    }
                });
                chanpinMes.append(";").append(kehuMes);
                System.out.println("推送信息：" + chanpinMes.toString());
                this.remoteDeepseek(chanpinMes.toString());
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        System.err.println("发送bosendDeepSeek参数：" + jsonMap);
        return res;
    }

    /**
     * 读取deepSeek接口
     */
    public String remoteDeepseek(String pushMessage){
        MallUser mallUser = UserUtils.getUser();
        Map<String, Object> jsonMap = new HashMap<>();
        jsonMap.put("user_id", mallUser.getStaffId());
        jsonMap.put("stream", stream);
        jsonMap.put("model", model);

        List messageList = Lists.newArrayList();
        messageList.add(new DeepSeekMessageEntity("user",pushMessage));
        jsonMap.put("messages", messageList);

        String res = HttpRequest.post(deepSeekUrl,jsonMap);
        if(StringUtils.isNotEmpty(res)){
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                RemoteDeepSeekRes responseBase = objectMapper.readValue(res, RemoteDeepSeekRes.class);
                List<RemoteDeepSeekChoices> choices = responseBase.getChoices();
                String mes = "";
                for (RemoteDeepSeekChoices choice : choices) {
                    mes = choice.getMessage().getContent();
                }

                DeepSeekEntity entity = new DeepSeekEntity();
                entity.setUserId(mallUser.getStaffId());
                entity.setPushContent(pushMessage);
                entity.setContent(mes);
                entity.setCreateTime(new Date());
                this.saveOrUpdate(entity);
                System.out.println("执行结束");
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return "";
    }
}
