package cn.chinaunicom.sdsi.product.service;


import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanPersonnel;
import cn.chinaunicom.sdsi.cloud.product.query.PersonnelQuery;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 产品支撑人信息
 *
 * <AUTHOR> 
 * @since  2024-04-20
 */
public interface TSanquanPersonnelService extends IService<TSanquanPersonnel> {

    // 根据工号查询当前人的信息
    TSanquanPersonnel findPersonnelJobNumber(String jobNumber);

    // 根据工号删除当前人信息
    Boolean delete(String id);

    // 查询经理表信息
    List<TSanquanPersonnel> findList(TSanquanPersonnel tSanquanPersonnel);

    // 分页查询信息
    IPage<TSanquanPersonnel> findPage(PersonnelQuery query);

    // 导入人员信息
    Boolean importData(MultipartFile file);

    // 导出人员模版
    void exportData();

    // 根据用户表中的数据添加产品使用的人员信息
    Boolean addUserToPersonnel(List<UserVo> userList);
}