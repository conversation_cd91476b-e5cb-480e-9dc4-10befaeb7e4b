package cn.chinaunicom.sdsi.model.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 模型表对象 t_sanquan_model
 * @Author: han
 * @Date: 2024-07-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "模型表对象", description = "模型表对象")
@TableName("t_sanquan_model")
public class TSanquanModel extends BaseEntity {

    @Schema(name = "")
    @TableId
    private String id;

    @Schema(name = "模型分类id")
    private String modelTypeId;

    @Schema(name = "模型名称")
    private String modelName;

    @Schema(name = "模型场景")
    private String modelScenceId;

    @Schema(name = "业务类型 1：算网数智 2：联网通信")
    private String businessType;

    @Schema(name = "模型编码")
    private String modelCode;

    @Schema(name = "模型描述")
    private String remark;

    @Schema(name = "自动审核 0否 1是")
    private String autoPass;

    @Schema(name = "行业")
    private String industry;

}
