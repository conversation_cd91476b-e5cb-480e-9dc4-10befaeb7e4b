package cn.chinaunicom.sdsi.customerGroup.service.impl;

import cn.chinaunicom.sdsi.customerGroup.entity.TSanquanCustomerGroupTag;
import cn.chinaunicom.sdsi.customerGroup.service.TSanquanCustomerGroupTagService;
import cn.chinaunicom.sdsi.customerGroup.vo.TSanquanCustomerGroupTagVO;
import cn.chinaunicom.sdsi.customerGroup.queryvo.TSanquanCustomerGroupTagQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.chinaunicom.sdsi.customerGroup.mapper.TSanquanCustomerGroupTagMapper;
import org.springframework.beans.BeanUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;

/**
 * 客户群标签业务实现类
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
public class TSanquanCustomerGroupTagServiceImpl extends ServiceImpl<TSanquanCustomerGroupTagMapper, TSanquanCustomerGroupTag> implements TSanquanCustomerGroupTagService {

    @Autowired
    private UnifastContext unifastContext;

    /**
     * 分页查询客户群标签
     * 
     * @param tSanquanCustomerGroupTagQueryVO
     * @return IPage<TSanquanCustomerGroupTagVO>
     */
    @Override
    public IPage<TSanquanCustomerGroupTagVO> findPage(TSanquanCustomerGroupTagQueryVO tSanquanCustomerGroupTagQueryVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanCustomerGroupTagQueryVO);
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            tSanquanCustomerGroupTagQueryVO.setCreateBy(unifastContext.getUser().getStaffId());
        }
        return baseMapper.findPage(page, tSanquanCustomerGroupTagQueryVO);
    }

    /**
     * 查询客户群标签详细信息
     * 
     * @param id
     * @return TSanquanCustomerGroupTagVO
     */
    @Override
    public TSanquanCustomerGroupTagVO findInfo(String id) {
        TSanquanCustomerGroupTag tSanquanCustomerGroupTag = baseMapper.selectById(id);
        TSanquanCustomerGroupTagVO tSanquanCustomerGroupTagVO = new TSanquanCustomerGroupTagVO();
        BeanUtils.copyProperties(tSanquanCustomerGroupTag, tSanquanCustomerGroupTagVO);
        return tSanquanCustomerGroupTagVO;
    }

    /**
     * 新增客户群标签
     * 
     * @param tSanquanCustomerGroupTagVO
     * @return String
     */
    @Override
    public String add(TSanquanCustomerGroupTagVO tSanquanCustomerGroupTagVO) {
        if (!UnifastConstants.USERT_TYPE_ADMIN.equals(
                unifastContext.getCustomerParamsByKey(UnifastConstants.USER_TYPE))) {
            String tenantId = (String) unifastContext.getCustomerParamsByKey(UnifastConstants.TENANT_ID);
            tSanquanCustomerGroupTagVO.setTenantId(tenantId);
        }
         TSanquanCustomerGroupTag tSanquanCustomerGroupTag = new TSanquanCustomerGroupTag();
         BeanUtils.copyProperties(tSanquanCustomerGroupTagVO, tSanquanCustomerGroupTag);
         baseMapper.insert(tSanquanCustomerGroupTag);
         return tSanquanCustomerGroupTag.getId();
    }

    /**
     * 修改客户群标签
     * 
     * @param tSanquanCustomerGroupTagVO
     * @return Boolean
     */
    @Override
    public Boolean update(TSanquanCustomerGroupTagVO tSanquanCustomerGroupTagVO) {
        TSanquanCustomerGroupTag tSanquanCustomerGroupTag = new TSanquanCustomerGroupTag();
        BeanUtils.copyProperties(tSanquanCustomerGroupTagVO, tSanquanCustomerGroupTag);
        return baseMapper.updateById(tSanquanCustomerGroupTag) > 0;
    }

    /**
     * 删除客户群标签
     * 
     * @param id
     * @return Boolean
     */
    @Override
    public Boolean delete(String id) {
        return baseMapper.deleteById(id) > 0;
    }

}
