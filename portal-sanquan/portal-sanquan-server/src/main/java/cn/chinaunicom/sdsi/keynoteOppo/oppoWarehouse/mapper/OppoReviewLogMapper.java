package cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.mapper;

import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoReviewLog;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoReviewLogQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoReviewLogVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 商机评审流程日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Mapper
public interface OppoReviewLogMapper extends BaseMapper<OppoReviewLog> {

    // 分页查询
    IPage<OppoReviewLogVo> findPage(@Param("page") IPage page, @Param("query") OppoReviewLogQueryVo oppoReviewLogVo);

    // 查询列表
    List<OppoReviewLog> findList(@Param("query") OppoReviewLogQueryVo oppoReviewLogVo);
}
