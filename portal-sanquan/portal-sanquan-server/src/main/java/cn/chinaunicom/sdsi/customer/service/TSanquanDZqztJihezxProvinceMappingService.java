package cn.chinaunicom.sdsi.customer.service;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanDZqztJihezxProvinceMapping;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanDZqztJihezxProvinceMappingQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 客户经理信息 服务层接口
 *
 * <AUTHOR>
 * @since 2024-05-16
 */
public interface TSanquanDZqztJihezxProvinceMappingService extends IService<TSanquanDZqztJihezxProvinceMapping> {

    /**
     * 分页查询客户经理信息
     * 
     * @param tSanquanDZqztJihezxProvinceMappingQueryVO
     * @return IPage<TSanquanDZqztJihezxProvinceMappingVO>
     */
    IPage<TSanquanDZqztJihezxProvinceMappingVO> findPage(TSanquanDZqztJihezxProvinceMappingQueryVO tSanquanDZqztJihezxProvinceMappingQueryVO);

    /**
     * 查询客户经理信息详细信息
     *
     * @param LOGIN
     * @return TSanquanDZqztJihezxProvinceMappingVO
     */
    TSanquanDZqztJihezxProvinceMappingVO findInfo(String LOGIN);

    /**
     * 新增客户经理信息
     *
     * @param tSanquanDZqztJihezxProvinceMappingVO
     * @return String
     */
    String add(TSanquanDZqztJihezxProvinceMappingVO tSanquanDZqztJihezxProvinceMappingVO);

    /**
     * 修改客户经理信息
     *
     * @param tSanquanDZqztJihezxProvinceMappingVO
     * @return Boolean
     */
    Boolean update(TSanquanDZqztJihezxProvinceMappingVO tSanquanDZqztJihezxProvinceMappingVO);

    /**
     * 删除客户经理信息
     *
     * @param LOGIN
     * @return Boolean
     */
    Boolean delete(String LOGIN);
    /**
     * 查询客户经理信息
     *
     * @param tSanquanDZqztJihezxProvinceMappingQueryVO
     * @return Boolean
     */
    List<TSanquanDZqztJihezxProvinceMappingVO> findCustomerManager(TSanquanDZqztJihezxProvinceMappingQueryVO tSanquanDZqztJihezxProvinceMappingQueryVO);
}
