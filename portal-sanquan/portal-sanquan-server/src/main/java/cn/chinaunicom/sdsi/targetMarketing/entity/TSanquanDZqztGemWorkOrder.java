package cn.chinaunicom.sdsi.targetMarketing.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * @Description: 靶向营销任务信息表对象 t_sanquan_d_zqzt_gem_work_order
 * @Author: han
 * @Date: 2024-07-30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "靶向营销任务信息表对象", description = "靶向营销任务信息表对象")
@TableName("t_sanquan_d_zqzt_gem_work_order")
public class TSanquanDZqztGemWorkOrder implements Serializable {

    @Schema(name = "主键id")
    private String id;

    @Schema(name = "任务编码")
    private String code;

    @Schema(name = "任务状态（1待执行，2已关单，3待改派，4跟进中，5到期未执行，")
    private String status;

    @Schema(name = "子策略编号")
    private String policyCode;

    @Schema(name = "上级策略编码")
    private String superiorPolicyCode;

    @Schema(name = "上级策略名称")
    private String superiorPolicyName;

    @Schema(name = "上级策略创建人id")
    private String superiorPolicyCreateUserId;

    @Schema(name = "上级策略创建人姓名")
    private String superiorPolicyCreateUserName;

    @Schema(name = "顶级策略编码")
    private String topPolicyCode;

    @Schema(name = "商机数")
    private String businessNum;

    @Schema(name = "战略签约数")
    private String signNum;

    @Schema(name = "预期收入")
    private String expectedIncome;

    @Schema(name = "发展用户数")
    private String developNum;

    @Schema(name = "任务开始时间")
    private String startDate;

    @Schema(name = "任务截止时间")
    private String deadline;

    @Schema(name = "业务场景")
    private String businessScene;

    @Schema(name = "策略是否失效（1是，0否）")
    private String policyExpired;

    @Schema(name = "是否允许多次反馈（1是，0否）")
    private String allowFeedbacks;

    @Schema(name = "是否配置任务有效期（1是，0否）")
    private String isConfTaskTime;

    @Schema(name = "任务有效期（单位：天）")
    private String taskTime;

    @Schema(name = "任务备注")
    private String remark;

    @Schema(name = "流程编码")
    private String bizCode;

    @Schema(name = "表单链接")
    private String formLink;

    @Schema(name = "省分编码")
    private String provNo;

    @Schema(name = "地市编码")
    private String areaNo;

    @Schema(name = "区县编码")
    private String cityNo;

    @Schema(name = "网格编码")
    private String gridNo;

    @Schema(name = "营销范围")
    private String marketingRange;

    @Schema(name = "集团客户编码")
    private String groupId;

    @Schema(name = "地域省份id")
    private String regionProvId;

    @Schema(name = "地域省份名称")
    private String regionProvName;

    @Schema(name = "地域地市id")
    private String regionAreaId;

    @Schema(name = "地域地市编码")
    private String regionAreaName;

    @Schema(name = "地域区县编码")
    private String regionCountId;

    @Schema(name = "地域区县名称")
    private String regionCountName;

    @Schema(name = "归属组织名称")
    private String orgName;

    @Schema(name = "归属组织编码")
    private String orgCode;

    @Schema(name = "执行人编码")
    private String managerNo;

    @Schema(name = "执行人名称")
    private String managerName;

    @Schema(name = "执行人手机号")
    private String managerMobile;

    @Schema(name = "执行人类型")
    private String managerType;

    @Schema(name = "客户经理编码")
    private String actualManagerNo;

    @Schema(name = "客户经理名称")
    private String actualManagerName;

    @Schema(name = "客户经理手机号")
    private String actualManagerMobile;

    @Schema(name = "客户id")
    private String customId;

    @Schema(name = "客户名称")
    private String customName;

    @Schema(name = "客户标记")
    private String customerMark;

    @Schema(name = "办公地址")
    private String officeAddress;

    @Schema(name = "实体客户id")
    private String entityCustomerId;

    @Schema(name = "实体客户名称")
    private String entityCustomerName;

    @Schema(name = "楼宇id")
    private String buildingId;

    @Schema(name = "楼宇名称")
    private String buildingName;

    @Schema(name = "客户联系人")
    private String customContactName;

    @Schema(name = "客户联系方式")
    private String customContactPhone;

    @Schema(name = "客户住址")
    private String customAddress;

    @Schema(name = "客户类型")
    private String customType;

    @Schema(name = "证件类型")
    private String customCardType;

    @Schema(name = "证件编码")
    private String customCardId;

    @Schema(name = "是否名单制客户")
    private String isListCustom;

    @Schema(name = "是否本人制名单制客户")
    private String isMylistCustom;

    @Schema(name = "客户主要负责人姓名")
    private String customMainPrincipalName;

    @Schema(name = "客户主要负责人电话")
    private String customMainPrincipalPhone;

    @Schema(name = "客户主要负责人部门")
    private String customMainPrincipalDept;

    @Schema(name = "客户主要负责人职务")
    private String customMainPrincipalPost;

    @Schema(name = "客户主要负责人倾向")
    private String customMainPrincipalPrefer;

    @Schema(name = "友情提示")
    private String friendlyTips;

    @Schema(name = "执行人ID")
    private String executePersonId;

    @Schema(name = "派发时间")
    private String sendTime;

    @Schema(name = "创建时间")
    private String gmtCreate;

    @Schema(name = "创建用户id")
    private String createUserId;

    @Schema(name = "创建用户名")
    private String createUserName;

    @Schema(name = "更新时间")
    private String gmtModified;

    @Schema(name = "更新用户id")
    private String updateUserId;

    @Schema(name = "更新用户名称")
    private String updateUserName;

    @Schema(name = "反馈触点id")
    private String touchId;

    @Schema(name = "客户中心拜访id")
    private String visitId;

    @Schema(name = "集客工号")
    private String userCode;

    @Schema(name = "最新反馈结果（1执行成功；0执行失败）")
    private String visitResult;

    @Schema(name = "是否继续拜访字段(1继续，0否)")
    private String isContinueVisit;

    @Schema(name = "拜访失败原因")
    private String noVisitReason;

    @Schema(name = "下次拜访时间")
    private String nextTime;

    @Schema(name = "客户中心商机id")
    private String busiOppt;

    @Schema(name = "最新反馈时间")
    private String backTime;

    @Schema(name = "拜访主题")
    private String visitTheme;

    @Schema(name = "拜访形式")
    private String visitWay;

    @Schema(name = "拜访时间")
    private String visitTime;

    @Schema(name = "拜访时间")
    private String visitSite;

    @Schema(name = "访谈人姓名")
    private String visitPerson;

    @Schema(name = "访谈人联系方式")
    private String visitPhone;

    @Schema(name = "访谈内容")
    private String visitContent;

    @Schema(name = "拜访总结")
    private String visitSummarize;

    @Schema(name = "商机类型")
    private String busiOppType;

    @Schema(name = "商机名称")
    private String busiOpptName;

    @Schema(name = "是否清单")
    private String ifListed;

    @Schema(name = "是否删除")
    private String isDelete;

    @Schema(name = "删除时间")
    private String deleteTime;

    @Schema(name = "客户属性")
    private String customerProperty;

    @Schema(name = "任务评价分数[0")
    private String score;

    @Schema(name = "任务类型")
    private String workOrderType;

    @Schema(name = "效果反馈状态0-未反馈，1-反馈")
    private String effectBackStatus;

    @Schema(name = "效果反馈时间")
    private String effectBackTime;

    @Schema(name = "效果反馈数据关联信息")
    private String effectRecordLink;

    @Schema(name = "任务退回原因")
    private String returnReason;

    @Schema(name = "到期任务改派次数")
    private String updateNum;

    @Schema(name = "关单原因")
    private String closeReason;

    @Schema(name = "关单时间")
    private String closeTime;

    @Schema(name = "名称")
    private String bDomainOrgName;

    @Schema(name = "组织编码")
    private String bDomainOrgCode;

    @Schema(name = "省份编码")
    private String bDomainProvinceCode;

    @Schema(name = "省份名称")
    private String bDomainProvinceName;

    @Schema(name = "地市编码")
    private String bDomainCityCode;

    @Schema(name = "地市名称")
    private String bDomainCityName;

    @Schema(name = "区县编码")
    private String bDomainCountyCode;

    @Schema(name = "区县名称")
    private String bDomainCountyName;

    @Schema(name = "")
    private String monthId;

    @Schema(name = "")
    private String dayId;

    @Schema(name = "潜在机会")
    private String opportunityId;

}
