package cn.chinaunicom.sdsi.platform.menu.mapper;

import cn.chinaunicom.sdsi.platform.menu.entity.Menu;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuQuery;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Mapper
public interface CoreMenuMapper extends BaseMapper<Menu> {

    //分页查询
    IPage<MenuVo> findPage(@Param("page") IPage page, @Param("query") MenuQuery query);

    //集合查询
    List<MenuVo> findList(@Param("query") MenuQuery query);

    /**
     * 查询角色菜单
     *
     * <AUTHOR>
     * @Date 2024/7/22 17:28
     */
    List<MenuVo> findByRoleId(@Param("roleId") String roleId);

    /**
     * 查询用户所用的菜单
     *
     * <AUTHOR>
     * @Date 2024/7/23 9:14
     */
    List<MenuVo> findByUserId(@Param("userId") String userId, @Param("menuType") String menuType);

    /**
     * 查询用户拥有的菜单
     *
     * <AUTHOR>
     * @Date 2024/8/9 14:29
     */
    List<MenuVo> findUserHasMenu(@Param("query") MenuQuery query, @Param("userId") String userId);

    List<MenuVo> userHasPermission(@Param("userId") String userId);

    // 菜单系统配置参数
    List<Map<String, String>> findMenusSystem();
}
