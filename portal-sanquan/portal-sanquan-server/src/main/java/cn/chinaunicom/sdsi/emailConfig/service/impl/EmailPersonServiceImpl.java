package cn.chinaunicom.sdsi.emailConfig.service.impl;

import cn.chinaunicom.sdsi.emailConfig.entity.EmailConfig;
import cn.chinaunicom.sdsi.emailConfig.entity.EmailPerson;
import cn.chinaunicom.sdsi.emailConfig.mapper.EmailConfigMapper;
import cn.chinaunicom.sdsi.emailConfig.mapper.EmailPersonMapper;
import cn.chinaunicom.sdsi.emailConfig.service.EmailConfigService;
import cn.chinaunicom.sdsi.emailConfig.service.EmailPersonService;
import cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonQueryVO;
import cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonVO;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 发送email配置入口
 *
 * <AUTHOR> 
 * @since  2024-06-13
 */
@Service
public class EmailPersonServiceImpl extends ServiceImpl<EmailPersonMapper, EmailPerson> implements EmailPersonService {

    @Autowired
    private EmailConfigMapper emailConfigMapper;

    @Autowired
    private EmailPersonMapper emailPersonMapper;

    public List<EmailPersonVO> getPersonList(EmailPersonVO personVO){
        return emailPersonMapper.getPersonList(personVO);
    }

    /**
     * h获取未配置的人员名单
     * @param personVO
     * @return
     */
    public IPage<EmailPersonVO> getConfigUserList(EmailPersonQueryVO personVO){
        IPage page = QueryVoToPageUtil.toPage(personVO);
        if(!StringUtils.isNotEmpty(personVO.getTaskId())){
            log.error("系统参数错误，没有当前任务的id");
//            return page;
        }
        return emailPersonMapper.getConfigUserList(page,personVO);
    }

    public IPage<EmailPersonVO> findPage(EmailPersonQueryVO tSanquanEmailPersonVO) {
        IPage page = QueryVoToPageUtil.toPage(tSanquanEmailPersonVO);
        return baseMapper.getPageList(page, tSanquanEmailPersonVO);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-16
     * @param id
     * @return TSanquanEmailPerson
     **/
    @Override
    public EmailPerson findOne(String id) {
        return baseMapper.selectById(id);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-16
     * @return List<TSanquanEmailPerson>
     **/
    @Override
    public List<EmailPerson> findList() {
        return baseMapper.selectList(null);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-16
     * @param tSanquanEmailPerson
     * @return int
     **/
    @Override
    public int add(EmailPerson tSanquanEmailPerson) {

        return baseMapper.insert(tSanquanEmailPerson);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-16
     * @param tSanquanEmailPerson
     * @return int
     **/
    @Override
    public int update(EmailPerson tSanquanEmailPerson) {
        return baseMapper.updateById(tSanquanEmailPerson);
    }

    /*
     * <AUTHOR>
     * @Description 分页查询
     * @Date 2025-06-16
     * @param id
     * @return int
     **/
    @Override
    public int delete(String id) {
        return baseMapper.deleteById(id);
    }

}