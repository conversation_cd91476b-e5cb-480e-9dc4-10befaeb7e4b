package cn.chinaunicom.sdsi.nature_customer.service;

import cn.chinaunicom.sdsi.cloud.nature_customer.entity.AddCustomerTag;
import cn.chinaunicom.sdsi.cloud.nature_customer.entity.NatureCoustomer;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.AddCustomerTagQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.query.NatureCoustomerQuery;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO;
import cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO;
import cn.chinaunicom.sdsi.cloud.product.vo.AddProductMarkVo;
import cn.chinaunicom.sdsi.tag.entity.TagDbVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 策略推荐结果表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface AddCustomerService extends IService<AddCustomerTag> {


    IPage<AddCustomerTagVO> findPage(AddCustomerTagQuery query);

    List<AddCustomerTagVO> findList(AddCustomerTagQuery query);

    AddCustomerTagVO findOneById(AddCustomerTagQuery query);

    List<String> findLabelNameByCustomer(AddCustomerTagQuery query);
    void insertBatchSomeColumn(List<AddCustomerTag> batchList);
    void deleteByRosterCustomerId(List<String> deleteRosterIds);
    List<AddCustomerTagVO> findBigTable(String date);

    List<TagDbVo> findBigTableColumn(Map<String, Object> params);
    /**
     * 查询表关联的列
     *
     * <AUTHOR>
     * @Date 2024/6/14 9:34
     */
    List<AddCustomerTagVO> findDictBigColumn(Map<String, Object> params);

    List<AddCustomerTagVO> findDictBigColumnDetail(Map<String, Object> params);

    // 查看客户标签数
    int getCustomerLabelNum(AddCustomerTagQuery query);

    // 查询产品打标的数据
    List<AddProductMarkVo> findProductBigTable(String date);
}
