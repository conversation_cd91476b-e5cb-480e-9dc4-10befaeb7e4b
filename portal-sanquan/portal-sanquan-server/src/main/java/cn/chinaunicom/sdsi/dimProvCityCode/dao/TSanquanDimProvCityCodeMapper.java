package cn.chinaunicom.sdsi.dimProvCityCode.dao;

import cn.chinaunicom.sdsi.cloud.dimProvCityCode.entity.TSanquanDimProvCityCode;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-15
 */
@Mapper
public interface TSanquanDimProvCityCodeMapper extends BaseMapper<TSanquanDimProvCityCode> {

    // 查询列表
    List<TSanquanDimProvCityCode> findList(@Param("query") TSanquanDimProvCityCode query);

    // 查询地市信息
    List<Map<String, String>> selectByProvName(@Param("query") TSanquanDimProvCityCode query);

    // 查询区域信息
    List<Map<String, String>> selectByCityName(@Param("query") TSanquanDimProvCityCode query);

    // 根据城市名称查询城市编码
    String getByCityCode(@Param("cityName") String cityName);

    // 根据城市编码查询城市名称
    String getCityNameByCode(@Param("cityCode") String cityCode);
}
