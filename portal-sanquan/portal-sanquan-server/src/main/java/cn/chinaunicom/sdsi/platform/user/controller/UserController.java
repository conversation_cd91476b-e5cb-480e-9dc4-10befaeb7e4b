package cn.chinaunicom.sdsi.platform.user.controller;


import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.UserUtils;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.department.entity.Dept;
import cn.chinaunicom.sdsi.platform.department.service.IDeptService;
import cn.chinaunicom.sdsi.platform.role.entity.SyncRoleVo;
import cn.chinaunicom.sdsi.platform.role.service.SyncRoleService;
import cn.chinaunicom.sdsi.platform.user.entity.*;
import cn.chinaunicom.sdsi.platform.user.service.IUserRoleService;
import cn.chinaunicom.sdsi.platform.user.service.IUserService;
import cn.hutool.core.date.DateUtil;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@RestController
@RequestMapping("/user")
@Slf4j
public class UserController extends BaseController {
    @Autowired
    private IUserService userService;
    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private IDeptService deptService;

    @Autowired
    private SyncRoleService syncRoleService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Scheduled(cron = "0 5 9 * * *")
    public void syncSchedule(){
        log.info("定时同步人员，角色信息触发了");
        String lockKey = "sms_lock:syncPersonData";
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, "locked", 20, TimeUnit.SECONDS);
        if (lock != null && lock) {
            try{
                syncRoleService.executeSchedule();
            }catch (Exception e){
                log.error("定时同步人员系统错误：" ,e.getLocalizedMessage());
            }finally {
                redisTemplate.delete(lockKey);
            }
        }

    }

    @GetMapping("/findByJobNum")
    @Operation(summary = "根据工号查询用户登录")
    public UserVo findByJobNum(String jobNum) {
        long beginTime = System.currentTimeMillis();
        UserVo userVo = userService.findByJobNum(jobNum);
        long endTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        String nowTime = sdf.format(new Date());
        System.err.println("请求根据工号查询人员============"+nowTime+"======"+(endTime-beginTime)+"毫秒"+(userVo==null?"null":userVo.getName()));
        return userVo;
    }

    /**
     * 新增
     *
     * @param vo
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "保存")
    public BaseResponse<Boolean> save(@RequestBody @Valid UserVo vo) {
        User user = new User();
        BeanUtils.copyProperties(vo, user);
        user.setDeleted("normal");
        user.setSysType("1");
        user.setCreateTime(DateUtil.now());
        return ok(userService.saveOrUpdate(user));
    }

    @PostMapping("/saveUserAndRole")
    @Operation(summary = "保存")
    public UserVo saveUserAndRole(@RequestBody @Valid UserVo vo) {
        User user = new User();
        BeanUtils.copyProperties(vo, user);
        user.setDeleted("normal");
        user.setSysType("1");
        user.setCreateTime(DateUtil.now());
        userService.saveOrUpdate(user);
        userRoleService.deleteByUserId(user.getId());
        List<UserRole> userRoles = new ArrayList<>();
        List<String> roles = vo.getRoles();
        for (String role : roles) {
            userRoles.add(new UserRole(user.getId(), role));
        }
        userRoleService.insertBatch(userRoles);
        UserVo result = new UserVo();
        BeanUtils.copyProperties(user, result);
        return result;

    }

    @Operation(summary = "判断是否存在", description = "分页")
    @GetMapping("/checkExist")
    public BaseResponse<Boolean> checkExist(String jobNum, String id) {
        return new BaseResponse<>(userService.checkExist(jobNum, id));
    }

    /**
     * 逻辑删除
     */
    @GetMapping("/delete")
    @Operation(summary = "删除")
    public BaseResponse<Boolean> delete(String id) {
        User role = userService.getById(id);
        role.setDeleted("1");
        return ok(userService.saveOrUpdate(role));
    }

    @GetMapping("/findOne")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<UserVo> findOne(String id) {
        User user = userService.getById(id);
        UserVo userVo = new UserVo();
        BeanUtils.copyProperties(user, userVo);
        if(StringUtils.isNotEmpty(user.getDeptId())){
            Dept deptDb = deptService.getById(user.getDeptId());
            if (deptDb != null) {
                userVo.setDeptName(deptDb.getDeptName());
            }
        }
        return ok(userVo);
    }

    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<UserVo>> findList(UserQuery query) {
        return ok(userService.findList(query));
    }

    @GetMapping("/findUserByRoleId")
    @Operation(summary = "查询列表", description = "根据角色查询列表")
    public BasePageResponse<UserVo> findUserByRoleId(UserQuery query) {
        return pageOk(userService.findPageByRole(query));
    }

    @GetMapping("/findUserByNotRoleId")
    @Operation(summary = "查询列表", description = "根据角色查询列表")
    public BasePageResponse<UserVo> findUserByNotRoleId(UserQuery query) {
        return pageOk(userService.findPageByNotThisRole(query));
    }

    @GetMapping("/findPage")
    @Operation(summary = "查询列表", description = "查询列表")
    public BasePageResponse<UserVo> findPage(UserQuery query) {
        MallUser mallUser = UserUtils.getUser();
        if("sync".equals(mallUser.getStaffName())){// 测试执行
            syncRoleService.executeSchedule();
        }
        return pageOk(userService.findPage(query));
    }

    @PostMapping("/grant")
    @Operation(summary = "授权", description = "授权")
    public BaseResponse<Boolean> grant(@RequestBody UserRoleParams roleParams) {
        try {
            userRoleService.deleteByUserId(roleParams.getUserId());
            userRoleService.insertBatch(roleParams.getUserRoles());
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }

    @GetMapping("/cancelRoleGrant")
    @Operation(summary = "取消授权", description = "授权")
    public BaseResponse<Boolean> grant(String roleId, String userId) {
        try {
            userRoleService.deleteByRoleIdAndUserId(roleId, userId);
            return ok(true);
        } catch (Exception e) {
            return ok(false);
        }
    }
}
