package cn.chinaunicom.sdsi.targetMarketing.service;

import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanDMrtZqztGemFdyinfo;
import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanDZqztGemWorkOrder;
import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanEmailEntity;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztGemWorkOrderExcel;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztGemWorkOrderFeedbackInfoVO;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztGemWorkOrderVO;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanDZqztGemWorkOrderQueryVO;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanEmailVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 靶向营销任务信息表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
public interface TSanquanDZqztGemWorkOrderService extends IService<TSanquanDZqztGemWorkOrder> {

    /**
     * 分页查询靶向营销任务信息表
     * 
     * @param tSanquanDZqztGemWorkOrderQueryVO
     * @return IPage<TSanquanDZqztGemWorkOrderVO>
     */
    IPage<TSanquanDZqztGemWorkOrderVO> findPage(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO);

    /**
     * 查询靶向营销任务列表的反馈信息-按策略查询
     */
    List<Map<String,Object>> findOrderList(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO);

    IPage<TSanquanDZqztGemWorkOrderVO> findTaskPage(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO);

    /**
     * 导出靶向，查询反馈信息(静态表，t_sanquan_d_zqzt_gem_work_order_feedback_info)
     * @param tSanquanDZqztGemWorkOrderQueryVO
     * @return
     */
    List<Map<String,Object>> getFeekInfoStatic(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO);
    /**
     * 导出靶向，查询反馈信息(动态表，t_sanquan_d_mrt_zqzt_gem_fdyinfo)
     * @param tSanquanDZqztGemWorkOrderQueryVO
     * @return
     */
    List<TSanquanDMrtZqztGemFdyinfo> findFeebackDynamicsList(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO);

    List<TSanquanDMrtZqztGemFdyinfo> findFeebackDengBao(TSanquanDZqztGemWorkOrderQueryVO tSanquanDZqztGemWorkOrderQueryVO);

    /**
     * 查询靶向营销任务信息表详细信息
     *
     * @param ID
     * @return TSanquanDZqztGemWorkOrderVO
     */
    TSanquanDZqztGemWorkOrderVO findInfo(String ID,String superiorPolicyCode,String maxDate);

    /**
     * 新增靶向营销任务信息表
     *
     * @param tSanquanDZqztGemWorkOrderVO
     * @return String
     */
    String add(TSanquanDZqztGemWorkOrderVO tSanquanDZqztGemWorkOrderVO);

    /**
     * 修改靶向营销任务信息表
     *
     * @param tSanquanDZqztGemWorkOrderVO
     * @return Boolean
     */
    Boolean update(TSanquanDZqztGemWorkOrderVO tSanquanDZqztGemWorkOrderVO);

    /**
     * 删除靶向营销任务信息表
     *
     * @param ID
     * @return Boolean
     */
    Boolean delete(String ID);

    // 根据潜在机会查询靶向任务信息
    TSanquanDZqztGemWorkOrder selectInfoByOpportunityId(String opportunityId);

    String getMaxDate(TSanquanDZqztGemWorkOrderQueryVO orderQueryVO);

    Boolean isDynamicFeeback(String superiorPolicyCode);

    List<Map<String,String>> getDynamicFeebackColumn( TSanquanDZqztGemWorkOrderQueryVO tSanquanDMrtZqztGemFdyinfo);

    List<TSanquanEmailEntity> selectExpireCustomerOrder(TSanquanEmailVO order);

}
