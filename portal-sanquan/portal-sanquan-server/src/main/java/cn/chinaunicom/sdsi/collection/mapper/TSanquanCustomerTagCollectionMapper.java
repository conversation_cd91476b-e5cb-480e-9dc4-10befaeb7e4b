package cn.chinaunicom.sdsi.collection.mapper;

import cn.chinaunicom.sdsi.collection.entity.TSanquanCustomerTagCollection;
import cn.chinaunicom.sdsi.collection.vo.TSanquanCustomerTagCollectionVO;
import cn.chinaunicom.sdsi.collection.queryvo.TSanquanCustomerTagCollectionQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户标签汇总表(采集表)Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-08
 */
@Mapper
public interface TSanquanCustomerTagCollectionMapper extends BaseMapper<TSanquanCustomerTagCollection> {

    /**
     * 分页查询客户标签汇总表(采集表)
     * 
     * @param page,tSanquanCustomerTagCollectionQueryVO
     * @return 客户标签汇总表(采集表)
     */
    IPage<TSanquanCustomerTagCollectionVO> findPage(@Param("page") IPage page, @Param("query") TSanquanCustomerTagCollectionQueryVO tSanquanCustomerTagCollectionQueryVO);

    List<TSanquanCustomerTagCollectionVO> findList(@Param("query") TSanquanCustomerTagCollectionQueryVO tSanquanCustomerTagCollectionQueryVO);

    List<TSanquanCustomerTagCollection> getTagList(@Param("query") TSanquanCustomerTagCollectionQueryVO tSanquanCustomerTagCollectionQueryVO);

}
