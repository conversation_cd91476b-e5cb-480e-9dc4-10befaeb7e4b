package cn.chinaunicom.sdsi.aiflow.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

@Component
public class HttpUtils {
    private final OkHttpClient okHttpClient = new OkHttpClient();
    public String postData(String url, String jsonBody) throws IOException {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        StringBuilder fullResponse = new StringBuilder();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return "Error";
            }
            String responseBody = response.body().string();
            return responseBody;
        } catch (IOException e) {
            return "Error: " + e.getMessage();
        }
    }

    /**
     * 调用ds
     * @param url
     * @param jsonBody
     * @return
     * @throws IOException
     */
    public String dsPostData(String url, String jsonBody) throws IOException {
        MediaType JSON = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(jsonBody, JSON);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        StringBuilder fullResponse = new StringBuilder();
        try (Response response = okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                return "Error";
            }
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(response.body().byteStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.startsWith("data: ")) {
                        String jsonData = line.substring(5).trim();
                        if (jsonData.equals("[DONE]")) {
                            break;
                        }else {
                            String jsonStr = line.substring(6);
                            JSONObject json = JSONObject.parseObject(jsonStr);
                            JSONArray choices = json.getJSONArray("choices");
                            if (!choices.isEmpty()) {
                                JSONObject firstChoice = choices.getJSONObject(0);
                                JSONObject delta = firstChoice.getJSONObject("delta");
                                String content = delta.getString("content");
                                fullResponse.append(content);
                            }
                        }
                    }
                }
            }
        } catch (IOException e) {
            return "Error";
        }
        return fullResponse.toString();
    }
}
