package cn.chinaunicom.sdsi.activityReport.entity;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;

/**
 * 专项活动报表配置
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ActivityReportConfig {
    /**
     * 策略编码
     */
    private String strategyId;
    /**
     * 策略名称
     */
    private String strategyName;
    /**
     * 政企运营运营平台策略ID
     */
    private String zqytStrategyId;
    /**
     * 政企运营运营平台策略名称
     */
    private String zqytStrategyName;

    /**
     * 产品营销日报是否展示，1是，0否
     */
    private String isShow;
    /**
     * 产品营销日报是否统计，1是，0否
     */
    private String isSummary;
    /**
     * 派单时间
     */
    private String sendTime;

    /* 上周时间 */
    @TableField(exist = false)
    private String weekTitle;

    public String getStrategyId() {
        return strategyId;
    }

    public ActivityReportConfig setStrategyId(String strategyId) {
        this.strategyId = strategyId;
        return this;
    }

    public String getStrategyName() {
        return strategyName;
    }

    public ActivityReportConfig setStrategyName(String strategyName) {
        this.strategyName = strategyName;
        return this;
    }

    public String getZqytStrategyId() {
        return zqytStrategyId;
    }

    public ActivityReportConfig setZqytStrategyId(String zqytStrategyId) {
        this.zqytStrategyId = zqytStrategyId;
        return this;
    }

    public String getZqytStrategyName() {
        return zqytStrategyName;
    }

    public ActivityReportConfig setZqytStrategyName(String zqytStrategyName) {
        this.zqytStrategyName = zqytStrategyName;
        return this;
    }
}
