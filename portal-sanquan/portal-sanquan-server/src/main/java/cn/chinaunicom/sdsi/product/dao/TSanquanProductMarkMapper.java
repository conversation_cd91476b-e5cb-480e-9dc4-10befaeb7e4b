package cn.chinaunicom.sdsi.product.dao;

import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductMark;
import cn.chinaunicom.sdsi.cloud.product.vo.ProductMarkVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* 1
*
* <AUTHOR> 
* @since  2024-04-19
*/
@Mapper
public interface TSanquanProductMarkMapper extends BaseMapper<TSanquanProductMark> {

    // 产品标签数量
    int selectProductLabelNum(@Param("query") ProductMarkVO vo);
}