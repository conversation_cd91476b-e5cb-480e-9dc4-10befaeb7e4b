package cn.chinaunicom.sdsi.chengxiaoNew.vo;

import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 潜在商机
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/26 11:12
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@HeadRowHeight(25) // 标题单元格高度
public class ChengXiaoDetailCityStrategyExcelVo implements Serializable {

    @ExcelIgnore
    String dataSource;

    @ExcelIgnore
    String dataSourceType;

    /*地市*/
    @ColumnWidth(20)
    @ExcelProperty(value = "地市")
    String cityName;

    /*策略名称*/
    @ColumnWidth(40)
    @ExcelProperty(value = "策略名称")
    String marketingName;

    /*策略名称*/
    @ColumnWidth(20)
    @ExcelProperty(value = "所属行业")
    String provinceIndustryName;

    /*任务数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "任务数量")
    String taskNum;

    /*执行数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "执行数量")
    String zhixing;

    /*未执行数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "未执行数量")
    String weiZhixing;

    /*执行率*/
    @ColumnWidth(20)
    @ExcelProperty(value = "执行率")
    String zhixingRate;

    /*商机数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "商机数量")
    String oppoNum;

    /*商机金额*/
    @ColumnWidth(20)
    @ExcelProperty(value = "商机金额(万元)")
    String oppoAmount;

    /*转化数*/
    @ColumnWidth(20)
    @ExcelProperty(value = "转化数")
    String zhuanhuaNum;

    /*转化率*/
    @ColumnWidth(20)
    @ExcelProperty(value = "转化率")
    String zhuanhuaRate;

    /*合同数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "合同数量")
    String contractNum;

    /*合同金额*/
    @ColumnWidth(20)
    @ExcelProperty(value = "合同金额(万元)")
    String contractAmount;

    /*项目数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "项目数量")
    String projectNum;

    /*项目金额*/
    @ColumnWidth(20)
    @ExcelProperty(value = "项目金额(万元)")
    String projectAmount;

    /*带动新业务数量*/
    @ColumnWidth(20)
    @ExcelProperty(value = "新发展业务数量")
    String newDevelopBusiness;

    /*新业务收入*/
    @ColumnWidth(20)
    @ExcelProperty(value = "新发展业务收入(元)")
    String newDevelopBusinessAmount;

    /*新业务数量提升率*/
    @ColumnWidth(20)
    @ExcelProperty(value = "新发展业务数量提升率")
    String newDevelopBusinessRate;

    /*新业务收入提升率*/
    @ColumnWidth(20)
    @ExcelProperty(value = "新发展业务收入提升率")
    String newDevelopBusinessAmountRate;

    public String getCityName() {
        if(dataSource != null && dataSource.contains("合计")){
//            return "合计";
        }
        if(StringUtils.isEmpty(cityName)){
            return "-";
        }
        return cityName;
    }

    public String getMarketingName() {
        if(cityName != null && cityName.contains("全省") && (dataSource == null || "-".equals(dataSource))){
            return "全省合计";
        }
        if(dataSource != null && dataSource.contains("合计")){
            return "合计";
        }
        if(StringUtils.isEmpty(marketingName)){
            return "-";
        }
        return marketingName;
    }

}
