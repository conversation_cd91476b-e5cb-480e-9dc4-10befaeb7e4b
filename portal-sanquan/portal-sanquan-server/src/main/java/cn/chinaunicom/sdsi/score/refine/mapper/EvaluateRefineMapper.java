package cn.chinaunicom.sdsi.score.refine.mapper;

import cn.chinaunicom.sdsi.score.evaluate.entity.EvaludateLinkRefine;
import cn.chinaunicom.sdsi.score.refine.entity.EvaluateRefine;
import cn.chinaunicom.sdsi.score.refine.entity.EvaluateRefineQuery;
import cn.chinaunicom.sdsi.score.refine.entity.EvaluateRefineVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【t_sanquan_score_model】的数据库操作Mapper
* @createDate 2024-09-14 10:12:53
* @Entity cn.chinaunicom.sdsi.score.model.ScoreModel
*/
@Mapper
public interface EvaluateRefineMapper extends BaseMapper<EvaluateRefine> {
    /**
     * 列表查询
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    List<EvaluateRefineVo> findList(@Param("query") EvaluateRefineQuery query);

    /**
     * 分页查询
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    IPage<EvaluateRefineVo> findPage(@Param("page") IPage<EvaluateRefineVo> page, @Param("query") EvaluateRefineQuery query);
    /**
     * 根据id查询
     *
     * <AUTHOR>
     * @since 2024-09-14
     */
    EvaluateRefineVo findOne(@Param("id")  String id);

    List<EvaludateLinkRefine> findListByEvaluateId(@Param("evaluateId")  String evaluateId);

}




