package cn.chinaunicom.sdsi.expireWarning.dao;

import cn.chinaunicom.sdsi.cloud.expireWarning.entity.TSanquanMaturityWarningInfo;
import cn.chinaunicom.sdsi.cloud.expireWarning.query.TSanquanMaturityWarningInfoQuery;
import cn.chinaunicom.sdsi.cloud.expireWarning.vo.TSanquanMaturityWarningInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* 预警信息
*
* <AUTHOR> 
* @since  2024-06-13
*/
@Mapper
public interface TSanquanMaturityWarningInfoMapper extends BaseMapper<TSanquanMaturityWarningInfo> {

    //分页查询
    IPage<TSanquanMaturityWarningInfoVO> findPage(@Param("page") IPage page,@Param("query") TSanquanMaturityWarningInfoQuery query);

    //列表查询
    List<TSanquanMaturityWarningInfoVO> findList(@Param("query") TSanquanMaturityWarningInfoQuery query);
}