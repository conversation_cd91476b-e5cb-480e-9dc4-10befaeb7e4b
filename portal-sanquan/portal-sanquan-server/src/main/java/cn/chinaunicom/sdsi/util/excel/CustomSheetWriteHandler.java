package cn.chinaunicom.sdsi.util.excel;

import cn.chinaunicom.sdsi.cloud.product.vo.DictionaryVO;
import cn.chinaunicom.sdsi.product.service.ProductService;
import cn.chinaunicom.sdsi.product.service.impl.ProductServiceImpl;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 自定义拦截器.对第一列第一行和第二行的数据新增下拉框，显示 测试1 测试2
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CustomSheetWriteHandler implements SheetWriteHandler {

    @Autowired
    private ProductService productService;

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        log.info("第{}个Sheet写入成功。", context.getWriteSheetHolder().getSheetNo());

        Sheet sheet = context.getWriteSheetHolder().getSheet();
        String sheetName = context.getWriteSheetHolder().getSheetName();

        if ("新增联网通信业务".equals(sheetName)) {
            applyValidation(sheet, 1, 10, 1, "6");
        }
        if ("新增算网数智业务".equals(sheetName)) {
            applyValidation(sheet, 1, 10, 3, "7");
        }
    }

    /**
     * 设置下拉框
     * @param sheet 工作表对象
     * @param firstRow 起始行
     * @param lastRow 结束行
     * @param colIndex 列索引
     * @param type 字典类型
     */
    private void applyValidation(Sheet sheet, int firstRow, int lastRow, int colIndex, String type) {
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(firstRow, lastRow, colIndex, colIndex);
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DictionaryVO dictionaryVO = new DictionaryVO();
        dictionaryVO.setType(type);
        List<DictionaryVO> dictionary = productService.findDictionary(dictionaryVO);
        String[] names = dictionary.stream().map(DictionaryVO::getName).toArray(String[]::new);
        DataValidationConstraint constraint = helper.createExplicitListConstraint(names);
        DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
        sheet.addValidationData(dataValidation);
    }
}