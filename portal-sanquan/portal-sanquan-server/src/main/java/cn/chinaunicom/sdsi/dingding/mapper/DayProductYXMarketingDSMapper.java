package cn.chinaunicom.sdsi.dingding.mapper;

import cn.chinaunicom.sdsi.dingding.entity.DayProductYXMarketingDS;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingDSQueryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

@Mapper
@Component
public interface DayProductYXMarketingDSMapper extends BaseMapper<DayProductYXMarketingDS> {

    IPage<DayProductYXMarketingDS> findPage(@Param("query") DayProductYXMarketingDSQueryVo chengXiaoDetail);

}
