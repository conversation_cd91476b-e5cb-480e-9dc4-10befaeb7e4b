package cn.chinaunicom.sdsi.expireWarning.controller;

import cn.chinaunicom.sdsi.cloud.expireWarning.entity.TSanquanMaturityWarningInfo;
import cn.chinaunicom.sdsi.cloud.expireWarning.query.TSanquanMaturityWarningInfoQuery;
import cn.chinaunicom.sdsi.cloud.expireWarning.vo.TSanquanMaturityWarningInfoVO;
import cn.chinaunicom.sdsi.expireWarning.service.TSanquanMaturityWarningInfoService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
* 预警信息
*
* <AUTHOR> 
* @since  2024-06-13
*/
@RestController
@RequestMapping("/maturity/warning/info")
@Tag(name="预警信息")
public class TSanquanMaturityWarningInfoController extends BaseController {


    @Autowired
    private TSanquanMaturityWarningInfoService tSanquanMaturityWarningInfoService;

    /*
     * <AUTHOR>
     * @description 分页查询
     * @since ${date}
     * @param TSanquanMaturityWarningInfoQuery
     * @return BasePageResponse<TSanquanMaturityWarningInfo>
     **/
    @GetMapping("/findPage")
    @Operation(summary = "分页查询", description = "分页查询")
    public BasePageResponse<TSanquanMaturityWarningInfoVO> findPage(TSanquanMaturityWarningInfoQuery query){
        return pageOk(tSanquanMaturityWarningInfoService.findPage(query));
    }

    /*
     * <AUTHOR>
     * @description 根据Id查询
     * @since ${date}
     * @param id
     * @return BaseResponse<TSanquanMaturityWarningInfoAddVO>
     **/
    @GetMapping("/findOne")
    @Operation(summary = "根据Id查询", description = "根据Id查询")
    public BaseResponse<TSanquanMaturityWarningInfoVO> findOne(String id) {
        return ok(tSanquanMaturityWarningInfoService.findOne(id));
    }

    /*
     * <AUTHOR>
     * @description 查询列表
     * @since ${date}
     * @return BaseResponse<List<TSanquanMaturityWarningInfo>>
     **/
    @GetMapping("/findList")
    @Operation(summary = "查询列表", description = "查询列表")
    public BaseResponse<List<TSanquanMaturityWarningInfoVO>> findList(TSanquanMaturityWarningInfoQuery query) {
        return ok(tSanquanMaturityWarningInfoService.findList(query));
    }
    /*
     * <AUTHOR>
     * @description 新增
     * @since ${date}
     * @param TSanquanMaturityWarningInfo
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/addOrUpdate")
    @Operation(summary = "新增", description = "新增")
    public BaseResponse<String> addOrUpdate(@RequestBody TSanquanMaturityWarningInfoVO tSanquanMaturityWarningInfoVO){
        String id = tSanquanMaturityWarningInfoService.addOrUpdate(tSanquanMaturityWarningInfoVO);
        if(StringUtils.isNotEmpty(id)){
            return ok(id);
        }
        return notOk();
    }

    /*
     * <AUTHOR>
     * @description 修改
     * @since ${date}
     * @param TSanquanMaturityWarningInfo
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    @Operation(summary = "修改", description = "修改")
    public BaseResponse<Boolean> update(@RequestBody TSanquanMaturityWarningInfo tSanquanMaturityWarningInfo) {
        return ok(tSanquanMaturityWarningInfoService.update(tSanquanMaturityWarningInfo));
    }

    /*
     * <AUTHOR>
     * @description 删除
     * @since ${date}
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    @Operation(summary = "删除", description = "删除")
    public BaseResponse<Boolean> delete(String id) {
        return ok(tSanquanMaturityWarningInfoService.delete(id));
    }

}