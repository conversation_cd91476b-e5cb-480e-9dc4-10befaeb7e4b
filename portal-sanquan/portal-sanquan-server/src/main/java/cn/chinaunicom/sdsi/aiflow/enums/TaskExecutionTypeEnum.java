package cn.chinaunicom.sdsi.aiflow.enums;

/**
 * @program: sanquan_server
 * @ClassName TaskExecutionTypeEnum
 * @description:
 * @author: ma<PERSON><PERSON>
 * @date: 2025-05-08 09:22
 * @Version 1.0
 **/
public enum TaskExecutionTypeEnum {
    MANUAL("manual", "手动执行"),
    SCHEDULED("scheduled", "定时执行"),
    AUTO_ONCE("auto_once", "自动执行(一次性)");

    private final String code;
    private final String msg;

    TaskExecutionTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

