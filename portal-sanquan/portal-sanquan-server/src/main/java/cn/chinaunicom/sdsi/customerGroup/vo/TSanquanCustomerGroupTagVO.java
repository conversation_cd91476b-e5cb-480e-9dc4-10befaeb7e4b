package cn.chinaunicom.sdsi.customerGroup.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 客户群标签视图对象
 * @Author: han<PERSON><PERSON><PERSON>
 * @Date: 2024-05-22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "客户群标签视图对象", description = "客户群标签视图对象")
public class TSanquanCustomerGroupTagVO {

    @Schema(name = "")
    private String id;

    @Schema(name = "客户群id")
    private String customerGroupId;

    @Schema(name = "标签id")
    private String tagId;

    @Schema(name = "标签名称")
    private String tagName;

    @Schema(name = "标签值")
    private String tagValues;

    @Schema(name = "标签sql")
    private String tagSql;

    @Schema(name = "标签json")
    private String tagJson;

    private String tenantId;

}
