package cn.chinaunicom.sdsi.keynoteOppo.asr.asrLog.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@TableName("t_sanquan_asr_log")
public class AsrLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private String id;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 文件是否已经删除（0、否，1、是）
     */
    private String isDelete;

    /**
     * asr识别之后的内容
     */
    private String asrText;

    /**
     * 是否已经识别（0、失败，1、成功，2、接口异常）
     */
    private String isIdentify;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 文件在线地址
     */
    private String fileUrl;
}
