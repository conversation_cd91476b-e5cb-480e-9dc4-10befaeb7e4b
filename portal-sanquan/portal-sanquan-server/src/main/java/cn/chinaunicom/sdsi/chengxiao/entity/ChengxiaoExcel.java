package cn.chinaunicom.sdsi.chengxiao.entity;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 * <AUTHOR>
 * @since 2024-11-13
 */
@Data
@TableName("ch_t_sanquan_chengxiao_zhuanhua_result")
public class ChengxiaoExcel implements Serializable {

    private static final long serialVersionUID = 1L;

    /*数据来源*/
    @ExcelProperty(title = "数据来源",width = 160)
    String dataSource;

    /*任务数量*/
    @ExcelProperty(title = "任务数量",width = 100)
    String taskNum;

    /*未执行数量*/
    @ExcelProperty(title = "未执行数量",width = 100)
    String weiZhixing;

    /*已执行数量*/
    @ExcelProperty(title = "已执行数量",width = 100)
    String zhixing;

    /*执行率*/
    @ExcelProperty(title = "执行率",width = 100)
    String zhixingRate;

    /*转化数*/
    @ExcelProperty(title = "转化数",width = 100)
    String zhuanhuaNum;

    /*转化率*/
    @ExcelProperty(title = "转化率",width = 100)
    String zhuanhuaRate;

    /*商机数量*/
    @ExcelProperty(title = "商机数量",width = 100)
    String oppoNum;

    /*商机金额*/
    @ExcelProperty(title = "商机金额",width = 100)
    String oppoAmount;

    /*项目数量*/
    @ExcelProperty(title = "项目数量",width = 100)
    String projectNum;

    /*项目金额(万元)*/
    @ExcelProperty(title = "项目金额(万元)",width = 100)
    String projectAmount;

    /*新发展业务数量*/
    @ExcelProperty(title = "新发展业务数量",width = 100)
    String newDevelopBusiness;

    /*新发展业务收入(元)*/
    @ExcelProperty(title = "新发展业务收入(元)",width = 100)
    String newDevelopBusinessAmount;

    /*业务数量提升率*/
    @ExcelProperty(title = "业务数量提升率",width = 100)
    String newDevelopBusinessRate;

    /*业务收入提升率*/
    @ExcelProperty(title = "业务收入提升率",width = 100)
    String newDevelopBusinessAmountRate;

}
