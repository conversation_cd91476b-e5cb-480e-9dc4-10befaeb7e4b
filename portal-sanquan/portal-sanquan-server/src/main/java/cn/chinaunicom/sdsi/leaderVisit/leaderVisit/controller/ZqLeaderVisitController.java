package cn.chinaunicom.sdsi.leaderVisit.leaderVisit.controller;

import cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.vo.ZqLeaderVisitQueryVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.leaderVisit.leaderVisit.service.ZqLeaderVisitService;
import cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.entity.ZqLeaderVisit;
import org.springframework.beans.factory.annotation.Autowired;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import org.springframework.web.bind.annotation.*;
import java.util.List;

/**
 * <p>
 * 高层拜访数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-18
 */
@RestController
@RequestMapping("/leaderVisit")
public class ZqLeaderVisitController extends BaseController {

    @Autowired
    private ZqLeaderVisitService zqLeaderVisitService;

    /**
     * <AUTHOR>
     * @description 分页查询
     * @since 2025-07-18
     * @param zqLeaderVisitVo
     * @return BasePageResponse<ZqLeaderVisit>
     **/
    @GetMapping("/findPage")
    public BasePageResponse<ZqLeaderVisit> findPage(ZqLeaderVisitQueryVo zqLeaderVisitVo){
        return pageOk(zqLeaderVisitService.findPage(zqLeaderVisitVo));
    }

    /**
     * <AUTHOR>
     * @description 根据Id查询
     * @since 2025-07-18
     * @param id
     * @return BaseResponse<ZqLeaderVisit>
     **/
    @GetMapping("/findOne")
    public BaseResponse<ZqLeaderVisit> findOne(String id) {
        return ok(zqLeaderVisitService.findOne(id));
    }

    /**
     * <AUTHOR>
     * @description 查询列表
     * @since 2025-07-18
     * @return BaseResponse<List<ZqLeaderVisit>>
     **/
    @GetMapping("/findList")
    public BaseResponse<List<ZqLeaderVisit>> findList(ZqLeaderVisitQueryVo zqLeaderVisitVo) {
        return ok(zqLeaderVisitService.findList(zqLeaderVisitVo));
    }

    /**
     * <AUTHOR>
     * @description 新增
     * @since 2025-07-18
     * @param zqLeaderVisit
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/add")
    public BaseResponse<Integer> add(@RequestBody ZqLeaderVisit zqLeaderVisit){
        return ok(zqLeaderVisitService.add(zqLeaderVisit));
    }

    /**
     * <AUTHOR>
     * @description 修改
     * @since 2025-07-18
     * @param zqLeaderVisit
     * @return BaseResponse<Integer>
     **/
    @PostMapping("/update")
    public BaseResponse<Integer> update(@RequestBody ZqLeaderVisit zqLeaderVisit) {
        return ok(zqLeaderVisitService.update(zqLeaderVisit));
    }

    /**
     * <AUTHOR>
     * @description 删除
     * @since 2025-07-18
     * @param id
     * @return BaseResponse<Integer>
     **/
    @GetMapping("/delete")
    public BaseResponse<Integer> delete(String id) {
        return ok(zqLeaderVisitService.delete(id));
    }
}
