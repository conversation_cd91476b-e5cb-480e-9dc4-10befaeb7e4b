package cn.chinaunicom.sdsi.mark.controller;

import cn.chinaunicom.sdsi.cloud.mark.entity.TSanquanMarkData;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.mark.service.TSanquanMarkDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
* 打标-标签值表
*
* <AUTHOR> 
* @since  2024-04-12
*/
@RestController
@RequestMapping("markData")
@Tag(name="打标-标签值表")
public class TSanquanMarkDataController extends BaseController {

    @Autowired
    private TSanquanMarkDataService tSanquanMarkDataService;

//    @GetMapping("page")
//    @Operation(summary = "分页")
//    public BaseResponse<PageResult<TSanquanMarkDataVO>> page(@ParameterObject @Valid TSanquanMarkDataQuery query){
//        PageResult<TSanquanMarkDataVO> page = tSanquanMarkDataService.page(query);
//
//        return pageOk(page);
//    }

    /**
     * 标志值查看
     * @param id
     * @return
     */
    @GetMapping("{id}")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanMarkData> get(@PathVariable("id") Long id){
        TSanquanMarkData entity = tSanquanMarkDataService.getById(id);
        return ok(entity);
    }

    /**
     * 标签值新增
     * @param vo
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "保存")
    public BaseResponse<String> save(@RequestBody TSanquanMarkData vo){
        tSanquanMarkDataService.save(vo);
        return ok(vo.getId());
    }

    /**
     * 标签值修改
     * @param vo
     * @return
     */
    @PostMapping("/update")
    @Operation(summary = "修改")
    public BaseResponse<Boolean> update(@RequestBody @Valid TSanquanMarkData vo){
        return ok(tSanquanMarkDataService.saveOrUpdate(vo));
    }

    /**
     * 标签值删除
     * @param id
     * @return
     */
    @PostMapping("/delete")
    @Operation(summary = "删除")
    public BaseResponse<Boolean> delete(@RequestBody String id){
        return ok(tSanquanMarkDataService.removeById(id));
    }
}