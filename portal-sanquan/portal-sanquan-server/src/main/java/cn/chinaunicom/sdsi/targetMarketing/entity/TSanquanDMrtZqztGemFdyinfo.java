package cn.chinaunicom.sdsi.targetMarketing.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 服务大厅动态反馈信息 t_sanquan_d_mrt_zqzt_gem_fdyinfo
 * @Author: han
 * @Date: 2024-08-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "服务大厅动态反馈信息", description = "服务大厅动态反馈信息")
@TableName("t_sanquan_d_mrt_zqzt_gem_fdyinfo")
public class TSanquanDMrtZqztGemFdyinfo implements Serializable {

    @Schema(name = "主键")
    private String ID;

    @Schema(name = "反馈订单号")
    private String feedbackOrderId;

    @Schema(name = "反馈id")
    private String feedbackId;

    @Schema(name = "任务编码")
    private String workOrderCode;

    @Schema(name = "字段编码")
    private String fieldCode;

    @Schema(name = "字段名称")
    private String fieldName;

    @Schema(name = "字段类型")
    private String fieldType;

    @Schema(name = "字段值")
    private String fieldValue;

    @Schema(name = "创建时间")
    private String createTime;

    @Schema(name = "")
    private String monthId;

    @Schema(name = "")
    private String dayId;

    @Schema(name = "策略编号")
    @TableField(exist = false)
    private String superiorPolicyCode;

}
