package cn.chinaunicom.sdsi.dingding.controller;

import cn.chinaunicom.sdsi.dingding.entity.DayProductYXMarketingDS;
import cn.chinaunicom.sdsi.dingding.service.DayProductYXMarketingDSService;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingDSQueryVo;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/productDay/ds")
public class DayProductYXMarketingDSController extends BaseController {

    @Autowired
    DayProductYXMarketingDSService marketingDSService;

    @GetMapping("/findPage")
    public BasePageResponse<DayProductYXMarketingDS> findPage(DayProductYXMarketingDSQueryVo detail){
        return pageOk(marketingDSService.findPage(detail));
    }

}
