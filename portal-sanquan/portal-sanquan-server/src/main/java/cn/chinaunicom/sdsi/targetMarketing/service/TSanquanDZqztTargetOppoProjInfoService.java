package cn.chinaunicom.sdsi.targetMarketing.service;

import cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanDZqztTargetOppoProjInfo;
import cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztTargetOppoProjInfoVO;
import cn.chinaunicom.sdsi.targetMarketing.queryvo.TSanquanDZqztTargetOppoProjInfoQueryVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 靶向营销算网数值商机项目详情表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-08-30
 */
public interface TSanquanDZqztTargetOppoProjInfoService extends IService<TSanquanDZqztTargetOppoProjInfo> {

    /**
     * 分页查询靶向营销算网数值商机项目详情表
     * 
     * @param tSanquanDZqztTargetOppoProjInfoQueryVO
     * @return IPage<TSanquanDZqztTargetOppoProjInfoVO>
     */
    IPage<TSanquanDZqztTargetOppoProjInfoVO> findPage(TSanquanDZqztTargetOppoProjInfoQueryVO tSanquanDZqztTargetOppoProjInfoQueryVO);

    /**
     * 查询靶向营销算网数值商机项目详情表详细信息
     *
     * @param taskCode
     * @return TSanquanDZqztTargetOppoProjInfoVO
     */
    TSanquanDZqztTargetOppoProjInfoVO findInfo(String taskCode);

    /**
     * 新增靶向营销算网数值商机项目详情表
     *
     * @param tSanquanDZqztTargetOppoProjInfoVO
     * @return String
     */
    String add(TSanquanDZqztTargetOppoProjInfoVO tSanquanDZqztTargetOppoProjInfoVO);

    /**
     * 修改靶向营销算网数值商机项目详情表
     *
     * @param tSanquanDZqztTargetOppoProjInfoVO
     * @return Boolean
     */
    Boolean update(TSanquanDZqztTargetOppoProjInfoVO tSanquanDZqztTargetOppoProjInfoVO);

    /**
     * 删除靶向营销算网数值商机项目详情表
     *
     * @param taskCode
     * @return Boolean
     */
    Boolean delete(String taskCode);

}
