package cn.chinaunicom.sdsi.dingtalkAbility.request;

import lombok.Data;

@Data
public class InteractiveCardRequest {
    private String robotCode;
    private String openConversationId;
    private String cardBizId;
    private String singleChatReceiver;
    private String cardTemplateId;
    private Object cardData;
    private SendOptions sendOptions;

    @Data
    public static class SendOptions {
        private String atUserListJson;
        private Boolean atAll;
        private String receiverListJson;
        private String cardPropertyJson;
    }
}

