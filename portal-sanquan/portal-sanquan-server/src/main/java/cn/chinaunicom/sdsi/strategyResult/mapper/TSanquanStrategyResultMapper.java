package cn.chinaunicom.sdsi.strategyResult.mapper;

import cn.chinaunicom.sdsi.strategy.entity.QueryStat;
import cn.chinaunicom.sdsi.strategy.vo.TSanquanStrategyConverRateExcelVO;
import cn.chinaunicom.sdsi.strategyResult.entity.TSanquanStrategyResult;
import cn.chinaunicom.sdsi.strategyResult.vo.AiResult;
import cn.chinaunicom.sdsi.strategyResult.vo.CityVO;
import cn.chinaunicom.sdsi.strategyResult.vo.TSanquanStrategyResultVO;
import cn.chinaunicom.sdsi.strategyResult.vo.TSanquanStrategyResultQueryVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 策略推荐结果表Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-16
 */
@Mapper
public interface TSanquanStrategyResultMapper extends BaseMapper<TSanquanStrategyResult> {

    /**
     * 分页查询策略推荐结果表
     * 
     * @param page,tSanquanStrategyResultQueryVO
     * @return 策略推荐结果表
     */
    IPage<TSanquanStrategyResultVO> findPage(@Param("page") IPage page, @Param("query") TSanquanStrategyResultQueryVO tSanquanStrategyResultQueryVO);

    List<Map<String, String>> strategyStat();

    List<Map<String, String>> allConverRate(@Param("query") QueryStat query);

    List<Map<String, String>> productRecommendation(@Param("query") QueryStat query);

    List<Map<String, String>> cityOpportunityArea(@Param("query") QueryStat query);

    List<Map<String, String>> oportunityConvCustTop(@Param("query") QueryStat query);

    List<Map<String, String>> strategyDownOne(@Param("query") QueryStat query);

    List<Map<String, String>> potentialOpportunityDownOne(@Param("query") QueryStat query);

    List<Map<String, String>> orderDownOne(@Param("query") QueryStat query);

    List<Map<String, String>> businessOpportunityDownOne(@Param("query") QueryStat query);
    IPage<Map<String, String>> businessOpportunityDownOnePage(@Param("page") IPage page, @Param("query") QueryStat query);

    List<Map<String, String>> executingDownTwo(@Param("query") QueryStat query);

    List<CityVO> findCityList();

    List<String> findCityNameList();

    List<Map<String, String>> allConverRateByCity(@Param("query") QueryStat query);
    //策略转化率分析(按策略)
    List<Map<String, String>> strategyConverRate(@Param("query") QueryStat query);
    IPage<TSanquanStrategyConverRateExcelVO> strategyConverRatePage(@Param("page") IPage page, @Param("query") QueryStat query);

    List<TSanquanStrategyConverRateExcelVO> strategyConverRatePageHeji(@Param("query") QueryStat query);
    // 查询策略结果信息（地市政企）
    TSanquanStrategyResultVO selectByCityZQ(@Param("id") String id);
    //首页策略评价-产品找客户
    Map<String, String> strategyEvaluationProToCus(QueryStat query);
    //首页策略评价-客户找产品
    Map<String, String> strategyEvaluationCusToPro(QueryStat query);
    //首页工单执行靶向营销
    Map<String, String> workOrderExecTarget(QueryStat query);
    //首页工单执行要客管家
    Map<String, String> workOrderExecYk(QueryStat query);

    // 策略数量（产品找客户、客户找产品）
    List<Map<String,String>> strategyNum(QueryStat query);

    List<AiResult> selectResultByAi(@Param("query") QueryStat query);
}
