package cn.chinaunicom.sdsi.keynoteOppo.oppoWarehouse.mapper;

import cn.chinaunicom.sdsi.businessOppoPushBrief.entity.BusinessOppoPushBrief;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.entity.OppoTrack;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.query.OppoTrackQueryVo;
import cn.chinaunicom.sdsi.cloud.keynoteOppo.oppoWarehouse.vo.OppoTrackVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * 商机跟踪表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Mapper
public interface OppoTrackMapper extends BaseMapper<OppoTrack> {

    // 分页查询
    IPage<OppoTrackVo> findPageList(@Param("page") IPage page, @Param("query") OppoTrackQueryVo oppoTrackVo);

    IPage<OppoTrackVo> findPageHis(@Param("page") IPage page, @Param("query") OppoTrackQueryVo oppoTrackVo);

    IPage<OppoTrackVo> findPage(@Param("page") IPage page, @Param("query") OppoTrackQueryVo oppoTrackVo);

    // 查询列表
    List<OppoTrackVo> findList(@Param("query") OppoTrackQueryVo oppoTrackVo);

    List<OppoTrackVo> getLastWeekUnTrack(@Param("query") OppoTrackQueryVo oppoTrackVo);

}
