package cn.chinaunicom.sdsi.aiflow.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@NoArgsConstructor
@AllArgsConstructor
public class CustomerInsightVO {
    /**
     * 客户洞察名称
     */
    private String insightName;
    /**
     * 客户数量
     */
    private Integer customerCount;
    /**
     * 客户洞察标签
     */
    private String customerTags;
    /**
     * 上传标识
     */
    private Boolean uploadSuccess;

    public String getInsightName() {
        return insightName;
    }

    public CustomerInsightVO setInsightName(String insightName) {
        this.insightName = insightName;
        return this;
    }

    public Integer getCustomerCount() {
        return customerCount;
    }

    public CustomerInsightVO setCustomerCount(Integer customerCount) {
        this.customerCount = customerCount;
        return this;
    }

    public String getCustomerTags() {
        return customerTags;
    }

    public CustomerInsightVO setCustomerTags(String customerTags) {
        this.customerTags = customerTags;
        return this;
    }

    public Boolean getUploadSuccess() {
        return uploadSuccess;
    }

    public CustomerInsightVO setUploadSuccess(Boolean uploadSuccess) {
        this.uploadSuccess = uploadSuccess;
        return this;
    }
}
