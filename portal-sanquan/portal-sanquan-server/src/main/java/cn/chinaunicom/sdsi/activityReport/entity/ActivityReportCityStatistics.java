package cn.chinaunicom.sdsi.activityReport.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.math.BigDecimal;

/**
 * 专项活动报表地市维度统计实体类
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ActivityReportCityStatistics {
    /**
     * 策略ID
     */
    @ExcelProperty(value = "策略ID")
    private String strategyId;

    /**
     * 策略名称
     */
    @ExcelProperty(value = "策略名称")
    private String strategyName;

    /**
     * 地市
     */
    @ExcelProperty(value = "地市")
    private String city;

    /**
     * 客户个数
     */
    @ExcelProperty(value = "客户个数")
    private String customerCount;
    /**
     * 客户经理有商机意向个数
     */
    @ExcelProperty(value = "客户经理有商机意向个数")
    private String cmOpportunityCount;
    /**
     * 客户经理有商机意向金额
     */
    @ExcelProperty(value = "客户经理有商机意向金额")
    private String cmOpportunityAmount;
    /**
     * 客户经理执行情况-待执行个数
     */
    @ExcelProperty(value = {"客户经理执行情况","待执行个数"})
    private String cmPendingCount;
    /**
     * 客户经理执行情况-执行中个数
     */
    @ExcelProperty(value = {"客户经理执行情况","执行中个数"})
    private String cmProcessingCount;

    /**
     * 客户经理执行情况-已完成个数
     */
    @ExcelProperty(value = {"客户经理执行情况","执行中个数"})
    private String cmCompletedCount;


    /**
     * 营销经理有商机意向个数
     */
    @ExcelProperty(value = "营销经理有商机意向个数")
    private String mmOpportunityCount;
    /**
     * 营销经理有商机意向金额
     */
    @ExcelProperty(value = "营销经理有商机意向金额")
    private String mmOpportunityAmount;
    /**
     * 营销经理执行情况-待执行个数
     */
    @ExcelProperty(value = {"营销经理执行情况","待执行个数"})
    private String mmPendingCount;
    /**
     * 营销经理执行情况-执行中个数
     */
    @ExcelProperty(value = {"营销经理执行情况","执行中个数"})
    private String mmProcessingCount;
    /**
     * 营销经理执行情况-已完成个数
     */
    @ExcelProperty(value = {"营销经理执行情况","已完成个数"})
    private String mmCompletedCount;
}
