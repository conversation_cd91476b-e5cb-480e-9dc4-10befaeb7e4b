package cn.chinaunicom.sdsi.tokenValidate;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @data 2024/12/06 16:45
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface TokenValidate {
    String value() default "";  // 可以加一个value值，方便扩展

    Class<?> tokenClass() default Object.class; // 默认值为Object.class，确保必须指定类型
}