package cn.chinaunicom.sdsi.customerGroup.controller;

import cn.chinaunicom.sdsi.customerGroup.vo.TSanquanCustomerGroupCustomerVO;
import cn.chinaunicom.sdsi.customerGroup.queryvo.TSanquanCustomerGroupCustomerQueryVO;
import cn.chinaunicom.sdsi.customerGroup.service.TSanquanCustomerGroupCustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 客户群关联客户 控制器
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@RestController
@RequestMapping("/customerGroup/customer")
public class TSanquanCustomerGroupCustomerController extends BaseController {

    @Autowired
    private TSanquanCustomerGroupCustomerService tSanquanCustomerGroupCustomerService;

    /*
     * <AUTHOR>
     * @description 分页查询客户群关联客户
     * @since 2024-05-20
     * @param tSanquanCustomerGroupCustomerQueryVO
     * @return BasePageResponse<TSanquanCustomerGroupCustomerVO>
     **/
    @Operation(summary = "分页查询客户群关联客户", description ="分页查询客户群关联客户数据")
    @GetMapping("/findPage")
    public BasePageResponse<TSanquanCustomerGroupCustomerVO> findPage(TSanquanCustomerGroupCustomerQueryVO tSanquanCustomerGroupCustomerQueryVO) {
        return pageOk(tSanquanCustomerGroupCustomerService.findPage(tSanquanCustomerGroupCustomerQueryVO));
    }
    @Operation(summary = "分页查询客户群关联客户", description ="分页查询客户群关联客户数据")
    @GetMapping("/findPageCustomerView")
    public BasePageResponse<TSanquanCustomerGroupCustomerVO> findPageCustomerView(TSanquanCustomerGroupCustomerQueryVO tSanquanCustomerGroupCustomerQueryVO) {
        return pageOk(tSanquanCustomerGroupCustomerService.findPageCustomerView(tSanquanCustomerGroupCustomerQueryVO));
    }

    /*
     * <AUTHOR>
     * @description 查询客户群关联客户详细信息
     * @since 2024-05-20
     * @param id
     * @return BaseResponse<TSanquanCustomerGroupCustomerVO>
     **/
    @Operation(summary = "查询客户群关联客户详细信息", description ="查询客户群关联客户详细信息")
    @GetMapping("/findInfo")
    public BaseResponse<TSanquanCustomerGroupCustomerVO> findInfo(String id) {
        return ok(tSanquanCustomerGroupCustomerService.findInfo(id));
    }

    /*
     * <AUTHOR>
     * @description 新增客户群关联客户
     * @since 2024-05-20
     * @param tSanquanCustomerGroupCustomerVO
     * @return BaseResponse<String>
     **/
    @Operation(summary = "新增客户群关联客户", description ="新增客户群关联客户")
    @PostMapping("/add")
    public BaseResponse<String> add(@RequestBody TSanquanCustomerGroupCustomerVO tSanquanCustomerGroupCustomerVO) {
        return ok(tSanquanCustomerGroupCustomerService.add(tSanquanCustomerGroupCustomerVO));
    }

    /*
     * <AUTHOR>
     * @description 修改客户群关联客户
     * @since 2024-05-20
     * @param tSanquanCustomerGroupCustomerVO
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "修改客户群关联客户", description ="修改客户群关联客户")
    @PostMapping("/update")
    public BaseResponse<Boolean> update(@RequestBody TSanquanCustomerGroupCustomerVO tSanquanCustomerGroupCustomerVO) {
        return ok(tSanquanCustomerGroupCustomerService.update(tSanquanCustomerGroupCustomerVO));
    }

    /*
     * <AUTHOR>
     * @description 删除客户群关联客户
     * @since 2024-05-20
     * @param id
     * @return BaseResponse<Boolean>
     **/
    @Operation(summary = "删除客户群关联客户", description ="删除客户群关联客户")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String id) {
        return ok(tSanquanCustomerGroupCustomerService.delete(id));
    }

}
