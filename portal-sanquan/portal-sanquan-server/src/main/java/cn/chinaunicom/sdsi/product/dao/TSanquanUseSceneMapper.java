package cn.chinaunicom.sdsi.product.dao;

import cn.chinaunicom.sdsi.cloud.product.entity.TSanquanUseScene;
import cn.chinaunicom.sdsi.cloud.product.query.TSanquanUseSceneQuery;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: 王永凯
 * @Date: 2024/11/7 下午2:38
 */
@Mapper
public interface TSanquanUseSceneMapper extends BaseMapper<TSanquanUseScene> {

    void insertBatchSomeColumn(@Param("list") List<TSanquanUseScene> insertList);

    IPage<TSanquanUseScene> findPage(@Param("page") IPage page, @Param("query") TSanquanUseSceneQuery query);

    IPage<TSanquanUseScene> findClusterScenePage(@Param("page") IPage page, @Param("query") TSanquanUseSceneQuery query);
}
