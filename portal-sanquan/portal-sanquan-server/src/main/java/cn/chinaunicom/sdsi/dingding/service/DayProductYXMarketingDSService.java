package cn.chinaunicom.sdsi.dingding.service;

import cn.chinaunicom.sdsi.dingding.entity.DayProductYXMarketingDS;
import cn.chinaunicom.sdsi.dingding.mapper.DayProductYXMarketingDSMapper;
import cn.chinaunicom.sdsi.dingding.vo.DayProductYXMarketingDSQueryVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DayProductYXMarketingDSService {

    @Autowired
    DayProductYXMarketingDSMapper dsMapper;


    public IPage<DayProductYXMarketingDS> findPage(DayProductYXMarketingDSQueryVo dsQueryVo){
        return dsMapper.findPage(dsQueryVo);
    }

}
