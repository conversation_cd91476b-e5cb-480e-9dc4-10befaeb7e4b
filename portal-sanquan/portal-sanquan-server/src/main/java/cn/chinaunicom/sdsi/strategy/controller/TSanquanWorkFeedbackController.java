package cn.chinaunicom.sdsi.strategy.controller;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanWorkFeedback;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanWorkFeedbackQuery;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanWorkOrderQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkFeedbackVO;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkOrderDetailVO;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkOrderVO;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.opportunity.queryvo.TSanquanPotentialOpportunityQueryVO;
import cn.chinaunicom.sdsi.opportunity.vo.TSanquanPotentialOpportunityExcelVO;
import cn.chinaunicom.sdsi.opportunity.vo.TSanquanPotentialOpportunityListExcelVO;
import cn.chinaunicom.sdsi.opportunity.vo.TSanquanPotentialOpportunityVO;
import cn.chinaunicom.sdsi.strategy.service.TSanquanWorkFeedbackService;
import cn.chinaunicom.sdsi.strategy.vo.SupporterAuditVO;
import cn.chinaunicom.sdsi.strategy.vo.TSanquanWorkFeedbackExcelVO;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
* 工单反馈表（潜在机会推送）
*
* <AUTHOR>
* @since  2024-04-16
*/
@RestController
@RequestMapping("/strategy/workFeedback")
@Tag(name="工单反馈表（潜在机会推送）")
@AllArgsConstructor
public class TSanquanWorkFeedbackController extends BaseController {

    @Autowired
    private TSanquanWorkFeedbackService tSanquanWorkFeedbackService;

    /**
     * 分页条件查询
     * @param query
     * @return
     */
    @GetMapping("/findPage")
    @Operation(summary = "分页")
    public BasePageResponse<TSanquanWorkFeedbackVO> findPage(TSanquanWorkFeedbackQuery query){
        return pageOk(tSanquanWorkFeedbackService.findPage(query));
    }

    /***
     * 新的工单反馈查询
     * @param query
     * @return
     */
    @GetMapping("/findPageOrder")
    @Operation(summary = "分页")
    public BasePageResponse<TSanquanWorkOrderVO> findPageOrder(TSanquanWorkOrderQuery query){
        return pageOk(tSanquanWorkFeedbackService.findPageOrder(query));
    }

    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    @GetMapping("/findInfo/{id}")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanWorkFeedback> findById(@PathVariable("id") String id){
        return ok(tSanquanWorkFeedbackService.getById(id));
    }
    /**
     * 根据id查询详情
     * @param id
     * @return
     */
    @GetMapping("/findInfo")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanWorkFeedbackVO> findInfoById(String id){
        return ok(tSanquanWorkFeedbackService.findInfoById(id));
    }
    @GetMapping("/findInfoOrder")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanWorkOrderDetailVO> findInfoOrderById(TSanquanWorkOrderVO orderInfo){
        return ok(tSanquanWorkFeedbackService.findInfoOrderById(orderInfo));
    }
    /**
     * 根据工单待办表id查询详情
     * @param todoOppoId
     * @return
     */
    @GetMapping("/findInfoByTodoOppoId")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanWorkFeedbackVO> findInfoByTodoOppoId(String todoOppoId){
        return ok(tSanquanWorkFeedbackService.findInfoByTodoOppoId(todoOppoId));
    }

    /**
     * 根据策略id查询详情
     * @param id
     * @return
     */
    @GetMapping("/findOpportunityIdInfo")
    @Operation(summary = "信息")
    public BaseResponse<TSanquanWorkFeedbackVO> findOpportunityIdInfo(String id){
        return ok(tSanquanWorkFeedbackService.findOpportunityIdInfo(id));
    }

    /**
     * 新增
     * @param tSanquanWorkFeedback
     * @return
     */
    @PostMapping("/save")
    @Operation(summary = "信息")
    public BaseResponse<String> save(@RequestBody TSanquanWorkFeedbackVO tSanquanWorkFeedback){
        return ok(tSanquanWorkFeedbackService.add(tSanquanWorkFeedback));
    }

    /**
     * 关单
     * @param tSanquanWorkFeedback
     * @return
     */
    @PostMapping("/closeTicket")
    @Operation(summary = "关单")
    public BaseResponse<String> closeTicket(@RequestBody TSanquanWorkFeedbackVO tSanquanWorkFeedback){
        return ok(tSanquanWorkFeedbackService.closeTicket(tSanquanWorkFeedback));
    }


    /**
     * 根据潜在机会id查询
     * @param id
     * @return
     */
    @GetMapping ("/findWorkFeedback/{opportunityId}")
    @Operation(summary = "根据潜在机会id查询")
    public BaseResponse<TSanquanWorkFeedbackVO> findWorkFeedback(@PathVariable("opportunityId") String id){
        return ok(tSanquanWorkFeedbackService.findWorkFeedback(id));
    }

    /**
     * 根据代表详情表Id查询
     * @param todoOppoId
     * @return
     */
    @GetMapping ("/findWorkFeedbackByTodoOppoId/{todoOppoId}")
    @Operation(summary = "根据潜在机会id查询")
    public BaseResponse<TSanquanWorkFeedbackVO> findWorkFeedbackByTodoOppoId(@PathVariable("todoOppoId") String todoOppoId){
        return ok(tSanquanWorkFeedbackService.findWorkFeedbackByTodoOppoId(todoOppoId));
    }

    /**
     * 工单信息导出
     * @param ids
     */
    @PostMapping("/exportWorkFeedbackData")
    public void exportAddmissionData(@RequestBody List<String> ids) {
//        // 1、查出导出的数据
        List<TSanquanWorkFeedback> tSanquanWorkFeedbacks = tSanquanWorkFeedbackService.listByIds(ids);
        List<TSanquanWorkFeedbackExcelVO> list = tSanquanWorkFeedbacks.stream().map(v -> {
            JSONObject json = JSONUtil.parseObj(v);
            return JSONUtil.toBean(json, TSanquanWorkFeedbackExcelVO.class);
        }).collect(Collectors.toList());
        try {
            // 3、导出数据
            ExcelUtils.exportExcel(list, TSanquanWorkFeedbackExcelVO.class, "导出数据信息", "导出数据信息");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /*
     * <AUTHOR>
     * @description 根据条件导出导出工单反馈信息
     * @since 2024-05-06
     * @param TSanquanWorkFeedbackQuery
     * @return BasePageResponse<TSanquanWorkFeedbackExcelVO>
     **/
    @Operation(summary = "根据条件导出导出工单反馈信息", description = "根据条件导出导出工单反馈信息")
    @PostMapping("/exportWorkFeedbackDataByQuery")
    public void exportWorkFeedbackDataByQuery(@RequestBody TSanquanWorkFeedbackQuery query) {
        tSanquanWorkFeedbackService.exportWorkFeedbackDataByQuery(query);
    }

    /**
     * 售前支撑人审核
     * @param supporterAuditVO
     * @return
     */
    @PostMapping("/supporterAudit")
    @Operation(summary = "售前支撑人审核")
    public BaseResponse<Boolean> supporterAudit(@RequestBody SupporterAuditVO supporterAuditVO){
        return ok(tSanquanWorkFeedbackService.supporterAudit(supporterAuditVO));
    }


    /**
     * 营销经理保存
     * @param tSanquanWorkFeedback
     * @return
     */
    @PostMapping("/marketingManageFeedbackSave")
    @Operation(summary = "营销经理反馈保存")
    public BaseResponse<String> marketingManageFeedbackSave(@RequestBody TSanquanWorkFeedbackVO tSanquanWorkFeedback){
        return ok(tSanquanWorkFeedbackService.marketingManageFeedbackSave(tSanquanWorkFeedback));
    }

    /**
     * 营销经理保存
     * @param tSanquanWorkFeedback
     * @return
     */
    @PostMapping("/marketingManageFeedbackClose")
    @Operation(summary = "营销经理反馈完成")
    public BaseResponse<String> marketingManageFeedbackClose(@RequestBody TSanquanWorkFeedbackVO tSanquanWorkFeedback){
        return ok(tSanquanWorkFeedbackService.marketingManageFeedbackClose(tSanquanWorkFeedback));
    }

}
