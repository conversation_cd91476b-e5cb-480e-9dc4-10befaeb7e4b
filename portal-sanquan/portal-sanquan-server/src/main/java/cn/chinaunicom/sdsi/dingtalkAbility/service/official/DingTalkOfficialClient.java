package cn.chinaunicom.sdsi.dingtalkAbility.service.official;

import cn.chinaunicom.sdsi.dingtalkAbility.request.BatchRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.GroupRecallRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.RobotBatchSendRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.request.RobotGroupMessageRequest;
import cn.chinaunicom.sdsi.dingtalkAbility.response.RecallCommonResponse;
import cn.chinaunicom.sdsi.dingtalkAbility.response.RobotBatchSendResponse;
import cn.chinaunicom.sdsi.dingtalkAbility.response.RobotSendMessageResponse;
import cn.chinaunicom.sdsi.dingtalkAbility.response.UploadResourceResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class DingTalkOfficialClient {
    /**
     * 名称：上传媒体文件
     * 官方前缀：https://oapi.dingtalk.com
     * 文档地址：https://open.dingtalk.com/document/orgapp/upload-media-files
     */
    private static final String UPLOAD_RESOURCE_URL_SUFFIX = "/media/upload";

    /**
     * 名称：批量发送人与机器人会话中机器人消息
     * 官方前缀：https://api.dingtalk.com
     * 文档地址：https://open.dingtalk.com/document/orgapp/chatbots-send-one-on-one-chat-messages-in-batches
     */
    private static final String BATCH_SEND_PERSON_URL_SUFFIX = "/v1.0/robot/oToMessages/batchSend";

    /**
     * 名称：根据手机号查询用户
     * 官方前缀：https://oapi.dingtalk.com
     * 文档地址：https://open.dingtalk.com/document/orgapp/query-users-by-phone-number
     */
    private static final String GET_USER_BY_MOBILE_URL_SUFFIX = "/topapi/v2/user/getbymobile";

    /**
     * 名称：机器人发送群聊消息
     * 官方前缀：https://api.dingtalk.com
     * 文档地址：https://open.dingtalk.com/document/orgapp/query-users-by-phone-number
     */
    private static final String BATCH_SEND_GROUP_URL_SUFFIX = "/v1.0/robot/groupMessages/send";

    /**
     * 名称：批量撤回人与机器人会话中机器人消息
     * 官方前缀：https://api.dingtalk.com
     * 文档地址：https://open.dingtalk.com/document/orgapp/batch-message-recall-chat
     */
    private static final String BATCH_RECALL_MESSAGE_URL_SUFFIX = "/v1.0/robot/otoMessages/batchRecall";


    /**
     * 名称：企业机器人撤回内部群消息
     * 官方前缀：https://api.dingtalk.com
     * 文档地址：https://open.dingtalk.com/document/orgapp/enterprise-chatbot-withdraws-internal-group-messages
     */
    private static final String BATCH_RECALL_GROUP_MESSAGE_URL_SUFFIX = "/v1.0/robot/groupMessages/recall";



    private final DingTalkOfficialConfigCenter dingTalkOfficialConfigCenter;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Autowired
    public DingTalkOfficialClient(DingTalkOfficialConfigCenter dingTalkOfficialConfigCenter) {
        this.dingTalkOfficialConfigCenter = dingTalkOfficialConfigCenter;
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
        this.objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }


    /**
     * 根据手机号和分组名获取用户的userId
     *
     * @param mobile    手机号
     * @param groupName 分组名(可为null)
     * @return userId 或 null(如果获取失败)
     */
    public String getUserIdByMobile(String mobile, String groupName) {
        if (mobile == null || mobile.isEmpty()) {
            log.warn("获取用户userId失败，手机号不能为空");
            return null;
        }

        if (groupName == null || groupName.isEmpty()) {
            log.warn("钉钉标识符不可为空");
            return null;
        }

        try {
            String accessToken = dingTalkOfficialConfigCenter.getAccessToken(groupName);

            if (accessToken == null) {
                log.error("获取accessToken失败，手机号: {}, 分组: {}", mobile, groupName);
                return null;
            }

            String url = dingTalkOfficialConfigCenter.getBaseUrl(true) + GET_USER_BY_MOBILE_URL_SUFFIX + "?access_token=" + accessToken;
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            JSONObject requestBody = new JSONObject();
            requestBody.put("mobile", mobile);

            HttpEntity<String> request = new HttpEntity<>(requestBody.toJSONString(), headers);

            String response = restTemplate.postForObject(url, request, String.class);


            if (response == null) {
                log.error("获取用户userId失败，响应为空，手机号: {}", mobile);
                return null;
            }

            JSONObject jsonResponse = JSON.parseObject(response);
            if (jsonResponse == null) {
                log.error("获取用户userId失败，响应解析失败，手机号: {}, 响应: {}", mobile, response);
                return null;
            }

            Integer errCode = jsonResponse.getInteger("errcode");
            if (errCode == null || errCode != 0) {
                log.error("获取用户userId失败，手机号: {}, 错误码: {}, 响应: {}",
                        mobile, errCode, response);
                return null;
            }

            JSONObject result = jsonResponse.getJSONObject("result");
            if (result == null) {
                log.error("获取用户userId失败，结果为空，手机号: {}, 响应: {}", mobile, response);
                return null;
            }

            String userId = result.getString("userid");
            if (userId == null || userId.isEmpty()) {
                log.error("获取的用户userId为空，手机号: {}, 响应: {}", mobile, response);
                return null;
            }

            return userId;
        } catch (Exception e) {
            log.error("获取用户userId异常，手机号: {}, 分组: {}", mobile, groupName, e);
            return null;
        }
    }


    /**
     * 根据手机号发送消息
     * @param group
     * @param request
     * @return
     */
    public RobotBatchSendResponse sendByMobile(String group, RobotBatchSendRequest request) {
        String url = dingTalkOfficialConfigCenter.getBaseUrl(false) + BATCH_SEND_PERSON_URL_SUFFIX;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-acs-dingtalk-access-token", dingTalkOfficialConfigCenter.getAccessToken(group));
        request.setRobotCode(dingTalkOfficialConfigCenter.getRobotCode(group));

        JSONObject requestBody = new JSONObject();
        requestBody.put("robotCode", request.getRobotCode());
        requestBody.put("userIds", request.getUserIds());
        requestBody.put("msgKey", request.getMsgKey());
        requestBody.put("msgParam", JSON.toJSONString(request.getMsgParam()));


        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class);
            if (response.getBody() == null) {
                log.error(response.toString());
                log.error("钉钉单聊反馈为空，{}", response.getStatusCode());
            }
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return JSON.parseObject(response.getBody(), RobotBatchSendResponse.class);
            } else {
                return null;
            }
        } catch (HttpClientErrorException e) {
            log.error("通过API发送消息失败, userIds: {}, status: {}, body: {}", request.getUserIds(), e.getStatusCode(), e.getResponseBodyAsString(), e);
            return null;
        } catch (Exception e) {
            log.error("通过API发送消息失败, userIds: {}", request.getUserIds(), e);
            return null;
        }
    }


    /**
     * 发送消息到指定群
     * @param group
     * @param request
     * @return
     */
    public RobotSendMessageResponse sendToGroup(String group, RobotGroupMessageRequest request) {
        String url = dingTalkOfficialConfigCenter.getBaseUrl(false) + BATCH_SEND_GROUP_URL_SUFFIX;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-acs-dingtalk-access-token", dingTalkOfficialConfigCenter.getAccessToken(group));
        request.setRobotCode(dingTalkOfficialConfigCenter.getRobotCode(group));
        request.setOpenConversationId(dingTalkOfficialConfigCenter.getOpenConversationId(group));

        JSONObject requestBody = new JSONObject();
        requestBody.put("robotCode", request.getRobotCode());
        requestBody.put("openConversationId", request.getOpenConversationId());
        requestBody.put("msgKey", request.getMsgKey());
        requestBody.put("msgParam", JSON.toJSONString(request.getMsgParam()));


        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody.toJSONString(), headers);
        try {
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class);
            if (response.getBody() == null) {
                log.error(response.toString());
                log.error("钉钉发送群消息反馈为空，{}", response.getStatusCode());
            }
            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                return JSON.parseObject(response.getBody(), RobotSendMessageResponse.class);
            } else {
                return null;
            }
        } catch (HttpClientErrorException e) {
            log.error("通过API发送群消息失败, conversationId: {}, status: {}, body: {}", request.getOpenConversationId(), e.getStatusCode(), e.getResponseBodyAsString(), e);
            return null;
        } catch (Exception e) {
            log.error("通过API发送群消息失败, conversationId: {}", request.getOpenConversationId(), e);
            return null;
        }
    }


    /**
     * 撤回消息
     * @param group
     * @param request
     * @return
     */
    public RecallCommonResponse batchRecallRobotMessages(String group, BatchRecallRequest request) {
        String url = dingTalkOfficialConfigCenter.getBaseUrl(false) + BATCH_RECALL_MESSAGE_URL_SUFFIX;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-acs-dingtalk-access-token", dingTalkOfficialConfigCenter.getAccessToken(group));
        request.setRobotCode(dingTalkOfficialConfigCenter.getRobotCode(group));

        String requestBody = null;
        try {
            requestBody = objectMapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("请求参数序列化失败", e);
        }

        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class);
        if (response.getBody() == null) {
            log.error(response.toString());
            log.error("撤回请求返回为空，{}", response.getStatusCode());
        }
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            return JSON.parseObject(response.getBody(), RecallCommonResponse.class);
        } else {
            return null;
        }
    }


    /**
     * 撤回群消息
     * @param group
     * @param request
     * @return
     */
    public RecallCommonResponse recallGroupMessages(String group, GroupRecallRequest request) {
        String url = dingTalkOfficialConfigCenter.getBaseUrl(false) + BATCH_RECALL_GROUP_MESSAGE_URL_SUFFIX;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("x-acs-dingtalk-access-token", dingTalkOfficialConfigCenter.getAccessToken(group));
        request.setRobotCode(dingTalkOfficialConfigCenter.getRobotCode(group));
        request.setOpenConversationId(dingTalkOfficialConfigCenter.getOpenConversationId(group));

        String requestBody = null;
        try {
            requestBody = objectMapper.writeValueAsString(request);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("请求参数序列化失败", e);
        }

        HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class);
        if (response.getBody() == null) {
            log.error(response.toString());
            log.error("撤回请求返回为空，{}", response.getStatusCode());
        }
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            return JSON.parseObject(response.getBody(), RecallCommonResponse.class);
        } else {
            return null;
        }
    }

    /**
     * 上传文件到钉钉服务器
     *
     * @param file
     * @param type image 图片、voice 语音、 file 普通文件、 video 视频
     * @return
     * @throws Exception
     */
    public UploadResourceResponse uploadFileToDingTalk(String group, File file, String type) {
        RestTemplate restTemplate = new RestTemplate();
        String url = dingTalkOfficialConfigCenter.getBaseUrl(true) + UPLOAD_RESOURCE_URL_SUFFIX + "?access_token=" + this.dingTalkOfficialConfigCenter.getAccessToken(group) + "&type=" + type;

        // 准备文件内容
        byte[] fileBytes = null;
        try {
            fileBytes = Files.readAllBytes(file.toPath());
        } catch (IOException e) {
            log.error("读入文件失败", e);
            return null;
        }

        // 构建multipart请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        LinkedMultiValueMap<String, Object> parts = new LinkedMultiValueMap<>();
        parts.add("media", new HttpEntity<>(fileBytes, createFileHeaders(file)));

        HttpEntity<LinkedMultiValueMap<String, Object>> requestEntity = new HttpEntity<>(parts, headers);

        // 发送上传请求
        ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
        );

        return JSON.parseObject(response.getBody(), UploadResourceResponse.class);
    }

    /**
     * 创建文件上传头信息
     */
    public HttpHeaders createFileHeaders(File file) {
        HttpHeaders fileHeaders = new HttpHeaders();
        fileHeaders.setContentDispositionFormData("media", file.getName());
        fileHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        return fileHeaders;
    }
}
