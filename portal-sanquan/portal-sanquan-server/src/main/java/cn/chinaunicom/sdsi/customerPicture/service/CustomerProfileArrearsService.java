package cn.chinaunicom.sdsi.customerPicture.service;

import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileArrears;
import cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOverdue;
import cn.chinaunicom.sdsi.customerPicture.vo.CustomerPictureVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 三全客户画像-回款信息-客户欠费表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-24
 */
public interface CustomerProfileArrearsService extends IService<CustomerProfileArrears> {

    //欠费总金额
    CustomerProfileArrears getArrearsTotal(CustomerPictureVo businessInfo);
    //逾期总金额
    CustomerProfileArrears getOverdueTotal(CustomerPictureVo businessInfo);
    //最大逾期时长
    CustomerProfileOverdue getMaxOverdueTime(CustomerPictureVo businessInfo);

}
