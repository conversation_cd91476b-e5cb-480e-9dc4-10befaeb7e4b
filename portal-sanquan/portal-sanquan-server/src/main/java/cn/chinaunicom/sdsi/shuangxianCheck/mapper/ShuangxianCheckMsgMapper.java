package cn.chinaunicom.sdsi.shuangxianCheck.mapper;

import cn.chinaunicom.sdsi.shuangxianCheck.entity.ShuangxianCheckMsg;
import cn.chinaunicom.sdsi.shuangxianCheck.vo.ShuangxianCheckMsgVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@Mapper
public interface ShuangxianCheckMsgMapper extends BaseMapper<ShuangxianCheckMsg> {

    List<ShuangxianCheckMsgVO> selectListNew(@Param("query")ShuangxianCheckMsgVO msgVO);

    List<ShuangxianCheckMsgVO> validSendMsgByDay(@Param("query")ShuangxianCheckMsgVO msgVO);
}
