package cn.chinaunicom.sdsi.model.mapper;

import cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description 针对表【t_sanquan_d_roster_nature_customer_info】的数据库操作Mapper
 * @createDate 2024-09-24 16:44:58
 * @Entity cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo
 */
@Mapper
public interface DRosterNatureCustomerInfoMapper extends BaseMapper<DRosterNatureCustomerInfo> {
    DRosterNatureCustomerInfo findByOrgCode(@Param("orgCode") String orgCode);

    DRosterNatureCustomerInfo findByTydm(@Param("tydm") String tydm);

    DRosterNatureCustomerInfo findByNatrueCustId(@Param("natureCustomerId") String natureCustomerId);

    DRosterNatureCustomerInfo findByNatrueCustName(@Param("natureCustomerName") String natureCustomerName);
}




