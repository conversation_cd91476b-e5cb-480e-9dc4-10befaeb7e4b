package cn.chinaunicom.sdsi.fiveMillion.service.impl;

import cn.chinaunicom.sdsi.fiveMillion.entity.FiveMillionOppoList;
import cn.chinaunicom.sdsi.fiveMillion.mapper.FiveMillionOppoListMapper;
import cn.chinaunicom.sdsi.fiveMillion.service.FiveMillionOppoListService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class FiveMillionOppoListServiceImpl extends ServiceImpl<FiveMillionOppoListMapper, FiveMillionOppoList> implements FiveMillionOppoListService {

    @Autowired
    private FiveMillionOppoListMapper fiveMillionOppoListMapper;

    @Override
    public List<FiveMillionOppoList> findList(String dayId) {
        return fiveMillionOppoListMapper.findList(dayId);
    }
}
