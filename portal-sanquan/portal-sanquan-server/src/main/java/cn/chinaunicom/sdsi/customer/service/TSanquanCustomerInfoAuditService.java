package cn.chinaunicom.sdsi.customer.service;

import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerInfoAudit;
import cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerInfoAudit;
import cn.chinaunicom.sdsi.cloud.customer.query.TSanquanCustomerInfoAuditQuery;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerInfoAuditRequestVO;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerInfoAuditVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * 客户资料审核
 *
 * <AUTHOR> 
 * @since  2024-05-15
 */
public interface TSanquanCustomerInfoAuditService extends IService<TSanquanCustomerInfoAudit> {
    // 分页查询
    IPage<TSanquanCustomerInfoAudit> findPage(TSanquanCustomerInfoAuditQuery tSanquanCustomerInfoAuditQuery);

    // 根据id查询
    TSanquanCustomerInfoAudit findOne(String id);

    // 查询列表
    List<TSanquanCustomerInfoAudit> findList(TSanquanCustomerInfoAuditQuery query);

    // 新增
    int add(TSanquanCustomerInfoAudit tSanquanCustomerInfoAudit);

    // 修改
    int update(TSanquanCustomerInfoAudit tSanquanCustomerInfoAudit);

    // 删除
    int delete(String id);

    //根据客户id查询
    List<TSanquanCustomerInfoAuditVO> getByCustomerIdList(String id);


    //用户资料审核
    Boolean customerInfoAudit(TSanquanCustomerInfoAuditRequestVO tSanquanCustomerInfoAuditRequestVO);
}