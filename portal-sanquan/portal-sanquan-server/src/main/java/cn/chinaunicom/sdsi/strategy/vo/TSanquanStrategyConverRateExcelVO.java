package cn.chinaunicom.sdsi.strategy.vo;

import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelTitle;
import cn.chinaunicom.sdsi.plugins.office.excel.annotation.TitleStyle;
import cn.chinaunicom.sdsi.plugins.office.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description: 工单反馈列表导出对象
 * @Author: dzk
 * @Date: 2024-04-22
 */
@Data
@ContentRowHeight(20) // 内容单元格高度
@TitleStyle(backgroundColor = 55)
public class TSanquanStrategyConverRateExcelVO {

    @Schema(name = "策略id、策略code")
    private String strategyId;

    @Schema(name = "业务场景")
    private String contactor;


    @ExcelProperty(title = "策略名称" ,cellSort = 0 ,width = 9000 ,rowSpan = 2)
    private String strategyName;

    @ExcelProperty(title = "地市" ,cellSort = 1,width = 4000 ,rowSpan = 2)
    private String cityName;

    @Schema(name = "总数")
    private String totalNum;

    @Schema(name = "转化率")
    @ExcelProperty(title = "转化率",cellSort = 2,width = 4000 ,rowSpan = 2)
    private String transfPercent;

    @Schema(name = "执行率")
    @ExcelProperty(title = "执行率",cellSort = 3,width = 4000 ,rowSpan = 2)
    private String execPercent;

    @ExcelProperty(title = "工单数",cellSort = 4,width = 4000 ,rowSpan = 2)
    private String orderNum;

    @ExcelProperty(title = "待执行",cellSort = 5,width = 4000 ,rowSpan = 2)
    private String daiExecNum;

    @ExcelProperty(title = "待改派",cellSort = 6,width = 4000 ,rowSpan = 2)
    private String daiChangeNum;

    @ExcelProperty(title = "执行中",cellSort = 7,width = 4000 ,rowSpan = 2)
    private String doingNum;

    @ExcelProperty(title = "已关单",cellSort = 8,width = 4000 ,rowSpan = 2)
    private String closeNum;

    @ExcelProperty(title = "到期未执行",cellSort = 9,width = 4000 ,rowSpan = 2)
    private String noExecNum;

//    @ColumnWidth(26)
//    @ExcelProperty(title = "反馈时间")
//    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
//    private Date feedbackDate;

    @ExcelProperty(title = "关联商机数量",cellSort = 10,width = 4000 ,firstRowCell = @ExcelTitle(title = "商机情况", colSpan = 4))
    private String assoOpportunityNum;

    @ExcelProperty(title = "关联商机金额",cellSort = 11,width = 4000, fillChildrenValue = true )
    private String assoOppMoney;

    @ExcelProperty(title = "匹配商机数量",cellSort = 12,width = 4000, fillChildrenValue = true )
    private String opportunityNum;


    @ExcelProperty(title = "匹配商机金额",cellSort = 13,width = 4000 , fillChildrenValue = true)
    private String oppMoney;

    @ExcelProperty(title = "关联项目数量",cellSort = 14,width = 4000 ,firstRowCell = @ExcelTitle(title = "项目情况", colSpan = 4))
    private String assoProjectNum;

    @ExcelProperty(title = "关联项目金额",cellSort = 15,width = 4000, fillChildrenValue = true)
    private String assoProjectMoney;

    @ExcelProperty(title = "项目数量",cellSort = 16,width = 4000, fillChildrenValue = true)
    private String projectNum;

    @ExcelProperty(title = "项目金额",cellSort = 17,width = 4000, fillChildrenValue = true)
    private String projectMoney;



    @ExcelProperty(title = "命中业务数量",cellSort = 18,width = 4000 ,rowSpan = 2)
    private String addDevNum;

    @ExcelProperty(title = "命中业务收入",cellSort = 19,width = 4000 ,rowSpan = 2)
    private String addDevFee;

    @ExcelProperty(title = "拉动业务数量",cellSort = 20,width = 4000 ,rowSpan = 2)
    private String newDevNum;

    @ExcelProperty(title = "拉动业务收入",cellSort = 21,width = 4000 ,rowSpan = 2)
    private String newDevFee;

    @ExcelProperty(title = "到期续约数",cellSort = 22,width = 4000 ,rowSpan = 2)
    private String renewalNum;

    @ExcelProperty(title = "到期合约数",cellSort = 23,width = 4000 ,rowSpan = 2)
    private String expirationNum;

    @ExcelProperty(title = "预警数量",cellSort = 24,width = 4000 ,rowSpan = 2)
    private String warningCount;

    @ExcelProperty(title = "催办数量",cellSort = 24,width = 4000 ,rowSpan = 2)
    private String reminderCount;

    @ExcelProperty(title = "督办数量",cellSort = 24,width = 4000 ,rowSpan = 2)
    private String supervisionCsount;

}
