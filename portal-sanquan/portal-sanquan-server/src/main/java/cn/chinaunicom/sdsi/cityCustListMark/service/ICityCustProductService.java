package cn.chinaunicom.sdsi.cityCustListMark.service;

import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustProductQueryDTO;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustProduct;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @program: sanquan_server
 * @ClassName ISanquanProductService
 * @description: 三全产品Service接口
 * @author: system
 * @date: 2024-12-19
 * @Version 1.0
 **/
public interface ICityCustProductService extends IService<CityCustProduct> {

    /**
     * 根据行业类型查询相关产品列表
     *
     * @param cityCustProductQueryDTO 查询参数
     * @return 产品列表
     */
    List<CityCustProduct> findProductByIndustryList(CityCustProductQueryDTO cityCustProductQueryDTO);
}
