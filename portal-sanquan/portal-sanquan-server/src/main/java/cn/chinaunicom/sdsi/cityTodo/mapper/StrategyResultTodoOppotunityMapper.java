package cn.chinaunicom.sdsi.cityTodo.mapper;

import cn.chinaunicom.sdsi.cityTodo.entity.StrategyResultTodoOppotunity;
import cn.chinaunicom.sdsi.cityTodo.vo.StrategyResultTodoOppoVo;
import cn.chinaunicom.sdsi.cityTodo.vo.SupportSituationResVo;
import cn.chinaunicom.sdsi.cityTodo.vo.SupportSituationVo;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanWorkFeedbackVO;
import cn.chinaunicom.sdsi.cloud.workMonitor.vo.TSanquanWorkMonitorConfigVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-12
 */
@Mapper
public interface StrategyResultTodoOppotunityMapper extends BaseMapper<StrategyResultTodoOppotunity> {
    void insertBatchSomeColumn(@Param("list") List<StrategyResultTodoOppotunity> batchList);

    /**
     * 根据待办查询待推送的数据
     *
     * <AUTHOR>
     * @Date 2024/7/12 17:52
     */
    List<String> findWaitPushByTodoCode(@Param("todoCode") String todoCode);

    // 地市未推送的数量
    int  findOppoCountByTodoCode(@Param("todoCode") String todoCode );

    // 根据id查询详情信息
    TSanquanWorkFeedbackVO findInfoByTodoOppoId(@Param("todoOppoId") String todoOppoId);

    // 根据todoCode和潜在机会 查询工单代办表中id
    StrategyResultTodoOppotunity findByTodoCodeAndOppoId(@Param("todoCode") String todoCode,@Param("oppoIdStr") String oppoIdStr);

    // 根据工单监控状态，查询工单详情信息
    List<StrategyResultTodoOppoVo> selectWorkTodoList(@Param("queryVo") TSanquanWorkMonitorConfigVO queryVo);

    // 支撑情况（营销经理）
    IPage<SupportSituationResVo> selectSupportSituation(@Param("page") IPage page,@Param("query") SupportSituationVo supportSituationVo);

    // 支撑人员情况统计（营销经理）
    IPage<SupportSituationResVo> getSupportStaffSituation(@Param("page") IPage page,@Param("query") SupportSituationVo supportSituationVo);

    // 支撑人员情况统计详情信息
    IPage<StrategyResultTodoOppoVo> selectSupportStaffSituationPageInfo(@Param("page") IPage page,@Param("query") SupportSituationVo supportSituationVo);
}
