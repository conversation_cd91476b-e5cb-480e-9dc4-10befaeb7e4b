package cn.chinaunicom.sdsi.common.entity;


import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.poi.ss.usermodel.CellStyle;

import java.util.List;
import java.util.Map;

@Data
public class PoiExcelEntity {

    String sheetName;

    int sheetIndex = 0;
    /**
     *  输出excel文件名称，带后缀
     */
    String fileName;
    /**
     * 文件路径，完整路径，参考：TemplateFacory.UPLOAD_PATCH
     */
    String filePath;

    // resource目录下文件目录名称，全称。如：oppo/商机简报数据模板.xlsx
    String resourceFilePath;

    /**
     * 类型，1，表示读取模板文件，渲染文件。输出到网络
     *       2，表示不读取任何文件，手工渲染生成文件。输出到网络
     *       3，表示读取模板文件，渲染文件。输出到文件
     *       4，表示不读取任何文件，手工渲染生成文件。输出到本地文件
     */
    String type;

    /**
     * 自定义表头样式
     */
    CellStyle headerStyle;
    /**
     * 自定义单元格样式
     */
    CellStyle excelStyle;

    /**
     * 单元格动态参数，单次注入
     */
    Map<String,Object> mapParams;

    /**
     * 默认行高
     */
    short rowHeight = 35;
    /**
     * 默认表头行高
     */
    short headHeight = 35;
    /**
     * 默认列宽
     */
    short colWidth = 20;
    /**
     * 渲染数据的开始行, 默认0. （不需要考虑表头占用行数）
     */
    int startRow = 0;

    int startCol = 0;
    /*
     * 表格数据，行数据。示例：
     * data1.add(Lists.newArrayList("客户经理", "测试推营销1", "", "","",""));
       data1.add(Lists.newArrayList("", "目标工单1", "完成工单1", "完成率1","商机个数1","商机规模1"));
    * */
    List<List<Object>> data;
    /**
     * 表格标题行，支持多行标题
     */
    List<List<String>> headData;
    /**
     * 合并单元格坐标，起始坐标-结束坐标，示例：(firstRow,lastRow,firstCol,lastCol)
     * 示例：
     * [[0,1,0,0]] : 第一列，上下两行合并
     * [[0,0,1,5]]: 第一行，第二列到第6列合并
     * 注：相对当前表格的坐标，而非sheet页的坐标
     */
    List<int[]> mergeCellPos = Lists.newArrayList();

}
