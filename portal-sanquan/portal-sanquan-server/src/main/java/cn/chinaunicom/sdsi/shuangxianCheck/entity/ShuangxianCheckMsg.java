package cn.chinaunicom.sdsi.shuangxianCheck.entity;

import cn.chinaunicom.sdsi.shuangxianCheck.vo.ShuangxianCheckCusVO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 双线纳入群组稽核
 */
@Data
@TableName("t_sanquan_shuangxian_check_msg")
public class ShuangxianCheckMsg  implements Serializable {
   String id;
   String userId;
   /*用户手机号码*/
   String deviceNumber;
    /*用户名称*/
   String custName;
    /*发展人ID*/
   String developerId;
    /*发展人工号*/
   String accountCode;
    /*发展人名称*/
   String devName;
    /*发展人手机号码*/
   String linkmanPhone;
    /*专线名称*/
   String productName;
   String dayId;
   String monthId;

   @TableField(exist = false)
   List<ShuangxianCheckCusVO> custs;
}
