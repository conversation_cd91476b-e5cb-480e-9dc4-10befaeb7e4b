package cn.chinaunicom.sdsi.strategy.workFeedbackHistory.service;

import cn.chinaunicom.sdsi.strategy.workFeedbackHistory.entity.TSanquanWorkFeedbackHistory;
import cn.chinaunicom.sdsi.strategy.workFeedbackHistory.vo.TSanquanWorkFeedbackHistoryVo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <p>
 * 工单反馈表（潜在机会推送） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-01
 */
public interface TSanquanWorkFeedbackHistoryService extends IService<TSanquanWorkFeedbackHistory> {

    // 分页查询
    IPage<TSanquanWorkFeedbackHistory> findPage(TSanquanWorkFeedbackHistoryVo tSanquanWorkFeedbackHistoryVo);

    // 根据id查询
    TSanquanWorkFeedbackHistory findOne(String id);

    // 查询列表
    List<TSanquanWorkFeedbackHistory> findList();

    // 新增
    int add(TSanquanWorkFeedbackHistory tSanquanWorkFeedbackHistory);

    // 修改
    int update(TSanquanWorkFeedbackHistory tSanquanWorkFeedbackHistory);

    // 删除
    int delete(String id);

}
