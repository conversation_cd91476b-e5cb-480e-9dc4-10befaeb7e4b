package cn.chinaunicom.sdsi.dingding.service;

import cn.chinaunicom.sdsi.dingding.controller.DingDingController;
import cn.chinaunicom.sdsi.targetMarketing.controller.TSanquanEmailCustomerController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 暴露定时任务实现bean类
 */
@Slf4j
@Service("dingDingTaskService")
public class DingDingTaskService {

    @Autowired
    private TSanquanEmailCustomerController emailController;

    @Autowired
    DingDingController dingDingController;

    /**
     * 周永康周报-靶向任务周报
     */
    public void pushWeekReportExcelToGroup(){
        try {
            dingDingController.pushExcelToGroup();
        } catch (Exception e) {
            log.error("推送周报错误：",e);
        }
    }

    /**
     * 单士钊-靶向任务邮件
     */
    public void pushEmailTaskBaxiang(){
        try {
            emailController.pushTask();
        } catch (Exception e) {
            log.error("推送周报错误：",e);
        }
    }


}
