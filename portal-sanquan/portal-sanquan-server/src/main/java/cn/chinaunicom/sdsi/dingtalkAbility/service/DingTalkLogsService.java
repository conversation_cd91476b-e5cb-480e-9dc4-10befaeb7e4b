package cn.chinaunicom.sdsi.dingtalkAbility.service;

import cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkLogs;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkLogsQueryVo;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkLogsVo;
import cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkLogsWithdrawQueryVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

public interface DingTalkLogsService extends IService<DingTalkLogs> {
    IPage<DingTalkLogsVo> findPage(DingTalkLogsQueryVo vo);

    String withdraw(DingTalkLogsWithdrawQueryVo vo);

    Integer processQueryKeysCount(DingTalkLogsWithdrawQueryVo vo);

    Integer batchIdCount(DingTalkLogsWithdrawQueryVo vo);
}
