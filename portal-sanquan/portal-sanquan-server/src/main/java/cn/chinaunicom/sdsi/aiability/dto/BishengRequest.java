package cn.chinaunicom.sdsi.aiability.dto;

import lombok.Data;

import java.util.Map;

/**
 * 毕昇智能体API请求对象
 */
@Data
public class BishengRequest {
    /**
     * 工作流ID
     */
    private String workflowId;

    /**
     * 会话ID，用于维持会话上下文
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 用户输入数据
     */
    private Map<String, Object> input;

    /**
     * 是否使用流式响应
     */
    private Boolean stream = true;
}
