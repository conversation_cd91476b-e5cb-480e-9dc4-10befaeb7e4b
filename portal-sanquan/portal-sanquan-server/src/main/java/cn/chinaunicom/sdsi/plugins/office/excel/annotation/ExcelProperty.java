package cn.chinaunicom.sdsi.plugins.office.excel.annotation;

import org.springframework.web.bind.annotation.Mapping;

import java.lang.annotation.*;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2024/8/16 19:57
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Mapping
public @interface ExcelProperty {

    //表格头
    String title() default "";

    // 列宽
    int width() default 3000;

    // 列搞
    int height() default 25;

    // 列排序
    int cellSort() default 0;

    // 填充child 中的值
    boolean fillChildrenValue() default false;

    // 一级标题
    ExcelTitle firstRowCell() default @ExcelTitle;

    //二级标题
    ExcelTitle secondRowCell() default @ExcelTitle;

    // 需要关联的表
    CellQueryTable cellQueryTable() default @CellQueryTable;

    // 合并的行数
    int rowSpan() default 1;

}
