package cn.chinaunicom.sdsi.fiveMillion.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 500万以上项目清单
 */
@Data
@TableName("five_million_project_list")
public class FiveMillionProjectList extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private String city;                // 地市
    private String country;             // 区县
    private String provinceIndustryName; // 省分行业
    private String projectName;         // 项目名称
    private String customerName;        // 客户名称
    private String revenueAmount;       // 收入合同金额
    private String advanceCost;         // 垫款成本
    private String grossMarginP;       // 立项毛利率
    private String projectContent;      // 项目内容技术方案及成果
    private String dayId;               // DAY_ID
}