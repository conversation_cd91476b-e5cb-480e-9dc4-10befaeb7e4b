package cn.chinaunicom.sdsi.tag.controller;

import cn.chinaunicom.sdsi.cloud.tag.entity.Tag;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import cn.chinaunicom.sdsi.cloud.tag.vo.TagVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BasePageResponse;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.strategyResult.service.TSanquanStrategyResultService;
import cn.chinaunicom.sdsi.strategyResult.vo.CityVO;
import cn.chinaunicom.sdsi.strategyResult.vo.TSanquanStrategyResultQueryVO;
import cn.chinaunicom.sdsi.strategyResult.vo.TSanquanStrategyResultVO;
import cn.chinaunicom.sdsi.tag.entity.TagDb;
import cn.chinaunicom.sdsi.tag.entity.TagForm;
import cn.chinaunicom.sdsi.tag.service.TagService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 标签 控制器
 */
@RestController
@RequestMapping("/tag/")
public class TagController extends BaseController {

    @Autowired
    private TagService baseService;

    @Operation(summary = "分页", description = "分页")
    @GetMapping("/findPage")
    public BasePageResponse<TagVO> findPage(TagQuery query) {
        return pageOk(baseService.findPage(query));
    }

    @Operation(summary = "判断是否存在", description = "分页")
    @GetMapping("/checkExist")
    public BaseResponse<Boolean> checkExist(TagQuery query) {
        return new BaseResponse<>(baseService.checkExist(query));
    }

    @Operation(summary = "查询列表", description = "查询列表")
    @GetMapping("/findList")
    public BaseResponse<List<TagVO>> findInfo(TagQuery query) {
        return ok(baseService.findList(query));
    }

    @Operation(summary = "根据id查询", description = "根据id查询")
    @GetMapping("/findOne")
    public BaseResponse<TagVO> findOne(String id) {
        TagQuery query = new TagQuery();
        query.setId(id);
        return ok(baseService.findOneById(query));
    }

    @Operation(summary = "根据id查询", description = "根据id查询")
    @GetMapping("/findFormOne")
    public BaseResponse<TagForm> findFormOne(String id) {
        TagQuery query = new TagQuery();
        query.setId(id);
        return ok(baseService.findTagForm(query));
    }

    @Operation(summary = "保存数据", description = "保存数据")
    @PostMapping("/save")
    public BaseResponse<Boolean> add(@RequestBody TagForm tagForm) {
        try {
            return ok(baseService.addAll(tagForm));
        } catch (Exception e) {
            e.printStackTrace();
            return new BaseResponse<>("0", false, e.getMessage(), null);
        }
    }

    @Operation(summary = "测试数据库链接", description = "测试数据库链接")
    @PostMapping("/testDbLink")
    public BaseResponse<Boolean> testDbLink(@RequestBody TagDb tagDb) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("table", tagDb.getDbTableName());
            params.put("column", tagDb.getDbTableColumn());
            params.put("collectionType", tagDb.getCollectionType());
            return ok(!baseService.findDbTest(params).isEmpty());
        } catch (Exception e) {
            return new BaseResponse<>("0", false, "数据库链接失败,数据表中需包含customer_id与"+tagDb.getDbTableColumn()+"字段", null);
        }
    }

    @Operation(summary = "统计", description = "统计")
    @PostMapping("/statistics")
    public BaseResponse<Map<String, Integer>> statistics(@RequestBody TagQuery entity) {
        Map<String, Integer> json = new HashMap<>();
        int a = baseService.findByMonthCount(entity);
        int b = baseService.findByTotalCount(entity);
        json.put("monthCount", a);
        json.put("totalCount", b);
        return ok(json);
    }

    @Operation(summary = "删除", description = "删除")
    @PostMapping("/delete")
    public BaseResponse<Boolean> delete(String id) {
        return ok(baseService.removeById(id));
    }

}
