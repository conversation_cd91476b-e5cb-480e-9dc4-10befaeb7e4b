package cn.chinaunicom.sdsi.customerVisit.mapper;

import cn.chinaunicom.sdsi.customerVisit.entity.TSanquanCustomerVisit;
import cn.chinaunicom.sdsi.customerVisit.vo.TSanquanCustomerVisitVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 客户拜访记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-07
 */
@Mapper
public interface TSanquanCustomerVisitMapper extends BaseMapper<TSanquanCustomerVisit> {

    List<TSanquanCustomerVisitVO> getYxFlagList();

    List<TSanquanCustomerVisitVO> getUpNewDate();

    List<TSanquanCustomerVisitVO> getCityList(String dayId);

}
