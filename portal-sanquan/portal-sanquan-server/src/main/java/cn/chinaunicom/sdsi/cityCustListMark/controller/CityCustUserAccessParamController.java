package cn.chinaunicom.sdsi.cityCustListMark.controller;

import cn.chinaunicom.sdsi.cityCustListMark.dto.CityCustUserAccessParamDTO;
import cn.chinaunicom.sdsi.cityCustListMark.entity.CityCustUserAccessParam;
import cn.chinaunicom.sdsi.cityCustListMark.service.ICityCustUserInfoService;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.quartz.util.StringUtils;
import com.alibaba.fastjson2.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

/**
 * @program: sanquan_server
 * @ClassName CityCustUserInfoController
 * @description:
 * @author: ma<PERSON>an
 * @date: 2025-05-23 16:12
 * @Version 1.0
 **/
@RestController
@RequestMapping("/custOperation")
public class CityCustUserAccessParamController extends BaseController {
    private static final String SUCCESS_CODE = "0000";
    private static final String PARAM_ERROR_CODE = "0001";
    private static final String DEFAULT_ERROR_CODE = "9999";

    @Autowired
    private ICityCustUserInfoService cityCustUserInfoService;

    /**
     * 根据UUID查询用户访问参数
     *
     * @param param
     * @return 用户访问参数实体
     */
    @PostMapping("/getUserInfo")
    @ResponseBody
    public JSONObject getAccessParam(@RequestBody CityCustUserAccessParamDTO param) {
        JSONObject result = new JSONObject();
        String uuid = param.getUuid();
        try {
            if(StringUtils.isNotEmpty(uuid)){
                CityCustUserAccessParam accessParam = cityCustUserInfoService.findUserAccessParamByUuid(uuid);
                if(accessParam != null){
                    JSONObject data = new JSONObject();
                    data.put("telphone", accessParam.getTelphone());
                    data.put("deviceNumber", accessParam.getDeviceNumber());
                    data.put("cityCode", accessParam.getCityCode());
                    data.put("isTitleShow", accessParam.getIsTitleShow());
                    result.put("msg", "成功");
                    result.put("code", SUCCESS_CODE);
                    result.put("data", data);
                }else {
                    result.put("code",DEFAULT_ERROR_CODE);
                    result.put("msg","未查询到用户访问参数");
                }
            }else {
                result.put("msg","必填参数为空");
                result.put("code",PARAM_ERROR_CODE);
            }
        }catch (Exception e){
            result.put("msg","接口异常");
            result.put("code",DEFAULT_ERROR_CODE);
        }
        return result;
    }

    /**
     * 新增用户访问参数
     * @param param 新增参数
     * @return 新增结果
     */
    @PostMapping("/addAccessParam")
    @ResponseBody
    public BaseResponse<String> saveAccessParam(@RequestBody CityCustUserAccessParamDTO param) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        param.setUuid(uuid);
        cityCustUserInfoService.saveUserAccessParam(param);
        return ok(uuid);
    }
}
