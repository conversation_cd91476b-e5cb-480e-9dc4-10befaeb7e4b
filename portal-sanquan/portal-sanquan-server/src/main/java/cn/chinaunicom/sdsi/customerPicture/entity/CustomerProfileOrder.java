package cn.chinaunicom.sdsi.customerPicture.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 三全客户画像-订单信息-订单数量表
 * </p>
 * <AUTHOR>
 * @since 2024-10-24
 */
@Data
@TableName("t_sanquan_customer_profile_order")
public class CustomerProfileOrder extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private String natureCustId;
    private String natureCustName;
    private String orderNumber;
    private String orderDate;
}
