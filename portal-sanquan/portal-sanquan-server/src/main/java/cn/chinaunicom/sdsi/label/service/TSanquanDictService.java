package cn.chinaunicom.sdsi.label.service;

import cn.chinaunicom.sdsi.cloud.label.dto.TSanquanDictPageDTO;
import cn.chinaunicom.sdsi.cloud.label.entity.TSanquanDictDetails;
import cn.chinaunicom.sdsi.cloud.label.entity.TSanquanDictType;
import cn.chinaunicom.sdsi.cloud.label.entity.TSanquanDictValue;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Author: 王永凯
 * @Date: 2024/5/11 13:53
 */
public interface TSanquanDictService extends IService<TSanquanDictType> {
    IPage<TSanquanDictType>  queryDictList(TSanquanDictPageDTO pageDTO);

    Integer addList(TSanquanDictDetails tSanquanDictDetails);

    List<TSanquanDictValue>  dictDetails(String id);

    Integer addDictValueList(TSanquanDictDetails tSanquanDictDetails);


    Integer addDictType(TSanquanDictType tSanquanDictType);

    Integer addDictValue(List<TSanquanDictValue> tSanquanDictValues);

    Integer addDictValueOnly(List<TSanquanDictValue> tSanquanDictValues);

    int deleteTypeByIds(List<String> ids);

    int deleteValueByIds(List<String> ids);

    int deleteBatchIds(List<String> ids);

    Integer dictTypeModifyone(TSanquanDictValue tSanquanDictValue);

    List<TSanquanDictType> findAll();

    List<TSanquanDictValue> finddictValueFindListById(String id);

    List<TSanquanDictValue> findDetails(String id, String search);
}
