package cn.chinaunicom.sdsi.coreGuestVisit.service.impl;

import cn.chinaunicom.sdsi.coreGuestVisit.entity.CoreGuestVisit;
import cn.chinaunicom.sdsi.coreGuestVisit.entity.CoreGuestZeroVisit;
import cn.chinaunicom.sdsi.coreGuestVisit.mapper.CoreGuestVisitMapper;
import cn.chinaunicom.sdsi.coreGuestVisit.service.CoreGuestVisitService;
import cn.chinaunicom.sdsi.common.entity.PoiExcelEntity;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.chinaunicom.sdsi.tourism.MailInfo;
import cn.chinaunicom.sdsi.tourism.MailUtil;
import cn.chinaunicom.sdsi.tourism.entity.EmailLog;
import cn.chinaunicom.sdsi.tourism.service.EmailLogService;
import cn.chinaunicom.sdsi.tourism.util.TemplateFacory;
import cn.chinaunicom.sdsi.util.excel.ExcelPoiUtil;
import cn.chinaunicom.sdsi.util.excel.ExcelUtils;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import org.apache.commons.mail.EmailAttachment;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class CoreGuestVisitServiceImpl extends ServiceImpl<CoreGuestVisitMapper, CoreGuestVisit> implements CoreGuestVisitService {

    // 固定
//    private static final String EMAIL = "<EMAIL>";
//    private static final String EMAIL_NAME = "王子龙";

     private static final String EMAIL = "<EMAIL>";
     private static final String EMAIL_NAME = "赵健";

    @Autowired
    private CoreGuestVisitMapper coreGuestVisitMapper;

    @Autowired
    private EmailLogService emailLogService;

    @Override
    public int pushCoreGuestVisitEmail() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String yesterdayStr = yesterday.format(formatter);

//        // 获取CoreGuestVisit数据
//        List<CoreGuestVisit> coreGuestVisitList = lambdaQuery()
//                .eq(CoreGuestVisit::getDayId, yesterdayStr)
//                .list();


        int pageSize = 5000; // 每页条数（根据数据大小调整）
        int currentPage = 1;
        boolean hasNext = true;
        List<CoreGuestVisit> coreGuestVisitList = new ArrayList<>();

        // 因为数据量可能较大，分批查询
        while (hasNext) {
            // 1. 执行分页查询
            Page<CoreGuestVisit> page = new Page<>(currentPage, pageSize);
            IPage<CoreGuestVisit> result = lambdaQuery()
                    .eq(CoreGuestVisit::getDayId, yesterdayStr)
                    .page(page);

            // 2. 获取当前页数据
            List<CoreGuestVisit> pageData = result.getRecords();
            coreGuestVisitList.addAll(pageData);

            // 3. 判断是否还有下一页
            hasNext = currentPage < result.getPages();
            currentPage++;
        }

        List<CoreGuestZeroVisit> zeroVisitList = coreGuestVisitMapper.selectAllZeroVisit(yesterdayStr);

        // 转换为excel文件 返回path文件
        String path = writeToFile(coreGuestVisitList, zeroVisitList);
        List<String> files = Collections.singletonList(path);

        // 以附件的形式发送邮件
        sendEmail(EMAIL_NAME, EMAIL, "核心要客拜访情况-推送", "周老师，您好：\r\n核心要客清单数据已提取，详见附件，请查收。", "核心要客拜访情况", files);

        return 0;
    }


    /**
     * 写入excel 文件
     * @param visitList
     * @param zeroVisitList
     * @return
     */
    public String writeToFile(List<CoreGuestVisit> visitList, List<CoreGuestZeroVisit> zeroVisitList){
        new TemplateFacory();
        ExcelWriter excelWriter = null;
        String excelFileName =  "核心要客拜访情况-" + new Date().getTime() + ".xlsx";
        try{
            List<List<Object>> detailsCoreVisit = visitList.stream()
                    .map(visit -> Arrays.asList(
                            visit.getAreaName() == null ? "" : visit.getAreaName(),
                            (Object) (visit.getRosterCustomerId() == null ? "" : visit.getRosterCustomerId()),
                            visit.getRosterCustomerName() == null ? "" : visit.getRosterCustomerName(),
                            visit.getFlag() == null ? "" : visit.getFlag(),
                            visit.getNatureCustId() == null ? "" : visit.getNatureCustId(),
                            visit.getNatureCustName() == null ? "" : visit.getNatureCustName(),
                            visit.getRecordId() == null ? "" : visit.getRecordId(),
                            visit.getVisitTime() == null ? "" : visit.getVisitTime(),
                            visit.getVisitTimeNew() == null ? "" : visit.getVisitTimeNew(),
                            visit.getCustManagerName() == null ? "" : visit.getCustManagerName(),
                            visit.getCustManagerCode() == null ? "" : visit.getCustManagerCode(),
                            visit.getOppoNumber() == null ? "" : visit.getOppoNumber(),
                            visit.getEstimatTotalContratAmount() == null ? "" : visit.getEstimatTotalContratAmount(),
                            visit.getOppoCreatetime() == null ? "" : visit.getOppoCreatetime()
                    ))
                    .collect(Collectors.toList());

            List<List<String>> detailsCoreVisitHeadList = Lists.newArrayList();
            detailsCoreVisitHeadList.add(Lists.newArrayList("地市", "名单制ID", "名单制名称", "名单制等级", "自然客户ID", "自然客户名称", "拜访ID", "拜访时间", "拜访月份", "客户经理姓名", "客户经理工号", "商机编码", "商机金额", "商机创建时间"));

            PoiExcelEntity excelEntity1 = new PoiExcelEntity();
            excelEntity1.setData(detailsCoreVisit);
            excelEntity1.setFileName(excelFileName);
            excelEntity1.setFilePath(TemplateFacory.UPLOAD_PATCH);
            excelEntity1.setHeadData(detailsCoreVisitHeadList);
            excelEntity1.setColWidth((short) 10);
            excelEntity1.setSheetName("核心要客本年拜访详情");
            excelEntity1.setSheetIndex(0);



            List<List<Object>> longTermZeroVisit = zeroVisitList.stream()
                    .map(visit -> Arrays.asList(
                            visit.getRosterType() == null ? "" : visit.getRosterType(),
                            (Object) (visit.getRosterCustomerId() == null ? "" : visit.getRosterCustomerId()),
                            visit.getRosterCustomerName() == null ? "" : visit.getRosterCustomerName(),
                            visit.getFlagZf() == null ? "" : visit.getFlagZf().equals("1") ? "是" : "否"
                    ))
                    .collect(Collectors.toList());

            List<List<String>> longTermZeroVisitHeaderList = Lists.newArrayList();
            longTermZeroVisitHeaderList.add(Lists.newArrayList("名单制等级", "名单制ID", "名单制名称", "是否长期零拜访"));

            PoiExcelEntity excelEntity2 = new PoiExcelEntity();
            excelEntity2.setData(longTermZeroVisit);
            excelEntity2.setFileName(excelFileName);
            excelEntity2.setFilePath(TemplateFacory.UPLOAD_PATCH);
            excelEntity2.setHeadData(longTermZeroVisitHeaderList);
            excelEntity2.setColWidth((short) 10);
            excelEntity2.setSheetName("核心要客长期零拜访");
            excelEntity2.setSheetIndex(1);

            ExcelPoiUtil poiUtil = new ExcelPoiUtil();
            poiUtil.workbook = new SXSSFWorkbook(1000);
            poiUtil.factoryPoiExcel(excelEntity1);
            poiUtil.factoryPoiExcel(excelEntity2);
            Sheet sheet = poiUtil.getSheet(0);
            sheet.setColumnWidth(0,8*256);
            sheet.setColumnWidth(1,25*256);
            sheet.setColumnWidth(2,10*256);
            sheet.setColumnWidth(3,10*256);
            sheet.setColumnWidth(4,10*256);
            sheet.setColumnWidth(5,10*256);
            sheet.setColumnWidth(6,10*256);
            sheet.setColumnWidth(7,15*256);
            sheet.setColumnWidth(8,15*256);

            poiUtil.outPutFileExcel(excelEntity1);
            return TemplateFacory.UPLOAD_PATCH + File.separator+ excelFileName;
        }catch (Exception e){
            log.error("系统异常:",e);
            e.printStackTrace();
        }finally {
            ExcelUtils.closeExcelWriter(excelWriter);
        }
        return null;
    }


    // 发送邮件
    private void sendEmail(String name, String email, String emailTitle, String content, String businessName, List<String> files){
        boolean isSend = false;
        String errorMsg= "";
        MailInfo mailInfo = new MailInfo();
        List<String> list = new ArrayList<>();
        list.add(email);
        mailInfo.setToAddress(list);// 收件人
        mailInfo.setSubject(emailTitle); // 标题
        mailInfo.setContent(content); // 内容
        if (files != null && !files.isEmpty()){
            List<EmailAttachment> filesAttachment = Lists.newArrayList();
            // 有附件
            for (String file : files){
                EmailAttachment attachment = new EmailAttachment();
                Path path = Paths.get(file);
                attachment.setName(path.getFileName().toString());
                attachment.setPath(path.toAbsolutePath().toString());
                filesAttachment.add(attachment);
            }
            mailInfo.setAttachments(filesAttachment);
        }

        try {
            MailUtil.sendEmail(mailInfo);
            isSend = true;
        } catch (Exception e) {
            // e.printStackTrace();
            log.error("系统错误",e);
            errorMsg = e.getLocalizedMessage();
        }

        if (isSend) {
            this.insertEmailLog(name, email, emailTitle, content, "成功", null, businessName);
        } else {
            this.insertEmailLog(name, email, emailTitle, content, "失败", errorMsg, businessName);
        }
    }

    // 插入发送邮件记录
    private void insertEmailLog(String name, String email, String emailTitle, String content, String status, String errorMsg, String businessName) {
        EmailLog emailLog = new EmailLog();
        emailLog.setUserName(name);
        emailLog.setTaskName(emailTitle);
        emailLog.setContent(content);
        emailLog.setEmail(email);
        emailLog.setStatus(status);
        emailLog.setCreateDate(TemplateFacory.formatLocal(new Date(), DateUtils.PATTERN_DATETIME));
        emailLog.setRemark(errorMsg);
        emailLog.setBusinessType(businessName);
        emailLogService.add(emailLog);
    }

}
