package cn.chinaunicom.sdsi.activityReport.service;

import cn.chinaunicom.sdsi.activityReport.dto.ActivityReportQueryDTO;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportCityStatistics;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportDetail;
import cn.chinaunicom.sdsi.activityReport.entity.ActivityReportPersonStatistics;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 专项活动报表服务接口
 */
public interface IActivityReportService {
    /**
     * 查询专项活动报表地市维度统计最大账期
     * @return
     */
    String selectMaxReportDate();

    /**
     * 查询专项活动报表地市维度统计数据
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    IPage<ActivityReportCityStatistics> selectCityStatisticsList(ActivityReportQueryDTO param);

    /**
     * 导出专项活动报表地市维度统计数据
     *
     * @param reportDate 统计日期
     * @param response   HTTP响应
     */
    void exportCityStatistics(ActivityReportQueryDTO param, HttpServletResponse response);

    /**
     * 查询专项活动报表人员维度统计数据
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    IPage<ActivityReportPersonStatistics> selectPersonStatisticsList(ActivityReportQueryDTO param);

    /**
     * 导出专项活动报表人员维度统计数据
     *
     * @param param
     * @param response HTTP响应
     */
    void exportPersonStatistics(ActivityReportQueryDTO param, HttpServletResponse response);

    /**
     * 查询专项活动报表明细数据
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    IPage<ActivityReportDetail> selectDetailList(ActivityReportQueryDTO param);

    /**
     * 导出专项活动报表明细数据
     *
     * @param param    查询参数
     * @param response HTTP响应
     */
    void exportDetailList(ActivityReportQueryDTO param, HttpServletResponse response);
}
