package cn.chinaunicom.sdsi.customerGroup.queryvo;

import cn.chinaunicom.sdsi.framework.entity.BaseQueryVO;
import lombok.Data;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * @Description: 客户群关联客户查询对象
 * @Author: han<PERSON><PERSON>o
 * @Date: 2024-05-20
 */
@Data
@Tag(name = "客户群关联客户查询对象", description = "客户群关联客户查询对象")
public class TSanquanCustomerGroupCustomerQueryVO extends BaseQueryVO {

    @Schema(name = "客户群id")
    private String customerGroupId;

    @Schema(name = "客户id")
    private String customerId;

    @Schema(name = "客户名称")
    private String customerName;

    @Schema(name = "创建人")
    private String createBy;

    private List<String> customerIdList;
}
