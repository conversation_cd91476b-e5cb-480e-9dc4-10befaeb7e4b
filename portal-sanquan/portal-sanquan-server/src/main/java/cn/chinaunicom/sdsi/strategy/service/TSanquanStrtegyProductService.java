package cn.chinaunicom.sdsi.strategy.service;

import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyConfiguration;
import cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrtegyProduct;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyConfigurationQuery;
import cn.chinaunicom.sdsi.cloud.strategy.query.TSanquanStrtegyProductQuery;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyConfigurationVO;
import cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanStrtegyProductVO;
import cn.chinaunicom.sdsi.cloud.tag.query.TagQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 策略推荐结果表 服务层接口
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
public interface TSanquanStrtegyProductService extends IService<TSanquanStrtegyProduct> {


    IPage<TSanquanStrtegyProductVO> findPage(TSanquanStrtegyProductQuery query);

    List<TSanquanStrtegyProductVO> findList(TSanquanStrtegyProductQuery query);
    int findByTotalCount( @Param("query") TagQuery query);
    int findByMonthCount( @Param("query") TagQuery query);
    TSanquanStrtegyProductVO findOneById( @Param("query") TSanquanStrtegyProductQuery query);

    // 根据策略id查询选择的产品名称
    List<String> findProductNameList(TSanquanStrtegyProductQuery productQuery);
}
