package cn.chinaunicom.sdsi.util.word;

import com.aspose.cells.PdfSaveOptions;
import com.aspose.cells.Workbook;
import com.aspose.pdf.PageCollection;
import com.aspose.pdf.devices.JpegDevice;
import com.aspose.words.License;
import com.aspose.words.SaveFormat;
import com.itextpdf.text.Document;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfWriter;
import org.apache.poi.hslf.usermodel.*;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.ArrayList;
import java.util.List;

public class PdfConverUtil {
    /**
     * @param inputStream  源文件输入流
     * @param outputStream PDF文件输入流
     * @return
     */
    public static boolean imgToPdf(InputStream inputStream, OutputStream outputStream) {
        Document document = null;
        try {
            //创建文档，三个字pdf页面的大小 A2-A9，个人觉得A3最合适
            document = new Document(PageSize.A3, 20, 20, 20, 20);
            //新建PDF文档，具体逻辑看.getInstance方法
            PdfWriter.getInstance(document, outputStream);

            document.open();
            document.newPage();

            //将文件流转换为字节流，便于格式转换
            BufferedInputStream bufferedInputStream = new BufferedInputStream(inputStream);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] bytes = new byte[1024];
            int length = 0;
            while (-1 != (length = bufferedInputStream.read(bytes))) {
                byteArrayOutputStream.write(bytes, 0, length);
            }
            //处理img图片
            Image image = Image.getInstance(byteArrayOutputStream.toByteArray());
            float height = image.getHeight();
            float width = image.getWidth();
            float percent = 0.0f;
            //设置像素或者长宽高将会影响图片的清晰度，因为只是对图片放大或缩小
            if (height > width) {
                //A4-A9
                percent = PageSize.A6.getHeight() / height * 100;
            } else {
                percent = PageSize.A6.getWidth() / width * 100;
            }
            image.setAlignment(Image.MIDDLE);
            image.scalePercent(percent);

            //将图片放入文档中，完成PDF转换
            document.add(image);
            System.out.println("image转换完毕");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            try {
                if (document != null) {
                    document.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    public static boolean wordToPdf(String wordPath, String pdfPath) {
        //验证License 若不验证则转化除的PDF文档会有水印产生
        if (!getLicense()) {
            return false;
        }
        FileInputStream inputStream = null;
        FileOutputStream outputStream = null;
        try {
            inputStream = new FileInputStream(wordPath);
            outputStream = new FileOutputStream(pdfPath);
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }

        try {
            com.aspose.words.Document doc = new com.aspose.words.Document(inputStream);
            //全面支持DOC DOCX OOXML RTF HTML OPENDOCUMENT PDF EPUB XPS SWF 相互转换
            doc.save(outputStream, SaveFormat.PDF);
            System.out.println("word转换完毕");
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return true;
    }

    // 官方文档的要求 无需理会
    public static boolean getLicense() {
        boolean result = false;
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            ByteArrayInputStream is = new ByteArrayInputStream(s.getBytes());
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static boolean excelToPdf(InputStream inputStream, OutputStream outputStream) {
        if (!getExeclLicense()) {
            return false;
        }
        try {
            Workbook workbook = new Workbook(inputStream);
            PdfSaveOptions pdfSaveOptions = new PdfSaveOptions();
            pdfSaveOptions.setOnePagePerSheet(false);
            int[] autoDrawSheets = {3};
            autoDraw(workbook, autoDrawSheets);
            int[] showSheets = {0};
            //隐藏workbook中不需要的sheet页
            printSheetPage(workbook, showSheets);
            workbook.save(outputStream, pdfSaveOptions);
            outputStream.flush();
            outputStream.close();
            System.out.println("excel转换完毕");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return true;
    }

    public static boolean getExeclLicense() {
        boolean result = false;
        try {
            String s = "<License><Data><Products><Product>Aspose.Total for Java</Product><Product>Aspose.Words for Java</Product></Products><EditionType>Enterprise</EditionType><SubscriptionExpiry>20991231</SubscriptionExpiry><LicenseExpiry>20991231</LicenseExpiry><SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber></Data><Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature></License>";
            ByteArrayInputStream is = new ByteArrayInputStream(s.getBytes());
            com.aspose.cells.License aposeLic = new com.aspose.cells.License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    //隐藏workbook中不需要的sheet页
    public static void printSheetPage(Workbook workbook, int[] page) {
        for (int i = 1; i < workbook.getWorksheets().getCount(); i++) {
            workbook.getWorksheets().get(i).setVisible(false);
        }
        if (null == page || page.length == 0) {
            workbook.getWorksheets().get(0).setVisible(true);
        } else {
            for (int i = 0; i < page.length; i++) {
                workbook.getWorksheets().get(i).setVisible(true);
            }
        }
    }

    //设置打印的sheet，自动拉伸比例
    public static void autoDraw(Workbook workbook, int[] page) {
        if (null != page && page.length > 0) {
            for (int i = 0; i < page.length; i++) {
                workbook.getWorksheets().get(i).getHorizontalPageBreaks().clear();
                workbook.getWorksheets().get(i).getVerticalPageBreaks().clear();
            }
        }
    }

    /**
     * pptToPdf
     *
     * @param inputStream
     * @param outputStream
     * @return
     */
    public static boolean pptToPdf(InputStream inputStream, OutputStream outputStream) {

        Document document = null;
        HSLFSlideShow hslfSlideShow = null;
        PdfWriter pdfWriter = null;

        try {
            hslfSlideShow = new HSLFSlideShow(inputStream);

            // 获取ppt文件页面
            Dimension dimension = hslfSlideShow.getPageSize();

            document = new Document();

            // pdfWriter实例
            pdfWriter = PdfWriter.getInstance(document, outputStream);

            document.open();

            PdfPTable pdfPTable = new PdfPTable(1);

            List<HSLFSlide> hslfSlideList = hslfSlideShow.getSlides();

            for (int i = 0; i < hslfSlideList.size(); i++) {
                HSLFSlide hslfSlide = hslfSlideList.get(i);
                // 设置字体, 解决中文乱码
                for (HSLFShape shape : hslfSlide.getShapes()) {
                    if (shape instanceof HSLFTextShape) {
                        HSLFTextShape textShape = (HSLFTextShape) shape;
                        for (HSLFTextParagraph textParagraph : textShape.getTextParagraphs()) {
                            for (HSLFTextRun textRun : textParagraph.getTextRuns()) {
                                textRun.setFontFamily("宋体");
                            }
                        }
                    }
                }
                BufferedImage bufferedImage = new BufferedImage((int) dimension.getWidth(), (int) dimension.getHeight(), BufferedImage.TYPE_INT_RGB);

                Graphics2D graphics2d = bufferedImage.createGraphics();

                graphics2d.setPaint(Color.white);
                graphics2d.setFont(new java.awt.Font("宋体", java.awt.Font.PLAIN, 12));

                hslfSlide.draw(graphics2d);

                graphics2d.dispose();

                Image image = Image.getInstance(bufferedImage, null);
                image.scalePercent(50f);

                // 写入单元格
                pdfPTable.addCell(new PdfPCell(image, true));
                document.add(image);

            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (document != null) {
                document.close();
            }
            if (pdfWriter != null) {
                pdfWriter.close();
            }
            if (outputStream != null) {
                try {
                    outputStream.flush();
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        System.out.println("ppt转换完毕");
        return true;
    }


    /**
     * pdf生成图片
     *
     * @param pdfPath
     * @param imgFullPathName
     */
    public static List<String> pdfToImg(String pdfPath, String imgFullPathName) {

        com.aspose.pdf.Document pdf = new com.aspose.pdf.Document(pdfPath);
        PageCollection pages = pdf.getPages();
        // pdf总页数
        int size = pages.size();
        /**
         * 图片宽度：800 图片高度：100 分辨率 960 Quality [0-100] 最大100 new JpegDevice(800, 1000,
         * resolution, 90);
         */
        JpegDevice jpegDevice = new JpegDevice(100);

        List<String> result = null;
        try {
            result = new ArrayList<>();
            if (size == 0) {
                return result;
            }
            for (int pageNum = 1; pageNum <= size; pageNum++) {
                // 输出路径
                FileOutputStream fileOs = null;
                try {
                    String pathTmp = imgFullPathName + "_" + pageNum + ".png";
                    File file = new File(pathTmp);
                    result.add(pathTmp);
                    if (!file.exists()) {
                        file.createNewFile();
                    }
                    fileOs = new FileOutputStream(file);
                    jpegDevice.process(pages.get_Item(pageNum), fileOs);
                } catch (IOException e) {
                    e.printStackTrace();
                } finally {
                    if (fileOs != null) {
                        try {
                            fileOs.flush();
                            fileOs.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
        } finally {
            pdf.close(); // 在所有循环结束后才关闭 PDF
        }
        return result;
    }
}