package cn.chinaunicom.sdsi.dingding.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.io.Serializable;

@Data
public class WeeklyBaxiangTotalExcelTask implements Serializable {

    @ColumnWidth(20)
    @ExcelProperty(value = "策略编号")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容水平居中
    private String marketingNumber;

    @ColumnWidth(40)
    @ExcelProperty(value = "策略名称")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容水平居中
    private String marketingName;

    @ColumnWidth(20)
    @ExcelProperty(value = "任务数")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER) // 内容水平居中
    private String taskNumber;

}
