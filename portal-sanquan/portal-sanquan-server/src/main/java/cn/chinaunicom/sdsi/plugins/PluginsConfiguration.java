package cn.chinaunicom.sdsi.plugins;

import cn.chinaunicom.sdsi.plugins.datasource.FrameDataSource;
import cn.chinaunicom.sdsi.plugins.office.excel.IExcelFileService;
import cn.chinaunicom.sdsi.plugins.office.excel.impl.ExcelFileService;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * TODO
 * 系统常用组件注册
 *
 * <AUTHOR>
 * @since 2024/8/16 19:07
 */
@Configuration
@AllArgsConstructor
public class PluginsConfiguration {

    private final FrameDataSource frameDataSource;

    @Bean
    public IExcelFileService excelFileService() {
        System.err.println("初始化,文件导出的---");
        return new ExcelFileService(frameDataSource);
    }
}
