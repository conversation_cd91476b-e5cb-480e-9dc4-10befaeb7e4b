package cn.chinaunicom.sdsi.private5GWorkflow.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 山东联通业务响应管理查询接口响应实体类
 */
@Data
public class GetYwxyDdrInfoByNoResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 系统编码，无需关注
     */
    private String insid;

    /**
     * 地市
     */
    private String city;

    /**
     * 工单编号
     */
    private String brNo;

    /**
     * 工单标题
     */
    private String brTitle;

    /**
     * 资源核查/业务申请详细描述
     */
    private String connment;

    /**
     * 要求反馈时间
     */
    private Long ftime;

    /**
     * 需求提交人
     */
    private String rqSubmitter;

    /**
     * 派单单位
     */
    private String pUnit;

    /**
     * 派单人
     */
    private String pPerson;

    /**
     * 所属调度功能
     */
    private String dSsgndd;

    /**
     * 移动用户数据调度内容描述
     */
    private String dYdyhsjddnrms;

    /**
     * 受理部门
     */
    private String dSlbmmc;

    /**
     * 受理人
     */
    private String dSlr;

    /**
     * 调度单省分审核人
     */
    private String dDddsfshr;

    /**
     * 流程记录数组
     */
    private List<FlowInfo> flows;

    /**
     * 流程记录信息内部类
     */
    @Data
    public static class FlowInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 任务开始时间
         */
        private String startDate;

        /**
         * 任务结束时间
         */
        private String endDate;

        /**
         * 步骤名称
         */
        private String step;

        /**
         * 处理人姓名
         */
        private String proName;

        /**
         * 处理人账号
         */
        private String proAccount;

        /**
         * 处理人电话
         */
        private String proTel;

        /**
         * 处理结果+审批意见
         */
        private String event;

        /**
         * 审批状态 0处理中，1已完成，2已删除, 3取消
         */
        private String status;
    }

    /**
     * 从JSON数据解析为实体对象
     *
     * @param jsonData JSON数据对象
     * @return 实体对象
     */
    public static GetYwxyDdrInfoByNoResponse fromJsonData(JSONObject jsonData) {
        GetYwxyDdrInfoByNoResponse response = new GetYwxyDdrInfoByNoResponse();
        try {
            if (jsonData != null) {
                // 解析基本信息
                response.setInsid(jsonData.getString("INSID"));
                response.setCity(jsonData.getString("CITY"));
                response.setBrNo(jsonData.getString("BR_NO"));
                response.setBrTitle(jsonData.getString("BR_TITLE"));
                response.setConnment(jsonData.getString("CONNMENT"));
                response.setFtime(jsonData.getLong("FTIME"));
                response.setRqSubmitter(jsonData.getString("RQ_SUBMITTER"));
                response.setPUnit(jsonData.getString("P_UNIT"));
                response.setPPerson(jsonData.getString("P_PERSON"));
                response.setDSsgndd(jsonData.getString("D_SSGNDD"));
                response.setDYdyhsjddnrms(jsonData.getString("D_YDYHSJDDNRMS"));
                response.setDSlbmmc(jsonData.getString("D_SLBMMC"));
                response.setDSlr(jsonData.getString("D_SLR"));
                response.setDDddsfshr(jsonData.getString("D_DDDSFSHR"));

                // 解析流程记录数组
                JSONArray flowsArray = jsonData.getJSONArray("FLOWS");
                if (flowsArray != null && flowsArray.size() > 0) {
                    List<FlowInfo> flowsList = new ArrayList<>();
                    for (int i = 0; i < flowsArray.size(); i++) {
                        JSONObject flowJson = flowsArray.getJSONObject(i);
                        FlowInfo flowInfo = new FlowInfo();
                        flowInfo.setStartDate(flowJson.getString("START_DATE"));
                        flowInfo.setEndDate(flowJson.getString("END_DATE"));
                        flowInfo.setStep(flowJson.getString("STEP"));
                        flowInfo.setProName(flowJson.getString("PRO_NAME"));
                        flowInfo.setProAccount(flowJson.getString("PRO_ACCOUNT"));
                        flowInfo.setProTel(flowJson.getString("PRO_TEL"));
                        flowInfo.setEvent(flowJson.getString("EVENT"));
                        flowInfo.setStatus(flowJson.getString("STATUS"));
                        flowsList.add(flowInfo);
                    }
                    response.setFlows(flowsList);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }
}
