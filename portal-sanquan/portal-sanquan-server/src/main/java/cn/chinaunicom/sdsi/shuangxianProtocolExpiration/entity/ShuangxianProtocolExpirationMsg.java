package cn.chinaunicom.sdsi.shuangxianProtocolExpiration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 双线纳入群组稽核 - 数据表实体类
 */
@Data
@TableName("t_sanquan_d_sx_zfdq")
public class ShuangxianProtocolExpirationMsg implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区域
     */
    @TableField("AREA")
    private String area;

    /**
     * 客户名称
     */
    @TableField("CUST_NAME")
    private String custName;


    /**
     * 城市名称
     */
    @TableField("CITY_NAME")
    private String cityName;

    /**
     * 营地名称
     */
    @TableField("CAMP_NAME")
    private String campName;

    /**
     * 用户ID
     */
    @TableField("USER_ID")
    private String userId;

    /**
     * 设备号码
     */
    @TableField("DEVICE_NUMBER")
    private String deviceNumber;

    /**
     * 产品ID
     */
    @TableField("PRODUCT_ID")
    private String productId;

    /**
     * 产品名称
     */
    @TableField("PRODUCT_NAME")
    private String productName;

    /**
     * 开始日期
     */
    @TableField("START_DATE")
    private String startDate;

    /**
     * 结束日期
     */
    @TableField("END_DATE")
    private String endDate;

    /**
     * 登录账号
     */
    @TableField("DEVELOPER_LOGIN_NO")
    private String developerLoginNo;

    /**
     * 登录名称
     */
    @TableField("DEVELOPER_NAME")
    private String developerName;

    /**
     * 管理员号码
     */
    @TableField("DEVELOPER_MOBILE")
    private String developerMobile;

    /**
     * 邮箱
     */
    @TableField("DEVELOPER_EMAIL")
    private String developerEmail;

    /**
     * 产品大类
     */
    @TableField("PROD_BIG_TYPE")
    private String prodBigType;

    /**
     * 产品类型
     */
    @TableField("PROD_TYPE")
    private String prodType;

    /**
     * 产品子类型
     */
    @TableField("PROD_TYPE_SUB")
    private String prodTypeSub;

    /**
     * CEO账号编码
     */
    @TableField("CEO_ACCOUNT_CODE")
    private String ceoAccountCode;

    /**
     * CEO员工姓名
     */
    @TableField("CEO_STAFF_NAME")
    private String ceoStaffName;

    /**
     * CEO手机号
     */
    @TableField("CEO_MOBILE")
    private String ceoMobile;

    /**
     * CEO邮箱
     */
    @TableField("CEO_EMAIL")
    private String ceoEmail;

    /**
     * 日ID
     */
    @TableField("DAY_ID")
    private String dayId;

    /**
     * 月ID
     */
    @TableField("MONTH_ID")
    private String monthId;
}