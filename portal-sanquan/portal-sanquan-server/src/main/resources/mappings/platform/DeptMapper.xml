<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.platform.department.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.platform.department.entity.Dept">
        <id column="id" property="id" />
        <result column="dept_name" property="deptName" />
        <result column="parent_id" property="parentId" />
        <result column="parent_name" property="parentName" />
        <result column="city" property="city" />
        <result column="county" property="county" />
        <result column="parent_id_list" property="parentIdList" />
        <result column="parent_name_list" property="parentNameList" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dept_name, parent_id, parent_name, city, county, parent_id_list, parent_name_list, deleted
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.platform.department.entity.Dept">
        select
            id, dept_name, parent_id, parent_name, city, county, parent_id_list, parent_name_list, deleted
        from t_sanquan_platform_dept
        <where>
            deleted='normal'
            <if test="query.city != null and query.city != ''">
                AND user_type = #{query.city}
            </if>
            <if test="query.county != null and query.county != ''">
                AND job_num = #{query.county}
            </if>
            <if test="query.deptName != null and query.deptName != ''">
                AND name like concat(concat("%",#{query.deptName}),"%")
            </if>
        </where>
    </select>
    <select id="findDept" resultType="cn.chinaunicom.sdsi.platform.department.entity.DeptVo">
        select * from t_sanquan_platform_dept
        <where>
            deleted='normal'
            <if test="deptParentId != null and deptParentId != ''">
                AND parent_id = #{deptParentId}
            </if>
        </where>
    </select>

</mapper>
