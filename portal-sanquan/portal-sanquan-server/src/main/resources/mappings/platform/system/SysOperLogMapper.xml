<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.platform.system.mapper.SysOperLogMapper">

	<resultMap type="cn.chinaunicom.sdsi.platform.service.entity.SysOperLog" id="SysOperLogResult">
		<id     property="operId"         column="oper_id"        />
		<result property="title"          column="title"          />
		<result property="businessType"   column="business_type"  />
		<result property="method"         column="method"         />
		<result property="requestMethod"  column="request_method" />
		<result property="operatorType"   column="operator_type"  />
		<result property="operName"       column="oper_name"      />
		<result property="deptName"       column="dept_name"      />
		<result property="operUrl"        column="oper_url"       />
		<result property="operIp"         column="oper_ip"        />
		<result property="operLocation"   column="oper_location"  />
		<result property="operParam"      column="oper_param"     />
		<result property="jsonResult"     column="json_result"    />
		<result property="status"         column="status"         />
		<result property="errorMsg"       column="error_msg"      />
		<result property="operTime"       column="oper_time"      />
		<result property="costTime"       column="cost_time"      />
		<result property="operOa"       column="oper_oa"      />
	</resultMap>

	<sql id="selectOperLogVo">
        select oper_id, title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time, cost_time, oper_oa
        from t_sanquan_platgorm_oper_log
    </sql>
    
	<insert id="insertOperlog" parameterType="cn.chinaunicom.sdsi.platform.service.entity.SysOperLog">
		insert into t_sanquan_platgorm_oper_log(title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, cost_time, oper_time, oper_oa)
        values (#{title}, #{businessType}, #{method}, #{requestMethod}, #{operatorType}, #{operName}, #{deptName}, #{operUrl}, #{operIp}, #{operLocation}, #{operParam}, #{jsonResult}, #{status}, #{errorMsg}, #{costTime}, sysdate(), #{operOa})
	</insert>
	

	
	<delete id="deleteOperLogByIds" parameterType="Long">
 		delete from t_sanquan_platgorm_oper_log where oper_id in
 		<foreach collection="array" item="operId" open="(" separator="," close=")">
 			#{operId}
        </foreach> 
 	</delete>
 	
 	<select id="selectOperLogById" parameterType="Long" resultMap="SysOperLogResult">
		<include refid="selectOperLogVo"/>
		where oper_id = #{operId}
	</select>
	<select id="findPage" resultType="cn.chinaunicom.sdsi.platform.service.entity.SysOperLog">
		<include refid="selectOperLogVo"/>
		<where>
			<if test="query.operIp != null and query.operIp != ''">
				AND oper_ip like concat('%', #{query.operIp}, '%')
			</if>
			<if test="query.title != null and query.title != ''">
				AND title like concat('%', #{query.title}, '%')
			</if>
			<if test="query.businessType != null">
				AND business_type = #{query.businessType}
			</if>
			<if test="query.status != null">
				AND status = #{query.status}
			</if>
			<if test="query.operName != null and query.operName != ''">
				AND oper_name like concat('%', #{query.operName}, '%')
			</if>
			<if test="query.beginTime != null and query.beginTime != ''">
				AND oper_time &gt;= #{query.beginTime}
			</if>
			<if test="query.operTime != null and query.operTime != ''">
				AND date_format(oper_time, '%Y-%m-%d') = #{query.operTime}
			</if>
		</where>
		order by oper_time desc, oper_id desc
	</select>
	<select id="selectOperLogList" resultType="cn.chinaunicom.sdsi.platform.service.entity.SysOperLog">
		<include refid="selectOperLogVo"/>
		<where>
			<if test="query.operIp != null and query.operIp != ''">
				AND oper_ip like concat('%', #{query.operIp}, '%')
			</if>
			<if test="query.title != null and query.title != ''">
				AND title like concat('%', #{query.title}, '%')
			</if>
			<if test="query.businessType != null">
				AND business_type = #{query.businessType}
			</if>
			<if test="query.status != null">
				AND status = #{query.status}
			</if>
			<if test="query.operName != null and query.operName != ''">
				AND oper_name like concat('%', #{query.operName}, '%')
			</if>
			<if test="query.beginTime != null and query.beginTime != ''">
				AND oper_time &gt;= #{query.beginTime}
			</if>
			<if test="query.operTime != null and query.operTime != ''">
				AND date_format(oper_time, '%Y-%m-%d') = #{query.operTime}
			</if>
		</where>
		order by oper_time desc, oper_id desc
	</select>
	<select id="findExcelList" resultType="cn.chinaunicom.sdsi.platform.system.vo.SysOperLogExcelVo">
		<include refid="selectOperLogVo"/>
		<where>
			<if test="query.operIp != null and query.operIp != ''">
				AND oper_ip like concat('%', #{query.operIp}, '%')
			</if>
			<if test="query.title != null and query.title != ''">
				AND title like concat('%', #{query.title}, '%')
			</if>
			<if test="query.businessType != null">
				AND business_type = #{query.businessType}
			</if>
			<if test="query.status != null">
				AND status = #{query.status}
			</if>
			<if test="query.operName != null and query.operName != ''">
				AND oper_name like concat('%', #{query.operName}, '%')
			</if>
			<if test="query.beginTime != null and query.beginTime != ''">
				AND oper_time &gt;= #{query.beginTime}
			</if>
			<if test="query.operTime != null and query.operTime != ''">
				AND date_format(oper_time, '%Y-%m-%d') = #{query.operTime}
			</if>
		</where>
		order by oper_time desc, oper_id desc
	</select>


	<update id="cleanOperLog">
        truncate table t_sanquan_platgorm_oper_log
    </update>

</mapper> 