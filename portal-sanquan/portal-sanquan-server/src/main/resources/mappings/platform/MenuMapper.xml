<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.platform.menu.mapper.CoreMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.platform.menu.entity.Menu">
        <id column="id" property="id" />
        <result column="path" property="path" />
        <result column="title" property="title" />
        <result column="icon" property="icon" />
        <result column="parent_id" property="parentId" />
        <result column="menu_load_file" property="menuLoadFile" />
        <result column="show_menu" property="showMenu" />
        <result column="menu_sort" property="menuSort" />
        <result column="menu_type" property="menuType" />
        <result column="menu_permission" property="menuPermission" />
        <result column="menu_system" property="menuSystem" />
    </resultMap>
    <sql id="sql_where">
        <where>
            <if test="query.title != null and query.title != ''">
                AND ${table_alias}title LIKE concat('%', #{query.title}, '%')
            </if>

            <if test="query.menuType != null and query.menuType != ''">
                AND ${table_alias}menu_type = #{query.menuType}
            </if>

            <if test="query.menuPermission != null and query.menuPermission != ''">
                AND ${table_alias}menu_permission LIKE concat('%', #{query.menuPermission}, '%')
            </if>
            <if test="query.showMenu != null and query.showMenu != ''">
                and   ${table_alias}show_menu = #{query.showMenu}
            </if>

            <if test="query.parentId != null and query.parentId != ''">
                AND ${table_alias}parent_Id  = #{query.parentId}
            </if>
            <if test="query.menuSystem != null and query.menuSystem != ''">
                AND ${table_alias}menu_system  = #{query.menuSystem}
            </if>
            <if test="query.menuSystem == null and query.menuSystem == ''">
                AND (
                ${table_alias}menu_system is null
                OR
                ${table_alias}menu_system = ''
                )
            </if>

            <if test="query.parentIdSet != null and query.parentIdSet != ''">
                AND FIND_IN_SET(#{query.parentIdSet},${table_alias}parent_id_set)
            </if>
        </where>
    </sql>
    
    <select id="findList" resultType="cn.chinaunicom.sdsi.platform.menu.entity.MenuVo">
        select * from t_sanquan_platform_menu t
        <include refid="sql_where"> <property name="table_alias" value="t."/></include>
    </select>

    <select id="findPage" resultType="cn.chinaunicom.sdsi.platform.menu.entity.MenuVo">
        select * from t_sanquan_platform_menu t
        <include refid="sql_where"> <property name="table_alias" value="t."/></include>
    </select>

    <select id="findByRoleId" resultType="cn.chinaunicom.sdsi.platform.menu.entity.MenuVo">
        select a.* from t_sanquan_platform_menu a,t_sanquan_platform_role_menu b
        <where>
            a.id=b.menu_id
            AND b.role_id = #{roleId}
        </where>
    </select>


    <select id="findByUserId" resultType="cn.chinaunicom.sdsi.platform.menu.entity.MenuVo">
            select * from ( select m.* from t_sanquan_platform_menu m ,(select a.parent_id from t_sanquan_platform_menu a,t_sanquan_platform_role_menu b,t_sanquan_platform_user_role c
                                                        WHERE  a.id=b.menu_id
                                                        AND b.role_id = c.role_id
                                                        and c.user_id=#{userId} GROUP BY parent_id) b where m.id=b.parent_id
                                                        UNION
                                                        select a.* from t_sanquan_platform_menu a,t_sanquan_platform_role_menu b,t_sanquan_platform_user_role c
                                                        WHERE  a.id=b.menu_id
                                                        AND b.role_id = c.role_id
                                                        and c.user_id=#{userId} GROUP BY a.id) menus
                     <where>
                         1=1
                         <if test="menuType != null and menuType != ''">
                             AND menus.menu_type  = #{menuType}
                         </if>
                     </where>
                     GROUP BY menus.id
    </select>

    <select id="findUserHasMenu"
            resultType="cn.chinaunicom.sdsi.platform.menu.entity.MenuVo">
        select menu.*
        from t_sanquan_platform_menu menu,
             (select parent_id
              from t_sanquan_platform_menu menu,
                   t_sanquan_platform_role_menu rm,
                   t_sanquan_platform_user_role ur
              where menu.id = rm.menu_id
                and rm.role_id = ur.role_id
                and ur.user_id = #{userId}
                and menu.menu_type='1'
                and menu.show_menu = '1'
              GROUP BY menu.parent_id) p_menu
        where p_menu.parent_id = menu.id
          and menu.menu_type = '1'
            <if test="query.menuSystem != null and query.menuSystem != ''">
                and menu.menu_system=#{query.menuSystem}
            </if>
            <if test="query.menuSystem == null or query.menuSystem == ''">
                and (menu.menu_system is null or menu.menu_system = '')
            </if>
        GROUP BY menu.id
            UNION
        select menu.*
        from t_sanquan_platform_menu menu,
             t_sanquan_platform_role_menu rm,
             t_sanquan_platform_user_role ur
        where menu.id = rm.menu_id
          and rm.role_id =  ur.role_id
          and ur.user_id = #{userId}
          and menu.menu_type = '1'
          and menu.show_menu = '1'
            <if test="query.menuSystem != null and query.menuSystem != ''">
                and menu.menu_system=#{query.menuSystem}
            </if>
            <if test="query.menuSystem == null or query.menuSystem == ''">
                and (menu.menu_system is null or menu.menu_system = '')
            </if>
        GROUP BY menu.id
    </select>

    <select id="userHasPermission"
            resultType="cn.chinaunicom.sdsi.platform.menu.entity.MenuVo">
        select menu.*
        from t_sanquan_platform_menu menu,
             t_sanquan_platform_role_menu rm,
             t_sanquan_platform_user_role ur
        where menu.id = rm.menu_id
          and rm.role_id = ur.role_id
          and ur.user_id = #{userId}
          and menu.menu_type = '2'
          and menu.show_menu = '1'
    </select>
    <select id="findMenusSystem" resultType="java.util.Map">
        select
            system_name as systemName,
            system_code as systemCode,
            system_path as systemPath
        from t_sanquan_platform_system_configure
    </select>
</mapper>
