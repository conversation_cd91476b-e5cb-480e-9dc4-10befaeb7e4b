<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.platform.user.mapper.UserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.platform.user.entity.UserRole">
        <id column="user_id" property="userId" />
        <result column="role_id" property="roleId" />
    </resultMap>

    <delete id="deleteByUserId">
        delete  from t_sanquan_platform_user_role where user_id=#{userId}
    </delete>
    <delete id="deleteByRoleId">
        delete  from t_sanquan_platform_user_role where role_id=#{roleId}
    </delete>
    <delete id="deleteByRoleIdAndUserId">
        delete  from t_sanquan_platform_user_role where role_id=#{roleId} and user_id=#{userId}
    </delete>
</mapper>
