<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.leaderVisit.custVisit.mapper.LeaderVisitCustMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity.LeaderVisitCust">
        <result column="visit_id" property="visitId" />
        <result column="parent_dept_charger" property="parentDeptCharger" />
        <result column="parent_dept_charger_oa" property="parentDeptChargerOa" />
        <result column="comp_leader" property="compLeader" />
        <result column="comp_leader_oa" property="compLeaderOa" />
        <result column="status" property="status" />
        <result column="update_time" property="updateTime" />
        <result column="day_id" property="dayId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        visit_id, parent_dept_charger, parent_dept_charger_oa, comp_leader, comp_leader_oa, status, update_time, day_id
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity.LeaderVisitCust">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_leader_visit_cust
        <where>
            <if test="query.visitId != null and query.visitId != ''">
                and visit_id = #{query.visitId}
            </if>
            <if test="query.parentDeptCharger != null and query.parentDeptCharger != ''">
                and parent_dept_charger = #{query.parentDeptCharger}
            </if>
            <if test="query.parentDeptChargerOa != null and query.parentDeptChargerOa != ''">
                and parent_dept_charger_oa = #{query.parentDeptChargerOa}
            </if>
            <if test="query.compLeader != null and query.compLeader != ''">
                and comp_leader = #{query.compLeader}
            </if>
            <if test="query.compLeaderOa != null and query.compLeaderOa != ''">
                and comp_leader_oa = #{query.compLeaderOa}
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
            <if test="query.updateTime != null">
                and update_time = #{query.updateTime}
            </if>
            <if test="query.dayId != null and query.dayId != ''">
                and day_id = #{query.dayId}
            </if>
        </where>
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.leaderVisit.custVisit.entity.LeaderVisitCust">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_leader_visit_cust
        <where>
            <if test="query.visitId != null and query.visitId != ''">
                and visit_id = #{query.visitId}
            </if>
            <if test="query.parentDeptCharger != null and query.parentDeptCharger != ''">
                and parent_dept_charger = #{query.parentDeptCharger}
            </if>
            <if test="query.parentDeptChargerOa != null and query.parentDeptChargerOa != ''">
                and parent_dept_charger_oa = #{query.parentDeptChargerOa}
            </if>
            <if test="query.compLeader != null and query.compLeader != ''">
                and comp_leader = #{query.compLeader}
            </if>
            <if test="query.compLeaderOa != null and query.compLeaderOa != ''">
                and comp_leader_oa = #{query.compLeaderOa}
            </if>
            <if test="query.status != null and query.status != ''">
                and status = #{query.status}
            </if>
            <if test="query.updateTime != null">
                and update_time = #{query.updateTime}
            </if>
            <if test="query.dayId != null and query.dayId != ''">
                and day_id = #{query.dayId}
            </if>
        </where>

    </select>

</mapper>
