<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.leaderVisit.leaderVisit.mapper.ZqLeaderVisitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.entity.ZqLeaderVisit">
        <result column="visit_id" property="visitId" />
        <result column="city" property="city" />
        <result column="sign_in_place" property="signInPlace" />
        <result column="visit_type" property="visitType" />
        <result column="visit_time" property="visitTime" />
        <result column="nature_cust_id" property="natureCustId" />
        <result column="nature_cust_name" property="natureCustName" />
        <result column="visit_summary" property="visitSummary" />
        <result column="interview_id" property="interviewId" />
        <result column="interview_content" property="interviewContent" />
        <result column="manager_name" property="managerName" />
        <result column="manager_oa" property="managerOa" />
        <result column="comp_leader" property="compLeader" />
        <result column="comp_leader_oa" property="compLeaderOa" />
        <result column="day_id" property="dayId" />
        <result column="manager_telphone" property="managerTelphone" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        visit_id, city, sign_in_place, visit_type, visit_time, nature_cust_id, nature_cust_name, visit_summary, interview_id, interview_content, manager_name, manager_oa, comp_leader, comp_leader_oa, day_id
            ,manager_telphone
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.entity.ZqLeaderVisit">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_zq_leader_visit
        <where>
            <if test="query.visitId != null and query.visitId != ''">
                and visit_id = #{query.visitId}
            </if>
            <if test="query.city != null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.signInPlace != null and query.signInPlace != ''">
                and sign_in_place = #{query.signInPlace}
            </if>
            <if test="query.visitType != null and query.visitType != ''">
                and visit_type = #{query.visitType}
            </if>
            <if test="query.visitTime != null">
                and visit_time = #{query.visitTime}
            </if>
            <if test="query.natureCustId != null and query.natureCustId != ''">
                and nature_cust_id = #{query.natureCustId}
            </if>
            <if test="query.natureCustName != null and query.natureCustName != ''">
                and nature_cust_name concat ('%',#{query.natureCustName},'%')
            </if>
            <if test="query.visitSummary != null and query.visitSummary != ''">
                and visit_summary = #{query.visitSummary}
            </if>
            <if test="query.interviewId != null and query.interviewId != ''">
                and interview_id = #{query.interviewId}
            </if>
            <if test="query.interviewContent != null and query.interviewContent != ''">
                and interview_content = #{query.interviewContent}
            </if>
            <if test="query.managerName != null and query.managerName != ''">
                and manager_name = #{query.managerName}
            </if>
            <if test="query.managerOa != null and query.managerOa != ''">
                and manager_oa = #{query.managerOa}
            </if>
            <if test="query.compLeader != null and query.compLeader != ''">
                and comp_leader = #{query.compLeader}
            </if>
            <if test="query.compLeaderOa != null and query.compLeaderOa != ''">
                and comp_leader_oa = #{query.compLeaderOa}
            </if>
            <if test="query.dayId != null and query.dayId != ''">
                and day_id = #{query.dayId}
            </if>
        </where>
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.leaderVisit.leaderVisit.entity.ZqLeaderVisit">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_zq_leader_visit
        <where>
            <if test="query.visitId != null and query.visitId != ''">
                and visit_id = #{query.visitId}
            </if>
            <if test="query.city != null and query.city != ''">
                and city = #{query.city}
            </if>
            <if test="query.signInPlace != null and query.signInPlace != ''">
                and sign_in_place = #{query.signInPlace}
            </if>
            <if test="query.visitType != null and query.visitType != ''">
                and visit_type = #{query.visitType}
            </if>
            <if test="query.visitTime != null">
                and visit_time = #{query.visitTime}
            </if>
            <if test="query.natureCustId != null and query.natureCustId != ''">
                and nature_cust_id = #{query.natureCustId}
            </if>
            <if test="query.natureCustName != null and query.natureCustName != ''">
                and nature_cust_name = #{query.natureCustName}
            </if>
            <if test="query.visitSummary != null and query.visitSummary != ''">
                and visit_summary = #{query.visitSummary}
            </if>
            <if test="query.interviewId != null and query.interviewId != ''">
                and interview_id = #{query.interviewId}
            </if>
            <if test="query.interviewContent != null and query.interviewContent != ''">
                and interview_content = #{query.interviewContent}
            </if>
            <if test="query.managerName != null and query.managerName != ''">
                and manager_name = #{query.managerName}
            </if>
            <if test="query.managerOa != null and query.managerOa != ''">
                and manager_oa = #{query.managerOa}
            </if>
            <if test="query.compLeader != null and query.compLeader != ''">
                and comp_leader = #{query.compLeader}
            </if>
            <if test="query.compLeaderOa != null and query.compLeaderOa != ''">
                and comp_leader_oa = #{query.compLeaderOa}
            </if>
            <if test="query.dayId != null and query.dayId != ''">
                and day_id = #{query.dayId}
            </if>
        </where>
    </select>

</mapper>
