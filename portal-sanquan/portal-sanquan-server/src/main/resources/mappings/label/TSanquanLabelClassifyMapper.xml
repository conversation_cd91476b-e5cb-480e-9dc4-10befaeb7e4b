<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.label.dao.TSanquanLabelClassifyMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.label.entity.TSanquanLabelClassify" id="tSanquanLabelClassifyMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="label" column="label"/>
        <result property="parentId" column="parent_id"/>
        <result property="parentCode" column="parent_code"/>
        <result property="parentLabel" column="parent_label"/>
        <result property="createDate" column="create_date"/>
        <result property="createBy" column="create_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="updateBy" column="update_by"/>
        <result property="versions" column="versions"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>

    <select id="selectListByParentId"
            resultType="cn.chinaunicom.sdsi.cloud.label.entity.TSanquanLabelClassify">
        select id, code, label, parent_id, parent_code, parent_label, create_date, update_date
        from t_sanquan_label_classify
        <where>
            delete_flag = 'normal'
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
        </where>
        order by id
    </select>
    <select id="selectTopList" resultType="cn.chinaunicom.sdsi.cloud.label.vo.TSanquanLabelClassifyTreeVO">
        select id, code, label, parent_id, parent_code, parent_label, create_date, update_date
        from t_sanquan_label_classify where delete_flag = 'normal' and (parent_id is null or parent_id = '')
    </select>
    <select id="findPageListByParentId"
            resultType="cn.chinaunicom.sdsi.cloud.label.entity.TSanquanLabelClassify">
        select * from t_sanquan_label_classify
        <where>
            delete_flag = 'normal'
            <if test="query.id != null">
               and id = #{query.id}
            </if>
            <if test="query.parentId != null">
                and parent_id = #{query.parentId}
            </if>
            <if test="query.label != null and query.label != ''">
                AND label LIKE concat('%', #{query.label}, '%')
            </if>
            <if test="query.code != null and query.code != ''">
                AND code LIKE concat('%', #{query.code}, '%')
            </if>
        </where>
    </select>
    <select id="getAll" resultType="cn.chinaunicom.sdsi.cloud.label.vo.TSanquanLabelClassifyTreeVO">
        select id, code, label, parent_id, parent_code, parent_label, create_date, update_date
        from t_sanquan_label_classify where delete_flag = 'normal'
    </select>
    <select id="findById" resultType="cn.chinaunicom.sdsi.cloud.label.entity.TSanquanLabelClassify">
        select id, code, label, parent_id, parent_code, parent_label
        from t_sanquan_label_classify where delete_flag = 'normal' and id = #{id}
    </select>

</mapper>