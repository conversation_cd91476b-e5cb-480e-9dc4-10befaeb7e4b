<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.score.evaluate.mapper.EvaluateMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, evaluate_name, evaluate_code, mobo, creator, create_time
        ,deleted
    </sql>
    <sql id="WHERE_ALL">
        <if test="query.evaluateName != null and query.evaluateName.trim()!=''">
            and evaluate_name = #{query.evaluateName}
        </if>
        <if test="query.evaluateCode!= null and query.evaluateCode.trim()!=''">
            and evaluate_name = #{query.evaluateCode}
        </if>
        <if test="query.deleted != null and query.deleted.trim()!='' ">
            and deleted = #{query.deleted}
        </if>
    </sql>

    <!--分页查询-->
    <select id="findPage"
            parameterType="cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateQuery"
            resultType="cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateVo">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_evaluate
        <where>
            1>0
            <include refid="WHERE_ALL"/>
        </where>
    </select>
    <!--查询数组-->
    <select id="findList"
            parameterType="cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateQuery"
            resultType="cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateVo">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_evaluate
        <where>
            1>0
            <include refid="WHERE_ALL"/>
        </where>
    </select>

    <select id="findListByDimensionId"
            resultType="cn.chinaunicom.sdsi.score.demension.entity.ScoreDimensionEvaluate">
        select
        b.*
        from t_sanquan_evaluate a,t_sanquan_score_dimension_evaluate b
        <where>
            a.id=b.evaluate_id
            and b.dimension_id=#{dimensionId}
        </where>
    </select>

    <!--根据主键查询-->
    <select id="findOne"
            resultType="cn.chinaunicom.sdsi.score.evaluate.entity.EvaluateVo">
        select
        <include refid="Base_Column_List"/>
        from t_sanquan_evaluate
        <where>
            id = #{id}
        </where>
    </select>
</mapper>
