<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.nature_customer.mapper.AddCustomerTagMapper">
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO">
        select *
        from t_sanquan_customer_tag
        <where>
            1=1
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name = #{query.customerName}
            </if>
        </where>
    </select>

    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO">
        select *
        from t_sanquan_customer_tag
        <where>
            1=1
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name = #{query.customerName}
            </if>
        </where>
    </select>
    <select id="findLabelNameByCustomer"
            resultType="java.lang.String">
        select *
        from t_sanquan_customer_tag
        <where>
            1=1
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name = #{query.customerName}
            </if>
        </where>
    </select>
    <select id="findOneById"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO">
        select *
        from t_sanquan_customer_tag
        <where>
            1=1
            and id= #{query.id}
        </where>
    </select>
    <delete id="deleteByRosterCustomerId">
        delete
        from t_sanquan_customer_tag
        <where>
            1=1
            and roster_customer_id in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </delete>

    <select id="findBigTable"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO">
        select a.customer_id,b.db_table_name as 'dbTable',a.label_code
        from t_sanquan_customer_tag a,
             t_sanquan_tag_db b
        where a.update_date = #{date}
          and a.tag_type='db'
        and a.label_code=b.tag_id
        GROUP BY b.db_table_name
    </select>

    <select id="findBigTableColumn"
            resultType="cn.chinaunicom.sdsi.tag.entity.TagDbVo">
        select a.*
        from t_sanquan_tag_db a,t_sanquan_tag b
        where  a.tag_id=b.id and b.delete_flag='normal' and a.db_table_name = #{dbTableName}
        GROUP BY a.db_table_column
    </select>

    <select id="findDictBigColumn"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO">

        select * from t_sanquan_customer_tag where update_date=#{date} and tag_type='dict' GROUP BY customer_id,big_column
    </select>

    <select id="findDictBigColumnDetail"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.AddCustomerTagVO">

        select *
        from t_sanquan_customer_tag
        where update_date = #{date}
          and (db_table = '' or db_column = '')
          and customer_id = #{customerId}
          and big_column = #{bigColumn}
    </select>
    <select id="selectCustomerLabelNum" resultType="java.lang.Integer">
        select count(*)
        from t_sanquan_customer_tag
        <where>
            1=1
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name = #{query.customerName}
            </if>
        </where>
    </select>
    <select id="findProductBigTable" resultType="cn.chinaunicom.sdsi.cloud.product.vo.AddProductMarkVo">
        select a.product_id,b.db_table_name as 'dbTable',a.mark_data_id
        from t_sanquan_product_mark a,
             t_sanquan_tag_db b
        where DATE(a.create_date) = #{date}
          and a.tag_type='db'
          and a.mark_data_id=b.tag_id
        GROUP BY b.db_table_name
    </select>

</mapper>
