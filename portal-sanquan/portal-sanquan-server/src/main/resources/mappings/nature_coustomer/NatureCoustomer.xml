<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.nature_customer.mapper.NatureCoustomerMapper">
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO">
        select *
        from t_sanquan_d_roster_nature_customer_info
        <where>
            1=1
            <if test="query.naturalCustomerName != null and query.naturalCustomerName != ''">
                AND natural_customer_name = #{query.naturalCustomerName}
            </if>
            <if test="query.listCustomerCity != null and query.listCustomerCity != ''">
                AND list_customer_city = #{query.listCustomerCity}
            </if>
            <if test="query.provinceIndustry != null and query.provinceIndustry != ''">
                AND province_industry = #{query.provinceIndustry}
            </if>
            <if test="query.headquartersIndustry != null and query.headquartersIndustry != ''">
                AND headquarters_industry = #{query.headquartersIndustry}
            </if>
        </where>
    </select>

    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO">
        select * from (
            select a.customer_id, a.roster_customer_id, a.natural_customer_name, a.org_type, a.province_industry,
            a.customer_manager_name, a.list_customer_city, '自然客户' as customerType
            from t_sanquan_d_roster_nature_customer_info a
            <where>
                1=1
                <if test="query.naturalCustomerName != null and query.naturalCustomerName != ''">
                    AND a.natural_customer_name LIKE concat('%', #{query.naturalCustomerName}, '%')
                </if>
                <if test="query.listCustomerCity != null and query.listCustomerCity != ''">
                    AND a.list_customer_city LIKE concat('%', #{query.listCustomerCity}, '%')
                </if>
                <if test="query.provinceIndustry != null and query.provinceIndustry != ''">
                    AND a.province_industry = #{query.provinceIndustry}
                </if>
            </where>

            union all

            select b.customer_id, b.roster_customer_id, b.customer_name as naturalCustomerName, b.subdivision as org_type,
            b.industry as province_industry, b.customer_manager_name, b.list_customer_city,
            '派出所' as customerType
            from t_sanquan_customer_info b
            <where>
                1=1
                and delete_flag = 'normal'
                <if test="query.naturalCustomerName != null and query.naturalCustomerName != ''">
                    AND b.customer_name LIKE concat('%', #{query.naturalCustomerName}, '%')
                </if>
                <if test="query.listCustomerCity != null and query.listCustomerCity != ''">
                    AND b.list_customer_city  LIKE concat('%', #{query.listCustomerCity}, '%')
                </if>
                <if test="query.provinceIndustry != null and query.provinceIndustry != ''">
                    AND b.industry = #{query.provinceIndustry}
                </if>
            </where>
                      ) z
        <where>
            <if test="query.customerType != null and query.customerType != ''">
                AND z.customerType = #{query.customerType}
            </if>
        </where>
    </select>

    <select id="findOneById"
            resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.NatureCoustomerVO">
        select *
        from t_sanquan_d_roster_nature_customer_info
        <where>
            1=1
            and roster_customer_id= #{query.rosterCustomerId}
        </where>
    </select>


    <select id="findCityByCityName"
            resultType="java.lang.String">
        select list_customer_city as label
        from t_sanquan_d_roster_nature_customer_info
        <where>
            1=1
            and list_customer_city is not null
        </where>
        group by list_customer_city
    </select>
    <select id="findCustomerViewInfoList" resultType="cn.chinaunicom.sdsi.cloud.nature_customer.vo.CustomerViewVO">
        select roster_customer_id, roster_customer_name,natural_customer_id,customer_type
        from t_sanquan_customer_view v
    </select>
</mapper>
