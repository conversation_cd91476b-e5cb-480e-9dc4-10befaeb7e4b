<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.targetMarketing.mapper.TSanquanDZqztGemWorkOrderFeedbackInfoMapper">
    
    <resultMap type="cn.chinaunicom.sdsi.targetMarketing.entity.TSanquanDZqztGemWorkOrderFeedbackInfo" id="TSanquanDZqztGemWorkOrderFeedbackInfoResult">
        <result property="ID"    column="ID"    />
        <result property="workOrderCode"    column="WORK_ORDER_CODE"    />
        <result property="touchId"    column="TOUCH_ID"    />
        <result property="visitId"    column="VISIT_ID"    />
        <result property="userCode"    column="USER_CODE"    />
        <result property="visitResult"    column="VISIT_RESULT"    />
        <result property="backTime"    column="BACK_TIME"    />
        <result property="isContinueVisit"    column="IS_CONTINUE_VISIT"    />
        <result property="noVisitReason"    column="NO_VISIT_REASON"    />
        <result property="nextTime"    column="NEXT_TIME"    />
        <result property="visitTheme"    column="VISIT_THEME"    />
        <result property="visitWay"    column="VISIT_WAY"    />
        <result property="visitTime"    column="VISIT_TIME"    />
        <result property="visitSite"    column="VISIT_SITE"    />
        <result property="visitPerson"    column="VISIT_PERSON"    />
        <result property="visitPhone"    column="VISIT_PHONE"    />
        <result property="visitContent"    column="VISIT_CONTENT"    />
        <result property="visitSummarize"    column="VISIT_SUMMARIZE"    />
        <result property="keyRecommendedProduct"    column="KEY_RECOMMENDED_PRODUCT"    />
        <result property="isBusiOppWilling"    column="IS_BUSI_OPP_WILLING"    />
        <result property="isNeedSupport"    column="IS_NEED_SUPPORT"    />
        <result property="busiOppt"    column="BUSI_OPPT"    />
        <result property="busiOppType"    column="BUSI_OPP_TYPE"    />
        <result property="busiOpptName"    column="BUSI_OPPT_NAME"    />
        <result property="isInsertDiff"    column="IS_INSERT_DIFF"    />
        <result property="OPERATOR"    column="OPERATOR"    />
        <result property="businessType"    column="BUSINESS_TYPE"    />
        <result property="endDate"    column="END_DATE"    />
        <result property="diffBusinessInfo"    column="DIFF_BUSINESS_INFO"    />
        <result property="pictureUrls"    column="PICTURE_URLS"    />
        <result property="customerWillingness"    column="CUSTOMER_WILLINGNESS"    />
        <result property="isRenewal"    column="IS_RENEWAL"    />
        <result property="estimatedRecoveryTime"    column="ESTIMATED_RECOVERY_TIME"    />
        <result property="isPaymentSuccess"    column="IS_PAYMENT_SUCCESS"    />
        <result property="isWriteOff"    column="IS_WRITE_OFF"    />
        <result property="isRenewalSuccess"    column="IS_RENEWAL_SUCCESS"    />
        <result property="renewalContent"    column="RENEWAL_CONTENT"    />
        <result property="isOffLineTendency"    column="IS_OFF_LINE_TENDENCY"    />
        <result property="customerWillingnessForBusiness"    column="CUSTOMER_WILLINGNESS_FOR_BUSINESS"    />
        <result property="isCompleteRenewalForBusiness"    column="IS_COMPLETE_RENEWAL_FOR_BUSINESS"    />
        <result property="isOffLineTendencyForIncome"    column="IS_OFF_LINE_TENDENCY_FOR_INCOME"    />
        <result property="zeroProductionReason"    column="ZERO_PRODUCTION_REASON"    />
        <result property="nextStep"    column="NEXT_STEP"    />
        <result property="isBuildingCustomer"    column="IS_BUILDING_CUSTOMER"    />
        <result property="isModifyCustomerCheckInfo"    column="IS_MODIFY_CUSTOMER_CHECK_INFO"    />
        <result property="isVisitId"    column="IS_VISIT_ID"    />
        <result property="isValid"    column="IS_VALID"    />
        <result property="invalidReason"    column="INVALID_REASON"    />
        <result property="signInPlace"    column="SIGN_IN_PLACE"    />
        <result property="signInStartTime"    column="SIGN_IN_START_TIME"    />
        <result property="signInEndTime"    column="SIGN_IN_END_TIME"    />
        <result property="extraInfoLink"    column="EXTRA_INFO_LINK"    />
        <result property="gmtCreate"    column="GMT_CREATE"    />
        <result property="createUserId"    column="CREATE_USER_ID"    />
        <result property="createUserName"    column="CREATE_USER_NAME"    />
        <result property="gmtModified"    column="GMT_MODIFIED"    />
        <result property="updateUserName"    column="UPDATE_USER_NAME"    />
        <result property="updateUserId"    column="UPDATE_USER_ID"    />
        <result property="isDelete"    column="IS_DELETE"    />
        <result property="deleteTime"    column="DELETE_TIME"    />
        <result property="monthId"    column="MONTH_ID"    />
        <result property="dayId"    column="DAY_ID"    />
    </resultMap>

    <sql id="Base_Column_List">
 ID, WORK_ORDER_CODE, TOUCH_ID, VISIT_ID, USER_CODE, VISIT_RESULT, BACK_TIME, IS_CONTINUE_VISIT, NO_VISIT_REASON, NEXT_TIME, VISIT_THEME, VISIT_WAY, VISIT_TIME, VISIT_SITE, VISIT_PERSON, VISIT_PHONE, VISIT_CONTENT, VISIT_SUMMARIZE, KEY_RECOMMENDED_PRODUCT, IS_BUSI_OPP_WILLING, IS_NEED_SUPPORT, BUSI_OPPT, BUSI_OPP_TYPE, BUSI_OPPT_NAME, IS_INSERT_DIFF, OPERATOR, BUSINESS_TYPE, END_DATE, DIFF_BUSINESS_INFO, PICTURE_URLS, CUSTOMER_WILLINGNESS, IS_RENEWAL, ESTIMATED_RECOVERY_TIME, IS_PAYMENT_SUCCESS, IS_WRITE_OFF, IS_RENEWAL_SUCCESS, RENEWAL_CONTENT, IS_OFF_LINE_TENDENCY, CUSTOMER_WILLINGNESS_FOR_BUSINESS, IS_COMPLETE_RENEWAL_FOR_BUSINESS, IS_OFF_LINE_TENDENCY_FOR_INCOME, ZERO_PRODUCTION_REASON, NEXT_STEP, IS_BUILDING_CUSTOMER, IS_MODIFY_CUSTOMER_CHECK_INFO, IS_VISIT_ID, IS_VALID, INVALID_REASON, SIGN_IN_PLACE, SIGN_IN_START_TIME, SIGN_IN_END_TIME, EXTRA_INFO_LINK, GMT_CREATE, CREATE_USER_ID, CREATE_USER_NAME, GMT_MODIFIED, UPDATE_USER_NAME, UPDATE_USER_ID, IS_DELETE, DELETE_TIME, MONTH_ID, DAY_ID    </sql>

    <select id="findPage" resultType="cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztGemWorkOrderFeedbackInfoVO">
        SELECT ID, WORK_ORDER_CODE, TOUCH_ID, VISIT_ID, USER_CODE, VISIT_RESULT, BACK_TIME, IS_CONTINUE_VISIT, NO_VISIT_REASON, NEXT_TIME, VISIT_THEME, VISIT_WAY, VISIT_TIME, VISIT_SITE, VISIT_PERSON, VISIT_PHONE, VISIT_CONTENT, VISIT_SUMMARIZE, KEY_RECOMMENDED_PRODUCT, IS_BUSI_OPP_WILLING, IS_NEED_SUPPORT, BUSI_OPPT, BUSI_OPP_TYPE, BUSI_OPPT_NAME, IS_INSERT_DIFF, OPERATOR, BUSINESS_TYPE, END_DATE, DIFF_BUSINESS_INFO, PICTURE_URLS, CUSTOMER_WILLINGNESS, IS_RENEWAL, ESTIMATED_RECOVERY_TIME, IS_PAYMENT_SUCCESS, IS_WRITE_OFF, IS_RENEWAL_SUCCESS, RENEWAL_CONTENT, IS_OFF_LINE_TENDENCY, CUSTOMER_WILLINGNESS_FOR_BUSINESS, IS_COMPLETE_RENEWAL_FOR_BUSINESS, IS_OFF_LINE_TENDENCY_FOR_INCOME, ZERO_PRODUCTION_REASON, NEXT_STEP, IS_BUILDING_CUSTOMER, IS_MODIFY_CUSTOMER_CHECK_INFO, IS_VISIT_ID, IS_VALID, INVALID_REASON, SIGN_IN_PLACE, SIGN_IN_START_TIME, SIGN_IN_END_TIME, EXTRA_INFO_LINK, GMT_CREATE, CREATE_USER_ID, CREATE_USER_NAME, GMT_MODIFIED, UPDATE_USER_NAME, UPDATE_USER_ID, IS_DELETE, DELETE_TIME, MONTH_ID, DAY_ID
        FROM t_sanquan_d_zqzt_gem_work_order_feedback_info
        <where>
            delete_flag = 'normal'
            <if test="query.ID != null  and query.ID !=''">
                AND ID = #{query.ID}
            </if>
            <if test="query.workOrderCode != null  and query.workOrderCode !=''">
                AND WORK_ORDER_CODE = #{query.workOrderCode}
            </if>
            <if test="query.touchId != null  and query.touchId !=''">
                AND TOUCH_ID = #{query.touchId}
            </if>
            <if test="query.visitId != null  and query.visitId !=''">
                AND VISIT_ID = #{query.visitId}
            </if>
            <if test="query.userCode != null  and query.userCode !=''">
                AND USER_CODE = #{query.userCode}
            </if>
            <if test="query.visitResult != null  and query.visitResult !=''">
                AND VISIT_RESULT = #{query.visitResult}
            </if>
            <if test="query.backTime != null  and query.backTime !=''">
                AND BACK_TIME = #{query.backTime}
            </if>
            <if test="query.isContinueVisit != null  and query.isContinueVisit !=''">
                AND IS_CONTINUE_VISIT = #{query.isContinueVisit}
            </if>
            <if test="query.noVisitReason != null  and query.noVisitReason !=''">
                AND NO_VISIT_REASON = #{query.noVisitReason}
            </if>
            <if test="query.nextTime != null  and query.nextTime !=''">
                AND NEXT_TIME = #{query.nextTime}
            </if>
            <if test="query.visitTheme != null  and query.visitTheme !=''">
                AND VISIT_THEME = #{query.visitTheme}
            </if>
            <if test="query.visitWay != null  and query.visitWay !=''">
                AND VISIT_WAY = #{query.visitWay}
            </if>
            <if test="query.visitTime != null  and query.visitTime !=''">
                AND VISIT_TIME = #{query.visitTime}
            </if>
            <if test="query.visitSite != null  and query.visitSite !=''">
                AND VISIT_SITE = #{query.visitSite}
            </if>
            <if test="query.visitPerson != null  and query.visitPerson !=''">
                AND VISIT_PERSON = #{query.visitPerson}
            </if>
            <if test="query.visitPhone != null  and query.visitPhone !=''">
                AND VISIT_PHONE = #{query.visitPhone}
            </if>
            <if test="query.visitContent != null  and query.visitContent !=''">
                AND VISIT_CONTENT = #{query.visitContent}
            </if>
            <if test="query.visitSummarize != null  and query.visitSummarize !=''">
                AND VISIT_SUMMARIZE = #{query.visitSummarize}
            </if>
            <if test="query.keyRecommendedProduct != null  and query.keyRecommendedProduct !=''">
                AND KEY_RECOMMENDED_PRODUCT = #{query.keyRecommendedProduct}
            </if>
            <if test="query.isBusiOppWilling != null  and query.isBusiOppWilling !=''">
                AND IS_BUSI_OPP_WILLING = #{query.isBusiOppWilling}
            </if>
            <if test="query.isNeedSupport != null  and query.isNeedSupport !=''">
                AND IS_NEED_SUPPORT = #{query.isNeedSupport}
            </if>
            <if test="query.busiOppt != null  and query.busiOppt !=''">
                AND BUSI_OPPT = #{query.busiOppt}
            </if>
            <if test="query.busiOppType != null  and query.busiOppType !=''">
                AND BUSI_OPP_TYPE = #{query.busiOppType}
            </if>
            <if test="query.busiOpptName != null  and query.busiOpptName != ''">
                AND BUSI_OPPT_NAME LIKE concat('%', #{query.busiOpptName}, '%')
            </if>
            <if test="query.isInsertDiff != null  and query.isInsertDiff !=''">
                AND IS_INSERT_DIFF = #{query.isInsertDiff}
            </if>
            <if test="query.OPERATOR != null  and query.OPERATOR !=''">
                AND OPERATOR = #{query.OPERATOR}
            </if>
            <if test="query.businessType != null  and query.businessType !=''">
                AND BUSINESS_TYPE = #{query.businessType}
            </if>
            <if test="query.endDate != null  and query.endDate !=''">
                AND END_DATE = #{query.endDate}
            </if>
            <if test="query.diffBusinessInfo != null  and query.diffBusinessInfo !=''">
                AND DIFF_BUSINESS_INFO = #{query.diffBusinessInfo}
            </if>
            <if test="query.pictureUrls != null  and query.pictureUrls !=''">
                AND PICTURE_URLS = #{query.pictureUrls}
            </if>
            <if test="query.customerWillingness != null  and query.customerWillingness !=''">
                AND CUSTOMER_WILLINGNESS = #{query.customerWillingness}
            </if>
            <if test="query.isRenewal != null  and query.isRenewal !=''">
                AND IS_RENEWAL = #{query.isRenewal}
            </if>
            <if test="query.estimatedRecoveryTime != null  and query.estimatedRecoveryTime !=''">
                AND ESTIMATED_RECOVERY_TIME = #{query.estimatedRecoveryTime}
            </if>
            <if test="query.isPaymentSuccess != null  and query.isPaymentSuccess !=''">
                AND IS_PAYMENT_SUCCESS = #{query.isPaymentSuccess}
            </if>
            <if test="query.isWriteOff != null  and query.isWriteOff !=''">
                AND IS_WRITE_OFF = #{query.isWriteOff}
            </if>
            <if test="query.isRenewalSuccess != null  and query.isRenewalSuccess !=''">
                AND IS_RENEWAL_SUCCESS = #{query.isRenewalSuccess}
            </if>
            <if test="query.renewalContent != null  and query.renewalContent !=''">
                AND RENEWAL_CONTENT = #{query.renewalContent}
            </if>
            <if test="query.isOffLineTendency != null  and query.isOffLineTendency !=''">
                AND IS_OFF_LINE_TENDENCY = #{query.isOffLineTendency}
            </if>
            <if test="query.customerWillingnessForBusiness != null  and query.customerWillingnessForBusiness !=''">
                AND CUSTOMER_WILLINGNESS_FOR_BUSINESS = #{query.customerWillingnessForBusiness}
            </if>
            <if test="query.isCompleteRenewalForBusiness != null  and query.isCompleteRenewalForBusiness !=''">
                AND IS_COMPLETE_RENEWAL_FOR_BUSINESS = #{query.isCompleteRenewalForBusiness}
            </if>
            <if test="query.isOffLineTendencyForIncome != null  and query.isOffLineTendencyForIncome !=''">
                AND IS_OFF_LINE_TENDENCY_FOR_INCOME = #{query.isOffLineTendencyForIncome}
            </if>
            <if test="query.zeroProductionReason != null  and query.zeroProductionReason !=''">
                AND ZERO_PRODUCTION_REASON = #{query.zeroProductionReason}
            </if>
            <if test="query.nextStep != null  and query.nextStep !=''">
                AND NEXT_STEP = #{query.nextStep}
            </if>
            <if test="query.isBuildingCustomer != null  and query.isBuildingCustomer !=''">
                AND IS_BUILDING_CUSTOMER = #{query.isBuildingCustomer}
            </if>
            <if test="query.isModifyCustomerCheckInfo != null  and query.isModifyCustomerCheckInfo !=''">
                AND IS_MODIFY_CUSTOMER_CHECK_INFO = #{query.isModifyCustomerCheckInfo}
            </if>
            <if test="query.isVisitId != null  and query.isVisitId !=''">
                AND IS_VISIT_ID = #{query.isVisitId}
            </if>
            <if test="query.isValid != null  and query.isValid !=''">
                AND IS_VALID = #{query.isValid}
            </if>
            <if test="query.invalidReason != null  and query.invalidReason !=''">
                AND INVALID_REASON = #{query.invalidReason}
            </if>
            <if test="query.signInPlace != null  and query.signInPlace !=''">
                AND SIGN_IN_PLACE = #{query.signInPlace}
            </if>
            <if test="query.signInStartTime != null  and query.signInStartTime !=''">
                AND SIGN_IN_START_TIME = #{query.signInStartTime}
            </if>
            <if test="query.signInEndTime != null  and query.signInEndTime !=''">
                AND SIGN_IN_END_TIME = #{query.signInEndTime}
            </if>
            <if test="query.extraInfoLink != null  and query.extraInfoLink !=''">
                AND EXTRA_INFO_LINK = #{query.extraInfoLink}
            </if>
            <if test="query.gmtCreate != null  and query.gmtCreate !=''">
                AND GMT_CREATE = #{query.gmtCreate}
            </if>
            <if test="query.createUserId != null  and query.createUserId !=''">
                AND CREATE_USER_ID = #{query.createUserId}
            </if>
            <if test="query.createUserName != null  and query.createUserName != ''">
                AND CREATE_USER_NAME LIKE concat('%', #{query.createUserName}, '%')
            </if>
            <if test="query.gmtModified != null  and query.gmtModified !=''">
                AND GMT_MODIFIED = #{query.gmtModified}
            </if>
            <if test="query.updateUserName != null  and query.updateUserName != ''">
                AND UPDATE_USER_NAME LIKE concat('%', #{query.updateUserName}, '%')
            </if>
            <if test="query.updateUserId != null  and query.updateUserId !=''">
                AND UPDATE_USER_ID = #{query.updateUserId}
            </if>
            <if test="query.isDelete != null  and query.isDelete !=''">
                AND IS_DELETE = #{query.isDelete}
            </if>
            <if test="query.deleteTime != null  and query.deleteTime !=''">
                AND DELETE_TIME = #{query.deleteTime}
            </if>
            <if test="query.monthId != null  and query.monthId !=''">
                AND MONTH_ID = #{query.monthId}
            </if>
            <if test="query.dayId != null  and query.dayId !=''">
                AND DAY_ID = #{query.dayId}
            </if>
        </where>
    </select>
    <select id="selectByWorkOrderId" resultType="cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanDZqztGemWorkOrderFeedbackInfoVO">
        select * from t_sanquan_d_zqzt_gem_work_order_feedback_info t where t.WORK_ORDER_CODE=#{code} order by back_time desc
    </select>

    <select id="getFeekInfoStatic" resultType="java.util.Map">
          select b.`ID`, `WORK_ORDER_CODE`, `TOUCH_ID`, `VISIT_ID`, `USER_CODE`, `VISIT_RESULT`, `BACK_TIME`, `IS_CONTINUE_VISIT`, `NO_VISIT_REASON`,
           `NEXT_TIME`, `VISIT_THEME`, `VISIT_WAY`, `VISIT_TIME`, `VISIT_SITE`, `VISIT_PERSON`, `VISIT_PHONE`,  `KEY_RECOMMENDED_PRODUCT`,
            `IS_BUSI_OPP_WILLING`, `IS_NEED_SUPPORT`, `BUSI_OPPT`, `BUSI_OPP_TYPE`, `BUSI_OPPT_NAME`, `IS_INSERT_DIFF`, `OPERATOR`,
             `BUSINESS_TYPE`, `END_DATE`, `DIFF_BUSINESS_INFO`, `PICTURE_URLS`, `CUSTOMER_WILLINGNESS`, `IS_RENEWAL`, `ESTIMATED_RECOVERY_TIME`,
            `IS_PAYMENT_SUCCESS`, `IS_WRITE_OFF`, `IS_RENEWAL_SUCCESS`, `RENEWAL_CONTENT`, `IS_OFF_LINE_TENDENCY`,
            `CUSTOMER_WILLINGNESS_FOR_BUSINESS`, `IS_COMPLETE_RENEWAL_FOR_BUSINESS`, `IS_OFF_LINE_TENDENCY_FOR_INCOME`,
            `ZERO_PRODUCTION_REASON`, `NEXT_STEP`, `IS_BUILDING_CUSTOMER`, `IS_MODIFY_CUSTOMER_CHECK_INFO`, `IS_VISIT_ID`,
            `IS_VALID`, `INVALID_REASON`, `SIGN_IN_PLACE`, `SIGN_IN_START_TIME`, `SIGN_IN_END_TIME`, `EXTRA_INFO_LINK`, `GMT_CREATE`,
            `CREATE_USER_ID`, `CREATE_USER_NAME`, `GMT_MODIFIED`
           from (
            SELECT b.id,ROW_NUMBER() OVER(PARTITION BY b.WORK_ORDER_CODE ORDER BY b.BACK_TIME desc,b.month_id desc,b.day_id desc) as rn
             FROM t_sanquan_d_zqzt_gem_work_order t
             join t_sanquan_d_zqzt_gem_work_order_feedback_info b on t.code = b.WORK_ORDER_CODE -- and t.MONTH_ID = b.MONTH_ID and t.DAY_ID = b.DAY_ID
            where 1=1
                <if test="query.ID != null  and query.ID !=''">
                    AND t.ID = #{query.ID}
                </if>
                <if test="query.code != null  and query.code !=''">
                    AND t.CODE = #{query.code}
                </if>
                <if test="query.city != null  and query.city !='' and query.city != '省公司' and query.city != '全部' ">
                    AND t.B_DOMAIN_CITY_NAME = #{query.city}
                </if>
                <if test="query.status != null  and query.status !=''">
                    AND t.STATUS = #{query.status}
                </if>
                <if test="query.policyCode != null  and query.policyCode !=''">
                    AND t.POLICY_CODE = #{query.policyCode}
                </if>
                <if test="query.superiorPolicyCode != null  and query.superiorPolicyCode !=''">
                    AND t.SUPERIOR_POLICY_CODE = #{query.superiorPolicyCode}
                </if>
                <if test="query.superiorPolicyName != null  and query.superiorPolicyName != ''">
                    AND t.SUPERIOR_POLICY_NAME LIKE concat('%', #{query.superiorPolicyName}, '%')
                </if>
                <if test="query.superiorPolicyCreateUserId != null  and query.superiorPolicyCreateUserId !=''">
                    AND t.SUPERIOR_POLICY_CREATE_USER_ID = #{query.superiorPolicyCreateUserId}
                </if>
                <if test="query.superiorPolicyCreateUserName != null  and query.superiorPolicyCreateUserName != ''">
                    AND t.SUPERIOR_POLICY_CREATE_USER_NAME LIKE concat('%', #{query.superiorPolicyCreateUserName}, '%')
                </if>
                <if test="query.topPolicyCode != null  and query.topPolicyCode !=''">
                    AND t.TOP_POLICY_CODE = #{query.topPolicyCode}
                </if>
                <if test="query.businessScene != null  and query.businessScene !=''">
                    AND t.BUSINESS_SCENE = #{query.businessScene}
                </if>
                <if test="query.orgName != null  and query.orgName != ''">
                    AND t.ORG_NAME LIKE concat('%', #{query.orgName}, '%')
                </if>
                <if test="query.orgCode != null  and query.orgCode !=''">
                    AND t.ORG_CODE = #{query.orgCode}
                </if>
                <if test="query.managerNo != null  and query.managerNo !=''">
                    AND t.MANAGER_NO = #{query.managerNo}
                </if>
                <if test="query.managerName != null  and query.managerName != ''">
                    AND t.MANAGER_NAME LIKE concat('%', #{query.managerName}, '%')
                </if>
                <if test="query.managerMobile != null  and query.managerMobile !=''">
                    AND t.MANAGER_MOBILE = #{query.managerMobile}
                </if>
                <if test="query.managerType != null  and query.managerType !=''">
                    AND t.MANAGER_TYPE = #{query.managerType}
                </if>
                <if test="query.actualManagerNo != null  and query.actualManagerNo !=''">
                    AND t.ACTUAL_MANAGER_NO = #{query.actualManagerNo}
                </if>
                <if test="query.actualManagerName != null  and query.actualManagerName != ''">
                    AND t.ACTUAL_MANAGER_NAME LIKE concat('%', #{query.actualManagerName}, '%')
                </if>
                <if test="query.actualManagerMobile != null  and query.actualManagerMobile !=''">
                    AND t.ACTUAL_MANAGER_MOBILE = #{query.actualManagerMobile}
                </if>
                <if test="query.customId != null  and query.customId !=''">
                    AND t.CUSTOM_ID = #{query.customId}
                </if>
                <if test="query.customName != null  and query.customName != ''">
                    AND t.CUSTOM_NAME LIKE concat('%', #{query.customName}, '%')
                </if>
                <if test="query.sendTimeStart != null  and query.sendTimeEnd != ''">
                    AND t.send_time between #{query.sendTimeStart} and #{query.sendTimeEnd}
                </if>
                <if test="query.beginDate != null and query.beginDate != ''">
                    AND DATE_FORMAT(t.BACK_TIME, '%Y%m%d') &gt;= #{query.beginDate}
                </if>
                <if test="query.endDate != null and query.endDate != ''">
                    AND DATE_FORMAT(t.BACK_TIME, '%Y%m%d') &lt;= #{query.endDate}
                </if>
           ) t
           join t_sanquan_d_zqzt_gem_work_order_feedback_info b on  t.id = b.id
           where t.rn = 1
    </select>

    <select id="selectOrderPropInfo" resultType="cn.chinaunicom.sdsi.targetMarketing.vo.TSanquanZqztOrderTypePropInfoVO">
        select * from t_sanquan_d_zqzt_work_order_type_prop_info t where t.WORK_ORDER_CODE=#{workOrderCode}
        AND SUBSTRING(CAST(#{createTime} AS CHAR), 1, 6) = MONTH_ID
         AND SUBSTRING(CAST(#{createTime} AS CHAR), 7, 2) = DAY_ID
    </select>
</mapper>
