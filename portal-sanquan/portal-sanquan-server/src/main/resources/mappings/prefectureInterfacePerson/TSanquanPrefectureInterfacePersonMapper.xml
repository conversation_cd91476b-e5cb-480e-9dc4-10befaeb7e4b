<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.prefectureInterfacePerson.dao.TSanquanPrefectureInterfacePersonMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity.TSanquanPrefectureInterfacePerson" id="tSanquanPrefectureInterfacePersonMap">
        <result property="id" column="id"/>
        <result property="jobNumber" column="job_number"/>
        <result property="name" column="name"/>
        <result property="tel" column="tel"/>
        <result property="industry" column="industry"/>
        <result property="listCustomerProvince" column="list_customer_province"/>
        <result property="listCustomerCity" column="list_customer_city"/>
        <result property="listCustomerDistrict" column="list_customer_district"/>
        <result property="rosterCustomerId" column="roster_customer_id"/>
        <result property="rosterCustomerName" column="roster_customer_name"/>
        <result property="customerManagerId" column="customer_manager_id"/>
        <result property="customerManagerName" column="customer_manager_name"/>
        <result property="provinceIndustry" column="province_industry"/>
        <result property="listCustomerLevel" column="list_customer_level"/>
        <result property="contactAddress" column="contact_address"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
        <result property="createDate" column="create_date"/>
        <result property="createBy" column="create_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="updateBy" column="update_by"/>
        <result property="versions" column="versions"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="cityRole" column="city_role"/>
    </resultMap>
    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO">
        select id,name,tel,industry,list_customer_province,list_customer_city,list_customer_district,
               roster_customer_id,roster_customer_name,customer_manager_id,customer_manager_name,
               province_industry,contact_address,job_number,type,city_role
        from t_sanquan_prefecture_interface_person
        <where>
            delete_flag = 'normal'
            <if test="query.name != null and query.name != ''">
                AND name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.jobNumber != null and query.jobNumber != ''">
                AND job_number = #{query.jobNumber}
            </if>
            <if test="query.tel != null and query.tel != ''">
                AND tel LIKE concat('%', #{query.tel}, '%')
            </if>
            <if test="query.industry != null and query.industry != ''">
                AND industry LIKE CONCAT('%', #{query.industry}, '%')
            </if>
            <if test="query.listCustomerCity != null and query.listCustomerCity != ''">
                AND list_customer_city LIKE CONCAT(REPLACE(#{query.listCustomerCity}, '市', ''), '%')
            </if>
            <if test="query.listCustomerProvince != null and query.listCustomerProvince != ''">
                AND list_customer_province LIKE CONCAT(#{query.listCustomerProvince}, '%')
            </if>
            <if test="query.listCustomerDistrict != null and query.listCustomerDistrict != ''">
                AND list_customer_district LIKE CONCAT(#{query.listCustomerDistrict}, '%')
            </if>
            <if test="query.provinceIndustry != null and query.provinceIndustry != ''">
                AND province_industry = #{query.provinceIndustry}
            </if>
            <if test="query.cityRole != null and query.cityRole != ''">
                AND city_role = #{query.cityRole}
            </if>
        </where>
    </select>
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO">
        select id,name,tel,industry,list_customer_province,list_customer_city,list_customer_district,
        roster_customer_id,roster_customer_name,customer_manager_id,customer_manager_name,
        province_industry,contact_address,job_number,type,city_role
        from t_sanquan_prefecture_interface_person
        <where>
            delete_flag = 'normal'
            <if test="query.name != null and query.name != ''">
                AND name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.jobNumber != null and query.jobNumber != ''">
                AND job_number = #{query.jobNumber}
            </if>
            <if test="query.tel != null and query.tel != ''">
                AND tel LIKE concat('%', #{query.tel}, '%')
            </if>
            <if test="query.industry != null and query.industry != ''">
                AND industry LIKE CONCAT('%', #{query.industry}, '%')
            </if>
            <if test="query.listCustomerCity != null and query.listCustomerCity != ''">
                AND list_customer_city LIKE CONCAT(REPLACE(#{query.listCustomerCity}, '市', ''), '%')
            </if>
            <if test="query.listCustomerProvince != null and query.listCustomerProvince != ''">
                AND list_customer_province LIKE CONCAT(#{query.listCustomerProvince}, '%')
            </if>
            <if test="query.listCustomerDistrict != null and query.listCustomerDistrict != ''">
                AND list_customer_district LIKE CONCAT(#{query.listCustomerDistrict}, '%')
            </if>
            <if test="query.provinceIndustry != null and query.provinceIndustry != ''">
                AND province_industry = #{query.provinceIndustry}
            </if>
        </where>
    </select>
    <select id="selectByJobNumber"
            resultType="cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.entity.TSanquanPrefectureInterfacePerson">
        select id,name,tel,industry,list_customer_province,list_customer_city,list_customer_district,
               roster_customer_id,roster_customer_name,customer_manager_id,customer_manager_name,
               province_industry,contact_address,job_number,type,city_role
        from t_sanquan_prefecture_interface_person
        where delete_flag = 'normal'
        and job_number = #{jobNumber}
    </select>
    <select id="verifyJobNumber" resultType="java.lang.Integer">
        select case when count(*) > 0 then 1 else 0 end
        from t_sanquan_prefecture_interface_person
        <where>
            delete_flag = 'normal'
            <if test="jobNumber != null and jobNumber != ''">
                and job_number = #{jobNumber}
            </if>
            <if test="id != null and id != ''">
                and id != #{id}
            </if>
        </where>
    </select>
    <select id="getCityInterfacePersonByCity"
            resultType="java.util.HashMap">
        select t.list_customer_city,name,job_number from t_sanquan_prefecture_interface_person t
        where t.delete_flag='normal' and (t.list_customer_district is null or t.list_customer_district='')
    </select>
</mapper>