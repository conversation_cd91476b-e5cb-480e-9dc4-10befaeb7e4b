<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.quartz.mapper.SysJobMapper">

	<resultMap type="cn.chinaunicom.sdsi.quartz.domain.SysJob" id="SysJobResult">
		<id     property="jobId"          column="job_id"          />
		<result property="jobName"        column="job_name"        />
		<result property="jobGroup"       column="job_group"       />
		<result property="invokeTarget"   column="invoke_target"   />
		<result property="cronExpression" column="cron_expression" />
		<result property="misfirePolicy"  column="misfire_policy"  />
		<result property="concurrent"     column="concurrent"      />
		<result property="status"         column="status"          />
		<result property="createBy"       column="create_by"       />
		<result property="createDate"     column="create_time"     />
		<result property="updateBy"       column="update_by"       />
		<result property="updateDate"     column="update_time"     />
		<result property="remark"         column="remark"          />
		<result property="businessId"     column="business_id"          />
		<result property="tenantId"     column="tenant_id"          />
	</resultMap>
	
	<sql id="selectJobVo">
        select job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark, business_id
		from t_sanquan_sys_job
    </sql>
	
	<select id="selectJobList" parameterType="cn.chinaunicom.sdsi.quartz.domain.SysJob" resultType="cn.chinaunicom.sdsi.quartz.domain.SysJob">
		<include refid="selectJobVo"/>
		<where>
			<if test="query.jobName != null and query.jobName != ''">
				AND job_name like concat('%', #{query.jobName}, '%')
			</if>
			<if test="query.jobGroup != null and query.jobGroup != ''">
				AND job_group = #{query.jobGroup}
			</if>
			<if test="query.status != null and query.status != ''">
				AND status = #{query.status}
			</if>
			<if test="query.invokeTarget != null and query.invokeTarget != ''">
				AND invoke_target like concat('%', #{query.invokeTarget}, '%')
			</if>
			<if test="query.businessId != null and query.businessId != ''">
				AND business_id = #{query.businessId}
			</if>
		</where>
	</select>
	<select id="findPage" parameterType="cn.chinaunicom.sdsi.quartz.domain.SysJobQueryVo" resultType="cn.chinaunicom.sdsi.quartz.domain.SysJob">
		<include refid="selectJobVo"/>
		<where>
			<if test="query.jobName != null and query.jobName != ''">
				AND job_name like concat('%', #{query.jobName}, '%')
			</if>
			<if test="query.jobGroup != null and query.jobGroup != ''">
				AND job_group = #{query.jobGroup}
			</if>
			<if test="query.status != null and query.status != ''">
				AND status = #{query.status}
			</if>
			<if test="query.invokeTarget != null and query.invokeTarget != ''">
				AND invoke_target like concat('%', #{query.invokeTarget}, '%')
			</if>
		</where>
	</select>
	<select id="selectJobAll" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
	</select>
	
	<select id="selectJobById" parameterType="Long" resultMap="SysJobResult">
		<include refid="selectJobVo"/>
		where job_id = #{jobId}
	</select>
	
	<delete id="deleteJobById" parameterType="Long">
 		delete from t_sanquan_sys_job where job_id = #{jobId}
 	</delete>
 	
 	<delete id="deleteJobByIds" parameterType="Long">
 		delete from t_sanquan_sys_job where job_id in
 		<foreach collection="array" item="jobId" open="(" separator="," close=")">
 			#{jobId}
        </foreach> 
 	</delete>
 	
 	<update id="updateJob" parameterType="cn.chinaunicom.sdsi.quartz.domain.SysJob">
 		update t_sanquan_sys_job
 		<set>
 			<if test="jobName != null and jobName != ''">job_name = #{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">job_group = #{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">invoke_target = #{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">cron_expression = #{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy = #{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">concurrent = #{concurrent},</if>
 			<if test="status !=null">status = #{status},</if>
 			<if test="remark != null and remark != ''">remark = #{remark},</if>
 			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
 			update_time = sysdate()
 		</set>
 		where job_id = #{jobId}
	</update>
 	
 	<insert id="insertJob" parameterType="cn.chinaunicom.sdsi.quartz.domain.SysJob" useGeneratedKeys="true" keyProperty="jobId">
 		insert into t_sanquan_sys_job(
 			<if test="jobId != null and jobId != 0">job_id,</if>
 			<if test="jobName != null and jobName != ''">job_name,</if>
 			<if test="jobGroup != null and jobGroup != ''">job_group,</if>
 			<if test="invokeTarget != null and invokeTarget != ''">invoke_target,</if>
 			<if test="cronExpression != null and cronExpression != ''">cron_expression,</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">misfire_policy,</if>
 			<if test="concurrent != null and concurrent != ''">concurrent,</if>
 			<if test="status != null and status != ''">status,</if>
 			<if test="remark != null and remark != ''">remark,</if>
 			<if test="createBy != null and createBy != ''">create_by,</if>
			<if test="businessId != null and businessId != ''">business_id,</if>
 			create_time
 		)values(
 			<if test="jobId != null and jobId != 0">#{jobId},</if>
 			<if test="jobName != null and jobName != ''">#{jobName},</if>
 			<if test="jobGroup != null and jobGroup != ''">#{jobGroup},</if>
 			<if test="invokeTarget != null and invokeTarget != ''">#{invokeTarget},</if>
 			<if test="cronExpression != null and cronExpression != ''">#{cronExpression},</if>
 			<if test="misfirePolicy != null and misfirePolicy != ''">#{misfirePolicy},</if>
 			<if test="concurrent != null and concurrent != ''">#{concurrent},</if>
 			<if test="status != null and status != ''">#{status},</if>
 			<if test="remark != null and remark != ''">#{remark},</if>
 			<if test="createBy != null and createBy != ''">#{createBy},</if>
			<if test="businessId != null and businessId != ''">#{businessId},</if>
 			sysdate()
 		)
	</insert>

</mapper> 