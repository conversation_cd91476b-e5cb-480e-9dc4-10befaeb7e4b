<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.dbcp.mapper.TSanquanDbcpReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpReport">
        <id column="id" property="id" />
        <result column="task_code" property="taskCode" />
        <result column="city" property="city" />
        <result column="SUPERIOR_POLICY_CODE" property="superiorPolicyCode" />
        <result column="SUPERIOR_POLICY_NAME" property="superiorPolicyName" />
        <result column="current_link" property="currentLink" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="industry" property="industry" />
        <result column="customer_manager" property="customerManager" />
        <result column="customer_manager_id" property="customerManagerId" />
        <result column="project_scale" property="projectScale" />
        <result column="evaluation_situation" property="evaluationSituation" />
        <result column="evaluation_org" property="evaluationOrg" />
        <result column="expiration_date" property="expirationDate" />
        <result column="project_budget" property="projectBudget" />
        <result column="work_plan" property="workPlan" />
        <result column="cooperate" property="cooperate" />
        <result column="service_requirement" property="serviceRequirement" />
        <result column="start_date" property="startDate" />
        <result column="friend_business" property="friendBusiness" />
        <result column="cust_intention_change" property="custIntentionChange" />
        <result column="remark" property="remark" />
        <result column="sfbzzbwj" property="sfbzzbwj" />
        <result column="sffbzbgg" property="sffbzbgg" />
        <result column="bmjz_date" property="bmjzDate" />
        <result column="kb_date" property="kbDate" />
        <result column="bid_situation" property="bidSituation" />
        <result column="project_start_date" property="projectStartDate" />
        <result column="project_end_date" property="projectEndDate" />
        <result column="project_deliver_appraise" property="projectDeliverAppraise" />
        <result column="project_deliver_appraise_opinion" property="projectDeliverAppraiseOpinion" />
        <result column="project_quality_appraise" property="projectQualityAppraise" />
        <result column="project_quality_appraise_opinion" property="projectQualityAppraiseOpinion" />
        <result column="cust_experience_appraise" property="custExperienceAppraise" />
        <result column="cust_experience_appraise_opinion" property="custExperienceAppraiseOpinion" />
        <result column="project_plan_finish_date" property="projectPlanFinishDate" />
        <result column="project_reality_finish_date" property="projectRealityFinishDate" />
        <result column="feedback_order_id" property="feedbackOrderId" />
        <result column="feedback_date" property="feedbackDate" />
        <result column="send_time" property="sendTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_code, city, SUPERIOR_POLICY_CODE, SUPERIOR_POLICY_NAME, current_link, customer_id, customer_name, industry,
          customer_manager, customer_manager_id, project_scale, evaluation_situation, evaluation_org, expiration_date,
          project_budget, work_plan, cooperate, service_requirement, start_date, friend_business, cust_intention_change,
          remark, sfbzzbwj, sffbzbgg, bmjz_date, kb_date, bid_situation,
          project_start_date, project_end_date, project_deliver_appraise, project_deliver_appraise_opinion,
          project_quality_appraise, project_quality_appraise_opinion, cust_experience_appraise, cust_experience_appraise_opinion,
          project_plan_finish_date, project_reality_finish_date, feedback_order_id, feedback_date,customer_manager_tel,
          track_time,sign_time,create_time, update_time,send_time
    </sql>

    <!-- 公共条件片段，支持动态表别名 -->
    <sql id="CommonConditions">
        <if test="query.id != null and query.id != ''">
            and ${tableAlias}id = #{query.id}
        </if>
        <if test="query.taskCode != null and query.taskCode != ''">
            and ${tableAlias}task_code = #{query.taskCode}
        </if>
        <if test="query.city != null and query.city != ''">
            and ${tableAlias}city = #{query.city}
        </if>
        <if test="query.citys != null and query.citys != ''">
            and ${tableAlias}city in
            <foreach item="city" index="index" collection="query.citys" open="(" separator="," close=")">
                #{city}
            </foreach>
        </if>
        <if test="query.superiorPolicyCode != null and query.superiorPolicyCode != ''">
            and ${tableAlias}SUPERIOR_POLICY_CODE = #{query.superiorPolicyCode}
        </if>
        <if test="query.superiorPolicyName != null and query.superiorPolicyName != ''">
            and ${tableAlias}SUPERIOR_POLICY_NAME = #{query.superiorPolicyName}
        </if>
        <if test="query.currentLink != null and query.currentLink != ''">
            and ${tableAlias}current_link = #{query.currentLink}
        </if>
        <if test="query.customerId != null and query.customerId != ''">
            and ${tableAlias}customer_id = #{query.customerId}
        </if>
        <if test="query.customerName != null and query.customerName != ''">
            and ${tableAlias}customer_name = #{query.customerName}
        </if>
        <if test="query.industry != null and query.industry != ''">
            and ${tableAlias}industry = #{query.industry}
        </if>
        <if test="query.customerManager != null and query.customerManager != ''">
            and ${tableAlias}customer_manager = #{query.customerManager}
        </if>
        <if test="query.customerManagerId != null and query.customerManagerId != ''">
            and ${tableAlias}customer_manager_id = #{query.customerManagerId}
        </if>
        <if test="query.projectScale != null and query.projectScale != ''">
            and ${tableAlias}project_scale = #{query.projectScale}
        </if>
        <if test="query.evaluationSituation != null and query.evaluationSituation != ''">
            and ${tableAlias}evaluation_situation = #{query.evaluationSituation}
        </if>
        <if test="query.evaluationOrg != null and query.evaluationOrg != ''">
            and ${tableAlias}evaluation_org = #{query.evaluationOrg}
        </if>
        <if test="query.expirationDate != null and query.expirationDate != ''">
            and ${tableAlias}expiration_date = #{query.expirationDate}
        </if>
        <if test="query.projectBudget != null and query.projectBudget != ''">
            and ${tableAlias}project_budget = #{query.projectBudget}
        </if>
        <if test="query.workPlan != null and query.workPlan != ''">
            and ${tableAlias}work_plan = #{query.workPlan}
        </if>
        <if test="query.cooperate != null and query.cooperate != ''">
            and ${tableAlias}cooperate = #{query.cooperate}
        </if>
        <if test="query.serviceRequirement != null and query.serviceRequirement != ''">
            and ${tableAlias}service_requirement = #{query.serviceRequirement}
        </if>
        <if test="query.startDate != null and query.startDate != ''">
            and ${tableAlias}start_date = #{query.startDate}
        </if>
        <if test="query.friendBusiness != null and query.friendBusiness != ''">
            and ${tableAlias}friend_business = #{query.friendBusiness}
        </if>
        <if test="query.custIntentionChange != null and query.custIntentionChange != ''">
            and ${tableAlias}cust_intention_change = #{query.custIntentionChange}
        </if>
        <if test="query.remark != null and query.remark != ''">
            and ${tableAlias}remark = #{query.remark}
        </if>
        <if test="query.sfbzzbwj != null and query.sfbzzbwj != ''">
            and ${tableAlias}sfbzzbwj = #{query.sfbzzbwj}
        </if>
        <if test="query.sffbzbgg != null and query.sffbzbgg != ''">
            and ${tableAlias}sffbzbgg = #{query.sffbzbgg}
        </if>
        <if test="query.bmjzDate != null and query.bmjzDate != ''">
            and ${tableAlias}bmjz_date = #{query.bmjzDate}
        </if>
        <if test="query.kbDate != null and query.kbDate != ''">
            and ${tableAlias}kb_date = #{query.kbDate}
        </if>
        <if test="query.bidSituation != null and query.bidSituation != ''">
            and ${tableAlias}bid_situation = #{query.bidSituation}
        </if>
        <if test="query.projectStartDate != null and query.projectStartDate != ''">
            and ${tableAlias}project_start_date = #{query.projectStartDate}
        </if>
        <if test="query.projectEndDate != null and query.projectEndDate != ''">
            and ${tableAlias}project_end_date = #{query.projectEndDate}
        </if>
        <if test="query.projectDeliverAppraise != null and query.projectDeliverAppraise != ''">
            and ${tableAlias}project_deliver_appraise = #{query.projectDeliverAppraise}
        </if>
        <if test="query.projectDeliverAppraiseOpinion != null and query.projectDeliverAppraiseOpinion != ''">
            and ${tableAlias}project_deliver_appraise_opinion = #{query.projectDeliverAppraiseOpinion}
        </if>
        <if test="query.projectQualityAppraise != null and query.projectQualityAppraise != ''">
            and ${tableAlias}project_quality_appraise = #{query.projectQualityAppraise}
        </if>
        <if test="query.projectQualityAppraiseOpinion != null and query.projectQualityAppraiseOpinion != ''">
            and ${tableAlias}project_quality_appraise_opinion = #{query.projectQualityAppraiseOpinion}
        </if>
        <if test="query.custExperienceAppraise != null and query.custExperienceAppraise != ''">
            and ${tableAlias}cust_experience_appraise = #{query.custExperienceAppraise}
        </if>
        <if test="query.custExperienceAppraiseOpinion != null and query.custExperienceAppraiseOpinion != ''">
            and ${tableAlias}cust_experience_appraise_opinion = #{query.custExperienceAppraiseOpinion}
        </if>
        <if test="query.projectPlanFinishDate != null and query.projectPlanFinishDate != ''">
            and ${tableAlias}project_plan_finish_date = #{query.projectPlanFinishDate}
        </if>
        <if test="query.projectRealityFinishDate != null and query.projectRealityFinishDate != ''">
            and ${tableAlias}project_reality_finish_date = #{query.projectRealityFinishDate}
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            and DATE_FORMAT(${tableAlias}send_time, '%Y-%m-%d') &gt;= #{query.beginDate}
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            AND DATE_FORMAT(${tableAlias}send_time, '%Y-%m-%d') &lt;= #{query.endDate}
        </if>
    </sql>

    <select id="selectPageList" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpReport">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_report dr
        <where>
            <include refid="CommonConditions">
                <property name="tableAlias" value="dr."/>
            </include>
        </where>
        order by dr.send_time desc
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.dbcp.entity.TSanquanDbcpReport">
        select
        <include refid="Base_Column_List" />
        from t_sanquan_dbcp_report dr
        <where>
            <include refid="CommonConditions">
                <property name="tableAlias" value="dr."/>
            </include>

        </where>
        order by dr.send_time desc
    </select>

</mapper>
