<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.keynoteOppo.asr.asrLog.mapper.AsrLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.keynoteOppo.asr.asrLog.entity.AsrLog">
        <id column="id" property="id" />
        <result column="file_id" property="fileId" />
        <result column="is_delete" property="isDelete" />
        <result column="asr_text" property="asrText" />
        <result column="is_identify" property="isIdentify" />
        <result column="create_time" property="createTime" />
        <result column="file_url" property="fileUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, file_id, is_delete, asr_text, is_identify, create_time, file_url
    </sql>
    <select id="findResult" resultType="cn.chinaunicom.sdsi.keynoteOppo.asr.asrLog.entity.AsrLog">
        select * from t_sanquan_asr_log where id = #{id}
    </select>

</mapper>
