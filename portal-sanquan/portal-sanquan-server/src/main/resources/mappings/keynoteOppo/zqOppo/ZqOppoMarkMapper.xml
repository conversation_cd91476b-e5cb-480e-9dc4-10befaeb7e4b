<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.keynoteOppo.zqOppo.mapper.ZqOppoMarkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoMark">
        <result column="oppo_number" property="oppoNumber" />
        <result column="product_ai" property="productAi" />
        <result column="req_keyword" property="reqKeyword" />
        <result column="status" property="status" />
        <result column="update_time" property="updateTime" />
        <result column="day_id" property="dayId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        oppo_number, product_ai, req_keyword, status, update_time, day_id
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoMark">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_zq_oppo_mark
        <where>
            <if test="query.oppoNumber != null and query.oppoNumber != ''">
                AND oppo_number = #{query.oppoNumber}
            </if>
            <if test="query.productAi != null and query.productAi != ''">
                AND product_ai = #{query.productAi}
            </if>
            <if test="query.reqKeyword != null and query.reqKeyword != ''">
                AND req_keyword = #{query.reqKeyword}
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.updateTime != null and query.updateTime != ''">
                AND update_time = #{query.updateTime}
            </if>
            <if test="query.dayId != null and query.dayId != ''">
                AND day_id = #{query.dayId}
            </if>
        </where>
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.keynoteOppo.zqOppo.entity.ZqOppoMark">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_zq_oppo_mark
        <where>
            <if test="query.oppoNumber != null and query.oppoNumber != ''">
                AND oppo_number = #{query.oppoNumber}
            </if>
            <if test="query.productAi != null and query.productAi != ''">
                AND product_ai = #{query.productAi}
            </if>
            <if test="query.reqKeyword != null and query.reqKeyword != ''">
                AND req_keyword = #{query.reqKeyword}
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.updateTime != null and query.updateTime != ''">
                AND update_time = #{query.updateTime}
            </if>
            <if test="query.dayId != null and query.dayId != ''">
                AND day_id = #{query.dayId}
            </if>
        </where>
    </select>

    <update id="updateOppoMark">
        update t_sanquan_zq_oppo_mark set
        <if test="query.status != null and query.status != ''">
            status = #{query.status}
        </if>

        <where>
            <if test="query.oppoNumber!= null and query.oppoNumber!=''">
                 oppo_number = #{query.oppoNumber}
            </if>
        </where>
    </update>

</mapper>
