<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.tag.mapper.TagDbMapper">
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.tag.entity.TagDb">
        select *,CONCAT('select customer_id,',db_table_column,' from ',db_table_name) as 'searchSql' from
        t_sanquan_tag_db
        <where>
            1=1
            <if test="query.tagId != null and query.tagId != ''">
                AND tag_id = #{query.tagId}
            </if>
        </where>
    </select>

    <delete id="deleteByTagId">
        delete
        from t_sanquan_tag_db
        where tag_id = #{tagId}

    </delete>


</mapper>
