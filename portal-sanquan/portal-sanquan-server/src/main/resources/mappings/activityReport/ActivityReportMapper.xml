<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.activityReport.mapper.ActivityReportMapper">
    <resultMap id="ActivityReportCityStatisticsResult" type="cn.chinaunicom.sdsi.activityReport.entity.ActivityReportCityStatistics">
        <result property="strategyId" column="strategyId"/>
        <result property="strategyName" column="strategyName"/>
        <result property="city" column="city"/>
        <result property="customerCount" column="customerCount"/>
        <result property="cmOpportunityCount" column="cmOpportunityCount"/>
        <result property="cmOpportunityAmount" column="cmOpportunityAmount"/>
        <result property="cmPendingCount" column="cmPendingCount"/>
        <result property="cmProcessingCount" column="cmProcessingCount"/>
        <result property="cmCompletedCount" column="cmCompletedCount"/>
        <result property="mmOpportunityCount" column="mmOpportunityCount"/>
        <result property="mmOpportunityAmount" column="mmOpportunityAmount"/>
        <result property="mmPendingCount" column="mmPendingCount"/>
        <result property="mmProcessingCount" column="mmProcessingCount"/>
        <result property="mmCompletedCount" column="mmCompletedCount"/>
    </resultMap>
    <resultMap id="ActivityReportPersonStatisticsResult" type="cn.chinaunicom.sdsi.activityReport.entity.ActivityReportPersonStatistics">
        <result property="strategyId" column="strategyId"/>
        <result property="strategyName" column="strategyName"/>
        <result property="city" column="city"/>
        <result property="marketingManager" column="marketingManager"/>
        <result property="customerCount" column="customerCount"/>
        <result property="opportunityCount" column="opportunityCount"/>
        <result property="opportunityAmount" column="opportunityAmount"/>
        <result property="pendingCount" column="pendingCount"/>
        <result property="processingCount" column="processingCount"/>
        <result property="completedCount" column="completedCount"/>
    </resultMap>
    <resultMap id="ActivityReportDetailResult" type="cn.chinaunicom.sdsi.activityReport.entity.ActivityReportDetail">
        <result property="strategyId" column="strategyId"/>
        <result property="strategyName" column="strategyName"/>
        <result property="todoCode" column="todoCode"/>
        <result property="opportunityId" column="opportunityId"/>
        <result property="customerId" column="customerId"/>
        <result property="customerName" column="customerName"/>
        <result property="customerManagerId" column="customerManagerId"/>
        <result property="customerManager" column="customerManager"/>
        <result property="executorPhone" column="executorPhone"/>
        <result property="productName" column="productName"/>
        <result property="cityCode" column="cityCode"/>
        <result property="marketingManager" column="marketingManager"/>
        <result property="marketingManagerId" column="marketingManagerId"/>
        <result property="marketingManagerFeedbackStatus" column="marketingManagerFeedbackStatus"/>
        <result property="executionStatus" column="executionStatus"/>
        <result property="visitDate" column="visitDate"/>
        <result property="visitMode" column="visitMode"/>
        <result property="contactType" column="contactType"/>
        <result property="contactName" column="contactName"/>
        <result property="interviewFeedback" column="interviewFeedback"/>
        <result property="feedbackType" column="feedbackType"/>
        <result property="feedbackContent" column="feedbackContent"/>
        <result property="updateDate" column="updateDate"/>
        <result property="closeTime" column="closeTime"/>
        <result property="projectScale" column="projectScale"/>
        <result property="softwareScale" column="softwareScale"/>
        <result property="grossProfit" column="grossProfit"/>
        <result property="isBrace" column="isBrace"/>
        <result property="solutionSupporter" column="solutionSupporter"/>
        <result property="supportTime" column="supportTime"/>
        <result property="supportForms" column="supportForms"/>
        <result property="supplementaryExplanation" column="supplementaryExplanation"/>
        <result property="actualManagerNo" column="actualManagerNo"/>
        <result property="actualManagerName" column="actualManagerName"/>
        <result property="actualManagerMobile" column="actualManagerMobile"/>
        <result property="status" column="status"/>
        <result property="businessNum" column="businessNum"/>
        <result property="signNum" column="signNum"/>
        <result property="expectedIncome" column="expectedIncome"/>
        <result property="developNum" column="developNum"/>
        <result property="startDate" column="startDate"/>
        <result property="businessScene" column="businessScene"/>
        <result property="touchId" column="touchId"/>
        <result property="visitId" column="visitId"/>
        <result property="userCode" column="userCode"/>
        <result property="visitResult" column="visitResult"/>
        <result property="backTime" column="backTime"/>
        <result property="isContinueVisit" column="isContinueVisit"/>
        <result property="noVisitReason" column="noVisitReason"/>
        <result property="nextTime" column="nextTime"/>
        <result property="visitTheme" column="visitTheme"/>
        <result property="visitWay" column="visitWay"/>
        <result property="visitTime" column="visitTime"/>
        <result property="visitSite" column="visitSite"/>
        <result property="visitPerson" column="visitPerson"/>
        <result property="visitPhone" column="visitPhone"/>
        <result property="visitContent" column="visitContent"/>
        <result property="visitSummarize" column="visitSummarize"/>
        <result property="keyRecommendedProduct" column="keyRecommendedProduct"/>
        <result property="isBusiOppWilling" column="isBusiOppWilling"/>
        <result property="isNeedSupport" column="isNeedSupport"/>
        <result property="busiOppt" column="busiOppt"/>
        <result property="busiOppType" column="busiOppType"/>
        <result property="busiOpptName" column="busiOpptName"/>
        <result property="isInsertDiff" column="isInsertDiff"/>
        <result property="operator" column="operator"/>
        <result property="businessType" column="businessType"/>
        <result property="endDate" column="endDate"/>
        <result property="diffBusinessInfo" column="diffBusinessInfo"/>
        <result property="pictureUrls" column="pictureUrls"/>
        <result property="customerWillingness" column="customerWillingness"/>
        <result property="isRenewal" column="isRenewal"/>
        <result property="estimatedRecoveryTime" column="estimatedRecoveryTime"/>
        <result property="isPaymentSuccess" column="isPaymentSuccess"/>
        <result property="isWriteOff" column="isWriteOff"/>
        <result property="isRenewalSuccess" column="isRenewalSuccess"/>
        <result property="renewalContent" column="renewalContent"/>
        <result property="isOffLineTendency" column="isOffLineTendency"/>
        <result property="customerWillingnessForBusiness" column="customerWillingnessForBusiness"/>
        <result property="isCompleteRenewalForBusiness" column="isCompleteRenewalForBusiness"/>
        <result property="isOffLineTendencyForIncome" column="isOffLineTendencyForIncome"/>
        <result property="zeroProductionReason" column="zeroProductionReason"/>
        <result property="nextStep" column="nextStep"/>
        <result property="isBuildingCustomer" column="isBuildingCustomer"/>
        <result property="isModifyCustomerCheckInfo" column="isModifyCustomerCheckInfo"/>
        <result property="isVisitId" column="isVisitId"/>
        <result property="isValid" column="isValid"/>
        <result property="invalidReason" column="invalidReason"/>
        <result property="signInPlace" column="signInPlace"/>
        <result property="isSignIn" column="isSignIn"/>
        <result property="signInStartTime" column="signInStartTime"/>
        <result property="signInEndTime" column="signInEndTime"/>
        <result property="extraInfoLink" column="extraInfoLink"/>
        <result property="gmtCreate" column="gmtCreate"/>
        <result property="createUserId" column="createUserId"/>
        <result property="createUserName" column="createUserName"/>
        <result property="gmtModified" column="gmtModified"/>
        <result property="updateUserName" column="updateUserName"/>
        <result property="updateUserId" column="updateUserId"/>

        <result property="gemIsSignIn" column="gemIsSignIn"/>
        <result property="gemVisitTime" column="gemVisitTime"/>
        <result property="gemSignInStartTime" column="gemSignInStartTime"/>
        <result property="gemIsOppo" column="gemIsOppo"/>
        <result property="gemEstimatTotalContratAmount" column="gemEstimatTotalContratAmount"/>
        <result property="gemCustReq" column="gemCustReq"/>
        <result property="gemIsProject" column="gemIsProject"/>
    </resultMap>
    <sql id="cityStatisticsQuery">
        SELECT
            #{param.strategyId} AS strategyId,
            #{param.strategyName} AS strategyName,
            po.city_code AS city,
            COUNT(DISTINCT po.customer_id) AS customerCount,
            -- 客户经理相关指标
            SUM(CASE WHEN cd.OPPO_NUM > 0 THEN 1 ELSE 0 END) AS cmOpportunityCount,
            SUM(COALESCE(cd.OPPO_AMOUNT, 0)) AS cmOpportunityAmount,
            SUM(CASE WHEN cd.STATUS = '1' THEN 1 ELSE 0 END) AS cmPendingCount,
            SUM(CASE WHEN cd.STATUS = '4' THEN 1 ELSE 0 END) AS cmProcessingCount,
            SUM(CASE WHEN cd.STATUS IN ('2', '3', '5') THEN 1 ELSE 0 END) AS cmCompletedCount,
            -- 营销经理相关指标
            SUM(CASE WHEN wf.feedback_type = '1' THEN 1 ELSE 0 END) AS mmOpportunityCount,
            SUM(CASE WHEN wf.feedback_type = '1' THEN COALESCE(wf.project_scale, 0) ELSE 0 END) AS mmOpportunityAmount,
            SUM(CASE WHEN rto.marketing_manager_feedback_status = '4' THEN 1 ELSE 0 END) AS mmPendingCount,
            SUM(CASE WHEN rto.marketing_manager_feedback_status = '0' THEN 1 ELSE 0 END) AS mmProcessingCount,
            SUM(CASE WHEN rto.marketing_manager_feedback_status = '2' THEN 1 ELSE 0 END) AS mmCompletedCount
        FROM
            t_sanquan_strategy_result_todo_oppotunity rto
                LEFT JOIN t_sanquan_work_feedback wf ON rto.todo_code = wf.todo_code
                LEFT JOIN t_sanquan_potential_opportunity po ON rto.opportunity_id = po.id
                INNER JOIN t_sanquan_chengxiao_detail cd ON po.customer_id = cd.NATURE_CUST_ID
        WHERE
                rto.todo_code IN (
                SELECT id
                FROM t_sanquan_strategy_result_todo
                WHERE strategy_config_id = #{param.strategyId}
                  AND is_transfer = '0'
            )
          AND cd.MARKETING_NUMBER = #{param.zqytStrategyId}
          AND cd.MONTH_ID = #{monthId}
          AND cd.DAY_ID = #{dayId}
        GROUP BY
            po.city_code
        ORDER BY
            CASE
                WHEN po.city_code = '济南' THEN 1
                WHEN po.city_code = '青岛' THEN 2
                WHEN po.city_code = '淄博' THEN 3
                WHEN po.city_code = '枣庄' THEN 4
                WHEN po.city_code = '东营' THEN 5
                WHEN po.city_code = '烟台' THEN 6
                WHEN po.city_code = '潍坊' THEN 7
                WHEN po.city_code = '济宁' THEN 8
                WHEN po.city_code = '泰安' THEN 9
                WHEN po.city_code = '威海' THEN 10
                WHEN po.city_code = '日照' THEN 11
                WHEN po.city_code = '莱芜' THEN 12
                WHEN po.city_code = '临沂' THEN 13
                WHEN po.city_code = '德州' THEN 14
                WHEN po.city_code = '聊城' THEN 15
                WHEN po.city_code = '滨州' THEN 16
                WHEN po.city_code = '菏泽' THEN 17
                ELSE 18
                END
    </sql>
    <sql id="personStatisticsQuery">
        SELECT
                #{param.strategyId} AS strategyId,
                #{param.strategyName} AS strategyName,
                /*po.city_code AS city,*/
                rto.current_person AS marketingManager,
                COUNT(DISTINCT po.customer_id) AS customerCount,
                SUM(CASE WHEN wf.feedback_type = '1' THEN 1 ELSE 0 END) AS opportunityCount,
                SUM(CASE WHEN wf.feedback_type = '1' THEN wf.project_scale ELSE 0 END) AS opportunityAmount,

                SUM(CASE WHEN rto.marketing_manager_feedback_status = '4' THEN 1 ELSE 0 END) AS pendingCount,
                SUM(CASE WHEN rto.marketing_manager_feedback_status = '0' THEN 1 ELSE 0 END) AS processingCount,
                SUM(CASE WHEN rto.marketing_manager_feedback_status = '2' THEN 1 ELSE 0 END) AS completedCount
        FROM
            t_sanquan_strategy_result_todo_oppotunity rto
                LEFT JOIN t_sanquan_work_feedback wf ON rto.todo_code = wf.todo_code
                LEFT JOIN t_sanquan_potential_opportunity po ON rto.opportunity_id = po.id
        WHERE
            rto.todo_code IN (
                SELECT id
                FROM t_sanquan_strategy_result_todo
                WHERE strategy_config_id =  #{param.strategyId}
            )
            <if test="param.marketingManagerName != null and param.marketingManagerName != ''">
                AND rto.current_person like concat('%', #{param.marketingManagerName}, '%')
            </if>
            AND is_transfer = '0'
        GROUP BY
            rto.current_person
        ORDER BY
            CASE
                WHEN po.city_code = '济南' THEN 1
                WHEN po.city_code = '青岛' THEN 2
                WHEN po.city_code = '淄博' THEN 3
                WHEN po.city_code = '枣庄' THEN 4
                WHEN po.city_code = '东营' THEN 5
                WHEN po.city_code = '烟台' THEN 6
                WHEN po.city_code = '潍坊' THEN 7
                WHEN po.city_code = '济宁' THEN 8
                WHEN po.city_code = '泰安' THEN 9
                WHEN po.city_code = '威海' THEN 10
                WHEN po.city_code = '日照' THEN 11
                WHEN po.city_code = '莱芜' THEN 12
                WHEN po.city_code = '临沂' THEN 13
                WHEN po.city_code = '德州' THEN 14
                WHEN po.city_code = '聊城' THEN 15
                WHEN po.city_code = '滨州' THEN 16
                WHEN po.city_code = '菏泽' THEN 17
                ELSE 18
                END,
            rto.current_person
    </sql>
    <sql id="detailListQuery">
        SELECT
            conf.strategy_id AS strategyId,
            conf.strategy_name AS strategyName,
            -- 营销经理相关字段
            rto.todo_code AS todoCode,
            rto.opportunity_id AS opportunityId,
            po.customer_id AS customerId,
            po.customer_name AS customerName,
            po.customer_manager_id AS customerManagerId,
            po.customer_manager AS customerManager,
            po.executor_phone AS executorPhone,
            po.product_name AS productName,
            po.city_code as cityCode,
            rto.current_person AS marketingManager,
            rto.current_person_id AS marketingManagerId,
            CASE
                WHEN rto.marketing_manager_feedback_status = '2' THEN '关单'
                WHEN rto.marketing_manager_feedback_status = '0' THEN '执行中'
                WHEN rto.marketing_manager_feedback_status = '4' THEN '未执行'
            END AS marketingManagerFeedbackStatus,
            CASE
                WHEN wf.status = '2' THEN '关单'
                WHEN wf.status = '1' THEN '已查看'
                WHEN wf.status = '0' THEN '执行中'
                WHEN wf.status = '3' THEN '待获取商机编码'
            END AS executionStatus,
            -- 摸排信息
            si.visit_date AS visitDate,
            si.visit_mode AS visitMode,
            si.contact_type AS contactType,
            si.contact_name AS contactName,
            si.interview_feedback AS interviewFeedback,
            -- 商机信息
            CASE
                WHEN wf.feedback_type = '2' THEN '无法转商机'
                WHEN wf.feedback_type = '1' THEN '可转商机'
            END AS feedbackType,
            wf.feedback_content AS feedbackContent,
            wf.update_date AS updateDate,
            wf.close_time AS closeTime,
            wf.project_scale AS projectScale,
            wf.software_scale AS softwareScale,
            wf.gross_profit AS grossProfit,
            CASE
                WHEN wf.is_brace = '0' THEN '否'
                WHEN wf.is_brace = '1' THEN '是'
            END AS isBrace,
            wf.solution_supporter AS solutionSupporter,
            wf.support_time AS supportTime,
            wf.support_forms AS supportForms,
            wf.supplementary_explanation AS supplementaryExplanation,

            -- 客户经理相关字段
            gwo.ACTUAL_MANAGER_NO AS actualManagerNo,
            gwo.ACTUAL_MANAGER_NAME AS actualManagerName,
            gwo.ACTUAL_MANAGER_MOBILE AS actualManagerMobile,
            CASE
                WHEN gwo.STATUS = '1' THEN '待执行'
                WHEN gwo.STATUS = '2' THEN '已关单'
                WHEN gwo.STATUS = '3' THEN '待改派'
                WHEN gwo.STATUS = '4' THEN '跟进中'
                WHEN gwo.STATUS = '5' THEN '到期未执行'
            END AS status,
            gwo.BUSINESS_NUM AS businessNum,
            gwo.SIGN_NUM AS signNum,
            gwo.EXPECTED_INCOME AS expectedIncome,
            gwo.DEVELOP_NUM AS developNum,
            gwo.START_DATE AS startDate,
            gwo.BUSINESS_SCENE AS businessScene,
            wofi.TOUCH_ID AS touchId,
            wofi.VISIT_ID AS visitId,
            wofi.USER_CODE AS userCode,
            CASE
                WHEN wofi.VISIT_RESULT = '1' THEN '执行成功'
                WHEN wofi.VISIT_RESULT = '0' THEN '无法执行'
            END AS visitResult,
            wofi.BACK_TIME AS backTime,
            wofi.IS_CONTINUE_VISIT AS isContinueVisit,
            wofi.NO_VISIT_REASON AS noVisitReason,
            wofi.NEXT_TIME AS nextTime,
            wofi.VISIT_THEME AS visitTheme,
            wofi.VISIT_WAY AS visitWay,
            wofi.VISIT_TIME AS visitTime,
            wofi.VISIT_SITE AS visitSite,
            wofi.VISIT_PERSON AS visitPerson,
            wofi.VISIT_PHONE AS visitPhone,
            wofi.VISIT_CONTENT AS visitContent,
            wofi.VISIT_SUMMARIZE AS visitSummarize,
            wofi.KEY_RECOMMENDED_PRODUCT AS keyRecommendedProduct,
            wofi.IS_BUSI_OPP_WILLING AS isBusiOppWilling,
            wofi.IS_NEED_SUPPORT AS isNeedSupport,
            wofi.BUSI_OPPT AS busiOppt,
            wofi.BUSI_OPP_TYPE AS busiOppType,
            wofi.BUSI_OPPT_NAME AS busiOpptName,
            wofi.IS_INSERT_DIFF AS isInsertDiff,
            wofi.OPERATOR AS operator,
            wofi.BUSINESS_TYPE AS businessType,
            wofi.END_DATE AS endDate,
            wofi.DIFF_BUSINESS_INFO AS diffBusinessInfo,
            wofi.PICTURE_URLS AS pictureUrls,
            wofi.CUSTOMER_WILLINGNESS AS customerWillingness,
            wofi.IS_RENEWAL AS isRenewal,
            wofi.ESTIMATED_RECOVERY_TIME AS estimatedRecoveryTime,
            wofi.IS_PAYMENT_SUCCESS AS isPaymentSuccess,
            wofi.IS_WRITE_OFF AS isWriteOff,
            wofi.IS_RENEWAL_SUCCESS AS isRenewalSuccess,
            wofi.RENEWAL_CONTENT AS renewalContent,
            wofi.IS_OFF_LINE_TENDENCY AS isOffLineTendency,
            wofi.CUSTOMER_WILLINGNESS_FOR_BUSINESS AS customerWillingnessForBusiness,
            wofi.IS_COMPLETE_RENEWAL_FOR_BUSINESS AS isCompleteRenewalForBusiness,
            wofi.IS_OFF_LINE_TENDENCY_FOR_INCOME AS isOffLineTendencyForIncome,
            wofi.ZERO_PRODUCTION_REASON AS zeroProductionReason,
            wofi.NEXT_STEP AS nextStep,
            wofi.IS_BUILDING_CUSTOMER AS isBuildingCustomer,
            wofi.IS_MODIFY_CUSTOMER_CHECK_INFO AS isModifyCustomerCheckInfo,
            wofi.IS_VISIT_ID AS isVisitId,
            wofi.IS_VALID AS isValid,
            wofi.INVALID_REASON AS invalidReason,
            wofi.SIGN_IN_PLACE AS signInPlace,
            if(wofi.SIGN_IN_START_TIME is not null,'是','否') AS isSignIn,
            wofi.SIGN_IN_START_TIME AS signInStartTime,
            wofi.SIGN_IN_END_TIME AS signInEndTime,
            wofi.EXTRA_INFO_LINK AS extraInfoLink,
            wofi.GMT_CREATE AS gmtCreate,
            wofi.CREATE_USER_ID AS createUserId,
            wofi.CREATE_USER_NAME AS createUserName,
            wofi.GMT_MODIFIED AS gmtModified,
            wofi.UPDATE_USER_NAME AS updateUserName,
            wofi.UPDATE_USER_ID AS updateUserId,

            if(gem.is_sign_in = '1','是','否') as gemIsSignIn,
            gem.VISIT_TIME as gemVisitTime,
            gem.sign_in_start_time as gemSignInStartTime,
            if(gem.is_oppo  = '1','是','否') as gemIsOppo,
            gem.estimat_total_contrat_amount as gemEstimatTotalContratAmount,
            gem.cust_req as gemCustReq,
            if(gem.is_project  = '1','是','否') as gemIsProject
        FROM
            t_sanquan_strategy_result_todo_oppotunity rto
            LEFT JOIN t_sanquan_work_feedback wf ON rto.todo_code = wf.todo_code
            --   LEFT JOIN t_sanquan_strategy_interview si ON rto.todo_code = si.todo_code
            left join t_sanquan_strategy_interview si ON wf.id = si.feedback_id
            left join t_sanquan_potential_opportunity po ON rto.opportunity_id = po.id
            inner join sanquan_activity_report_config conf on po.strategy_id = conf.strategy_id
            inner join t_sanquan_d_zqzt_gem_work_order gwo ON po.customer_id = gwo.CUSTOM_ID and gwo.SUPERIOR_POLICY_CODE = conf.zqyt_strategy_id
            left join (select * from (select *,row_number() over (partition by work_order_code order by back_time desc) as rn from t_sanquan_d_zqzt_gem_work_order_feedback_info) t where t.rn = 1) wofi ON gwo.CODE = wofi.WORK_ORDER_CODE
            left join (select work_order_code, record_id, visit_time, sign_in_start_time, is_sign_in, is_oppo, bussiness_id, bussiness_name, estimat_total_contrat_amount, cust_req, is_project from (select *,row_number() over (partition by work_order_code order by visit_time desc) as rn from T_SANQUAN_GEM_VISIT) t where t.rn = 1) gem on gwo.CODE = gem.work_order_code
        WHERE
            rto.is_transfer = '0'
            <if test="param.strategyIds != null and !param.strategyIds.isEmpty()">
                and po.strategy_id IN
                <foreach item="id" collection="param.strategyIds" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="param.customerName != null and param.customerName != ''">
                AND po.customer_name like concat('%', #{param.customerName}, '%')
            </if>
        ORDER BY
            po.customer_id
    </sql>
    <select id="selectMaxReportDate" resultType="java.lang.String">
        select concat(substr(month_id,1,4),'-',substr(month_id,5,6),'-',max(day_id)) as reportDate
        from t_sanquan_chengxiao_detail
        where month_id = (select max(month_id)  from t_sanquan_chengxiao_detail)
    </select>
    <select id="selectCityStatisticsList" resultMap="ActivityReportCityStatisticsResult">
        <bind name="monthId" value="param.monthId" />
        <bind name="dayId" value="param.dayId" />
        <include refid="cityStatisticsQuery" />
    </select>
    <select id="selectAllCityStatistics" resultMap="ActivityReportCityStatisticsResult">
        <bind name="monthId" value="param.monthId" />
        <bind name="dayId" value="param.dayId" />
        <include refid="cityStatisticsQuery" />
    </select>
    <select id="selectPersonStatisticsList" resultMap="ActivityReportPersonStatisticsResult">
        <include refid="personStatisticsQuery" />
    </select>
    <select id="selectAllPersonStatistics" resultMap="ActivityReportPersonStatisticsResult">
        <include refid="personStatisticsQuery" />
    </select>
    <select id="selectDetailList" resultMap="ActivityReportDetailResult">
        <include refid="detailListQuery" />
    </select>
    <select id="selectAllDetailList" resultMap="ActivityReportDetailResult">
        <include refid="detailListQuery" />
    </select>
</mapper>
