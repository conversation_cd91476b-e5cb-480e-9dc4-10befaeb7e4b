<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.chinaunicom.sdsi.aiflow.mapper.AiflowConfMapper">

    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.aiflow.entity.AiflowConf">
        <id column="id" property="id"/>
        <result column="model_code" property="modelCode"/>
        <result column="model_name" property="modelName"/>
        <result column="prompt_id" property="promptId"/>
        <result column="prompt_name" property="promptName"/>
        <result column="insight_id" property="insightId"/>
        <result column="insight_name" property="insightName"/>
        <result column="customer_tags" property="customerTags"/>
        <result column="selected_tags" property="selectedTags"/>
        <result column="keyword_count" property="keywordCount"/>
        <result column="match_threshold" property="matchThreshold"/>
        <result column="frequency" property="frequency"/>
        <result column="execute_time" property="executeTime"/>
        <result column="corn" property="corn"/>
        <result column="job_id" property="jobId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t0.id, model_code, model_name, t0.prompt_id, t0.insight_id, selected_tags, keyword_count,
        match_threshold, frequency,execute_time, corn, job_id, t0.create_time
    </sql>

    <select id="getAiflowConfByConfigId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_customer_aiflow_conf t0
        WHERE id = #{configId}
        LIMIT 1
    </select>

    <select id="getAiflowConfByModelId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
            ,t1.prompt_name,t2.insight_name,t2.customer_tags
        FROM t_sanquan_customer_aiflow_conf t0
        left join t_sanquan_prompt t1 on t0.prompt_id = t1.id
        left join t_sanquan_customer_insight_info t2 on t0.insight_id = t2.insight_id
        WHERE model_code = #{modelCode}
        LIMIT 1
    </select>

    <select id="selectAiflowConfList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_customer_aiflow_conf
        <where>
            <if test="aiflowConf.modelCode != null and aiflowConf.modelCode != ''">
                AND model_code = #{aiflowConf.modelCode}
            </if>
            <if test="aiflowConf.modelName != null and aiflowConf.modelName != ''">
                AND model_name LIKE CONCAT('%', #{aiflowConf.modelName}, '%')
            </if>
            <if test="aiflowConf.promptId != null and aiflowConf.promptId != ''">
                AND prompt_id = #{aiflowConf.promptId}
            </if>
            <if test="aiflowConf.insightId != null and aiflowConf.insightId != ''">
                AND insight_id = #{aiflowConf.insightId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <insert id="insertAIConf">
        INSERT INTO t_sanquan_customer_aiflow_conf(model_code, model_name, prompt_id, insight_id, selected_tags, keyword_count, match_threshold, frequency, execute_time)
        VALUES (#{param.modelCode}, #{param.modelName}, #{param.promptId}, #{param.insightId}, #{param.selectedTags}, #{param.keywordCount}, #{param.matchThreshold}, #{param.frequency}, #{param.executeTime})
    </insert>

    <delete id="deleteAIConfByModelCode">
        delete from t_sanquan_customer_aiflow_conf
        where model_code = #{modelCode}
    </delete>

    <update id="updateAIConfByModelCode">
        UPDATE t_sanquan_customer_aiflow_conf
        <set>
            <if test="param.modelName != null and param.modelName != ''">
                model_name = #{param.modelName},
            </if>
            <if test="param.promptId != null and param.promptId != ''">
                prompt_id = #{param.promptId},
            </if>
            <if test="param.insightId != null and param.insightId != ''">
                insight_id = #{param.insightId},
            </if>
            <if test="param.selectedTags != null and param.selectedTags != ''">
                selected_tags = #{param.selectedTags},
            </if>
            <if test="param.keywordCount != null and param.keywordCount != ''">
                keyword_count = #{param.keywordCount},
            </if>
            <if test="param.matchThreshold != null and param.matchThreshold != ''">
                match_threshold = #{param.matchThreshold},
            </if>
            <if test="param.frequency != null and param.frequency != ''">
                frequency = #{param.frequency},
            </if>
            <if test="param.executeTime != null and param.executeTime != ''">
                execute_time = #{param.executeTime},
            </if>
            <if test="param.corn != null and param.corn != ''">
                corn = #{param.corn},
            </if>
            <if test="param.jobId != null and param.jobId != ''">
                job_id = #{param.jobId},
            </if>
        </set>
        WHERE model_code = #{param.modelCode}
    </update>
</mapper>
