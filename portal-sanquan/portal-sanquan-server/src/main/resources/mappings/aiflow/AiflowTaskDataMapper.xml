<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.chinaunicom.sdsi.aiflow.mapper.AiflowTaskDataMapper">

    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.aiflow.entity.AiflowTaskData">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="model_code" property="modelCode"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_insightinfo" property="customerInsightinfo"/>
        <result column="process" property="process"/>
        <result column="keywords" property="keywords"/>
        <result column="recommend_product" property="recommendProduct"/>
        <result column="recommend_product_accurate" property="recommendProductAccurate"/>
        <result column="status" property="status"/>
        <result column="remark" property="remark"/>
        <result column="timest" property="timest"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, task_id, model_code, customer_id, customer_name, customer_insightinfo, process, keywords,
        recommend_product, recommend_product_accurate, status, remark,timest, create_time, update_time
    </sql>

    <select id="selectAiflowTaskDataTimest" resultType="java.lang.String">
        select distinct timest from t_sanquan_customer_aiflow_task_data
        <where>
            <if test="param.modelCode != null and param.modelCode != ''">
                AND model_code = #{param.modelCode}
            </if>
        </where>
        order by cast(timest as decimal) desc;
    </select>

    <select id="selectAllAiflowTaskData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_customer_aiflow_task_data
        <where>
            <if test="param.modelCode != null and param.modelCode != ''">
                AND model_code = #{param.modelCode}
            </if>
            <if test="param.timest != null and param.timest != ''">
                AND timest = #{param.timest}
            </if>
            <if test="param.customerName != null and param.customerName != ''">
                AND customer_name like concat('%',#{param.customerName},'%')
            </if>
            <if test="param.status != null and param.status != ''">
                AND status = #{param.status}
            </if>
        </where>
        order by create_time
    </select>

    <select id="selectAiflowTaskData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_customer_aiflow_task_data
        <where>
            <if test="param.modelCode != null and param.modelCode != ''">
                AND model_code = #{param.modelCode}
            </if>
            <if test="param.timest != null and param.timest != ''">
                AND timest = #{param.timest}
            </if>
            <if test="param.customerName != null and param.customerName != ''">
                AND customer_name like concat('%',#{param.customerName},'%')
            </if>
            <if test="param.status != null and param.status != ''">
                AND status = #{param.status}
            </if>
        </where>
        order by create_time
    </select>

    <select id="selectPendingDataByTaskId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_customer_aiflow_task_data
        WHERE task_id = #{taskId} AND status = '0'
        ORDER BY create_time DESC
    </select>

    <delete id="deleteByModelCode">
        DELETE FROM t_sanquan_customer_aiflow_task_data
        WHERE model_code = #{modelCode}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_sanquan_customer_aiflow_task_data(
            task_id, model_code, customer_id, customer_name, customer_insightinfo,timest
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.taskId}, #{item.modelCode}, #{item.customerId}, #{item.customerName},
            #{item.customerInsightinfo},#{item.timest}
            )
        </foreach>
    </insert>

    <update id="updateTaskDataById">
        UPDATE t_sanquan_customer_aiflow_task_data
        <set>
            <if test="taskData.process != null and taskData.process != ''">
                process = #{taskData.process},
            </if>
            <if test="taskData.keywords != null and taskData.keywords != ''">
                keywords = #{taskData.keywords},
            </if>
            <if test="taskData.recommendProduct != null and taskData.recommendProduct != ''">
                recommend_product = #{taskData.recommendProduct},
            </if>
            <if test="taskData.recommendProductAccurate != null and taskData.recommendProductAccurate != ''">
                recommend_product_accurate = #{taskData.recommendProductAccurate},
            </if>
            <if test="taskData.status != null and taskData.status != ''">
                status = #{taskData.status},
            </if>
            <if test="taskData.remark != null and taskData.remark != ''">
                remark = #{taskData.remark},
            </if>
        </set>
        WHERE id = #{taskData.id}
    </update>

    <delete id="deleteTaskDataByModelCodeAndTimest">
        DELETE FROM t_sanquan_customer_aiflow_task_data
        WHERE model_code = #{modelCode} AND timest = #{timest}
    </delete>

</mapper>
