<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.aiflow.mapper.CustomerInsightDataMapper">
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.aiflow.entity.CustomerInsightData">
        <result column="insight_id" property="insightId" />
        <result column="customer_id" property="customerId" />
        <result column="customer_name" property="customerName" />
        <result column="customer_insightinfo" property="customerInsightInfo" />
    </resultMap>

    <sql id="Base_Column_List">
        insight_id, customer_id, customer_name, customer_insightinfo
    </sql>

    <select id="selectAllCustomerInsightDataByInsightId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_customer_insight_data
        WHERE insight_id = #{insightId}
    </select>

    <select id="selectCustomerInsightData" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_sanquan_customer_insight_data
        <where>
            insight_id = #{insightId}
            <if test="param.customerName != null and param.customerName != ''">
                and  customer_name LIKE CONCAT('%', #{param.customerName}, '%')
            </if>
        </where>
        ORDER BY create_time
    </select>

    <insert id="batchInsertCustomerInsightData">
        insert into t_sanquan_customer_insight_data (insight_id, customer_id, customer_name, customer_insightinfo)
        values
        <foreach item="item" collection="list" separator="," index="index">
            (#{item.insightId}, #{item.customerId}, #{item.customerName}, #{item.customerInsightInfo})
        </foreach>
    </insert>

    <delete id="deleteAllCustomerInsightDataByIds">
        delete from t_sanquan_customer_insight_data
        where insight_id in
        <foreach item="id" collection="insightIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteCustomerInsightDataByIdAndStatus">
        delete from t_sanquan_customer_insight_data
        where insight_id = #{insightId} and status = #{status}
    </delete>

    <update id="updateDataStatusById">
        update t_sanquan_customer_insight_data
        set status = #{status}
        where insight_id = #{insightId}
    </update>
</mapper>
