<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.emailConfig.mapper.EmailPersonMapper">

     <select id="getPersonList" resultType="cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonVO">
         select p.id,p.job_num,p.email,p.city,u.name as userName,u.industry
         <if test='query.dataType == "2" '>
             ,c.district_name
         </if>
         from t_sanquan_email_person p
         join t_sanquan_platform_user u on p.job_num = u.job_num and u.deleted = 'normal'
         <if test='query.dataType == "2" '>
             join (select distinct oa,district_name from t_sanquan_permission_config c where DELETED_ = 'normal') c
             on p.job_num = c.oa
         </if>
         where p.task_id = #{query.taskId} and p.status = 1
     </select>

    <select id="getPageList" resultType="cn.chinaunicom.sdsi.emailConfig.vo.EmailPersonVO">
        select p.id,p.task_id,p.job_num,p.email,p.status,p.city,p.district as districtName,u.name as user_name,c.task_name from t_sanquan_email_person p
        join t_sanquan_platform_user u on p.job_num = u.job_num and u.deleted = 'normal'
        join t_sanquan_email_config c on p.task_id = c.id
        <where> c.status = 1
            <if test="query.city != null and query.city != '' ">
                and p.city = #{query.city}
            </if>
            <if test="query.status != null and query.status != '' ">
                and p.status = #{query.status}
            </if>
            <if test="query.taskName != null and query.taskName != '' ">
                and c.task_name like concat('%',#{query.taskName},'%')
            </if>
        </where>
        order by p.create_date desc
    </select>

    <select id="getConfigUserList" resultType="cn.chinaunicom.sdsi.emailConfig.entity.EmailPerson">
        select distinct u.id,u.name as userName,u.job_num,u.city,c.district_name as district from t_sanquan_email_person p
        right join t_sanquan_platform_user u on p.job_num = u.job_num and p.task_id = #{query.taskId}
        left join (select distinct oa,district_name from t_sanquan_permission_config c where DELETED_ = 'normal') c
             on u.job_num = c.oa
        where  u.deleted = 'normal' and p.id is null
        <if test="query.jobNum != null and query.jobNum != ''">
            and u.job_num like concat('%',#{query.jobNum},'%')
        </if>
        <if test="query.userName != null and query.userName != ''">
            and u.user_name like concat('%',#{query.userName},'%')
        </if>
    </select>


</mapper>