<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.shuangxianCheck.mapper.ShuangxianCheckMsgMapper">

    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.shuangxianCheck.vo.ShuangxianCheckMsgVO">
        <id column="id" property="id" />
        <result column="developer_id" property="developerId" />
        <result column="account_code" property="accountCode" />
        <result column="dev_name" property="devName" />
        <result column="linkman_phone" property="linkmanPhone" />
        <result column="product_name" property="productName" />
    </resultMap>
    <!-- ,account_code,dev_name,linkman_phone,product_name -->
    <select id="selectListNew" resultMap="BaseResultMap">
        select developer_id,account_code,dev_name,linkman_phone,product_name
        , GROUP_CONCAT(cust_name,device_number ORDER BY developer_id,PRODUCT_NAME SEPARATOR ', ') AS content
        from t_sanquan_shuangxian_check_msg
        <where> 1=1
            <if test="query.dayId != null">
                and day_id = #{query.dayId}
            </if>
            <if test="query.monthId != null">
                and month_id = #{query.monthId}
            </if>
        </where>
        group by linkman_phone,product_name
        order by DEVELOPER_ID,PRODUCT_NAME
    </select>
    <select id="validSendMsgByDay" resultMap="BaseResultMap">
        select * from t_sanquan_sms_log
        where business_type = #{query.businessType} and send_time BETWEEN #{query.startTime} and #{query.endTime}
          and telphone = #{query.linkmanPhone} and LOCATE(#{query.content}, send_content) > 0 and is_send = 1
    </select>
</mapper>
