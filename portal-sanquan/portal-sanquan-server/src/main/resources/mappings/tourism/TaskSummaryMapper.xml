<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.tourism.mapper.TaskSummaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.tourism.entity.TaskSummary">
        <result column="city" property="city" />
        <result column="transfer_rate" property="transferRate" />
        <result column="zhixing_rate" property="zhixingRate" />
        <result column="order_num" property="orderNum" />
        <result column="unzhixing_num" property="unzhixingNum" />
        <result column="zhixing_num" property="zhixingNum" />
        <result column="end_num" property="endNum" />
        <result column="oppo_num" property="oppoNum" />
        <result column="oppo_price" property="oppoPrice" />
        <result column="project_num" property="projectNum" />
        <result column="project_price" property="projectPrice" />
        <result column="task_type" property="taskType" />
        <result column="industry" property="industry" />
    </resultMap>

    <resultMap id="BaseDetailResultMap" type="cn.chinaunicom.sdsi.tourism.entity.TaskDetail">
        <result column="SUPERIOR_POLICY_CODE" property="superiorPolicyCode" />
        <result column="SUPERIOR_POLICY_CREATE_USER_NAME" property="superiorPolicyCreateName" />
        <result column="CUSTOM_NAME" property="customerName" />
        <result column="REGION_AREA_NAME" property="customerCity" />
        <result column="B_DOMAIN_ORG_NAME" property="managerOrg" />
        <result column="ACTUAL_MANAGER_NAME" property="areaManager" />
        <result column="MANAGER_NAME" property="managerName" />
        <result column="MANAGER_NO" property="managerOA" />
        <result column="MANAGER_MOBILE" property="managerTel" />

        <result column="PROJECTNAME" property="projectName" />
        <result column="INDUSTRY" property="industry" />
        <result column="OPPO_URL" property="oppoUrl" />
        <result column="REMARK" property="oppoDes" />
        <result column="GMT_CREATE" property="taskIssueTime" />
        <result column="DEADLINE" property="executeEndTime" />
        <result column="BACK_TIME" property="feedbackTime" />
        <result column="STATUS" property="taskStatus" />

        <result column="customerNum" property="customerNum" />
        <result column="contacts" property="contacts" />
        <result column="contactsLevel" property="contactsLevel" />
        <result column="contactsAssessment" property="contactsAssessment" />
        <result column="mobileNet" property="mobileNet" />
        <result column="fixNet" property="fixNet" />
        <result column="projectStage" property="projectStage" />
        <result column="oppoStatus" property="oppoStatus" />
        <result column="oppoRemark" property="oppoRemark" />
        <result column="oppoBuildContent" property="oppoBuildContent" />
        <result column="oppoType" property="oppoType" />
        <result column="yidongJoin" property="yidongJoin" />
        <result column="dianxinJoin" property="dianxinJoin" />
        <result column="oppoCode" property="oppoCode" />
        <result column="oppo_name" property="oppoName" />

        <result column="list_name" property="listName" />
        <result column="industry_name" property="industryName" />
        <result column="oppoPreCheck" property="oppoPreCheck" />
        <result column="execute_status" property="executeStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        city, transfer_rate, zhixing_rate, order_num, unzhixing_num, zhixing_num, end_num, oppo_num, oppo_price,
        project_num, project_price, task_type, industry, oppoPreCheck
    </sql>

    <select id="getSummaryData" resultMap="BaseResultMap">
        select
        city,
        sum(oppo_num)/sum(order_num) as transfer_rate, sum(end_num)/sum(order_num) as zhixing_rate, sum(order_num) order_num, sum(unzhixing_num) unzhixing_num, sum(zhixing_num) zhixing_num,
        sum(end_num) end_num, sum(oppo_num) oppo_num, sum(oppo_price) oppo_price, sum(project_num) project_num, sum(project_price) project_price,
        task_type, industry
        from t_sanquan_task_summary
        where task_type = #{taskType}
        <if test="city != null and city != '' ">
            and city = #{city}
        </if>
        <if test="citys != null and citys.size() > 0">
            and city in
            <foreach item="item" index="index" collection="citys" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="industrys != null and industrys.size() > 0">
            and industry in
            <foreach item="item" index="index" collection="industrys" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by city
    </select>
    <!--,if((status = '2' or !(EFFECT_BACK_TIME is null or EFFECT_BACK_TIME = 'null' or EFFECT_BACK_TIME = '') != 0),'已执行','未执行') as execute_status-->
     <select id="getTaskDetail" resultMap="BaseDetailResultMap">
         select a.SUPERIOR_POLICY_CODE, a.SUPERIOR_POLICY_CREATE_USER_NAME, a.CUSTOM_NAME,a.B_DOMAIN_ORG_NAME as B_DOMAIN_ORG_NAME,
         if(a.REGION_PROV_NAME = 'null',null,a.REGION_PROV_NAME) REGION_PROV_NAME,
         if(a.REGION_AREA_NAME = 'null',null,a.REGION_AREA_NAME) REGION_AREA_NAME,
         a.ORG_NAME, a.MANAGER_NAME, a.MANAGER_NO, a.MANAGER_MOBILE,
        a.GMT_CREATE, a.DEADLINE ,if(a.BACK_TIME = 'null',null,a.BACK_TIME) BACK_TIME, a.STATUS,
        ppo.*,ppt.*,ppl.OPPO_NAME,ppi.list_name,ppi.industry_name,a.ACTUAL_MANAGER_NAME
         ,if((BACK_TIME is null or BACK_TIME = 'null' or BACK_TIME = ''),'未执行','已执行') as execute_status
         ,EFFECT_BACK_TIME,status
         from t_sanquan_d_zqzt_gem_work_order a
        left join (
            select a.code,a.id,
              max(if(b.FIELD_CODE = 'textField_jemzIdb0',b.FIELD_VALUE,'')) as 'customerNum',
              max(if(b.FIELD_CODE = 'textField_362FDhQa',b.FIELD_VALUE,'')) as 'contacts',
              max(if(b.FIELD_CODE = 'radioField_EVcgZHkY',b.FIELD_VALUE,'')) as 'contactsLevel',
              max(if(b.FIELD_CODE = 'radioField_lAnpes7N',b.FIELD_VALUE,'')) as 'contactsAssessment',
              max(if(b.FIELD_CODE = 'radioField_sjtSwod9',b.FIELD_VALUE,'')) as 'mobileNet',
              max(if(b.FIELD_CODE = 'radioField_WSylKDok',b.FIELD_VALUE,'')) as 'fixNet',
              max(if(b.FIELD_CODE = 'radioField_7pbfAhA9',b.FIELD_VALUE,'')) as 'projectStage',
              max(if(b.FIELD_CODE = 'radioField_IAHIC56U',b.FIELD_VALUE,'')) as 'oppoStatus',
              max(if(b.FIELD_CODE = 'textareaField_skEXmzj0',b.FIELD_VALUE,'')) as 'oppoRemark',
              max(if(b.FIELD_CODE = 'radioField_ygfqBLtc',b.FIELD_VALUE,'')) as 'oppoBuildContent',
              max(if(b.FIELD_CODE = 'checkboxField_uDS6UE5U',b.FIELD_VALUE,'')) as 'oppoType',
              max(if(b.FIELD_CODE = 'radioField_YYN02Ncy',b.FIELD_VALUE,'')) as 'yidongJoin',
              max(if(b.FIELD_CODE = 'radioField_FVWRjgjK',b.FIELD_VALUE,'')) as 'dianxinJoin',
              max(if(b.FIELD_CODE = 'textField_gksX7ZxX',b.FIELD_VALUE,'')) as 'oppoCode',
              max(if(b.FIELD_CODE = 'textareaField_W86UatUS',b.FIELD_VALUE,'')) as 'oppoPreCheck'
            from t_sanquan_d_zqzt_gem_work_order a
            left join t_sanquan_d_mrt_zqzt_gem_fdyinfo b on a.code = b.WORK_ORDER_CODE
            group by a.code,a.id
        ) ppo on a.id = ppo.id
        left join (
           select a.code,a.id,
              max(if(b.property_code = 'project_name_all',b.property_value,'')) as 'PROJECTNAME',
              max(if(b.property_code = 'segments',b.property_value,'')) as 'INDUSTRY',
              max(if(b.property_code = 'product_name1',b.property_value,'')) as 'OPPO_URL',
              max(if(b.property_code = 'industry_mainly_product',b.property_value,'')) as 'REMARK'
           from t_sanquan_d_zqzt_gem_work_order a
           left join t_sanquan_d_zqzt_work_order_type_prop_info b on a.code = b.WORK_ORDER_CODE
           group by a.code,a.id
        ) ppt on a.id = ppt.id
         left join t_sanquan_summary_OPPO_POOLS ppl on ppo.oppoCode = ppl.OPPO_NUMBER
         left join t_sanquan_summary_LIST_CUSTOMER_INFO ppi on a.CUSTOM_ID = ppi.cust_id
         where
             a.SUPERIOR_POLICY_CODE = 'P17202410080027' and a.custom_name not like '%公安%'
             <if test="city != null and city != '' ">
                 and (a.B_DOMAIN_CITY_NAME = #{city} )
             </if>
             <if test="country != null and country != '' and country != 'noZhanke'">
                 and a.B_DOMAIN_COUNTY_NAME = #{country}
             </if>
             <if test="country != null and country == 'noZhanke'">
                 and (a.B_DOMAIN_COUNTY_NAME is null or B_DOMAIN_COUNTY_NAME = '' or B_DOMAIN_COUNTY_NAME = 'null')
             </if>
     </select>

    <select id="getIndustryDetail" resultMap="BaseDetailResultMap">
        select a.SUPERIOR_POLICY_CODE, a.SUPERIOR_POLICY_CREATE_USER_NAME, a.CUSTOM_NAME,a.B_DOMAIN_ORG_NAME as B_DOMAIN_ORG_NAME,
        if(a.REGION_PROV_NAME = 'null',null,a.REGION_PROV_NAME) REGION_PROV_NAME,
        if(a.REGION_AREA_NAME = 'null',null,a.REGION_AREA_NAME) REGION_AREA_NAME,
        a.ORG_NAME, a.MANAGER_NAME, a.MANAGER_NO, a.MANAGER_MOBILE,
        a.GMT_CREATE, a.DEADLINE ,if(a.BACK_TIME = 'null',null,a.BACK_TIME) BACK_TIME, a.STATUS,
        ppo.*,ppt.*,ppl.OPPO_NAME,ppi.list_name,ppi.industry_name,a.ACTUAL_MANAGER_NAME
        ,if((BACK_TIME is null or BACK_TIME = 'null' or BACK_TIME = ''),'未执行','已执行') as execute_status
        ,EFFECT_BACK_TIME,status
        from t_sanquan_d_zqzt_gem_work_order a
        left join (
        select a.code,a.id,
        max(if(b.FIELD_CODE = 'textField_jemzIdb0',b.FIELD_VALUE,'')) as 'customerNum',
        max(if(b.FIELD_CODE = 'textField_362FDhQa',b.FIELD_VALUE,'')) as 'contacts',
        max(if(b.FIELD_CODE = 'radioField_EVcgZHkY',b.FIELD_VALUE,'')) as 'contactsLevel',
        max(if(b.FIELD_CODE = 'radioField_lAnpes7N',b.FIELD_VALUE,'')) as 'contactsAssessment',
        max(if(b.FIELD_CODE = 'radioField_sjtSwod9',b.FIELD_VALUE,'')) as 'mobileNet',
        max(if(b.FIELD_CODE = 'radioField_WSylKDok',b.FIELD_VALUE,'')) as 'fixNet',
        max(if(b.FIELD_CODE = 'radioField_7pbfAhA9',b.FIELD_VALUE,'')) as 'projectStage',
        max(if(b.FIELD_CODE = 'radioField_IAHIC56U',b.FIELD_VALUE,'')) as 'oppoStatus',
        max(if(b.FIELD_CODE = 'textareaField_skEXmzj0',b.FIELD_VALUE,'')) as 'oppoRemark',
        max(if(b.FIELD_CODE = 'radioField_ygfqBLtc',b.FIELD_VALUE,'')) as 'oppoBuildContent',
        max(if(b.FIELD_CODE = 'checkboxField_uDS6UE5U',b.FIELD_VALUE,'')) as 'oppoType',
        max(if(b.FIELD_CODE = 'radioField_YYN02Ncy',b.FIELD_VALUE,'')) as 'yidongJoin',
        max(if(b.FIELD_CODE = 'radioField_FVWRjgjK',b.FIELD_VALUE,'')) as 'dianxinJoin',
        max(if(b.FIELD_CODE = 'textField_gksX7ZxX',b.FIELD_VALUE,'')) as 'oppoCode',
        max(if(b.FIELD_CODE = 'textareaField_W86UatUS',b.FIELD_VALUE,'')) as 'oppoPreCheck'
        from t_sanquan_d_zqzt_gem_work_order a
        left join t_sanquan_d_mrt_zqzt_gem_fdyinfo b on a.code = b.WORK_ORDER_CODE
        group by a.code,a.id
        ) ppo on a.id = ppo.id
        left join (
        select a.code,a.id,
        max(if(b.property_code = 'project_name_all',b.property_value,'')) as 'PROJECTNAME',
        max(if(b.property_code = 'segments',b.property_value,'')) as 'INDUSTRY',
        max(if(b.property_code = 'product_name1',b.property_value,'')) as 'OPPO_URL',
        max(if(b.property_code = 'industry_mainly_product',b.property_value,'')) as 'REMARK'
        from t_sanquan_d_zqzt_gem_work_order a
        left join t_sanquan_d_zqzt_work_order_type_prop_info b on a.code = b.WORK_ORDER_CODE
        group by a.code,a.id
        <if test="industry != null and industry.size()>0">
            having industry in
            <foreach item="item" index="index" collection="industry" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) ppt on a.id = ppt.id
        left join t_sanquan_summary_OPPO_POOLS ppl on ppo.oppoCode = ppl.OPPO_NUMBER
        left join t_sanquan_summary_LIST_CUSTOMER_INFO ppi on a.CUSTOM_ID = ppi.cust_id
        where
        a.SUPERIOR_POLICY_CODE = 'P17202410080027' and a.custom_name not like '%公安%'
        <if test="industry != null and industry.size()>0">
            and ppt.industry in
            <foreach item="item" index="index" collection="industry" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

</mapper>
