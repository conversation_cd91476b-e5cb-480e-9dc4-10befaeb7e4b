<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.tourism.mapper.EmailLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.tourism.entity.EmailLog">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="position" property="position" />
        <result column="status" property="status" />
        <result column="email" property="email" />
        <result column="cycle" property="cycle" />
        <result column="city" property="city" />
        <result column="country" property="country" />
        <result column="cycle_type" property="cycleType" />
        <result column="content" property="content" />
        <result column="file_path" property="filePath" />
        <result column="create_date" property="createDate" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="update_date" property="updateDate" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, user_name, position, status, email, cycle, content, file_path, create_date, create_by, update_by, update_date, remark,city,cycle_type
    </sql>

    <select id="findCity" resultType="java.lang.String">
        select city from t_sanquan_tourism group by city
    </select>

    <select id="getExecutorSql" resultType="java.lang.String">
        select execute_sql from t_sanquan_push_data_hand where status = 1
    </select>

    <select id="executeScheduleSql" resultType="java.util.LinkedHashMap">
        ${executeSql}
    </select>


</mapper>
