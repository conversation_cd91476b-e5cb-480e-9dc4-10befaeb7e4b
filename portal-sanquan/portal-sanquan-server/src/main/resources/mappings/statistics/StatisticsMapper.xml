<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.statistics.mapper.StatisticsMapper">

    <select id="findStatistics"
            resultType="cn.chinaunicom.sdsi.statistics.entity.StatisticsResult">

        select a.*,ifnull(b.hasCount,0) hasCount,IFNULL(a.counts,0)-ifnull(b.hasCount,0) noCount from (select
        a.list_customer_city,a.customer_id,a.list_customer_district,count(a.customer_id) as counts from
        t_sanquan_customer_info a where a.delete_flag='normal' and list_customer_district is not null GROUP BY a.list_customer_city,a.list_customer_district) a
        LEFT JOIN
        (SELECT count(*) hasCount, b.list_customer_district,b.list_customer_city 	FROM
        (SELECT	a.customer_id,b.list_customer_city,b.list_customer_district FROM	t_sanquan_customer_info_audit a,	t_sanquan_customer_info b
        WHERE	a.customer_id = b.customer_id  and a.is_passed is not null and a.delete_flag='normal'
        GROUP BY a.customer_id ) b
        GROUP BY b.list_customer_city,b.list_customer_district ) b

        on a.list_customer_city=b.list_customer_city and a.list_customer_district=b.list_customer_district
        <where>
            1=1
            <if test="query.city != null and query.city != ''">
                AND a.list_customer_city = #{query.city}
            </if>
            <if test="query.district != null and query.district != ''">
                AND a.list_customer_district = #{query.district}
            </if>
        </where>
        order by a.list_customer_city,a.counts
    </select>

    <select id="findStatisticsDetails"
            resultType="cn.chinaunicom.sdsi.statistics.entity.StatisticsDetailResult">

        select  a.customer_id,a.super_unit_name,a.roster_customer_name,a.customer_name,customer_manager_name,a.customer_manager_tel, IF(b.customer_id is null,'未反馈','已反馈') result  from  t_sanquan_customer_info a
        LEFT JOIN
        (select  a.customer_id ,b.list_customer_city,b.list_customer_district from t_sanquan_customer_info_audit a, t_sanquan_customer_info b
        where a.customer_id=b.customer_id and a.delete_flag='normal' GROUP BY a.customer_id
        ) b on b.customer_id=a.customer_id
        <where>
            1=1
            <if test="query.city != null and query.city != ''">
                AND a.list_customer_city = #{query.city}
            </if>
            <if test="query.district != null and query.district != ''">
                AND a.list_customer_district = #{query.district}
            </if>
            <if test="query.result != null and query.result != ''">
                and IF(b.customer_id_selected is null,'未反馈','已反馈') = #{query.result}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and a.customer_name = #{query.customerName}
            </if>
            and a.delete_flag='normal'
        </where>
        GROUP BY a.customer_id
    </select>


    <select  id="findCityGroup" resultType="string">
        select  list_customer_city cityName from  t_sanquan_customer_info where  list_customer_city is not null GROUP BY list_customer_city;
    </select>

    <select  id="findCityGroupAndAllOrder" resultType="string">
        select city_name cityName from t_sanquan_city where is_status = 1
        <if test="query.cityName != null and query.cityName != ''">
           and city_name = #{query.cityName}
        </if>
        order by city_id
    </select>

    <select  id="findDistrictGroup" resultType="string">
        select  list_customer_district cityName from  t_sanquan_customer_info
        where list_customer_city= #{query.city} and list_customer_district is not null
        GROUP BY list_customer_district;
    </select>

    <select id="findList" resultType="cn.chinaunicom.sdsi.statistics.entity.StatisticsResult">
        select a.*,ifnull(b.hasCount,0) hasCount,IFNULL(a.counts,0)-ifnull(b.hasCount,0) noCount from (select
        a.list_customer_city,a.customer_id,a.list_customer_district,count(a.customer_id) as counts from
        t_sanquan_customer_info a where a.delete_flag='normal' and list_customer_district is not null GROUP BY a.list_customer_city,a.list_customer_district) a
        LEFT JOIN
        (SELECT count(*) hasCount, b.list_customer_district,b.list_customer_city 	FROM
        (SELECT	a.customer_id,b.list_customer_city,b.list_customer_district FROM	t_sanquan_customer_info_audit a,	t_sanquan_customer_info b
        WHERE	a.customer_id = b.customer_id  and a.is_passed is not null and a.delete_flag='normal'
        GROUP BY a.customer_id ) b
        GROUP BY b.list_customer_city,b.list_customer_district ) b

        on a.list_customer_city=b.list_customer_city and a.list_customer_district=b.list_customer_district
        <where>
            1=1
            <if test="query.city != null and query.city != ''">
                AND a.list_customer_city = #{query.city}
            </if>
            <if test="query.district != null and query.district != ''">
                AND a.list_customer_district = #{query.district}
            </if>
        </where>
        order by a.list_customer_city,a.counts
    </select>
    <select id="findStatisticsDetailsList"
            resultType="cn.chinaunicom.sdsi.statistics.vo.StatisticsDetailResultExcelVO">

        select
                IFNULL(NULLIF(a.customer_id, 'null'), '') AS customer_id,
                IFNULL(NULLIF(a.super_unit_name, 'null'), '') AS super_unit_name,
                IFNULL(NULLIF(a.roster_customer_name, 'null'), '') AS roster_customer_name,
                IFNULL(NULLIF(a.customer_name, 'null'), '') AS customer_name,
                IFNULL(NULLIF(a.customer_manager_name, 'null'), '') AS customer_manager_name,
                IFNULL(NULLIF(a.customer_manager_tel, 'null'), '') AS customer_manager_tel,
                IF(b.customer_id is null,'未反馈','已反馈') result,
                IFNULL(NULLIF(a.list_customer_city, 'null'), '') AS list_customer_city,
                IFNULL(NULLIF(a.list_customer_district, 'null'), '') AS list_customer_district
        from  t_sanquan_customer_info a
        LEFT JOIN
        (select  a.customer_id ,b.list_customer_city,b.list_customer_district from t_sanquan_customer_info_audit a, t_sanquan_customer_info b
        where a.customer_id=b.customer_id and a.delete_flag = 'normal' GROUP BY a.customer_id
        ) b on b.customer_id=a.customer_id
        <where>
            1=1
            <if test="query.city != null and query.city != ''">
                AND a.list_customer_city = #{query.city}
            </if>
            <if test="query.district != null and query.district != ''">
                AND a.list_customer_district = #{query.district}
            </if>
            <if test="query.result != null and query.result != ''">
                and IF(b.customer_id_selected is null,'未反馈','已反馈') = #{query.result}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                and a.customer_name = #{query.customerName}
            </if>
            and a.delete_flag='normal'
        </where>
        GROUP BY a.customer_id
    </select>
</mapper>
