<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.product.dao.ProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.product.entity.ProductEntity">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="type" property="type" />
        <result column="provincial_branch" property="provincialBranch" />
        <result column="export_company" property="exportCompany" />
        <result column="business_classification" property="businessClassification" />
        <result column="subdivision" property="subdivision" />
        <result column="business_category" property="businessCategory" />
        <result column="presentation" property="presentation" />
        <result column="customer_description" property="customerDescription" />
        <result column="attachment" property="attachment" />
        <result column="example" property="example" />
        <result column="contact" property="contact" />
        <result column="supporter" property="supporter" />
        <result column="regional_support" property="regionalSupport" />
        <result column="prefectural_support" property="prefecturalSupport" />
        <result column="is_making" property="isMaking" />
        <result column="business_scenario" property="businessScenario" />
        <result column="empower_link" property="empowerLink" />
        <result column="attr1" property="attr1" />
        <result column="attr2" property="attr2" />
        <result column="issue_month" property="issueMonth" />
        <result column="product_line" property="productLine" />
        <result column="product_category" property="productCategory" />
        <result column="product_series" property="productSeries" />
        <result column="product_type" property="productType" />
        <result column="city" property="city" />
        <result column="is_industry_main" property="isIndustryMain" />
        <result column="is_benchmark" property="isBenchmark" />
    </resultMap>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.product.vo.ProductEntityVO">
        SELECT
        p.id,
        p.name,
        p.type,
        p.provincial_branch,
        p.export_company,
        p.business_classification,
        p.subdivision,
        p.business_category,
        p.presentation,
        p.attachment,
        p.example,
        p.contact,
        p.supporter,
        p.regional_support,
        p.prefectural_support,
        p.is_making,
        p.customer_description,
        p.empower_link,
        p.attr1,
        p.attr2,
        p.issue_month,
        IFNULL(l.link_addr, '') AS linkAddr,
        IFNULL(l.link_name, '') AS linkName,
        p.create_by,
        us.name as createByName,
        p.product_line,
        p.product_category,
        p.product_series,
        p.product_type,
        p.city,
        p.is_industry_main,
        p.is_benchmark
        FROM
        t_sanquan_product p
        LEFT JOIN
        t_sanquan_product_link_config l ON p.id = l.product_id
        left join t_sanquan_platform_user us on p.create_by = us.job_num and us.deleted = 'normal'
        <where>
            p.delete_flag = 'normal'
            <if test="query.provincialBranch != null and query.provincialBranch != ''"  >
                AND p.provincial_branch = #{query.provincialBranch}
            </if>
            <if test="query.businessClassification != null and query.businessClassification != ''"  >
                AND p.business_classification LIKE concat('%', #{query.businessClassification}, '%')
            </if>
            <if test="query.businessCategory != null and query.businessCategory != ''"  >
                AND p.business_category = #{query.businessCategory}
            </if>
            <if test="query.name != null and query.name != ''"  >
                AND p.name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.regionalSupport != null and query.regionalSupport != ''"  >
                AND p.regional_support LIKE concat('%', #{query.regionalSupport}, '%')
            </if>
            <if test="query.prefecturalSupport != null and query.prefecturalSupport != ''"  >
                AND p.prefectural_support LIKE concat('%', #{query.prefecturalSupport}, '%')
            </if>
            <if test="query.isMaking != null and query.isMaking != ''"  >
                AND p.is_making = #{query.isMaking}
            </if>
            <if test="query.clusterId != null and query.clusterId != ''">
                and p.id in (
                    select DISTINCT b.product_id from t_sanquan_product_use_scene b where b.scene_id in (
                        select DISTINCT a.scene_id from t_sanquan_cluster_scene a where a.cluster_id = #{query.clusterId}
                    )
                )
            </if>
        </where>
        ORDER BY p.update_date desc,p.id desc
    </select>
    <select id="findPageProductPageListFromResult" resultType="cn.chinaunicom.sdsi.cloud.product.vo.ProductEntityVO">
        select p.* from
        (
        SELECT
        t.id,
        t.name,
        t.type,
        t.provincial_branch,
        t.export_company,
        t.business_classification,
        t.subdivision,
        t.business_category,
        t.presentation,
        t.attachment,
        t.example,
        t.contact,
        t.supporter,
        t.regional_support,
        t.prefectural_support,
        t.is_making,
        t.customer_description,
        t.empower_link,
        t.attr1,
        t.attr2,
        t.delete_flag,
        t.update_date,
        IF(m.product_id IS NULL OR m.product_id='',0,1) ismodel,
        IF(h.product_id IS NULL OR h.product_id='',0,1) ishotsale,
        IF(z.product_id IS NULL OR h.product_id='',0,1) ismaincommend
        FROM
        t_sanquan_product t
        INNER JOIN (select distinct r.product_id from t_sanquan_potential_opportunity r where r.delete_flag='normal' and (r.is_push!=1 or r.is_push is null)
        <if test="query.modelId != null and query.modelId != ''">
            and r.model_id=#{query.modelId}
        </if>
        ) m ON t.id = m.product_id
        LEFT JOIN t_sanquan_product_hotselling h ON t.id = h.product_id
        LEFT JOIN t_sanquan_product_industry_mainrecommend z ON t.id = z.product_id
        ) p
        <where>
            p.delete_flag = 'normal'
            <choose>
                <when test="query.productType != null and query.productType != ''">
                    and ( 1=0
                    <if test="query.productType.contains('模型结果')">
                        or p.ismodel=1
                    </if>
                    <if test="query.productType.contains('行业主推')">
                        OR p.ismaincommend=1
                            <if test="query.industryList != null">
                                AND p.provincial_branch in
                                <foreach collection="query.industryList" item="industry" index="index" open="(" separator="," close=")">
                                    #{industry}
                                </foreach>
                            </if>

                    </if>
                    <if test="query.productType.contains('热销产品')">
                        OR p.ishotsale=1

                        <if test="query.industryList != null">
                            AND p.provincial_branch in
                            <foreach collection="query.industryList" item="industry" index="index" open="(" separator="," close=")">
                                #{industry}
                            </foreach>
                        </if>
                    </if>
                    )
                </when>
                <otherwise>
                    and 1=0
                </otherwise>
            </choose>

            <if test="query.provincialBranch != null and query.provincialBranch != ''"  >
                AND p.provincial_branch = #{query.provincialBranch}
            </if>
            <if test="query.businessClassification != null and query.businessClassification != ''"  >
                AND p.business_classification LIKE concat('%', #{query.businessClassification}, '%')
            </if>
            <if test="query.businessCategory != null and query.businessCategory != ''"  >
                AND p.business_category = #{query.businessCategory}
            </if>
            <if test="query.name != null and query.name != ''"  >
                AND p.name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.regionalSupport != null and query.regionalSupport != ''"  >
                AND p.regional_support LIKE concat('%', #{query.regionalSupport}, '%')
            </if>
            <if test="query.prefecturalSupport != null and query.prefecturalSupport != ''"  >
                AND p.prefectural_support LIKE concat('%', #{query.prefecturalSupport}, '%')
            </if>
            <if test="query.isMaking != null and query.isMaking != ''"  >
                AND p.is_making = #{query.isMaking}
            </if>
        </where>
        ORDER BY p.update_date desc,p.id desc
    </select>
    <select id="getIndustryList" resultType="cn.chinaunicom.sdsi.cloud.product.vo.IndustryVO">
        select distinct (INDUSTRY_ID) as `value`,INDUSTRY_NAME as label from td_b_industry_prov where PARENT_INDUSTRY_ID='0' and INDUSTRY_LEVEL='0'
    </select>

    <select id="getDictionaryList" resultType="cn.chinaunicom.sdsi.cloud.product.vo.DictionaryVO">
        select type,name,`value` from t_sanquan_dictionary
        <where>
            <if test="query.type != null and query.type != ''"  >
                AND type = #{query.type}
            </if>
            <if test="query.name != null and query.name != ''"  >
                AND name = #{query.name}
            </if>
            <if test="query.value != null and query.value != ''"  >
                AND value = #{query.value}
            </if>
        </where>
    </select>

    <select id="getPersonnel" resultType="cn.chinaunicom.sdsi.cloud.product.entity.TSanquanPersonnel">
        select
            id,
            name,
            phone,
            email,
            department,
            department_id as departmentId,
            prefecture_id as prefectureId,
            prefecture,
            region_id as regionId,
            region,
            employment_certificate as employmentCertificate,
            evaluator_certificate as evaluatorCertificate,
            job_title as jobTitle,
            job_type as jobType,
            job_number,delete_flag,create_date,create_by,update_date,update_by
        from t_sanquan_personnel
        <where>
            delete_flag = 'normal'
            <if test="query.id != null and query.id != ''"  >
                AND id = #{query.id}
            </if>
            <if test="query.name != null and query.name != ''"  >
                AND name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.phone != null and query.phone != ''"  >
                AND phone = #{query.phone}
            </if>
            <if test="query.departmentId != null and query. departmentId!= ''"  >
                AND department_id = #{query.departmentId}
            </if>
            <if test="query.prefectureId != null and query.prefectureId != ''"  >
                AND prefecture_id = #{query.prefectureId}
            </if>
            <if test="query.prefecture != null and query.prefecture != ''">
                AND prefecture LIKE CONCAT('%', #{query.prefecture}, '%')
            </if>
            <if test="query.region != null and query.region != ''"  >
                AND region LIKE concat('%', #{query.region}, '%')
            </if>
            <if test="query.regionId != null and query.regionId != ''"  >
                AND region_id = #{query.regionId}
            </if>
            <if test="query.jobType != null and query.jobType != ''"  >
                AND job_type LIKE concat('%', #{query.jobType}, '%')
            </if>
        </where>
    </select>
    <select id="findLabelTree" resultType="cn.chinaunicom.sdsi.cloud.product.vo.NodeTreeVO">
        SELECT
            id,
            CODE nodeCode,
            label nodeLabel,
            IF(parent_id = '' or parent_id=null, '1', parent_id) parentId,
            '1' AS nodeType,
            null as tag_type,
            null as tag_type_value,
            null as tag_source,
            null as tag_num_value,
            null as acct_date,
            null as db_table_name,
            null as db_table_column,
            null as db_match_type,
            null as business_caliber,
            null as acct_date_type,
            null as more_dict_value
        FROM
            t_sanquan_label_classify
        WHERE
            delete_flag = 'normal' UNION ALL
        SELECT
            id,
            tag_code nodeCode,
            tag_name nodeLabel,
            tag_type_id parentId,
            '2' AS nodeType,
            tag_type,
            tag_type_value,
            tag_source,
            tag_num_value,
            acct_date,
            db_table_name,
            db_table_column,
            db_match_type,
            business_caliber,
            acct_date_type,
            more_dict_value
        FROM
            t_sanquan_tag
        WHERE
            delete_flag = 'normal'
    </select>
    <select id="findTagNodeList" resultType="cn.chinaunicom.sdsi.cloud.product.vo.NodeTreeVO">
        SELECT
            id,
            tag_code nodeCode,
            tag_name nodeLabel,
            tag_type_id parentId,
            '2' AS nodeType,
            tag_type,
            tag_type_value,
            tag_source,
            tag_num_value,
            acct_date,
            db_table_name,
            db_table_column,
            db_match_type,
            business_caliber,
            acct_date_type
        FROM
            t_sanquan_tag
        WHERE
            delete_flag = 'normal'
    </select>

    <select id="selectProductIdByLink" resultType="cn.chinaunicom.sdsi.cloud.product.vo.ProductEntityVO">
        SELECT
            p.id,
            p.name,
            p.type,
            p.provincial_branch,
            p.export_company,
            p.business_classification,
            p.subdivision,
            p.business_category,
            p.presentation,
            p.attachment,
            p.example,
            p.contact,
            p.supporter,
            p.regional_support,
            p.prefectural_support,
            p.is_making,
            p.customer_description,
            p.empower_link,
            p.attr1,
            p.attr2,
            p.issue_month,
            IFNULL(l.link_addr, '') AS linkAddr,
            IFNULL(l.link_name, '') AS linkName
        FROM
            t_sanquan_product p
                LEFT JOIN t_sanquan_product_link_config l ON p.id = l.product_id
        where p.id = #{productId}
    </select>
    <select id="findProductList" resultType="cn.chinaunicom.sdsi.cloud.product.vo.ProductVO">
        SELECT
        p.id,
        p.name,
        p.type,
        p.provincial_branch,
        p.export_company,
        p.business_classification,
        p.subdivision,
        p.business_category,
        p.presentation,
        p.attachment,
        p.example,
        p.contact,
        p.supporter,
        p.regional_support,
        p.prefectural_support,
        p.is_making,
        p.customer_description,
        p.empower_link,
        p.attr1,
        p.attr2,
        p.issue_month
        FROM
        t_sanquan_product p
        <where>
            p.delete_flag = 'normal'
            <if test="query.provincialBranch != null and query.provincialBranch != ''"  >
                AND p.provincial_branch = #{query.provincialBranch}
            </if>
            <if test="query.businessClassification != null and query.businessClassification != ''"  >
                AND p.business_classification LIKE concat('%', #{query.businessClassification}, '%')
            </if>
            <if test="query.businessCategory != null and query.businessCategory != ''"  >
                AND p.business_category = #{query.businessCategory}
            </if>
            <if test="query.name != null and query.name != ''"  >
                AND p.name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.regionalSupport != null and query.regionalSupport != ''"  >
                AND p.regional_support LIKE concat('%', #{query.regionalSupport}, '%')
            </if>
            <if test="query.prefecturalSupport != null and query.prefecturalSupport != ''"  >
                AND p.prefectural_support LIKE concat('%', #{query.prefecturalSupport}, '%')
            </if>
            <if test="query.isMaking != null and query.isMaking != ''"  >
                AND p.is_making = #{query.isMaking}
            </if>
            <if test="query.productList !=null and query.productList.size != 0 ">
                AND id in
                <foreach collection="query.productList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY p.update_date desc,p.id desc
    </select>
    <select id="selectProductNum" resultType="java.lang.Integer">
        select count(*) from t_sanquan_product p
        <where>
            p.delete_flag = 'normal'
            <if test="query.provincialBranch != null and query.provincialBranch != ''"  >
                AND p.provincial_branch = #{query.provincialBranch}
            </if>
            <if test="query.businessClassification != null and query.businessClassification != ''"  >
                AND p.business_classification LIKE concat('%', #{query.businessClassification}, '%')
            </if>
            <if test="query.businessCategory != null and query.businessCategory != ''"  >
                AND p.business_category = #{query.businessCategory}
            </if>
            <if test="query.name != null and query.name != ''"  >
                AND p.name LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="query.regionalSupport != null and query.regionalSupport != ''"  >
                AND p.regional_support LIKE concat('%', #{query.regionalSupport}, '%')
            </if>
            <if test="query.prefecturalSupport != null and query.prefecturalSupport != ''"  >
                AND p.prefectural_support LIKE concat('%', #{query.prefecturalSupport}, '%')
            </if>
            <if test="query.isMaking != null and query.isMaking != ''"  >
                AND p.is_making = #{query.isMaking}
            </if>
        </where>
    </select>
    <select id="selectSegments" resultType="java.util.Map">
        select segments_name as label, segments_name as `value` from t_sanquan_industry_segments
        where industry in
        <foreach collection="industry" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
