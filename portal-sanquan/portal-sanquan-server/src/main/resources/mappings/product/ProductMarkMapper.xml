<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.product.dao.TSanquanProductMarkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductMark">
        <id column="id" property="id" />

    </resultMap>
    <select id="selectProductLabelNum" resultType="java.lang.Integer">
        select count(*) from t_sanquan_product_mark
    </select>


</mapper>
