<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.product.dao.TSanquanProductLinkConfigMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.product.entity.TSanquanProductLinkConfig" id="tSanquanProductLinkConfigMap">
        <result property="id" column="id"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="linkName" column="link_name"/>
        <result property="linkAddr" column="link_addr"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createDate" column="create_date"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="versions" column="versions"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.product.vo.TSanquanProductLinkConfigVO">
        select id,product_id,product_name,link_name,link_addr,attr1,attr2,attr3
                ,delete_flag,create_by,create_date,update_by,update_date,versions,tenant_id
        from t_sanquan_product_link_config
        <where>
            delete_flag = 'normal'
            <if test="query.productName != null and query.productName != ''">
                AND product_name = #{query.productName}
            </if>
            <if test="query.productId != null and query.productId != ''">
                AND product_id = #{query.productId}
            </if>
            <if test="query.linkName != null and query.linkName != ''">
                AND link_name LIKE concat('%', #{query.linkName}, '%')
            </if>
        </where>
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.product.vo.TSanquanProductLinkConfigVO">
        select id,product_id,product_name,link_name,link_addr,attr1,attr2,attr3
        ,delete_flag,create_by,create_date,update_by,update_date,versions,tenant_id
        from t_sanquan_product_link_config
        <where>
            delete_flag = 'normal'
            <if test="query.productName != null and query.productName != ''">
                AND product_name = #{query.productName}
            </if>
            <if test="query.productId != null and query.productId != ''">
                AND product_id = #{query.productId}
            </if>
            <if test="query.linkName != null and query.linkName != ''">
                AND link_name LIKE concat('%', #{query.linkName}, '%')
            </if>
        </where>
    </select>

</mapper>