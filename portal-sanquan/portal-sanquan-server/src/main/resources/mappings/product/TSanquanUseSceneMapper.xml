<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.product.dao.TSanquanUseSceneMapper">

    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.product.entity.TSanquanUseScene">
        select t.* from t_sanquan_use_scene t
        <where>
            <if test="query.name != null and query.name != ''">
                and t.name like concat('%', #{query.name}, '%')
            </if>
            <if test="query.associated != null and query.associated != ''">
                <choose>
                    <when test='query.associated == "1"'>
                        <if test="query.sceneIds.size() > 0">
                            and t.id in
                            <foreach collection="query.sceneIds" item="item" separator="," open="(" close=")" index="index">
                                #{item}
                            </foreach>
                        </if>
                    </when>
                    <otherwise>
                        <if test="query.sceneIds.size() > 0">
                            and t.id not in
                            <foreach collection="query.sceneIds" item="item" separator="," open="(" close=")" index="index">
                                #{item}
                            </foreach>
                        </if>
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <select id="findClusterScenePage" resultType="cn.chinaunicom.sdsi.cloud.product.entity.TSanquanUseScene">
        select t.* from t_sanquan_cluster_scene d
            left join t_sanquan_use_scene t on d.scene_id = t.id
        <where>
            <if test="query.clusterId != null and query.clusterId !=''">
                and d.cluster_id = #{query.clusterId}
            </if>
            <if test="query.name != null and query.name != ''">
                and t.name like concat('%', #{query.name}, '%')
            </if>
        </where>
    </select>
</mapper>
