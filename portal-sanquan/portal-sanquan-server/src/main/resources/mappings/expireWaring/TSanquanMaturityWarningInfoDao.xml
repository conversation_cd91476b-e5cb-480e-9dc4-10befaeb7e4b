<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.expireWarning.dao.TSanquanMaturityWarningInfoMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.expireWarning.entity.TSanquanMaturityWarningInfo" id="tSanquanMaturityWarningInfoMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="issueDate" column="issue_date"/>
        <result property="strategyResultId" column="strategy_result_id"/>
        <result property="status" column="status"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
    </resultMap>
    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.expireWarning.vo.TSanquanMaturityWarningInfoVO">
        select id,name,issue_date,strategy_result_id,status,attr1,attr2,attr3
        from t_sanquan_maturity_warning_info
        <where>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.name != null and query.name != ''">
                AND name Like concat('%', #{query.name},'%')
            </if>
            <if test="query.strategyResultId != null and query.strategyResultId != ''">
                AND strategyResultId = #{query.strategyResultId}
            </if>
            <if test="query.issueDate != null and query.issueDate != ''">
                AND issue_date = #{query.issueDate}
            </if>
        </where>
        order by issue_date desc,id
    </select>
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.expireWarning.vo.TSanquanMaturityWarningInfoVO">
        select id,name,issue_date,strategy_result_id,status,attr1,attr2,attr3
        from t_sanquan_maturity_warning_info
        <where>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.name != null and query.name != ''">
                AND name Like concat('%', #{query.name},'%')
            </if>
            <if test="query.strategyResultId != null and query.strategyResultId != ''">
                AND strategyResultId = #{query.strategyResultId}
            </if>
            <if test="query.issueDate != null and query.issueDate != ''">
                AND issue_date = #{query.issueDate}
            </if>
        </where>
        order by issue_date desc,id
    </select>

</mapper>