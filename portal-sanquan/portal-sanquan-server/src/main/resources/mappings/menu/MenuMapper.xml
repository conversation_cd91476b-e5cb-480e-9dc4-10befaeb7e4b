<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.menus.mapper.MenuMapper">
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.menus.vo.MenuVO">
        select *
            from  t_sanquan_menus
        <where>
            <if test="query.title != null and query.title != ''">
                AND title = #{query.title}
            </if>
            and show_menu='1'
        </where>
        order by menu_sort
    </select>

    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.menus.vo.MenuVO">
        select *
        from  t_sanquan_menus
        <where>
            <if test="query.title != null and query.title != ''">
                AND title = #{query.title}
            </if>
            and show_menu='1'

        </where>
    </select>

</mapper>
