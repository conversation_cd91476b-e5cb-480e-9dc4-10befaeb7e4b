<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.dingtalkAbility.mapper.DingTalkConfigMapper">
    <sql id="Base_Column_List">
        id, APP_KEY, APP_SECRET, ROBOT_CODE, open_conversation_id, chat_id, group_remark, status, remark
    </sql>

    <select id="findAllActiveConfigs" resultType="cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig">
        select
            *
        from
            t_sanquan_dingding_config
        where status = 1
    </select>


    <select id="findActiveConfig" resultType="cn.chinaunicom.sdsi.dingtalkAbility.entity.DingTalkConfig">
        select
            *
        from t_sanquan_dingding_config
        where
            status = 1
            <if test="group != null and group != ''">
                and group_remark = #{group}
            </if>
    </select>


    <select id="findPage" resultType="cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkConfigVo">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_sanquan_dingding_config
        <where>
            <if test="vo.id != null and vo.id != ''">
                AND id = #{vo.id}
            </if>
            <if test="vo.appKey != null and vo.appKey != ''">
                AND APP_KEY = #{vo.appKey}
            </if>
            <if test="vo.robotCode != null and vo.robotCode != ''">
                AND ROBOT_CODE = #{vo.robotCode}
            </if>
            <if test="vo.openConversationId != null and vo.openConversationId != ''">
                AND open_conversation_id = #{vo.openConversationId}
            </if>
            <if test="vo.chatId != null and vo.chatId != ''">
                AND chat_id = #{vo.chatId}
            </if>
            <if test="vo.groupRemark != null and vo.groupRemark != ''">
                AND group_remark = #{vo.groupRemark}
            </if>
            <if test="vo.status != null and vo.status != ''">
                AND status = #{vo.status}
            </if>
            <if test="vo.remark != null and vo.remark != ''">
                AND remark = #{vo.remark}
            </if>
        </where>

    </select>


</mapper>