<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.dingtalkAbility.mapper.DingTalkLogsMapper">


    <select id="findPage" resultType="cn.chinaunicom.sdsi.dingtalkAbility.vo.DingTalkLogsVo">
        SELECT
            l.id AS id,
            l.batch_id AS batchId,
            l.process_query_key AS processQueryKey,
            l.type AS type,
            l.content AS content,
            l.biz_name AS bizName,
            l.target_info AS targetInfo,
            c.name AS userName,
            c.login_name AS loginName,
            c.city AS city,
            l.group_remark AS groupRemark,
            l.status AS status,
            l.create_time AS createTime,
            l.recall_time AS recallTime,
            l.recall_reason AS recallReason,
            l.error_code AS errorCode,
            l.error_msg AS errorMsg,
            l.remark AS remark
        FROM t_sanquan_dingding_logs l
        LEFT JOIN (select name,
            login_name,
            city,
            mobile,
            business_category,
            row_number() over (partition by mobile, business_category) rn
            from t_sanquan_dingding_contact
            where status = 0
            and is_deleted = 0) c
        ON l.target_info = c.mobile and l.group_remark = c.business_category and c.rn = 1
        <where>
            <if test="vo.id != null and vo.id != ''">
                AND l.id = #{vo.id}
            </if>
            <if test="vo.batchId != null and vo.batchId != ''">
                AND l.batch_id = #{vo.batchId}
            </if>
            <if test="vo.processQueryKey != null and vo.processQueryKey != ''">
                AND l.process_query_key = #{vo.processQueryKey}
            </if>
            <if test="vo.type != null">
                AND l.type = #{vo.type}
            </if>
            <if test="vo.mobile != null and vo.mobile != ''">
                AND l.target_info = #{vo.mobile}
            </if>
            <if test="vo.groupRemark != null and vo.groupRemark != ''">
                AND l.group_remark = #{vo.groupRemark}
            </if>
            <if test="vo.bizName != null and vo.bizName != ''">
                AND l.biz_name LIKE CONCAT('%', #{vo.bizName}, '%')
            </if>
            <if test="vo.timeRange != null and vo.timeRange.size() == 2">
                AND l.create_time BETWEEN
                STR_TO_DATE(#{vo.timeRange[0]}, '%a %b %d %Y %H:%i:%s GMT+0800')
                and
                STR_TO_DATE(#{vo.timeRange[1]}, '%a %b %d %Y %H:%i:%s GMT+0800')
            </if>
        </where>
        ORDER BY l.create_time desc

    </select>
</mapper>