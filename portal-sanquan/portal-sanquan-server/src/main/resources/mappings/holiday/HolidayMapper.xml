<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.holiday.mapper.HolidayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.cloud.holiday.entity.Holiday">
        <id column="id" property="id" />
        <result column="holiday_date" property="holidayDate" />
        <result column="holiday_type" property="holidayType" />
        <result column="holiday_name" property="holidayName" />
        <result column="is_workday" property="isWorkday" />
        <result column="year" property="year" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, holiday_date, holiday_type, holiday_name, is_workday, year, create_time, update_time
    </sql>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.holiday.entity.Holiday">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_holiday
        WHERE 1=1
        <if test="query.id != null and query.id != ''">
            AND id = #{query.id}
        </if>
        <if test="query.holidayDate != null and query.holidayDate != ''">
            AND holiday_date = #{query.holidayDate}
        </if>
        <if test="query.holidayType != null and query.holidayType != ''">
            AND holiday_type = #{query.holidayType}
        </if>
        <if test="query.holidayName != null and query.holidayName != ''">
            AND holiday_name = #{query.holidayName}
        </if>
        <if test="query.isWorkday != null and query.isWorkday != ''">
            AND is_workday = #{query.isWorkday}
        </if>
        <if test="query.year != null and query.year != ''">
            AND year = #{query.year}
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            AND create_time = #{query.createTime}
        </if>
        <if test="query.updateTime != null and query.updateTime != ''">
            AND update_time = #{query.updateTime}
        </if>
<!--        <if test="query.createTimeStart != null and query.createTimeStart != ''">-->
<!--            <![CDATA[ AND create_time >= #{query.createTimeStart} ]]>-->
<!--        </if>-->
<!--        <if test="query.createTimeEnd != null and query.createTimeEnd != ''">-->
<!--            <![CDATA[ AND create_time <= #{query.createTimeEnd} ]]>-->
<!--        </if>-->
<!--        <if test="query.updateTimeStart != null and query.updateTimeStart != ''">-->
<!--            <![CDATA[ AND update_time >= #{query.updateTimeStart} ]]>-->
<!--        </if>-->
<!--        <if test="query.updateTimeEnd != null and query.updateTimeEnd != ''">-->
<!--            <![CDATA[ AND update_time <= #{query.updateTimeEnd} ]]>-->
<!--        </if>-->
        order by holiday_date desc, id
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.holiday.entity.Holiday">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_sanquan_holiday
        WHERE 1=1
        <if test="query.id != null and query.id != ''">
            AND id = #{query.id}
        </if>
        <if test="query.holidayDate != null and query.holidayDate != ''">
            AND holiday_date = #{query.holidayDate}
        </if>
        <if test="query.holidayType != null and query.holidayType != ''">
            AND holiday_type = #{query.holidayType}
        </if>
        <if test="query.holidayName != null and query.holidayName != ''">
            AND holiday_name = #{query.holidayName}
        </if>
        <if test="query.isWorkday != null and query.isWorkday != ''">
            AND is_workday = #{query.isWorkday}
        </if>
        <if test="query.year != null and query.year != ''">
            AND year = #{query.year}
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            AND create_time = #{query.createTime}
        </if>
        <if test="query.updateTime != null and query.updateTime != ''">
            AND update_time = #{query.updateTime}
        </if>
        <!--        <if test="query.createTimeStart != null and query.createTimeStart != ''">-->
        <!--            <![CDATA[ AND create_time >= #{query.createTimeStart} ]]>-->
        <!--        </if>-->
        <!--        <if test="query.createTimeEnd != null and query.createTimeEnd != ''">-->
        <!--            <![CDATA[ AND create_time <= #{query.createTimeEnd} ]]>-->
        <!--        </if>-->
        <!--        <if test="query.updateTimeStart != null and query.updateTimeStart != ''">-->
        <!--            <![CDATA[ AND update_time >= #{query.updateTimeStart} ]]>-->
        <!--        </if>-->
        <!--        <if test="query.updateTimeEnd != null and query.updateTimeEnd != ''">-->
        <!--            <![CDATA[ AND update_time <= #{query.updateTimeEnd} ]]>-->
        <!--        </if>-->
        order by holiday_date desc, id
    </select>

</mapper>
