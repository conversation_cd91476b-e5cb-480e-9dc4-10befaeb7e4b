<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.shuangxianProtocolExpiration.mapper.ShuangxianProtocolExpirationMsgMapper">

    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.shuangxianProtocolExpiration.vo.ShuangxianProtocolExpirationMsgVO">
<!--        <id column="id" property="id" />-->
        <result column="DEVELOPER_LOGIN_NO" property="developerLoginNo" />
        <result column="DEVELOPER_NAME" property="developerName" />
        <result column="DEVELOPER_MOBILE" property="developerMobile" />
        <result column="DEVELOPER_EMAIL" property="developerEmail" />
        <result column="PRODUCT_ID" property="productId" />
        <result column="PRODUCT_NAME" property="productName" />
        <result column="END_DATE" property="endDate" />
        <result column="CEO_STAFF_NAME" property="ceoStaffName" />
        <result column="CEO_MOBILE" property="ceoMobile" />
        <result column="CEO_EMAIL" property="ceoEmail" />
        <result column="CUST_NAME" property="custName" />
        <result column="product_content" property="productContent" />
    </resultMap>
    <!-- ,account_code,dev_name,linkman_phone,product_name -->
    <select id="selectList2DeveloperSMS" resultMap="BaseResultMap">
        SELECT
        developer_mobile,developer_name,CUST_NAME,GROUP_CONCAT(CONCAT('(',PRODUCT_NAME,')', device_numbers) ORDER BY PRODUCT_NAME SEPARATOR ', ') as product_content, min(end_date) end_date
        FROM
        (
            select developer_mobile,developer_name,PRODUCT_NAME,CUST_NAME
            , CONCAT('(',GROUP_CONCAT(DEVICE_NUMBER ORDER BY DEVICE_NUMBER SEPARATOR ', '),')') AS device_numbers,
            min(end_date) end_date
            from t_sanquan_d_sx_zfdq
            <where> 1=1
                <if test="query.dayId != null">
                    and day_id = #{query.dayId}
                </if>
                <if test="query.monthId != null">
                    and month_id = #{query.monthId}
                </if>
            </where>
            group by developer_mobile,developer_name,PRODUCT_NAME,CUST_NAME
            order by developer_mobile,developer_name,PRODUCT_NAME,CUST_NAME
        ) a
        GROUP BY developer_mobile,developer_name,CUST_NAME
        ORDER BY developer_mobile,developer_name,CUST_NAME
    </select>
    <select id="selectList2CeoSMS" resultMap="BaseResultMap">
        SELECT
            ceo_mobile,CEO_STAFF_NAME,CUST_NAME,GROUP_CONCAT(CONCAT('(',PRODUCT_NAME,')', device_numbers) ORDER BY PRODUCT_NAME SEPARATOR ', ') as product_content, min(end_date) end_date
        FROM
            (
                select ceo_mobile,CEO_STAFF_NAME,PRODUCT_NAME,CUST_NAME
                        , CONCAT('(',GROUP_CONCAT(DEVICE_NUMBER ORDER BY DEVICE_NUMBER SEPARATOR ', '),')') AS device_numbers,
                       min(end_date) end_date
                from t_sanquan_d_sx_zfdq
                <where> 1=1
                    <if test="query.dayId != null">
                        and day_id = #{query.dayId}
                    </if>
                    <if test="query.monthId != null">
                        and month_id = #{query.monthId}
                    </if>
                </where>
                group by ceo_mobile,CEO_STAFF_NAME,PRODUCT_NAME,CUST_NAME
                order by ceo_mobile,CEO_STAFF_NAME,PRODUCT_NAME,CUST_NAME
            ) a
        GROUP BY ceo_mobile,CEO_STAFF_NAME,CUST_NAME
        ORDER BY ceo_mobile,CEO_STAFF_NAME,CUST_NAME
    </select>

    <select id="selectList2DeveloperEmail" resultMap="BaseResultMap">
        SELECT
        DEVELOPER_EMAIL,developer_name,CUST_NAME,GROUP_CONCAT(CONCAT('(',PRODUCT_NAME,')', device_numbers) ORDER BY PRODUCT_NAME SEPARATOR ', ') as product_content, min(end_date) end_date
        FROM
        (
        select DEVELOPER_EMAIL,developer_name,PRODUCT_NAME,CUST_NAME
        , CONCAT('(',GROUP_CONCAT(DEVICE_NUMBER ORDER BY DEVICE_NUMBER SEPARATOR ', '),')') AS device_numbers,
        min(end_date) end_date
        from t_sanquan_d_sx_zfdq
        <where> 1=1
            <if test="query.dayId != null">
                and day_id = #{query.dayId}
            </if>
            <if test="query.monthId != null">
                and month_id = #{query.monthId}
            </if>
        </where>
        group by DEVELOPER_EMAIL,developer_name,PRODUCT_NAME,CUST_NAME
        order by DEVELOPER_EMAIL,developer_name,PRODUCT_NAME,CUST_NAME
        ) a
        GROUP BY DEVELOPER_EMAIL,developer_name,CUST_NAME
        ORDER BY DEVELOPER_EMAIL,developer_name,CUST_NAME
    </select>
    <select id="selectList2CeoEmail" resultMap="BaseResultMap">
        SELECT
        ceo_email,CEO_STAFF_NAME,CUST_NAME,GROUP_CONCAT(CONCAT('(',PRODUCT_NAME,')', device_numbers) ORDER BY PRODUCT_NAME SEPARATOR ', ') as product_content, min(end_date) end_date
        FROM
        (
        select ceo_email,CEO_STAFF_NAME,PRODUCT_NAME,CUST_NAME
        , CONCAT('(',GROUP_CONCAT(DEVICE_NUMBER ORDER BY DEVICE_NUMBER SEPARATOR ', '),')') AS device_numbers,
        min(end_date) end_date
        from t_sanquan_d_sx_zfdq
        <where> 1=1
            <if test="query.dayId != null">
                and day_id = #{query.dayId}
            </if>
            <if test="query.monthId != null">
                and month_id = #{query.monthId}
            </if>
        </where>
        group by ceo_email,CEO_STAFF_NAME,PRODUCT_NAME,CUST_NAME
        order by ceo_email,CEO_STAFF_NAME,PRODUCT_NAME,CUST_NAME
        ) a
        GROUP BY ceo_email,CEO_STAFF_NAME,CUST_NAME
        ORDER BY ceo_email,CEO_STAFF_NAME,CUST_NAME
    </select>

    <select id="validSendMsgSMS" resultMap="BaseResultMap">
        select * from t_sanquan_sms_log
        where business_type = #{query.businessType} and send_time BETWEEN #{query.startTime} and #{query.endTime}
          and telphone = #{query.telephone} and LOCATE(#{query.content}, send_content) > 0 and is_send = 1
    </select>

    <select id="validSendMsgEmail" resultMap="BaseResultMap">
        select * from t_sanquan_email_log
        where business_type = #{query.businessType} and create_date BETWEEN #{query.startTime} and #{query.endTime}
          and email = #{query.email} and LOCATE(#{query.emailContent}, content) > 0 and status = '成功'
    </select>
</mapper>
