<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.chengxiaoNew.mapper.ChengxiaoDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoDetail">
        <result column="task_id" property="taskId" />
        <result column="data_source" property="dataSource" />
        <result column="STATUS" property="status" />
        <result column="exec_status" property="execStatus" />
        <result column="province_industry_name" property="provinceIndustryName" />
        <result column="province_detail_industry_name" property="provinceDetailIndustryName" />
        <result column="head_industry_name" property="headIndustryName" />
        <result column="prov_name" property="provName" />
        <result column="city_name" property="cityName" />
        <result column="district_name" property="districtName" />
        <result column="grid_name" property="gridName" />
        <result column="dispatch_source" property="dispatchSource" />
        <result column="business_type" property="businessType" />
        <result column="detail_scene" property="detailScene" />
        <result column="marketing_number" property="marketingNumber" />
        <result column="marketing_name" property="marketingName" />
        <result column="executor_name" property="executorName" />
        <result column="executor_oa_id" property="executorOaId" />
        <result column="executor_phone" property="executorPhone" />
        <result column="exec_time" property="execTime" />

        <result column="creator" property="creator" />
        <result column="creator_oa_id" property="creatorOaId" />
        <result column="creator_phone" property="creatorPhone" />
        <result column="dispatch_time" property="dispatchTime" />
        <result column="visit_result" property="visitResult" />
        <result column="is_transform" property="isTransform" />

        <result column="NATURE_CUST_ID" property="natureCustId" />
        <result column="NATURE_CUST_NAME" property="natureCustName" />
        <result column="detail_CUST_NAME" property="detailCustName" />
        <result column="PUSH_CONTENT" property="pushContent" />
        <result column="transform_num" property="transformNum" />
        <result column="transform_fenmu" property="transformFenmu" />

        <result column="OPPO_AMOUNT" property="oppoAmount" />
        <result column="OPPO_NUM" property="oppoNum" />
        <result column="CONTRACT_AMOUNT" property="contractAmout" />
        <result column="CONTRACT_NUM" property="contractNum" />
        <result column="REVENUE_AMOUNT" property="revenueAmout" />
        <result column="PROJECT_NUM" property="projectNum" />

        <result column="prev_new_develop_business" property="prevNewDevelopBusiness" />
        <result column="new_develop_business" property="newDevelopBusiness" />
        <result column="develop_business_id" property="developBusinessId" />
        <result column="prev_new_develop_business_amount" property="prevNewDevelogBusinessAmount" />
        <result column="new_develop_business_amount" property="newDevelopBusinessAmount" />

        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        data_source, bus_scene, prov_name, city_name, district_name, task_num, zhixing, wei_zhixing, zhixing_rate, oppo_num, oppo_amount, zhuanhua_num, zhuanhua_rate, project_num, project_amount, new_develop_business, new_develop_business_amount, new_develop_business_rate, new_develop_business_amount_rate, create_time
    </sql>

    <select id="getDataSourceList" resultType="java.lang.String">
        select data_source from t_sanquan_chengxiao_zhuanhua_result where data_source != '合计' group by data_source
    </select>




</mapper>
