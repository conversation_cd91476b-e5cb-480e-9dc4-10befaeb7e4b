<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.chengxiao.mapper.ChengxiaoZhuanhuaMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.chengxiao.entity.ChengxiaoZhuanhua">
        <result column="data_source" property="dataSource" />
        <result column="bus_scene" property="busScene" />
        <result column="prov_name" property="provName" />
        <result column="city_name" property="cityName" />
        <result column="district_name" property="districtName" />
        <result column="task_num" property="taskNum" />
        <result column="zhixing" property="zhixing" />
        <result column="wei_zhixing" property="weiZhixing" />
        <result column="zhixing_rate" property="zhixingRate" />
        <result column="oppo_num" property="oppoNum" />
        <result column="oppo_amount" property="oppoAmount" />
        <result column="zhuanhua_num" property="zhuanhuaNum" />
        <result column="zhuanhua_rate" property="zhuanhuaRate" />
        <result column="zhuanhua_amount_rate" property="zhuanhuaAmountRate" />
        <result column="project_num" property="projectNum" />
        <result column="project_amount" property="projectAmount" />
        <result column="new_develop_business" property="newDevelopBusiness" />
        <result column="new_develop_business_amount" property="newDevelopBusinessAmount" />
        <result column="new_develop_business_rate" property="newDevelopBusinessRate" />
        <result column="new_develop_business_amount_rate" property="newDevelopBusinessAmountRate" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        data_source, bus_scene, prov_name, city_name, district_name, task_num, zhixing, wei_zhixing, zhixing_rate, oppo_num, oppo_amount, zhuanhua_num, zhuanhua_rate, project_num, project_amount, new_develop_business, new_develop_business_amount, new_develop_business_rate, new_develop_business_amount_rate, create_time
    </sql>

    <select id="getDataSourceList" resultType="java.lang.String">
        select data_source from t_sanquan_chengxiao_zhuanhua_result where data_source != '合计' group by data_source
    </select>

    <sql id="selectPageColumn">
        select data_source, bus_scene, prov_name, city_name, district_name, task_num, zhixing,
        wei_zhixing, CAST(zhixing_rate AS DECIMAL(10, 4)) zhixing_rate, oppo_num, CAST(oppo_amount AS DECIMAL(10, 4)) oppo_amount, zhuanhua_num,
        CAST(zhuanhua_rate AS DECIMAL(10, 4)) zhuanhua_rate, project_num, CAST(zhuanhua_amount_rate AS DECIMAL(10, 4)) zhuanhua_amount_rate,
        project_amount, new_develop_business, new_develop_business_amount,
        CAST(new_develop_business_rate AS DECIMAL(10, 4)) new_develop_business_rate,
        CAST(new_develop_business_amount_rate AS DECIMAL(10, 4)) new_develop_business_amount_rate,
        create_time,res_sort
    </sql>
    <select id="selectPageProv" resultMap="BaseResultMap">
        select a.* from (
            <include refid="selectPageColumn"/>
            from t_sanquan_chengxiao_zhuanhua_result
            where prov_name = '山东' and (city_name is null or city_name = '') and (bus_scene is null or bus_scene= '')
            <if test="query.dataSource != null and query.dataSource != ''">
                and data_source = #{query.dataSource}
            </if>
            <if test="query.createTime != null and query.createTime != ''">
                and create_time = #{query.createTime}
            </if>
            <if test="query.cityName != null and query.cityName != ''">
                and city_name = #{query.cityName}
            </if>
            <if test="query.busScene != null and query.busScene != ''">
                and bus_scene like concat('%',#{query.busScene},'%')
            </if>
            UNION
            <include refid="selectPageColumn"/>
            from t_sanquan_chengxiao_zhuanhua_result
            where prov_name = '山东' and (city_name is null or city_name = '') and ( bus_scene like '靶向营销-%' )
            <if test="query.dataSource != null and query.dataSource != ''">
                and data_source = #{query.dataSource}
            </if>
            <if test="query.createTime != null and query.createTime != ''">
                and create_time = #{query.createTime}
            </if>
            <if test="query.cityName != null and query.cityName != ''">
                and city_name = #{query.cityName}
            </if>
            <if test="query.busScene != null and query.busScene != ''">
                and bus_scene like concat('%',#{query.busScene},'%')
            </if>
        ) a
        left join t_sanquan_chengxiao_zhuanhua_result_sort b on a.data_source = b.data_source and b.data_type = 1
        ORDER BY b.res_sort
    </select>

    <select id="selectPageCountry" resultMap="BaseResultMap">
        <include refid="selectPageColumn"/>
        from t_sanquan_chengxiao_zhuanhua_result
        where data_source = #{query.dataSource}
        and create_time = #{query.createTime}
        <if test="query.busScene != null and query.busScene != ''">
            and bus_scene like concat('%',#{query.busScene},'%')
        </if>
        <if test="query.cityName != null and query.cityName != ''">
            and city_name = #{query.cityName}
        </if>
        <if test="query.dataSource == '线索' or query.dataSource == '标讯'">
            order by res_sort,cast(task_num as decimal) desc
        </if>
        <if test="query.dataSource != '线索' and query.dataSource != '标讯'">
            order by city_name,res_sort,cast(task_num as decimal) desc
        </if>

    </select>
    <select id="selectPageCountrybak" resultMap="BaseResultMap">
        <include refid="selectPageColumn"/>
        from t_sanquan_chengxiao_zhuanhua_result
        <if test="query.dataSource != null and (query.dataSource == '线索' or query.dataSource == '标讯' ) ">
            where prov_name = '山东' and (city_name is null or city_name = '') and (bus_scene is null or bus_scene= '')
        </if>
        <if test="query.dataSource != null and (query.dataSource != '线索' and query.dataSource != '标讯' ) ">
            where prov_name = '山东' and (city_name is null or city_name = '') and ( bus_scene like '靶向营销-%' )
        </if>
        and data_source = #{query.dataSource}
        and create_time = #{query.createTime}
         union
        <include refid="selectPageColumn"/>
        from t_sanquan_chengxiao_zhuanhua_result
        where (city_name is not null and city_name != '') and (bus_scene is null or bus_scene= '')
        <if test="query.dataSource != null and query.dataSource != ''">
            and data_source = #{query.dataSource}
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            and create_time = #{query.createTime}
        </if>
        <if test="query.busScene != null and query.busScene != ''">
            and bus_scene like concat('%',#{query.busScene},'%')
        </if>
        UNION
        <include refid="selectPageColumn"/>
        from t_sanquan_chengxiao_zhuanhua_result
        where (city_name is not null and city_name != '') and ( bus_scene not like '靶向营销-%' )
        <if test="query.dataSource != null and query.dataSource != ''">
            and data_source = #{query.dataSource}
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            and create_time = #{query.createTime}
        </if>
        <if test="query.busScene != null and query.busScene != ''">
            and bus_scene like concat('%',#{query.busScene},'%')
        </if>
        order by res_sort,cast(task_num as decimal) desc
    </select>

    <select id="getMaxDate" resultType="java.lang.String">
        select max(create_time) create_time from t_sanquan_chengxiao_zhuanhua_result
    </select>

    <select id="getCityList" resultType="java.lang.String">
         select city_name from t_sanquan_chengxiao_zhuanhua_result where city_name is not null and city_name != '' and city_name not like '%合计%' and res_sort is not null
         group by city_name order by res_sort,cast(task_num as decimal) desc
    </select>

</mapper>
