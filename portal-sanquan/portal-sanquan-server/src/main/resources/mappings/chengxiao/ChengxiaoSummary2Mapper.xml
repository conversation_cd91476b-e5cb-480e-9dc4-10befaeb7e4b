<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.chengxiaoNew.mapper.ChengxiaoSummary2Mapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <result column="data_source" property="dataSource" />
        <result column="bus_scene" property="busScene" />
        <result column="prov_name" property="provName" />
        <result column="city_name" property="cityName" />
        <result column="district_name" property="districtName" />
        <result column="task_num" property="taskNum" />
        <result column="zhixing" property="zhixing" />
        <result column="wei_zhixing" property="weiZhixing" />
        <result column="zhixing_rate" property="zhixingRate" />
        <result column="oppo_num" property="oppoNum" />
        <result column="oppo_amount" property="oppoAmount" />
        <result column="zhuanhua_num" property="zhuanhuaNum" />
        <result column="zhuanhua_rate" property="zhuanhuaRate" />
        <result column="zhuanhua_amount_rate" property="zhuanhuaAmountRate" />
        <result column="project_num" property="projectNum" />
        <result column="project_amount" property="projectAmount" />
        <result column="new_develop_business" property="newDevelopBusiness" />
        <result column="new_develop_business_amount" property="newDevelopBusinessAmount" />
        <result column="new_develop_business_rate" property="newDevelopBusinessRate" />
        <result column="new_develop_business_amount_rate" property="newDevelopBusinessAmountRate" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        data_source, bus_scene, prov_name, city_name, district_name, task_num, zhixing, wei_zhixing, zhixing_rate, oppo_num, oppo_amount, zhuanhua_num, zhuanhua_rate, project_num, project_amount, new_develop_business, new_develop_business_amount, new_develop_business_rate, new_develop_business_amount_rate, create_time
    </sql>
    <sql id="getDataByParamSql">
        with chengxiaoTaskCodeTable as (
        select de.*
        from t_sanquan_chengxiao_detail de
        where de.data_source in ('1','3','4')
        <if test="query.dataSource != null and query.dataSource != ''">
            and de.data_source = #{query.dataSource}
        </if>
        <if test="query.cityName != null and query.cityName != ''">
            and de.city_name = #{query.cityName}
        </if>
        <if test="query.provinceIndustryName != null and query.provinceIndustryName != ''">
            and de.province_industry_name = #{query.provinceIndustryName}
        </if>
        <if test="query.provinceDetailIndustryName != null and query.provinceDetailIndustryName != ''">
            and de.PROVINCE_DETAIL_INDUSTRY_NAME = #{query.provinceDetailIndustryName}
        </if>
        <if test="query.districtName != null and query.districtName != ''">
            and de.district_name like concat('%',#{query.districtName},'%')
        </if>
        <if test="query.gridName != null and query.gridName != ''">
            and grid_name like concat('%',#{query.gridName},'%')
        </if>
        <if test="query.executorName != null and query.executorName != ''">
            and de.executor_name like concat('%',#{query.executorName},'%')
        </if>
        <if test="query.detailScene != null and query.detailScene != ''">
            and de.detail_scene like concat('%', #{query.detailScene},'%')
        </if>
        <if test="query.dispatchSource != null and query.dispatchSource != ''">
            and de.DISPATCH_SOURCE = #{query.dispatchSource}
        </if>
        <if test="query.businessType != null and query.businessType != ''">
            <if test="query.businessType == 'SW'">
                AND (de.business_type = 'SW' OR de.business_type = 'SWLW')
            </if>
            <if test="query.businessType == 'LW'">
                AND (de.business_type = 'LW' OR de.business_type = 'SWLW')
            </if>
        </if>
        <if test="query.createTime != null and query.createTime != ''">
            AND SUBSTRING(CAST(#{query.createTime} AS CHAR), 1, 6) = de.MONTH_ID
            AND SUBSTRING(CAST(#{query.createTime} AS CHAR), 7, 2) = de.DAY_ID
        </if>
        <if test="query.marketingName != null and query.marketingName != ''">
            and de.MARKETING_NAME like concat('%',#{query.marketingName},'%')
        </if>
        <if test="query.isShowDBCP != null and query.isShowDBCP != ''">
            and de.MARKETING_NAME != '等保测评'
        </if>
        <if test="query.creatorOaId != null and query.creatorOaId != ''">
            and de.creator_oa_id like concat('%',#{query.creatorOaId},'%')
        </if>
        <if test="query.startTime != null and query.endTime != ''">
            and de.create_time between #{query.startTime} and #{query.endTime}
        </if>
        ${query.dataScopeSqlFilter}
        ),
        catchTableDetailInfo as (
            select
            ROW_NUMBER() OVER (PARTITION BY OPPO_NUMBER ORDER BY OPPO_CREATED_DATE desc) AS rn1,
            ROW_NUMBER() OVER (PARTITION BY PROJECT_NUMBER ORDER BY PROJECT_CREATED_DATE desc) AS rn2,
            ROW_NUMBER() OVER (PARTITION BY CONTRACT_NUMBER ORDER BY CONTRACT_CREATED_DATE desc) AS rn3,
            copci.TASK_CODE,copci.OPPO_NUMBER ,copci.OPPO_AMOUNT ,copci.CONTRACT_NUMBER ,copci.CONTRACT_AMOUNT,copci.PROJECT_NUMBER ,copci.PROJECT_AMOUNT,
            tscd.data_source,tscd.province_industry_name,tscd.grid_name,tscd.executor_name,tscd.city_name,tscd.district_name
            from chengxiaoTaskCodeTable tscd
            left join T_SANQUAN_CHENGXIAO_OPPO_PROJ_CONTRACT_INFO copci on tscd.task_id = copci.TASK_CODE
            and tscd.MONTH_ID = copci.MONTH_ID
            and tscd.DAY_ID = copci.DAY_ID
            where tscd.data_source in ('1','3','4')
            and tscd.task_id is not null
            and tscd.task_id != ''
        ),
        catchTableLeft as (
            select a.*,
            ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
            ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
            ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
            ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate
            from (
                select
                data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name, city_name, district_name,
                grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
                count(if(exec_status = 0 and MARKETING_NAME != '等保测评' and create_time > '2025', 1, null)) weiZhixing,
                count(if(exec_status = 1 and MARKETING_NAME != '等保测评' and create_time > '2025', 1, null)) zhixing,
                sum(transform_num) zhuanhuaNum,
                sum(transform_fenmu) transformFenmu,

                sum(new_develop_business) newDevelopBusiness,
                sum(new_develop_business_amount) newDevelopBusinessAmount,
                sum(prev_new_develop_business) preNewDevelopBusiness,
                sum(prev_new_develop_business_amount) preNewDevelopBusinessAmount
                from chengxiaoTaskCodeTable
                group by ${query.column}
            ) a
        )
    </sql>

    <select id="getDataByParamlevel1" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select
        lefta.data_source, '' as dataSourceType, lefta.province_industry_name, lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
           select data_source,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by data_source
        ) info1 on lefta.data_source = info1.data_source
        left join (
           select data_source,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by data_source
        ) info2 on lefta.data_source = info2.data_source
        left join (
           select data_source,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by data_source
        ) info3 on lefta.data_source = info3.data_source
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
         ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
           ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
           ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
           ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
           select 'heji' as data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
                  city_name, district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
                sum(weiZhixing) weiZhixing ,
                sum(zhixing) zhixing ,
                sum(zhuanhuaNum) zhuanhuaNum ,
                sum(transformFenmu) transformFenmu ,
                sum(newDevelopBusiness) newDevelopBusiness,
                sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
                sum(preNewDevelopBusiness) preNewDevelopBusiness,
                sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
                  zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
           from catchTableLeft group by '1'
        ) lefta
        left join (
           select 'heji' as data_source,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.data_source = info1.data_source
        left join (
           select 'heji' as data_source,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.data_source = info2.data_source
        left join (
           select 'heji' as data_source,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.data_source = info3.data_source
        order by data_source
    </select>

    <select id="getDataByParamlevel1City" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select * from (
            select
        if(('1' = '' || '1' = null),
        '400',
        '1') AS data_source, '1' as dataSourceType, lefta.province_industry_name, lefta.province_detail_industry_name, lefta.head_industry_name, ifnull(lefta.prov_name, '山东') AS prov_name,
            lefta.city_name as cityName, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
            (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
            ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
            ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
            from catchTableLeft lefta
            left join (
              select city_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by city_name
            ) info1 on lefta.city_name = info1.city_name
            left join (
              select city_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by city_name
            ) info2 on lefta.city_name = info2.city_name
            left join (
              select city_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by city_name
            ) info3 on lefta.city_name = info3.city_name
            union ALL
            select
            '0合计' as data_source, '1' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
            lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
            (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
            ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
            ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
            ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
            ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
            info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
            from (
                select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
                'heji' as city_name, district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
                sum(weiZhixing) weiZhixing ,
                sum(zhixing) zhixing ,
                sum(zhuanhuaNum) zhuanhuaNum ,
                sum(transformFenmu) transformFenmu ,
                sum(newDevelopBusiness) newDevelopBusiness,
                sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
                sum(preNewDevelopBusiness) preNewDevelopBusiness,
                sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
                zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
                from catchTableLeft group by '1'
            ) lefta
            left join (
              select 'heji' as city_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
            ) info1 on lefta.city_name = info1.city_name
            left join (
              select 'heji' as city_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
            ) info2 on lefta.city_name = info2.city_name
            left join (
              select 'heji' as city_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
            ) info3 on lefta.city_name = info3.city_name
        ) a
        <if test="query.cityCodeOrder != null and query.cityCodeOrder == 'cityCode'">
            left join t_sanquan_city b on a.cityName = b.city_name and b.city_id > '0500' AND b.is_status = 1
        </if>
        <if test="query.orderColumn != null ">
            order by ${query.orderColumn}
        </if>
    </select>


    <select id="getDataByParamlevel2Hangye" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType,
        if(lefta.province_industry_name = '未知','0未知',lefta.province_industry_name) as province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select province_industry_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by province_industry_name
        ) info1 on lefta.province_industry_name = info1.province_industry_name
        left join (
          select province_industry_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by province_industry_name
        ) info2 on lefta.province_industry_name = info2.province_industry_name
        left join (
          select province_industry_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by province_industry_name
        ) info3 on lefta.province_industry_name = info3.province_industry_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select data_source, '' as dataSourceType, 'heji' as province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as province_industry_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.province_industry_name = info1.province_industry_name
        left join (
          select 'heji' as province_industry_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.province_industry_name = info2.province_industry_name
        left join (
          select 'heji' as province_industry_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.province_industry_name = info3.province_industry_name
        order by data_source,province_industry_name
    </select>

    <select id="getDataByParamlevel4" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType, province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select grid_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by grid_name
        ) info1 on lefta.grid_name = info1.grid_name
        left join (
          select grid_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by grid_name
        ) info2 on lefta.grid_name = info2.grid_name
        left join (
          select grid_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by grid_name
        ) info3 on lefta.grid_name = info3.grid_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, 'heji' as grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as grid_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.grid_name = info1.grid_name
        left join (
          select 'heji' as grid_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.grid_name = info2.grid_name
        left join (
          select 'heji' as grid_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.grid_name = info3.grid_name
        order by data_source,grid_name
    </select>

    <select id="getDataByParamlevel3" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType, province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select district_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by district_name
        ) info1 on lefta.district_name = info1.district_name
        left join (
          select district_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by district_name
        ) info2 on lefta.district_name = info2.district_name
        left join (
          select district_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by district_name
        ) info3 on lefta.district_name = info3.district_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, 'heji' as district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as  district_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.district_name = info1.district_name
        left join (
          select 'heji' as district_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.district_name = info2.district_name
        left join (
          select 'heji' as district_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.district_name = info3.district_name
        order by data_source,district_name
    </select>

    <select id="getDataByParamlevel5" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType, province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, lefta.executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select executor_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by executor_name
        ) info1 on lefta.executor_name = info1.executor_name
        left join (
          select executor_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by executor_name
        ) info2 on lefta.executor_name = info2.executor_name
        left join (
          select executor_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by executor_name
        ) info3 on lefta.executor_name = info3.executor_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, lefta.executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from (
            select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, grid_name, dispatch_source, business_type, 'heji' as executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as executor_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.executor_name = info1.executor_name
        left join (
          select 'heji' as executor_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.executor_name = info2.executor_name
        left join (
          select 'heji' as executor_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.executor_name = info3.executor_name
        order by data_source,executor_name
    </select>


    <!--  导出列表不分页  -->
    <select id="getDataByParamlevel1NoPage" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select
        lefta.data_source, '' as dataSourceType, lefta.province_industry_name, lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select data_source,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by data_source
        ) info1 on lefta.data_source = info1.data_source
        left join (
          select data_source,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by data_source
        ) info2 on lefta.data_source = info2.data_source
        left join (
         select data_source,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by data_source
        ) info3 on lefta.data_source = info3.data_source
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select 'heji' as data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as data_source,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.data_source = info1.data_source
        left join (
          select 'heji' as data_source,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.data_source = info2.data_source
        left join (
          select 'heji' as data_source,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.data_source = info3.data_source
        order by data_source
    </select>

    <select id="getDataByParamlevel1CityNoPage" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select * from (
        select
        if(('1' = '' || '1' = null),
        '400',
        '1') AS data_source, '1' as dataSourceType, lefta.province_industry_name, lefta.province_detail_industry_name, lefta.head_industry_name, ifnull(lefta.prov_name, '山东') AS prov_name,
        lefta.city_name as cityName, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
        select city_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by city_name
        ) info1 on lefta.city_name = info1.city_name
        left join (
        select city_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by city_name
        ) info2 on lefta.city_name = info2.city_name
        left join (
        select city_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by city_name
        ) info3 on lefta.city_name = info3.city_name
        union ALL
        select
        '0合计' as data_source, '1' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
        select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
        'heji' as city_name, district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
        sum(weiZhixing) weiZhixing ,
        sum(zhixing) zhixing ,
        sum(zhuanhuaNum) zhuanhuaNum ,
        sum(transformFenmu) transformFenmu ,
        sum(newDevelopBusiness) newDevelopBusiness,
        sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
        sum(preNewDevelopBusiness) preNewDevelopBusiness,
        sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
        from catchTableLeft group by '1'
        ) lefta
        left join (
        select 'heji' as city_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.city_name = info1.city_name
        left join (
        select 'heji' as city_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.city_name = info2.city_name
        left join (
        select 'heji' as city_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.city_name = info3.city_name
        ) a
        <if test="query.cityCodeOrder != null and query.cityCodeOrder == 'cityCode'">
            left join t_sanquan_city b on a.cityName = b.city_name and b.city_id > '0500' AND b.is_status = 1
        </if>
        <if test="query.orderColumn != null ">
            order by ${query.orderColumn}
        </if>
    </select>


    <select id="getDataByParamlevel2HangyeNoPage" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType,
        if(lefta.province_industry_name = '未知','0未知',lefta.province_industry_name) as province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select province_industry_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by province_industry_name
        ) info1 on lefta.province_industry_name = info1.province_industry_name
        left join (
          select province_industry_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by province_industry_name
        ) info2 on lefta.province_industry_name = info2.province_industry_name
        left join (
          select province_industry_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by province_industry_name
        ) info3 on lefta.province_industry_name = info3.province_industry_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select data_source, '' as dataSourceType, 'heji' as province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as province_industry_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.province_industry_name = info1.province_industry_name
        left join (
          select 'heji' as province_industry_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.province_industry_name = info2.province_industry_name
        left join (
          select 'heji' as province_industry_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.province_industry_name = info3.province_industry_name
        order by data_source,province_industry_name
    </select>

    <select id="getDataByParamlevel4NoPage" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType, province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select grid_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by grid_name
        ) info1 on lefta.grid_name = info1.grid_name
        left join (
          select grid_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by grid_name
        ) info2 on lefta.grid_name = info2.grid_name
        left join (
          select grid_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by grid_name
        ) info3 on lefta.grid_name = info3.grid_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, 'heji' as grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as grid_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.grid_name = info1.grid_name
        left join (
          select 'heji' as grid_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.grid_name = info2.grid_name
        left join (
          select 'heji' as grid_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.grid_name = info3.grid_name
        order by data_source,grid_name
    </select>

    <select id="getDataByParamlevel3NoPage" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType, province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select district_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by district_name
        ) info1 on lefta.district_name = info1.district_name
        left join (
          select district_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by district_name
        ) info2 on lefta.district_name = info2.district_name
        left join (
          select district_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by district_name
        ) info3 on lefta.district_name = info3.district_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        info1.oppoNum,info1.oppoAmount,info2.projectNum,info2.projectAmount,info3.contractNum,info3.contractAmount
        from (
            select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, 'heji' as district_name, grid_name, dispatch_source, business_type, executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
          select 'heji' as district_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.district_name = info1.district_name
        left join (
          select 'heji' as district_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.district_name = info2.district_name
        left join (
          select 'heji' as district_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.district_name = info3.district_name
        order by data_source,district_name
    </select>

    <select id="getDataByParamlevel5NoPage" resultType="cn.chinaunicom.sdsi.chengxiaoNew.entity.ChengXiaoSummary">
        <include refid="getDataByParamSql"></include>
        select '1' as data_source,#{query.dataSource} as dataSourceType, province_industry_name,
        lefta.province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, lefta.executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from catchTableLeft lefta
        left join (
          select executor_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by executor_name
        ) info1 on lefta.executor_name = info1.executor_name
        left join (
          select executor_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by executor_name
        ) info2 on lefta.executor_name = info2.executor_name
        left join (
          select executor_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by executor_name
        ) info3 on lefta.executor_name = info3.executor_name
        union ALL
        select
        '0合计' as data_source, '' as dataSourceType, '' province_industry_name, '' province_detail_industry_name, lefta.head_industry_name, lefta.prov_name,
        lefta.city_name, lefta.district_name, lefta.grid_name, lefta.dispatch_source, lefta.business_type, lefta.executor_name, marketing_number, marketing_name, detail_scene,
        (weiZhixing+zhixing) taskNum,weiZhixing,zhixing,zhuanhuaNum,transformFenmu,newDevelopBusiness,newDevelopBusinessAmount,preNewDevelopBusiness,preNewDevelopBusinessAmount,
        ROUND(zhixing /(weiZhixing + zhixing), 4) zhixingRate,
        ROUND(zhuanhuaNum / transformFenmu, 4) as zhuanhuaRate,
        ROUND(newDevelopBusiness / preNewDevelopBusiness, 4) as newDevelopBusinessRate,
        ROUND(newDevelopBusinessAmount / preNewDevelopBusinessAmount, 4) as newDevelopBusinessAmountRate,
        ifnull(info1.oppoNum,0) as oppoNum,ifnull(info1.oppoAmount,0) as oppoAmount,ifnull(info2.projectNum,0) as projectNum,ifnull(info2.projectAmount,0) as projectAmount,
        ifnull(info3.contractNum,0) as contractNum,ifnull(info3.contractAmount,0) as contractAmount
        from (
            select data_source, '' as dataSourceType, province_industry_name, province_detail_industry_name, head_industry_name, prov_name,
            city_name, district_name, grid_name, dispatch_source, business_type, 'heji' as executor_name, marketing_number, marketing_name, detail_scene,
            sum(weiZhixing) weiZhixing ,
            sum(zhixing) zhixing ,
            sum(zhuanhuaNum) zhuanhuaNum ,
            sum(transformFenmu) transformFenmu ,
            sum(newDevelopBusiness) newDevelopBusiness,
            sum(newDevelopBusinessAmount) newDevelopBusinessAmount,
            sum(preNewDevelopBusiness) preNewDevelopBusiness,
            sum(preNewDevelopBusinessAmount) preNewDevelopBusinessAmount,
            zhixingRate,zhuanhuaRate,newDevelopBusinessRate,newDevelopBusinessAmountRate
            from catchTableLeft group by '1'
        ) lefta
        left join (
         select 'heji' as executor_name,count(oppo_number) as oppoNum,sum(OPPO_AMOUNT) as oppoAmount from catchTableDetailInfo where rn1=1 and oppo_number != '' group by '1'
        ) info1 on lefta.executor_name = info1.executor_name
        left join (
          select 'heji' as executor_name,count(PROJECT_NUMBER) as projectNum,sum(PROJECT_AMOUNT) as projectAmount from catchTableDetailInfo where rn2=1 and PROJECT_NUMBER != '' group by '1'
        ) info2 on lefta.executor_name = info2.executor_name
        left join (
          select 'heji' as executor_name,count(CONTRACT_NUMBER) as contractNum,sum(CONTRACT_AMOUNT) as contractAmount from catchTableDetailInfo where rn3=1 and CONTRACT_NUMBER != '' group by '1'
        ) info3 on lefta.executor_name = info3.executor_name
        order by data_source,executor_name
    </select>


</mapper>
