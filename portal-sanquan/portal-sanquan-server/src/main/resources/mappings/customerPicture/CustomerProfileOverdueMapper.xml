<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.customerPicture.mapper.CustomerProfileOverdueMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOverdue">
        <result column="nature_cust_id" property="natureCustId" />
        <result column="nature_cust_name" property="natureCustName" />
        <result column="project_number" property="projectNumber" />
        <result column="project_name" property="projectName" />
        <result column="contract_number" property="contractNumber" />
        <result column="PERIOD_MONTH" property="periodMonth" />
        <result column="max_month" property="maxMonth" />
        <result column="overdue_month_1" property="overdueMonth1" />
        <result column="overdue_month_2" property="overdueMonth2" />
        <result column="overdue_month_3" property="overdueMonth3" />
        <result column="overdue_month_4" property="overdueMonth4" />
        <result column="overdue_month_5" property="overdueMonth5" />
        <result column="overdue_month_6" property="overdueMonth6" />
        <result column="overdue_month_7" property="overdueMonth7" />
        <result column="overdue_month_8" property="overdueMonth8" />
        <result column="overdue_month_9" property="overdueMonth9" />
        <result column="overdue_month_10" property="overdueMonth10" />
        <result column="overdue_month_11" property="overdueMonth11" />
        <result column="overdue_month_12" property="overdueMonth12" />
        <result column="overdue_month_13" property="overdueMonth13" />
        <result column="overdue_month_14" property="overdueMonth14" />
        <result column="overdue_month_15" property="overdueMonth15" />
        <result column="overdue_month_16" property="overdueMonth16" />
        <result column="overdue_month_17" property="overdueMonth17" />
        <result column="overdue_month_18" property="overdueMonth18" />
        <result column="overdue_month_19" property="overdueMonth19" />
        <result column="overdue_month_20" property="overdueMonth20" />
        <result column="overdue_month_21" property="overdueMonth21" />
        <result column="overdue_month_22" property="overdueMonth22" />
        <result column="overdue_month_23" property="overdueMonth23" />
        <result column="overdue_month_24" property="overdueMonth24" />
        <result column="overdue_month_25_36" property="overdueMonth2536" />
        <result column="overdue_month_37_48" property="overdueMonth3748" />
        <result column="overdue_month_49_60" property="overdueMonth4960" />
        <result column="overdue_month_61_x" property="overdueMonth61X" />
        <result column="overdue_month_total" property="overdueMonthTotal" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nature_cust_id, nature_cust_name, project_number, project_name, contract_number, PERIOD_MONTH, overdue_month_1, overdue_month_2, overdue_month_3, overdue_month_4, overdue_month_5, overdue_month_6, overdue_month_7, overdue_month_8, overdue_month_9, overdue_month_10, overdue_month_11, overdue_month_12, overdue_month_13, overdue_month_14, overdue_month_15, overdue_month_16, overdue_month_17, overdue_month_18, overdue_month_19, overdue_month_20, overdue_month_21, overdue_month_22, overdue_month_23, overdue_month_24, overdue_month_25_36, overdue_month_37_48, overdue_month_49_60, overdue_month_61_x, overdue_month_total
    </sql>

    <select id="getMaxOverdueByCustomer" resultMap="BaseResultMap">
select * from t_sanquan_customer_profile_overdue where nature_cust_id = #{natureCustId} limit 1
    </select>

    <select id="getArrearsInfo" resultMap="BaseResultMap">
        select b.nature_cust_id, b.nature_cust_name, max(b.max_month) as max_month, sum(cast(overdue_month_total as decimal)) overdue_month_total from (select
        nature_cust_id,
        nature_cust_name,
        project_number,
        project_name,
        contract_number,
        period_month,
        max_period,
        max_month,
        row_number()over(partition by nature_cust_id, project_number order by max_month desc) as rk,
        overdue_month_total
        from (
        select
        nature_cust_id, nature_cust_name,
        project_number,
        project_name,
        contract_number,
        period_month,
        case when overdue_month_1 &lt;&gt; '0' then '1月'
        when overdue_month_2 &lt;&gt; '0' then '2月'
        when overdue_month_3 &lt;&gt; '0' then '3月'
        when overdue_month_4 &lt;&gt; '0' then '4月'
        when overdue_month_5 &lt;&gt; '0' then '5月'
        when overdue_month_6 &lt;&gt; '0' then '6月'
        when overdue_month_7 &lt;&gt; '0' then '7月'
        when overdue_month_8 &lt;&gt; '0' then '8月'
        when overdue_month_9 &lt;&gt; '0' then '9月'
        when overdue_month_10 &lt;&gt; '0' then '10月'
        when overdue_month_11 &lt;&gt; '0' then '11月'
        when overdue_month_12 &lt;&gt; '0' then '12月'
        when overdue_month_13 &lt;&gt; '0' then '13月'
        when overdue_month_14 &lt;&gt; '0' then '14月'
        when overdue_month_15 &lt;&gt; '0' then '15月'
        when overdue_month_16 &lt;&gt; '0' then '16月'
        when overdue_month_17 &lt;&gt; '0' then '17月'
        when overdue_month_18 &lt;&gt; '0' then '18月'
        when overdue_month_19 &lt;&gt; '0' then '19月'
        when overdue_month_20 &lt;&gt; '0' then '20月'
        when overdue_month_21 &lt;&gt; '0' then '21月'
        when overdue_month_22 &lt;&gt; '0' then '22月'
        when overdue_month_23 &lt;&gt; '0' then '23月'
        when overdue_month_24 &lt;&gt; '0' then '24月'
        when overdue_month_25_36 &lt;&gt; '0' then '2-3年'
        when overdue_month_37_48 &lt;&gt; '0' then '3-4年'
        when overdue_month_49_60 &lt;&gt; '0' then '4-5年'
        when overdue_month_61_x &lt;&gt; '0' then '>5年'
        else '0月'
        end as max_period,
        case when overdue_month_1 &lt;&gt; '0' then 1
        when overdue_month_2 &lt;&gt; '0' then 2
        when overdue_month_3 &lt;&gt; '0' then 3
        when overdue_month_4 &lt;&gt; '0' then 4
        when overdue_month_5 &lt;&gt; '0' then 5
        when overdue_month_6 &lt;&gt; '0' then 6
        when overdue_month_7 &lt;&gt; '0' then 7
        when overdue_month_8 &lt;&gt; '0' then 8
        when overdue_month_9 &lt;&gt; '0' then 9
        when overdue_month_10 &lt;&gt; '0' then 10
        when overdue_month_11 &lt;&gt; '0' then 11
        when overdue_month_12 &lt;&gt; '0' then 12
        when overdue_month_13 &lt;&gt; '0' then 13
        when overdue_month_14 &lt;&gt; '0' then 14
        when overdue_month_15 &lt;&gt; '0' then 15
        when overdue_month_16 &lt;&gt; '0' then 16
        when overdue_month_17 &lt;&gt; '0' then 17
        when overdue_month_18 &lt;&gt; '0' then 18
        when overdue_month_19 &lt;&gt; '0' then 19
        when overdue_month_20 &lt;&gt; '0' then 20
        when overdue_month_21 &lt;&gt; '0' then 21
        when overdue_month_22 &lt;&gt; '0' then 22
        when overdue_month_23 &lt;&gt; '0' then 23
        when overdue_month_24 &lt;&gt; '0' then 24
        when overdue_month_25_36 &lt;&gt; '0' then 36
        when overdue_month_37_48 &lt;&gt; '0' then 48
        when overdue_month_49_60 &lt;&gt; '0' then 60
        when overdue_month_61_x &lt;&gt; '0' then 61
        else 0
        end as max_month,
        overdue_month_total
        from t_sanquan_customer_profile_overdue where period_month=(
        select max(period_month) from t_sanquan_customer_profile_overdue where nature_cust_id = #{natureCustId}
        ) and nature_cust_id=#{natureCustId}
        ) a ) b where b.rk = 1
        group by b.nature_cust_id, b.nature_cust_name limit 1
    </select>

</mapper>
