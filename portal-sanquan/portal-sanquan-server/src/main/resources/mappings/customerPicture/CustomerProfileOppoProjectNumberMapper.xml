<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.customerPicture.mapper.CustomerProfileOppoProjectNumberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileOppoProjectNumber">
        <result column="nature_cust_id" property="natureCustId" />
        <result column="nature_cust_name" property="natureCustName" />
        <result column="project_number" property="projectNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nature_cust_id, nature_cust_name, project_number
    </sql>

    <select id="getProjectNum" resultType="java.lang.Integer">
SELECT COUNT(1) FROM t_sanquan_customer_profile_project WHERE nature_cust_id = #{natureCustId} AND project_start_date LIKE concat(#{accountPeriod},'%')
    </select>

    <select id="getAccountSum" resultMap="BaseResultMap">
SELECT SUM(revenue_amount) FROM t_sanquan_customer_profile_project WHERE nature_cust_id = #{natureCustId} AND project_start_date LIKE concat(#{accountPeriod},'%')
    </select>

</mapper>
