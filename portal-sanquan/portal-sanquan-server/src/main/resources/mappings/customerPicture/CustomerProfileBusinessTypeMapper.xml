<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.customerPicture.mapper.CustomerProfileBusinessTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.customerPicture.entity.CustomerProfileBusinessType">
        <result column="nature_cust_id" property="natureCustId" />
        <result column="nature_cust_name" property="natureCustName" />
        <result column="service_kind_name" property="serviceKindName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        nature_cust_id, nature_cust_name, service_kind_name
    </sql>

    <select id="selectBusinessTypeName" resultType="String">
        select service_kind_name from t_sanquan_customer_profile_business_type where nature_cust_id = #{natureCustId}
    </select>

    <select id="selectAllBusinessTypeName" resultType="String">
        select service_kind_name from t_sanquan_customer_profile_business_type group by service_kind_name
    </select>

    <select id="getBusinessTypeNum" resultType="java.lang.Integer">
SELECT COUNT(service_kind_name) FROM t_sanquan_customer_profile_business_type WHERE nature_cust_id = #{natureCustId}
    </select>

    <select id="getBusinessTypeTotalNum" resultType="java.lang.Integer">
SELECT COUNT(service_kind_name)FROM (
SELECT service_kind_name FROM t_sanquan_customer_profile_business_type GROUP by SERVICE_KIND_NAME
) a
    </select>

    <select id="getBusinessTypeExpandNum" resultType="java.lang.Integer">
SELECT COUNT(service_kind_name)FROM (
  SELECT service_kind_name FROM t_sanquan_customer_profile_business_type WHERE nature_cust_id = #{natureCustId}
)a
    </select>

</mapper>
