<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.model.mapper.DRosterNatureCustomerInfoMapper">

    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo">
            <result property="rosterCustomerId" column="roster_customer_id" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="rosterCustomerName" column="roster_customer_name" jdbcType="VARCHAR"/>
            <result property="keyCoreCustomer" column="key_core_customer" jdbcType="VARCHAR"/>
            <result property="listCustomerLevel" column="list_customer_level" jdbcType="VARCHAR"/>
            <result property="listCustomerProvince" column="list_customer_province" jdbcType="VARCHAR"/>
            <result property="listCustomerCity" column="list_customer_city" jdbcType="VARCHAR"/>
            <result property="listCustomerDistrict" column="list_customer_district" jdbcType="VARCHAR"/>
            <result property="listCustomerGridName" column="list_customer_grid_name" jdbcType="VARCHAR"/>
            <result property="headquartersIndustry" column="headquarters_industry" jdbcType="VARCHAR"/>
            <result property="provinceIndustry" column="province_industry" jdbcType="VARCHAR"/>
            <result property="chiefCustomerManagerA" column="chief_customer_manager_a" jdbcType="VARCHAR"/>
            <result property="chiefCustomerManagerAOaId" column="chief_customer_manager_a_oa_id" jdbcType="VARCHAR"/>
            <result property="chiefCustomerManagerAMobile" column="chief_customer_manager_a_mobile" jdbcType="VARCHAR"/>
            <result property="serviceCustomerManagerB" column="service_customer_manager_b" jdbcType="VARCHAR"/>
            <result property="serviceCustomerManagerBOaId" column="service_customer_manager_b_oa_id" jdbcType="VARCHAR"/>
            <result property="chiefCustomerManagerBMobile" column="chief_customer_manager_b_mobile" jdbcType="VARCHAR"/>
            <result property="leadExecutive" column="lead_executive" jdbcType="VARCHAR"/>
            <result property="leadExecutiveOaId" column="lead_executive_oa_id" jdbcType="VARCHAR"/>
            <result property="leadExecutiveLevel" column="lead_executive_level" jdbcType="VARCHAR"/>
            <result property="dataScienceContact" column="data_science_contact" jdbcType="VARCHAR"/>
            <result property="dataScienceContactOaId" column="data_science_contact_oa_id" jdbcType="VARCHAR"/>
            <result property="naturalCustomerName" column="natural_customer_name" jdbcType="VARCHAR"/>
            <result property="naturalCustomerId" column="natural_customer_id" jdbcType="VARCHAR"/>
            <result property="unifiedSocialCreditCode" column="unified_social_credit_code" jdbcType="VARCHAR"/>
            <result property="organizationCode" column="organization_code" jdbcType="VARCHAR"/>
            <result property="collectionRelationshipMaintenanceNo" column="collection_relationship_maintenance_no" jdbcType="VARCHAR"/>
            <result property="nationalEconomicIndustry" column="national_economic_industry" jdbcType="VARCHAR"/>
            <result property="headquartersTwoLevelIndustry" column="headquarters_two_level_industry" jdbcType="VARCHAR"/>
            <result property="groupOffice" column="group_office" jdbcType="VARCHAR"/>
            <result property="hasOrganizationalRelationship" column="has_organizational_relationship" jdbcType="VARCHAR"/>
            <result property="customerLevel" column="customer_level" jdbcType="VARCHAR"/>
            <result property="networkStatus" column="network_status" jdbcType="VARCHAR"/>
            <result property="naturalCustomerHasManager" column="natural_customer_has_manager" jdbcType="VARCHAR"/>
            <result property="customerManagerName" column="customer_manager_name" jdbcType="VARCHAR"/>
            <result property="customerManagerId" column="customer_manager_id" jdbcType="VARCHAR"/>
            <result property="totalCustomerInformationEntries" column="total_customer_information_entries" jdbcType="VARCHAR"/>
            <result property="keyPersonCount" column="key_person_count" jdbcType="VARCHAR"/>
            <result property="svipKeyPersonCount" column="svip_key_person_count" jdbcType="VARCHAR"/>
            <result property="visitRecordCount" column="visit_record_count" jdbcType="VARCHAR"/>
            <result property="employeeCountRecord" column="employee_count_record" jdbcType="VARCHAR"/>
            <result property="itInvestmentBudgetRecordCount" column="it_investment_budget_record_count" jdbcType="VARCHAR"/>
            <result property="marketShareDataVolume" column="market_share_data_volume" jdbcType="VARCHAR"/>
            <result property="otherNetworkBusinessInfoCount" column="other_network_business_info_count" jdbcType="VARCHAR"/>
            <result property="officeAddressCount" column="office_address_count" jdbcType="VARCHAR"/>
            <result property="strategicCooperationInfoCount" column="strategic_cooperation_info_count" jdbcType="VARCHAR"/>
            <result property="customerPlanningEntries" column="customer_planning_entries" jdbcType="VARCHAR"/>
            <result property="keyDecisionProcessEntries" column="key_decision_process_entries" jdbcType="VARCHAR"/>
            <result property="orgCode" column="ORG_CODE" jdbcType="VARCHAR"/>
            <result property="tydm" column="TYDM" jdbcType="VARCHAR"/>
            <result property="enrollCode" column="ENROLL_CODE" jdbcType="VARCHAR"/>
            <result property="custAddress" column="CUST_ADDRESS" jdbcType="VARCHAR"/>
            <result property="legalRepresentative" column="LEGAL_REPRESENTATIVE" jdbcType="VARCHAR"/>
            <result property="enrollFund" column="ENROLL_FUND" jdbcType="VARCHAR"/>
            <result property="enrollCurrency" column="ENROLL_CURRENCY" jdbcType="VARCHAR"/>
            <result property="orgType" column="ORG_TYPE" jdbcType="VARCHAR"/>
            <result property="jydz" column="JYDZ" jdbcType="VARCHAR"/>
            <result property="telephone" column="TELEPHONE" jdbcType="VARCHAR"/>
            <result property="eeCount" column="EE_COUNT" jdbcType="VARCHAR"/>
            <result property="privateFlag" column="PRIVATE_FLAG" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        roster_customer_id,customer_id,roster_customer_name,
        key_core_customer,list_customer_level,list_customer_province,
        list_customer_city,list_customer_district,list_customer_grid_name,
        headquarters_industry,province_industry,chief_customer_manager_a,
        chief_customer_manager_a_oa_id,chief_customer_manager_a_mobile,service_customer_manager_b,
        service_customer_manager_b_oa_id,chief_customer_manager_b_mobile,lead_executive,
        lead_executive_oa_id,lead_executive_level,data_science_contact,
        data_science_contact_oa_id,natural_customer_name,natural_customer_id,
        unified_social_credit_code,organization_code,collection_relationship_maintenance_no,
        national_economic_industry,headquarters_two_level_industry,group_office,
        has_organizational_relationship,customer_level,network_status,
        natural_customer_has_manager,customer_manager_name,customer_manager_id,
        total_customer_information_entries,key_person_count,svip_key_person_count,
        visit_record_count,employee_count_record,it_investment_budget_record_count,
        market_share_data_volume,other_network_business_info_count,office_address_count,
        strategic_cooperation_info_count,customer_planning_entries,key_decision_process_entries,
        ORG_CODE,TYDM,ENROLL_CODE,
        CUST_ADDRESS,LEGAL_REPRESENTATIVE,ENROLL_FUND,
        ENROLL_CURRENCY,ORG_TYPE,JYDZ,
        TELEPHONE,EE_COUNT,PRIVATE_FLAG
    </sql>
    <select id="findByOrgCode"
        resultType="cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo"
    >
        <!--
        select *
        from t_sanquan_d_roster_nature_customer_info
        where organization_code = #{orgCode}
        -->
        select t.NATURE_CUST_ID naturalCustomerId,t.NATURE_CUST_NAME naturalCustomerName
        from t_sanquan_src_sd_d_zqzt_cust_t_nature_cust t where t.org_code=#{orgCode}
    </select>
    <select id="findByTydm" resultType="cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo">
        select t.NATURE_CUST_ID naturalCustomerId,t.NATURE_CUST_NAME naturalCustomerName
        from t_sanquan_src_sd_d_zqzt_cust_t_nature_cust t where t.tydm=#{tydm}
    </select>
    <select id="findByNatrueCustId" resultType="cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo">
        select t.NATURE_CUST_ID naturalCustomerId,t.NATURE_CUST_NAME naturalCustomerName
        from t_sanquan_src_sd_d_zqzt_cust_t_nature_cust t where t.NATURE_CUST_ID=#{natureCustomerId}
    </select>
    <select id="findByNatrueCustName" resultType="cn.chinaunicom.sdsi.model.nature.customer.DRosterNatureCustomerInfo">
        select t.NATURE_CUST_ID naturalCustomerId,t.NATURE_CUST_NAME naturalCustomerName
        from t_sanquan_src_sd_d_zqzt_cust_t_nature_cust t where t.NATURE_CUST_NAME=#{natureCustomerName}
    </select>

</mapper>
