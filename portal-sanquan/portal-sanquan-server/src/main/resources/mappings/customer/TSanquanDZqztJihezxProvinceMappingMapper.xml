<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.customer.dao.TSanquanDZqztJihezxProvinceMappingMapper">
    
    <resultMap type="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanDZqztJihezxProvinceMapping" id="TSanquanDZqztJihezxProvinceMappingResult">
        <result property="LOGIN"    column="LOGIN"    />
        <result property="PROVINCE"    column="PROVINCE"    />
        <result property="CITY"    column="CITY"    />
        <result property="NAME"    column="NAME"    />
        <result property="NUMBERS"    column="NUMBERS"    />
        <result property="monthId"    column="MONTH_ID"    />
        <result property="dayId"    column="DAY_ID"    />
    </resultMap>

    <sql id="Base_Column_List">
 LOGIN, PROVINCE, CITY, NAME, NUMBERS, MONTH_ID, DAY_ID    </sql>

    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO">
        SELECT LOGIN, PROVINCE, CITY, NAME, NUMBERS, MONTH_ID, DAY_ID
        FROM t_sanquan_d_zqzt_jihezx_province_mapping
        <where>
            1 = 1
            <if test="query.LOGIN != null  and query.LOGIN !=''">
                AND LOGIN = #{query.LOGIN}
            </if>
            <if test="query.PROVINCE != null  and query.PROVINCE !=''">
                AND PROVINCE = #{query.PROVINCE}
            </if>
            <if test="query.CITY != null  and query.CITY !=''">
                AND CITY = #{query.CITY}
            </if>
            <if test="query.NAME != null  and query.NAME != ''">
                AND NAME LIKE concat('%', #{query.NAME}, '%')
            </if>
            <if test="query.NUMBERS != null  and query.NUMBERS !=''">
                AND NUMBERS = #{query.NUMBERS}
            </if>
            <if test="query.monthId != null  and query.monthId !=''">
                AND MONTH_ID = #{query.monthId}
            </if>
            <if test="query.dayId != null  and query.dayId !=''">
                AND DAY_ID = #{query.dayId}
            </if>
        </where>
    </select>
    <select id="findCustomerManager" resultType="cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO">
        SELECT LOGIN, PROVINCE, CITY, NAME, NUMBERS, MONTH_ID, DAY_ID
        FROM t_sanquan_d_zqzt_jihezx_province_mapping
        <where>
            1 = 1
            <if test="query.LOGIN != null  and query.LOGIN !=''">
                AND LOGIN = #{query.LOGIN}
            </if>
            <if test="query.PROVINCE != null  and query.PROVINCE !=''">
                AND PROVINCE = #{query.PROVINCE}
            </if>
            <if test="query.CITY != null  and query.CITY !=''">
                AND CITY = #{query.CITY}
            </if>
            <if test="query.NAME != null  and query.NAME != ''">
                AND NAME LIKE concat('%', #{query.NAME}, '%')
            </if>
            <if test="query.NUMBERS != null  and query.NUMBERS !=''">
                AND NUMBERS = #{query.NUMBERS}
            </if>
            <if test="query.monthId != null  and query.monthId !=''">
                AND MONTH_ID = #{query.monthId}
            </if>
            <if test="query.dayId != null  and query.dayId !=''">
                AND DAY_ID = #{query.dayId}
            </if>
        </where>
    </select>

</mapper>
