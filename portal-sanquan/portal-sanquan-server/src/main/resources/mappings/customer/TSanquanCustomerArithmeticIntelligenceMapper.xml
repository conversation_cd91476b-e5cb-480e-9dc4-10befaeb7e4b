<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.customer.dao.TSanquanCustomerArithmeticIntelligenceMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerArithmeticIntelligence" id="tSanquanCustomerArithmeticIntelligenceMap">
        <result property="id" column="id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="rosterCustomerName" column="roster_customer_name"/>
        <result property="rosterCustomerId" column="roster_customer_id"/>
        <result property="sort" column="sort"/>
        <result property="projectsName" column="projects_name"/>
        <result property="contractorName" column="contractor_name"/>
        <result property="expireDate" column="expire_date"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
        <result property="createDate" column="create_date"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="versions" column="versions"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="businessType" column="business_type"/>
        <result property="businessName" column="business_name"/>
        <result property="operators" column="operators"/>
        <result property="custom" column="custom"/>
    </resultMap>
    <select id="selectByCustomerIdList"
            resultType="cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerArithmeticIntelligenceVO">
        select * from t_sanquan_customer_arithmetic_intelligence
        where 1=1 and customer_id = #{id}
          and delete_flag = 'normal'
    </select>
    <select id="findPage"
            resultType="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerArithmeticIntelligence">
        select id,
        customer_id,
        customer_name,
        roster_customer_name,
        roster_customer_id,
        sort,
        projects_name,
        contractor_name,
        expire_date,
        attr1,
        attr2,
        attr3,
        business_type,
        business_name,
        operators,
        custom
        from t_sanquan_customer_arithmetic_intelligence
        <where>
            delete_flag = 'normal'
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name Like concat('%', #{query.customerName},'%')
            </if>
            <if test="query.rosterCustomerName != null and query.rosterCustomerName != ''">
                AND roster_customer_name Like concat('%', #{query.rosterCustomerName},'%')
            </if>
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.sort != null and query.sort != ''">
                AND sort = #{query.sort}
            </if>
            <if test="query.projectsName != null and query.projectsName != ''">
                AND projects_name Like concat('%', #{query.projectsName},'%')
            </if>
            <if test="query.contractorName != null and query.contractorName != ''">
                AND contractor_name Like concat('%', #{query.contractorName},'%')
            </if>
        </where>
        order by update_date desc
    </select>
    <select id="findList"
            resultType="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerArithmeticIntelligence">
        select id,
        customer_id,
        customer_name,
        roster_customer_name,
        roster_customer_id,
        sort,
        projects_name,
        contractor_name,
        expire_date,
        attr1,
        attr2,
        attr3,
        business_type,
        business_name,
        operators,
        custom
        from t_sanquan_customer_arithmetic_intelligence
        <where>
            delete_flag = 'normal'
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name Like concat('%', #{query.customerName},'%')
            </if>
            <if test="query.rosterCustomerName != null and query.rosterCustomerName != ''">
                AND roster_customer_name Like concat('%', #{query.rosterCustomerName},'%')
            </if>
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.sort != null and query.sort != ''">
                AND sort = #{query.sort}
            </if>
            <if test="query.projectsName != null and query.projectsName != ''">
                AND projects_name Like concat('%', #{query.projectsName},'%')
            </if>
            <if test="query.contractorName != null and query.contractorName != ''">
                AND contractor_name Like concat('%', #{query.contractorName},'%')
            </if>
        </where>
        order by update_date desc
    </select>
<!--    <select id="findPercentage"-->
<!--            resultType="cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerArithmeticIntelligencePercentageVO">-->
<!--        SELECT-->
<!--            t.business_name,-->
<!--            t.operators,-->
<!--            COUNT(*) AS operatorCount,-->
<!--            ROUND(COUNT(*) * 100 / NULLIF(subquery.total_operators_count, 0), 2) AS percentage-->
<!--        FROM-->
<!--            t_sanquan_customer_arithmetic_intelligence t-->
<!--                LEFT JOIN (-->
<!--                SELECT-->
<!--                    ti.business_name,-->
<!--                    COUNT(*) AS total_operators_count-->
<!--                FROM-->
<!--                    t_sanquan_customer_arithmetic_intelligence ti-->
<!--                WHERE-->
<!--                    ti.customer_id = #{id}-->
<!--                  and ti.delete_flag = 'normal'-->
<!--                GROUP BY-->
<!--                    ti.business_name-->
<!--            ) subquery ON t.business_name = subquery.business_name-->
<!--        WHERE-->
<!--            t.customer_id = #{id}-->
<!--            and t.delete_flag = 'normal'-->
<!--        GROUP BY-->
<!--            t.business_name, t.operators-->
<!--        ORDER BY-->
<!--            t.business_name, operatorCount DESC;-->
<!--    </select>-->

    <select id="findPercentage" resultType="cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerArithmeticIntelligencePercentageVO">
        SELECT
            cd.business_name,
            cd.operators,
            cd.operatorCount,
            ROUND(cd.operatorCount * 100.0 / COALESCE(tc.totalOperatorsCount, 1), 2) AS percentage
        FROM
            (
                SELECT business_name, operators, COUNT(*) AS operatorCount
                FROM (
                         SELECT business_name, operators
                         FROM t_sanquan_customer_arithmetic_intelligence
                         WHERE customer_id = #{id}
                           AND delete_flag = 'normal'
                           AND (operators != '联通' OR operators IS NULL)
                         UNION ALL
                         SELECT business_name, '联通' AS operators
                         FROM t_sanquan_customer_project_order_relations_selected
                         WHERE customer_id_selected = #{id}
                     ) combinedData
                GROUP BY business_name, operators
            ) cd
                LEFT JOIN (
                SELECT business_name,
                       SUM(operatorCount) AS totalOperatorsCount,
                       SUM(CASE WHEN operators = '联通' THEN operatorCount ELSE 0 END) AS unicomOperatorsCount
                FROM (
                         SELECT business_name, operators, COUNT(*) AS operatorCount
                         FROM t_sanquan_customer_arithmetic_intelligence
                         WHERE customer_id = #{id}
                           AND delete_flag = 'normal'
                           AND (operators != '联通' OR operators IS NULL)
                         GROUP BY business_name, operators
                         UNION ALL
                         SELECT business_name, '联通' AS operators, COUNT(*) AS operatorCount
                         FROM t_sanquan_customer_project_order_relations_selected
                         WHERE customer_id_selected = #{id}
                         GROUP BY business_name
                     ) subquery
                GROUP BY business_name
            ) tc ON cd.business_name = tc.business_name
        ORDER BY cd.business_name, cd.operatorCount DESC;
    </select>


</mapper>