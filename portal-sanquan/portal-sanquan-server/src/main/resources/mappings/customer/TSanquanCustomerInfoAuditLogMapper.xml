<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.customer.dao.TSanquanCustomerInfoAuditLogMapper">
    <resultMap type="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerInfoAuditLog" id="tSanquanCustomerInfoAuditLogMap">
        <result property="id" column="id"/>
        <result property="auditId" column="audit_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="operatorType" column="operator_type"/>
        <result property="operatorName" column="operator_name"/>
        <result property="operatorJobNumber" column="operator_job_number"/>
        <result property="operation" column="operation"/>
        <result property="operationDate" column="operation_date"/>
        <result property="isAudit" column="is_audit"/>
    </resultMap>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerInfoAuditLog">
        select id,audit_id,customer_id,operator_type,operator_name,operator_job_number,operation,operation_date,is_audit
        from t_sanquan_customer_info_audit_log
        <where>
            <if test="query.auditId != null and query.auditId != ''">
                AND audit_id = #{query.auditId}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.operatorType != null and query.operatorType != ''">
                AND operator_type = #{query.operatorType}
            </if>
            <if test="query.operatorName != null and query.operatorName != ''">
                AND operator_name = #{query.operatorName}
            </if>
            <if test="query.operatorJobNumber != null and query.operatorJobNumber != ''">
                AND operator_job_number = #{query.operatorJobNumber}
            </if>
            <if test="query.operation != null and query.operation != ''">
                AND operation = #{query.operation}
            </if>
            <if test="query.operationDate != null and query.operationDate != ''">
                AND operation_date = #{query.operationDate}
            </if>
        </where>
        order by operation_date
    </select>

</mapper>
