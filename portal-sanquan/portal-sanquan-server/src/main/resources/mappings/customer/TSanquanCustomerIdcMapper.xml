<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.customer.dao.TSanquanCustomerIdcMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerIdc" id="tSanquanCustomerIdcMap">
        <result property="idcId" column="idc_id"/>
        <result property="customerId" column="customer_id"/>
        <result property="customerName" column="customer_name"/>
        <result property="rosterCustomerName" column="roster_customer_name"/>
        <result property="rosterCustomerId" column="roster_customer_id"/>
        <result property="primaryMachineSituation" column="primary_machine_situation"/>
        <result property="disasterPreparednessSituation" column="disaster_preparedness_situation"/>
        <result property="loadSystem" column="load_system"/>
        <result property="isRelocation" column="is_relocation"/>
        <result property="isExpansion" column="is_expansion"/>
        <result property="isHashrate" column="is_hashrate"/>
        <result property="isVdc" column="is_vdc"/>
        <result property="supportStaff" column="support_staff"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
        <result property="createDate" column="create_date"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="versions" column="versions"/>
        <result property="tenantId" column="tenant_id"/>
    </resultMap>
    <select id="selectByCustomerIdList"
            resultType="cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanCustomerIdcVO">
        select * from t_sanquan_customer_idc
        where 1=1 and customer_id = #{id}
          and delete_flag = 'normal'
    </select>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerIdc">
       select idc_id AS idcId,
        customer_id AS customerId,
        customer_name AS customerName,
        roster_customer_name AS rosterCustomerName,
        roster_customer_id AS rosterCustomerId,
        primary_machine_situation AS primaryMachineSituation,
        disaster_preparedness_situation AS disasterPreparednessSituation,
        load_system AS loadSystem,
        is_relocation AS isRelocation,
        is_expansion AS isExpansion,
        is_hashrate AS isHashrate,
        is_vdc AS isVdc,
        support_staff AS supportStaff,
        attr1,
        attr2,
        attr3
       from t_sanquan_customer_idc
       <where>
           delete_flag = 'normal'
           <if test="query.idcId != null and query.idcId != ''">
               AND idc_id = #{query.idcId}
           </if>
           <if test="query.customerId != null and query.customerId != ''">
               AND customer_id = #{query.customerId}
           </if>
           <if test="query.customerName != null and query.customerName != ''">
               AND customer_name Like concat('%', #{query.customerName},'%')
           </if>
           <if test="query.rosterCustomerName != null and query.rosterCustomerName != ''">
               AND roster_customer_name Like concat('%', #{query.rosterCustomerName},'%')
           </if>
           <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
               AND roster_customer_id = #{query.rosterCustomerId}
           </if>
           <if test="query.primaryMachineSituation != null and query.primaryMachineSituation != ''">
               AND primary_machine_situation = #{query.primaryMachineSituation}
           </if>
           <if test="query.disasterPreparednessSituation != null and query.disasterPreparednessSituation != ''">
               AND disaster_preparedness_situation = #{query.disasterPreparednessSituation}
           </if>
           <if test="query.loadSystem != null and query.loadSystem != ''">
               AND loadSystem = #{query.loadSystem}
           </if>
           <if test="query.isRelocation != null and query.isRelocation != ''">
               AND is_relocation = #{query.isRelocation}
           </if>
           <if test="query.isExpansion != null and query.isExpansion != ''">
               AND is_expansion = #{query.isExpansion}
           </if>
           <if test="query.isHashrate != null and query.isHashrate != ''">
               AND is_hashrate = #{query.isHashrate}
           </if>
           <if test="query.isVdc != null and query.isVdc != ''">
               AND is_vdc = #{query.isVdc}
           </if>
           <if test="query.supportStaff != null and query.supportStaff != ''">
               AND support_staff = #{query.supportStaff}
           </if>
       </where>
        order by update_date desc
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.customer.entity.TSanquanCustomerIdc">
        select idc_id AS idcId,
        customer_id AS customerId,
        customer_name AS customerName,
        roster_customer_name AS rosterCustomerName,
        roster_customer_id AS rosterCustomerId,
        primary_machine_situation AS primaryMachineSituation,
        disaster_preparedness_situation AS disasterPreparednessSituation,
        load_system AS loadSystem,
        is_relocation AS isRelocation,
        is_expansion AS isExpansion,
        is_hashrate AS isHashrate,
        is_vdc AS isVdc,
        support_staff AS supportStaff,
        attr1,
        attr2,
        attr3
        from t_sanquan_customer_idc
        <where>
            delete_flag = 'normal'
            <if test="query.idcId != null and query.idcId != ''">
                AND idc_id = #{query.idcId}
            </if>
            <if test="query.customerId != null and query.customerId != ''">
                AND customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name Like concat('%', #{query.customerName},'%')
            </if>
            <if test="query.rosterCustomerName != null and query.rosterCustomerName != ''">
                AND roster_customer_name Like concat('%', #{query.rosterCustomerName},'%')
            </if>
            <if test="query.rosterCustomerId != null and query.rosterCustomerId != ''">
                AND roster_customer_id = #{query.rosterCustomerId}
            </if>
            <if test="query.primaryMachineSituation != null and query.primaryMachineSituation != ''">
                AND primary_machine_situation = #{query.primaryMachineSituation}
            </if>
            <if test="query.disasterPreparednessSituation != null and query.disasterPreparednessSituation != ''">
                AND disaster_preparedness_situation = #{query.disasterPreparednessSituation}
            </if>
            <if test="query.loadSystem != null and query.loadSystem != ''">
                AND loadSystem = #{query.loadSystem}
            </if>
            <if test="query.isRelocation != null and query.isRelocation != ''">
                AND is_relocation = #{query.isRelocation}
            </if>
            <if test="query.isExpansion != null and query.isExpansion != ''">
                AND is_expansion = #{query.isExpansion}
            </if>
            <if test="query.isHashrate != null and query.isHashrate != ''">
                AND is_hashrate = #{query.isHashrate}
            </if>
            <if test="query.isVdc != null and query.isVdc != ''">
                AND is_vdc = #{query.isVdc}
            </if>
            <if test="query.supportStaff != null and query.supportStaff != ''">
                AND support_staff = #{query.supportStaff}
            </if>
        </where>
        order by update_date desc
    </select>

</mapper>