<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.strategy.dao.TSanquanStrategyConfigIndustryDataMapper">
    <resultMap type="cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanStrategyConfigIndustryData" id="TSanquanStrategyConfigIndustryResult">
        <result property="id"    column="id"    />
        <result property="strategyConfigId"    column="strategy_config_id"    />
        <result property="industry"    column="industry"    />
    </resultMap>

    <sql id="Base_Column_List">
 id, strategy_config_id, industry    </sql>

</mapper>
