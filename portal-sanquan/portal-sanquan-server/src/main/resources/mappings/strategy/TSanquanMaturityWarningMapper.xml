<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.chinaunicom.sdsi.strategy.dao.TSanquanMaturityWarningMapper">

    <resultMap type="cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanMaturityWarning" id="tSanquanMaturityWarningMap">
        <result property="id" column="id"/>
        <result property="opportunityId" column="opportunity_id"/>
        <result property="contractor" column="contractor"/>
        <result property="contractorDate" column="contractor_date"/>
        <result property="issueDate" column="issue_date"/>
        <result property="rosterCustomerId" column="roster_customer_id"/>
        <result property="rosterCustomerName" column="roster_customer_name"/>
        <result property="customerManager" column="customer_manager"/>
        <result property="customerManagerId" column="customer_manager_id"/>
        <result property="status" column="status"/>
        <result property="productId" column="product_id"/>
        <result property="productName" column="product_name"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
        <result property="createDate" column="create_date"/>
        <result property="createBy" column="create_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="updateBy" column="update_by"/>
        <result property="versions" column="versions"/>
        <result property="deleteFlag" column="delete_flag"/>
        <result property="tenantId" column="tenant_id"/>
        <result property="strategyResultId" column="strategy_result_id"/>
        <result property="strategyResultIdNew" column="strategy_result_id_new"/>
        <result property="industry" column="industry"/>
        <result property="warningInfoId" column="warning_info_id"/>
    </resultMap>
    <select id="findPage" resultType="cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanMaturityWarningVO">
        select id,opportunity_id,contractor,contractor_date,issue_date,roster_customer_id,roster_customer_name,customer_manager,
            customer_manager_id,status,product_id,product_name,attr1,attr2,attr3,industry,strategy_result_id,strategy_result_id_new,
            warning_info_id
        from t_sanquan_maturity_warning
        <where>
            delete_flag = 'normal'
            <if test="query.opportunityId != null and query.opportunityId != ''">
                AND opportunity_id = #{query.opportunityId}
            </if>
            <if test="query.contractor != null and query.contractor != ''">
                AND contractor = #{query.contractor}
            </if>
            <if test="query.contractorDate != null and query.contractorDate != ''">
                AND contractor_date = #{query.contractorDate}
            </if>
            <if test="query.issueDate != null and query.issueDate != ''">
                AND issue_date = #{query.issueDate}
            </if>
            <if test="query.rosterCustomerName != null and query.rosterCustomerName != ''"  >
                AND roster_customer_name LIKE concat('%', #{query.rosterCustomerName}, '%')
            </if>
            <if test="query.customerManager != null and query.customerManager != ''"  >
                AND customer_manager LIKE concat('%', #{query.customerManager}, '%')
            </if>
            <if test="query.productName != null and query.productName != ''"  >
                AND product_name LIKE concat('%', #{query.productName}, '%')
            </if>
            <if test="query.status != null and query.status != ''"  >
                AND status = #{query.status}
            </if>
            <if test="query.industry != null and query.industry != ''"  >
                AND industry = #{query.industry}
            </if>
        </where>
        order by contractor_date,issue_date,id desc
    </select>
    <select id="findList" resultType="cn.chinaunicom.sdsi.cloud.strategy.vo.TSanquanMaturityWarningVO">
        select id,opportunity_id,contractor,contractor_date,issue_date,roster_customer_id,roster_customer_name,customer_manager,
        customer_manager_id,status,product_id,product_name,attr1,attr2,attr3,industry,strategy_result_id,strategy_result_id_new,
        warning_info_id
        from t_sanquan_maturity_warning
        <where>
            delete_flag = 'normal'
            <if test="query.opportunityId != null and query.opportunityId != ''">
                AND opportunity_id = #{query.opportunityId}
            </if>
            <if test="query.contractor != null and query.contractor != ''">
                AND contractor = #{query.contractor}
            </if>
            <if test="query.contractorDate != null and query.contractorDate != ''">
                AND contractor_date = #{query.contractorDate}
            </if>
            <if test="query.issueDate != null and query.issueDate != ''">
                AND issue_date = #{query.issueDate}
            </if>
            <if test="query.rosterCustomerName != null and query.rosterCustomerName != ''"  >
                AND roster_customer_name LIKE concat('%', #{query.rosterCustomerName}, '%')
            </if>
            <if test="query.customerManager != null and query.customerManager != ''"  >
                AND customer_manager LIKE concat('%', #{query.customerManager}, '%')
            </if>
            <if test="query.productName != null and query.productName != ''"  >
                AND product_name LIKE concat('%', #{query.productName}, '%')
            </if>
            <if test="query.status != null"  >
                AND status = #{query.status}
            </if>
            <if test="query.industry != null and query.industry != ''"  >
                AND industry = #{query.industry}
            </if>
        </where>
        order by contractor_date,issue_date,id desc
    </select>
    <select id="selectToDay" resultType="cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanMaturityWarning">
        SELECT
            id, opportunity_id, contractor, contractor_date, issue_date,
            roster_customer_id, roster_customer_name, customer_manager,
            customer_manager_id, status, product_id, product_name, attr1, attr2, attr3,industry,strategy_result_id
        ,strategy_result_id_new,warning_info_id
        FROM
            t_sanquan_maturity_warning
        <where>
            status = '0'
            AND delete_flag = 'normal'
            <if test="isCurrentDateOnly == true">
                AND DATE(issue_date) =CURRENT_DATE()
            </if>
            <if test="isCurrentDateOnly == false">
                AND DATE(issue_date) &lt;= CURRENT_DATE()
            </if>
        </where>
   </select>
    <select id="findYesterdayData"
            resultType="cn.chinaunicom.sdsi.cloud.strategy.entity.TSanquanMaturityWarning">
        select id, opportunity_id, contractor, contractor_date, issue_date,
               roster_customer_id, roster_customer_name, customer_manager,
               customer_manager_id, status, product_id, product_name, attr1, attr2, attr3,industry,strategy_result_id
                ,strategy_result_id_new,warning_info_id
        from t_sanquan_maturity_warning
        where status = '0'
        AND delete_flag = 'normal'
        AND DATE(create_date) = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
    </select>

</mapper>