<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.chinaunicom.sdsi.opportunity.mapper.PotentialOpportunityMapper">

    <resultMap id="BaseResultMap" type="cn.chinaunicom.sdsi.opportunity.entity.PotentialOpportunity">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="strategyId" column="strategy_id" jdbcType="VARCHAR"/>
            <result property="strategyResultId" column="strategy_result_id" jdbcType="VARCHAR"/>
            <result property="customerId" column="customer_id" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="productId" column="product_id" jdbcType="VARCHAR"/>
            <result property="productName" column="product_name" jdbcType="VARCHAR"/>
            <result property="industry" column="industry" jdbcType="VARCHAR"/>
            <result property="isRead" column="is_read" jdbcType="INTEGER"/>
            <result property="isPush" column="is_push" jdbcType="INTEGER"/>
            <result property="isPushCity" column="is_push_city" jdbcType="TINYINT"/>
            <result property="isAudit" column="is_audit" jdbcType="INTEGER"/>
            <result property="viewTime" column="view_time" jdbcType="TIMESTAMP"/>
            <result property="pushTime" column="push_time" jdbcType="TIMESTAMP"/>
            <result property="pushTodoCode" column="push_todo_code" jdbcType="VARCHAR"/>
            <result property="attr1" column="attr1" jdbcType="VARCHAR"/>
            <result property="attr2" column="attr2" jdbcType="VARCHAR"/>
            <result property="attr3" column="attr3" jdbcType="VARCHAR"/>
            <result property="createDate" column="create_date" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateDate" column="update_date" jdbcType="TIMESTAMP"/>
            <result property="deleteFlag" column="delete_flag" jdbcType="VARCHAR"/>
            <result property="versions" column="versions" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="productType" column="product_type" jdbcType="VARCHAR"/>
            <result property="pushDescribe" column="push_describe" jdbcType="VARCHAR"/>
            <result property="productBraceUserName" column="product_brace_user_name" jdbcType="VARCHAR"/>
            <result property="productBraceUserPhone" column="product_brace_user_phone" jdbcType="VARCHAR"/>
            <result property="customerManager" column="customer_manager" jdbcType="VARCHAR"/>
            <result property="customerManagerId" column="customer_manager_id" jdbcType="VARCHAR"/>
            <result property="cityCode" column="city_code" jdbcType="VARCHAR"/>
            <result property="monthId" column="month_id" jdbcType="VARCHAR"/>
            <result property="modelType" column="model_type" jdbcType="VARCHAR"/>
            <result property="importFlag" column="import_flag" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="cityInterfacePerson" column="city_interface_person" jdbcType="VARCHAR"/>
            <result property="modelId" column="model_id" jdbcType="VARCHAR"/>
            <result property="modelBusinessType" column="model_business_type" jdbcType="VARCHAR"/>
            <result property="mainPushProductId" column="main_push_product_id" jdbcType="VARCHAR"/>
            <result property="mainPushProductName" column="main_push_product_name" jdbcType="VARCHAR"/>
            <result property="hotSaleProductId" column="hot_sale_product_id" jdbcType="VARCHAR"/>
            <result property="hotSaleProductName" column="hot_sale_product_name" jdbcType="VARCHAR"/>
            <result property="assistPushProductId" column="assist_push_product_id" jdbcType="VARCHAR"/>
            <result property="assistPushProductName" column="assist_push_product_name" jdbcType="VARCHAR"/>
            <result property="referralBusinessType" column="referral_business_type" jdbcType="VARCHAR"/>
            <result property="industryPromotion" column="industry_promotion" jdbcType="VARCHAR"/>
            <result property="customerDimension" column="customer_dimension" jdbcType="VARCHAR"/>
            <result property="customerAttributes" column="customer_attributes" jdbcType="VARCHAR"/>
            <result property="provincialBranch" column="provincial_branch" jdbcType="VARCHAR"/>
            <result property="executor" column="executor" jdbcType="VARCHAR"/>
            <result property="executorPhone" column="executor_phone" jdbcType="VARCHAR"/>
            <result property="executorOaId" column="executor_oa_id" jdbcType="VARCHAR"/>
            <result property="rosterCustomerId" column="roster_customer_id" jdbcType="VARCHAR"/>
            <result property="rosterCustomerName" column="roster_customer_name" jdbcType="VARCHAR"/>
            <result property="customerCityCode" column="customer_city_code" jdbcType="VARCHAR"/>
            <result property="customerDistrictsCode" column="customer_districts_code" jdbcType="VARCHAR"/>
            <result property="customerCityName" column="customer_city_name" jdbcType="VARCHAR"/>
            <result property="customerDistrictsName" column="customer_districts_name" jdbcType="VARCHAR"/>
            <result property="mobo1" column="mobo1" jdbcType="VARCHAR"/>
            <result property="mobo2" column="mobo2" jdbcType="VARCHAR"/>
            <result property="mobo3" column="mobo3" jdbcType="VARCHAR"/>
            <result property="mobo4" column="mobo4" jdbcType="VARCHAR"/>
            <result property="mobo6" column="mobo6" jdbcType="VARCHAR"/>
            <result property="mobo5" column="mobo5" jdbcType="VARCHAR"/>
            <result property="isTask" column="is_task" jdbcType="VARCHAR"/>
            <result property="taskId" column="task_id" jdbcType="VARCHAR"/>
            <result property="taskName" column="task_name" jdbcType="VARCHAR"/>
            <result property="monitorStatus" column="monitor_status" jdbcType="VARCHAR"/>
            <result property="customerType" column="customer_type" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,strategy_id,strategy_result_id,
        customer_id,customer_name,product_id,
        product_name,industry,is_read,
        is_push,is_push_city,is_audit,
        view_time,push_time,push_todo_code,
        attr1,attr2,attr3,
        create_date,create_by,update_by,
        update_date,delete_flag,versions,
        tenant_id,product_type,push_describe,
        product_brace_user_name,product_brace_user_phone,customer_manager,
        customer_manager_id,city_code,month_id,
        model_type,import_flag,status,
        city_interface_person,model_id,model_business_type,
        main_push_product_id,main_push_product_name,hot_sale_product_id,
        hot_sale_product_name,assist_push_product_id,assist_push_product_name,
        referral_business_type,industry_promotion,customer_dimension,
        customer_attributes,provincial_branch,executor,
        executor_phone,executor_oa_id,roster_customer_id,
        roster_customer_name,customer_city_code,customer_districts_code,
        customer_city_name,customer_districts_name,mobo1,
        mobo2,mobo3,mobo4,
        mobo6,mobo5,is_task,
        task_id,task_name,monitor_status,
        customer_type
    </sql>
</mapper>
