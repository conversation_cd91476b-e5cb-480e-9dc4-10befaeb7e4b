package cn.chinaunicom.sdsi.cloud.auth.fegin;

import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.cloud.roles.entity.SanquanRole;
import cn.chinaunicom.sdsi.cloud.ssoPath.entity.TSanquanSsoPath;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuVo;
import cn.chinaunicom.sdsi.platform.role.entity.RoleVo;
import cn.chinaunicom.sdsi.platform.service.entity.SysLogininfor;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author： cuiyuanzhen
 * @Date: 2023/3/17 15:37
 */
//@FeignClient(url = "http://************:18084")
@FeignClient("sanquan")
public interface SanquanOpportunityFeignClient {

    //获取用户信息
    @GetMapping("/user/findByJobNum")
    UserVo findStaff(@RequestParam("jobNum") String jobNum);

    //获取用户信息
    @PostMapping("/user/saveUserAndRole")
    UserVo saveUser(@RequestBody UserVo userVo);

    /**
     * 根据用户查询角色
     * <AUTHOR>
     * @Date 2024/8/8 13:13
     */
    @GetMapping("/role/findByUserId2")
    List<RoleVo> findRoleByUserId2(@RequestParam("userId") String userId);

    /**
     * 返回用户名称
     *
     * @param id
     * @return
     */
    @Operation(summary = "查询潜在机会(商机)表详细信息", description = "查询潜在机会(商机)表详细信息")
    @GetMapping("/opportunity/opportunity/findByIdName")
    Map<String, String> findInfo(@RequestParam("id") String id);

    /**
     * 返回用户名称
     *
     * @param LOGIN
     * @return
     */
    @Operation(summary = "查询潜在机会(商机)表详细信息", description = "查询潜在机会(商机)表详细信息")
    @GetMapping("/customer/mapping/findOne")
    TSanquanDZqztJihezxProvinceMappingVO findKhjlByLogin(@RequestParam("LOGIN") String LOGIN);

    /**
     * 查询角色编码
     */
    @Operation(summary = "查询角色编码", description = "查询角色编码")
    @GetMapping("/menu/findRoleById")
    SanquanRole findRoleById(@RequestParam("roleId") String roleId);


    /**
     * 查询角色编码
     */
    @Operation(summary = "根据工号查询地市用户信息", description = "根据工号查询地市用户信息")
    @GetMapping("/menu/findCityUserByJobNumber")
    TSanquanPrefectureInterfacePersonVO findCityUserByJobNumber(@RequestParam("jobNumber") String jobNumber);


    @Operation(summary = "根据工号查询地市用户信息", description = "根据工号查询地市用户信息")
    @GetMapping("/user/findByJobNum")
    UserVo findUserVoByJobNum(@RequestParam("jobNumber") String jobNumber);

    @Operation(summary = "根据用户查询用户有用的菜单", description = "根据用户查询用户有用的菜单")
    @GetMapping("/core/menu/findByUserId")
    List<MenuVo> findMenuVoByUserId(@RequestParam("userId") String userId, @RequestParam("menuType") String menuType);

    @Operation(summary = "根据用户查询用户拥有的角色", description = "根据用户查询用户拥有的角色")
    @GetMapping("/role/findByUserId2")
    List<RoleVo> findRoleVoByUserId(@RequestParam("userId") String userId);

    @Operation(summary = "根据用户查询用户有用的菜单", description = "根据用户查询用户有用的菜单")
    @GetMapping("/core/menu/findUserHasPermission")
    List<MenuVo> findUserHasPermission(@RequestParam("userId") String userId);

    @Operation(summary = "查询单点信息", description = "查询单点信息")
    @GetMapping("/ssoPath/findByType")
    TSanquanSsoPath findByType(@RequestParam("ssoType") String ssoType);

    @Operation(summary = "添加登录信息", description = "添加登录信息")
    @PostMapping("/monitor/logininfor/add")
    Integer add(@RequestBody SysLogininfor logininfor);
}
