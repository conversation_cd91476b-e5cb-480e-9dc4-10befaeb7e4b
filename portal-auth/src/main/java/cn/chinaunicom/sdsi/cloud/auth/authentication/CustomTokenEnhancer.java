package cn.chinaunicom.sdsi.cloud.auth.authentication;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: token增强，此处主要在login之后增加除token之外的额外信息返回，放在additionalInformation 节点下
 * @author: zhaofc
 * @time: 2021/1/12 19:15
 */
@Component
public class CustomTokenEnhancer implements TokenEnhancer {

    @Autowired
    private RedisUtils redisUtils;


    private final String uniKey = "232bJu9gzobWOKYPrZtDQRhpLuuiE7";

    /**
     * @param accessToken
     * @param authentication
     * @return
     */
    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        final Map<String, Object> additionalInfo = new HashMap<>();
        Authentication userAuthentication = authentication.getUserAuthentication();
        if(userAuthentication == null){
            // 客户端认证时，没有userAuthentication
            return accessToken;
        }
        MallUser mallUser = (MallUser) userAuthentication.getPrincipal();
        additionalInfo.put("userid", mallUser.getStaffId());
        additionalInfo.put("permissions", mallUser.getPermissions());
        additionalInfo.put("authorityList", mallUser.getAuthorityList());
        additionalInfo.put("userJobDetailVOList", mallUser.getUserJobDetailVOList());
        additionalInfo.put("staffName", mallUser.getStaffName());
        additionalInfo.put("customParam", mallUser.getCustomParam());
        additionalInfo.put("currentOrgId", mallUser.getOrgId());
        additionalInfo.put("currentOrgName", mallUser.getOrgName());
        ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
        mutilClientsAccessTokenCache(mallUser,accessToken);
        //Map<String, Object> claims = Maps.newHashMapWithExpectedSize(1);

//            final String compact = Jwts.builder()
//                    //.setClaims(claims)
//                    .setSubject(mallUser.getStaffId())
////                .setIssuedAt(createdDate)
////                .setExpiration(expirationDate)
//                    .setId(mallUser.getOu())
//                    .signWith(SignatureAlgorithm.HS512, generalKey())
////                .compressWith(CompressionCodecs.DEFLATE)
//                    .compact();
//
//            ((DefaultOAuth2AccessToken) accessToken).setValue(compact);

        return accessToken;
    }


    /**
     * 根据私钥参数生成加密key
     *
     * @return SecretKeySpec
     */
    public SecretKey generalKey() {
        byte[] encodedKey = Base64.getEncoder().encode(uniKey.getBytes());
        return new SecretKeySpec(encodedKey, 0, encodedKey.length, "AES");
    }

    /**
     * 统一放置登录后access key，用来在登出的时候释放所有clientid对应的同一个账号的token
     */
    public void mutilClientsAccessTokenCache(MallUser user,OAuth2AccessToken token){
        redisUtils.addSet("UniCloud_Auth_Tokens:"+user.getStaffId(),token.getValue());
    }

//    public static void main(String args[]) {
//        CustomTokenEnhancer enhancer = new CustomTokenEnhancer();
//        //String jwtToken = "eyJhbGciOiJIUzUxMiJ9.eyJ1aWQiOiJhYXNkZiIsInN1YiI6ImFhc2RmIiwib3UiOiIwMDEwMDAwNTAwMCJ9.4gpz7LeiqfdeFQnGN9yDtqp4IhYWi-LXOvBtzi_BBwL0ncyR71XjSIDdsfvgUSrMuJPJCYtn6MUKtnm-IBBRxw";
//        String jwtToken = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJhYXNkZiIsImp0aSI6IjAwMTAwMDA1MDAwIn0.AQHbVVKlgAoA1BbFS5x9xSaEB8KWh7xRqPJjxIwvvhbVElFTTCW7bRwiQO2RV6rqURs9jssKrVhq7cgbUINBHA";
//        Claims claims = Jwts.parser().setSigningKey(enhancer.generalKey()).parseClaimsJws(jwtToken).getBody();
//        String ou = claims.get("ou", String.class);
//        String uid = claims.get("uid", String.class);
//        System.out.println(claims.getSubject() +""+claims.getId());
//        System.out.println(ou+";"+uid);
//    }

}
