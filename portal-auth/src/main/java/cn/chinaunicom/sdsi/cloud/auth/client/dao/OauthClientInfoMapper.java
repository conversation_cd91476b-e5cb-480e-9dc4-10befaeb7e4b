package cn.chinaunicom.sdsi.cloud.auth.client.dao;

import cn.chinaunicom.sdsi.cloud.auth.client.entity.OauthClientInfoPO;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoQuery;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@Mapper
public interface OauthClientInfoMapper extends BaseMapper<OauthClientInfoPO> {

    /**
     * 分页查询接入方系统信息
     *
     * @param page IPage
     * @param query OauthClientInfoQuery
     * @return IPage<OauthClientInfoVO>
     */
    IPage<OauthClientInfoVO> selectClientPage(@Param("page") IPage page, @Param("query") OauthClientInfoQuery query);

    /**
     * 分页查询接入方系统信息
     *
     * @param  code String
     * @return int
     */
    int deleteCode(@Param("code") String code);
}
