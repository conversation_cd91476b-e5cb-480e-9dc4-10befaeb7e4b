package cn.chinaunicom.sdsi.cloud.auth.authentication;

import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * 登录成功处理类，自动获取oauth2的token返回
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 * 参考：https://www.jianshu.com/p/19059060036b
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/11/30
 */
@Component
public class DingLoginDecideFilterSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private ClientDetailsService clientDetailsService;

    @Resource
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Resource
    private CustomTokenEnhancer customTokenEnhancer;
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException {
        logger.debug("登录成功");
        //获取clientId 和 clientSecret
        String clientId = request.getHeader("clientId");
        //获取 ClientDetails
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        String scope = request.getHeader("scope");
        Set<String> set = new HashSet();
        if (StringUtils.isEmpty(scope)) {
            set = clientDetails.getScope();
        }
        //密码授权 模式, 组建 authentication
        TokenRequest tokenRequest = new TokenRequest(Maps.newHashMap(), clientId, set,
                "password");
        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);
        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
        request.getSession(true);
        //原token删除，新生成token
        //redisTokenStore.removeAccessToken(redisTokenStore.getAccessToken(oAuth2Authentication));
        OAuth2AccessToken token = authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
        customTokenEnhancer.enhance(token, oAuth2Authentication);
        response.setContentType("application/json;charset=UTF-8");
        Cookie cookie = new Cookie("unifast_token", token.getValue());
        cookie.setPath("/");
        cookie.setDomain("");
        cookie.setSecure(Boolean.TRUE);
        cookie.setHttpOnly(Boolean.TRUE);
        response.addCookie(cookie);
        BaseResponse<OAuth2AccessToken> tokenResponse = new BaseResponse<>();
        tokenResponse.setCode("1");
        tokenResponse.setMessage("请求成功");
        tokenResponse.setSuccess(true);
        tokenResponse.setData(token);
        response.getWriter().write(JSONUtil.toJsonStr(tokenResponse));
    }
}


