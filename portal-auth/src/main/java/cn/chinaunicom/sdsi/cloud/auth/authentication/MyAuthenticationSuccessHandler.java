package cn.chinaunicom.sdsi.cloud.auth.authentication;

import cn.chinaunicom.sdsi.cloud.config.feign.FeignConfig;
import cn.chinaunicom.sdsi.cloud.system.feign.UserCenterFeignClient;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.hutool.core.lang.Assert;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 登录成功处理类，自动获取oauth2的token返回
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 * 参考：https://www.jianshu.com/p/19059060036b
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/11/30
 */
@Slf4j
@Component
public class MyAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private ClientDetailsService clientDetailsService;

    @Autowired
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Autowired
    private CustomTokenEnhancer customTokenEnhancer;

    @Resource
    UserCenterFeignClient userCenterFeignClient;

    // 是否https部署，
    @Value("${unifast.run.https:false}")
    private boolean isHttps;

    /**
     * 获取认证中心地址
     */
    @Value("${security.oauth2.resource.token-info-uri:}")
    public String AUTH_URL;
    /**
     * 获取clientid
     */
    @Value("${security.oauth2.client.client-id:}")
    public String CLIENT_ID;
    /**
     * 获取CLIENT_SECRET
     */
    @Value("${security.oauth2.client.client-secret:}")
    public String CLIENT_SECRET;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {

        logger.debug("登录成功");
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Assert.notNull(servletRequestAttributes, "The servletRequestAttributes must not be null");
        servletRequestAttributes.setAttribute(FeignConfig.AUTH_HEAD, FeignConfig.TOKEN_HEAD + this.clientTokenHandler(), RequestAttributes.SCOPE_REQUEST);

        String clientId = request.getHeader("clientId");
        //获取 ClientDetails
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        //密码授权 模式, 组建 authentication
        TokenRequest tokenRequest = new TokenRequest(Maps.newHashMap(), clientId, clientDetails.getScope(),
                "password");
        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);
        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);

        HttpSession session = request.getSession(false);
        if(session!=null){
            session.invalidate();
        }
        request.getSession(true);

        OAuth2AccessToken token = authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
        customTokenEnhancer.enhance(token, oAuth2Authentication);

        response.setContentType("application/json;charset=UTF-8");
        Cookie cookie = new Cookie("unifast_token", token.getValue());
        cookie.setPath("/");
        cookie.setDomain("");
        if(isHttps) {
            cookie.setSecure(Boolean.TRUE);
        }
        cookie.setHttpOnly(Boolean.TRUE);
        response.addCookie(cookie);
        BaseResponse<OAuth2AccessToken> tokenResponse = new BaseResponse<>();
        tokenResponse.setCode("1");
        tokenResponse.setMessage("请求成功");
        tokenResponse.setSuccess(true);
        tokenResponse.setData(token);
        response.getWriter().write(JSONUtil.toJsonStr(tokenResponse));
    }

    /**
     * 获取客户端token接口
     */
    private String clientTokenHandler() {
        if (StringUtils.hasText(AUTH_URL)) {
            try {
                String oauthTokenUrl = AUTH_URL.split("/oauth/check_token")[0] + "/oauth/token" +
                        "?grant_type=client_credentials" +
                        "&client_id=" + CLIENT_ID +
                        "&client_secret=" + this.CLIENT_SECRET;
                String oauthTokenResult = HttpUtil.get(oauthTokenUrl);
                // 获取响应结果中的 token 门户统一认证令牌
                String token = new ObjectMapper().readTree(oauthTokenResult).get("data").get("value").asText();
                log.info(" - - - - token:[" + token + "]");
                return token;
            } catch (Exception e) {
                return "noAuth";
            }
        } else {
            log.error("未配置认证中心地址和必要凭据");
            return "noAuthNoConfig";
        }
    }
}


