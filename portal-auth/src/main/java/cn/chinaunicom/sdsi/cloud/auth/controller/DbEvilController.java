package cn.chinaunicom.sdsi.cloud.auth.controller;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.digitalMarker.ImageSign;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.UnifastContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;

/**
 * 数字水印生成:生成人眼不可见的水印
 * dbevil=don't be evil
 * <AUTHOR>
 * @date 2021-12-2 14:35:33
 */
@Slf4j
@RestController
@RequestMapping("/stat")
public class DbEvilController  extends BaseController {

    @Autowired
    UnifastContext unifastContext;

    @Autowired
    public ImageSign imageSign;


    /**
     * 删去：data:image/png;base64,
     * 将前四位iVBO反转为OBVi
     * 将最后的=改为w
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/speed")
    public BaseResponse digitalWaterMark(HttpServletRequest request, HttpServletResponse response) throws Exception {
        MallUser user=unifastContext.getUser();
        String content=user.getStaffName()+"\n"+user.getStaffId()+"\n"+user.getOrgId();
        return  ok(imageSign.Image2Base64(content, Color.black,150,120,"",15,true,false,true));
    }

}
