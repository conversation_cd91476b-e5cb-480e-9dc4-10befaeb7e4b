//package cn.chinaunicom.sdsi.cloud.auth.filter;
//
//import cn.chinaunicom.sdsi.cloud.auth.MallUser;
//import cn.chinaunicom.sdsi.cloud.auth.service.PortalUserDetailsService;
//import cn.chinaunicom.sdsi.cloud.auth.service.SaaSUserDetailsService;
//
////import cn.chinaunicom.sdsi.cloud.exchange.dingtalk.feign.ScanCodeFeignClient;
//import cn.chinaunicom.sdsi.cloud.system.application.ApplicationPO;
//import cn.chinaunicom.sdsi.cloud.system.application.ApplicationVO;
//import cn.chinaunicom.sdsi.cloud.system.feign.ApplicationFeignClient;
//import cn.chinaunicom.sdsi.cloud.system.feign.UserCenterFeignClient;
//import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffVO;
//import cn.chinaunicom.sdsi.cloud.system.staffext.entity.SysStaffExtPO;
//import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
//import cn.chinaunicom.sdsi.framework.response.BaseResponse;
//import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
//import com.alibaba.csp.sentinel.util.StringUtil;
//import com.google.gson.Gson;
//import com.google.gson.JsonObject;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.security.authentication.AuthenticationServiceException;
//import org.springframework.security.core.Authentication;
//import org.springframework.security.core.AuthenticationException;
//import org.springframework.security.core.userdetails.UserDetails;
//import org.springframework.security.core.userdetails.UserDetailsService;
//import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
//import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
//
//import javax.servlet.ServletException;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
///**
// * @description:钉钉授权登录
// * @time: 2021/8/16 13:37
// */
//public class DingAuthenticationFilter extends AbstractAuthenticationProcessingFilter {
//
//    private boolean postOnly = true;
//    @Autowired
//    private Gson gson;
//
//    @Autowired
//    private UserDetailsService userDetailsService;
//
//    @Autowired
//    PortalUserDetailsService portalUserDetailsService;
//
////    @Autowired
////    private ScanCodeFeignClient scanCodeFeignClient;
//
//    @Autowired
//    private UserCenterFeignClient userCenterFeignClient;
//
//
//    @Autowired
//    private ApplicationFeignClient applicationFeignClient;
//
//    @Autowired
//    private SaaSUserDetailsService saaSUserDetailsService;
//
//    public DingAuthenticationFilter() {
//        super(new AntPathRequestMatcher("/v1.0/dingAuthLogin", "GET"));
//    }
//
//
//    @Override
//    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException, ServletException {
//        if (this.postOnly && !request.getMethod().equals("GET")) {
//            throw new AuthenticationServiceException("SmsCode Authentication method not supported: " + request.getMethod());
//        } else {
//            String code = request.getParameter("authCode");//临时授权码
//            String state = request.getParameter("state");//state
//            if (StringUtil.isEmpty(code)) {
//                throw new AuthenticationServiceException("临时授权码不能为空");
//            }
//            if (StringUtil.isEmpty(state)) {
//                throw new AuthenticationServiceException("state不能为空");
//            }
//
//            //获取钉钉用户手机号等信息
//            BaseResponse<JsonObject> dingUserInfo = scanCodeFeignClient.getDingUserInfo(code, state);
//            JsonObject data = dingUserInfo.getData();
//            String userId = data.get("userid").getAsString();
//            String mobile = data.get("mobile").getAsString();
//
//            //查询租户应用
//            String[] split = state.split(",");//必填
//            String corpId = split[0];//必填 组织id
//            String appLable = split[1];//必填 应用标识
//            ApplicationVO applicationVO = new ApplicationVO();
//            applicationVO.setCropId(corpId);
//            applicationVO.setApplicationId(appLable);
//            BaseResponse<ApplicationPO> result = applicationFeignClient.findApplicationByApplication(applicationVO);
//            ApplicationPO applicationPO = result.getData();
//
//
//            String loginName = "";
//            String tenantId = "";
//            //根据手机号,租户id查询信息
//            UserDetails userDetails = portalUserDetailsService.loadUserByMobile(mobile,applicationPO.getTenantId());
//            if (userDetails != null) {
//                MallUser mallUser = (MallUser) userDetails;
//                loginName = mallUser.getLoginName();
//                tenantId = mallUser.getTenantId();
//                //根据staffId添加钉钉用户
//                SysStaffExtPO sysStaffExtPO = new SysStaffExtPO();
//                sysStaffExtPO.setStaffId(mallUser.getStaffId());
//                sysStaffExtPO.setDingUserId(userId);
//                userCenterFeignClient.updateDingUserId(sysStaffExtPO);
//            } else {
//                response.setContentType("application/json;charset=utf-8");
//                response.setStatus(HttpStatus.OK.value());
//                response.getWriter().println(gson.toJson(makeErrorResult("系统未查到您的信息")));
//                return null;
//            }
//            //一切校验通过过进行登录
//            SysStaffVO staffVO=new SysStaffVO();
//            staffVO.setLoginName(loginName);
//            staffVO.setTenantId(tenantId);
//            MallUser mallUser = saaSUserDetailsService.loadUserByParams(staffVO);
//            if (mallUser == null) {
//                throw new AuthenticationServiceException("当前用户不存在！");
//            }
//
//            UnifastAuthToken unifastAuthToken = new UnifastAuthToken(mallUser, mallUser.getPassword(),
//                    mallUser.getAuthorities());
//            //跳转到申请进入应用页面
//            return unifastAuthToken;
//
//        }
//    }
//
//
//    private BaseResponse<String> makeErrorResult(String message) {
//        BaseResponse<String> baseResponse = new BaseResponse<>();
//        baseResponse.setSuccess(false);
//        baseResponse.setCode(ResponseEnum.FAIL.getCode());
//        baseResponse.setResponseStatus(ResponseEnum.FAIL);
//        baseResponse.setMessage(message);
//        return baseResponse;
//    }
//}
