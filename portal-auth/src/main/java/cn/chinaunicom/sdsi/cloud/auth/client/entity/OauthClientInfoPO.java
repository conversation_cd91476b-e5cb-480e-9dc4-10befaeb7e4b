package cn.chinaunicom.sdsi.cloud.auth.client.entity;

import cn.chinaunicom.sdsi.framework.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;

/**
 * 接入系统信息对象
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("oauth_client_info")
@Schema(name= "OauthClientInfoPO对象")
public class OauthClientInfoPO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId
    @Schema(name= "主键")
    private String clientId;

    @Schema(name= "接入系统名")
    private String clientApp;

    @Schema(name= "接入系统的Key")
    private String clientKey;

    @Schema(name= "接入系统的密码原文")
    private String clientSecret;

    @Schema(name= "接入系统的密码")
    private String clientPass;

    @Schema(name="状态")
    private String clientStatus;

    @Schema(name="有效期")
    private LocalDate clientExpiration;

    @Schema(name= "系统负责单位")
    private String clientUnit;

    @Schema(name= "联系人")
    private String clientContact;

    @Schema(name= "联系人电话")
    private String clientTel;

    @Schema(name= "联系人邮箱")
    private String clientEmail;

    @Schema(name= "系统访问地址")
    private String clientUrl;

    @Schema(name= "系统回调地址")
    private String clientRedirect;

    @Schema(name= "描述")
    private String clientDescription;
    /**
     * 令牌有效期（秒）
     */
    private Integer clientTokenValidity;
    /**
     * 刷新令牌有效期（秒）
     */
    private Integer clientRefreshValidity;

    @Schema(name= "备注")
    private String clientRemark;

    @Schema(name= "备用字段a")
    private String attra;

    @Schema(name= "备用字段b")
    private String attrb;

    @Schema(name= "备用字段c")
    private String attrc;

    @Schema(name= "备用字段d")
    private String attrd;

    @Schema(name= "备用字段e")
    private String attre;


}
