package cn.chinaunicom.sdsi.cloud.auth.controller;


import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.net.URI;
import java.net.URLEncoder;

/**
 * 合作方门户单点登录首页
 * <AUTHOR> 2018年5月15日
 *
 */
@Slf4j
@RestController
@RequestMapping("ssoPartner")
public class SSOPartnerPortalController {

	@Value("${auth.url.ssoPartnerReadAppUrl}")
	private String ssoPartnerReadAppUrl;

	@Value("${auth.url.ssoPartnerPortalUrl}")
	private String ssoPartnerPortalUrl;

	@Value("${auth.url.ssoPartnerErrorAppUrl}")
	private String ssoPartnerErrorAppUrl;

	@Value("${auth.url.ssoPartnerAppid}")
	private String ssoPartnerAppid;

    /**
     * 合作方未登录直接单点到平台
     * <AUTHOR>
     * @date 2018年5月31日
     * @param request
     * @param response
     * @throws Exception
     */
	@GetMapping(value = "/partnerPortal")
	public void partnerPortal(HttpServletRequest request,
			HttpServletResponse response) throws BusinessException {
		String succUrl = ssoPartnerReadAppUrl + "/ssoPartnerPortalLogin";
		String errUrl = ssoPartnerReadAppUrl + "/ssoPartner/partnerPortalCheckError";
		String accountId = request.getParameter("loginId");
		String password = request.getParameter("password");
		String captcha = request.getParameter("veryCode");
		try {
			log.info("合作方门户未登录单点....");
			// 若尚未登陆，构造一个登录界面，并提交到合作方门户相应的认证地址，并传递过去一个成功后处理的url地址，
			response.setContentType("text/html;charset=utf-8");
			PrintWriter out = response.getWriter();

			out.println("<html><head>");
			out.println("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">");
			out.println("<meta name=\"renderer\" content=\"webkit\"/>");
			out.println("<meta name=\"force-rendering\" content=\"webkit\"/>");
			out.println("<meta http-equiv=\"X-UA-Compatible\" content=\"IE=EDGE,chrome=1\"/>");
			out.println("<meta http-equiv=\"pragma\" content=\"no-cache\">");
			out.println("<meta http-equiv=\"cache-control\" content=\"no-cache\">");
			out.println("<meta http-equiv=\"expires\" content=\"0\">");
			out.println("</head><body>");
			out.println("<form id=\"checkForm\" name=\"checkForm\" action=\""
					+ ssoPartnerPortalUrl
					+ "/rest/authentication/login\" method=\"post\">");
			out.println("		<input type=\"hidden\" name=\"success\" value=\""
					+ succUrl + "?loginId="+accountId+"\">");
			out.println("		<input type=\"hidden\" name=\"error\" value=\""
					+ errUrl + "\">");
			out.println("		<input type=\"hidden\" name=\"appid\" value=\""
					+ ssoPartnerAppid + "\">");
			out.println("		<input type=\"hidden\" name=\"return\" value=\"\">");
			out.println("		<input type=\"hidden\" name=\"accountId\" value=\"" + accountId + "\">");
			out.println("		<input type=\"hidden\" name=\"password\" value=\"" + password + "\">");
			out.println("		<input type=\"hidden\" name=\"captcha\" value=\"" + captcha + "\">");
			out.println("</form>");
			out.println("<script>");
			out.println("  document.checkForm.submit()");
			out.println("</script>");
			out.println("</body>");
			out.println("</html>");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 合作方已登录直接单点到平台
	 * <AUTHOR>
	 * @date 2018年5月31日
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@GetMapping(value = "/ssoPartnerPortal")
	public void ssoPartnerPortal(HttpServletRequest request,
			HttpServletResponse response) throws BusinessException {
		try {
			String succUrl = ssoPartnerReadAppUrl + "/ssoPartnerPortalLogin";
			String errUrl = ssoPartnerReadAppUrl + "/ssoPartner/partnerPortalCheckError";
			log.info("合作方门户已登录单点....");


			URI uri = new URIBuilder(ssoPartnerPortalUrl + "/rest/authentication/check_login")
						.setParameter("success", succUrl)
						.setParameter("error",errUrl)
						.setParameter("appid", ssoPartnerAppid)
						.build();

			log.info("rui==================={}",uri);


			// 若尚未登陆，构造一个登录界面，并提交到合作方门户相应的认证地址，并传递过去一个成功后处理的url地址，
			response.setContentType("text/html;charset=utf-8");
			PrintWriter out = response.getWriter();

			out.println("<html>");
			out.println("<meta http-equiv=\"content-type\" content=\"text/html; charset=UTF-8\">");
			out.println("<body>");
			//out.println("<body\">");
			out.println("<form id=\"checkForm\" name=\"checkForm\" action=\""
					+ ssoPartnerPortalUrl
					+ "/rest/authentication/check_login\" method=\"get\">");
			out.println("		<input type=\"hidden\" name=\"success\" value=\""
					+ succUrl + "?tip=1\">");//tip=1表示合作方已登录单点
			out.println("		<input type=\"hidden\" name=\"error\" value=\""
					+ errUrl + "\">");
			out.println("		<input type=\"hidden\" name=\"appid\" value=\""
					+ ssoPartnerAppid + "\">");
			out.println("		<input type=\"hidden\" name=\"return\" value=\"\">");
			out.println("</form>");
			out.println("<script>");
			out.println("  document.checkForm.submit()");
			out.println("</script>");
			out.println("</body>");
			out.println("</html>");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 单点失败后的提示地址
	 *
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@GetMapping(value = "/partnerPortalCheckError")
	public ModelAndView partnerPortalCheckError(HttpServletRequest request,
												HttpServletResponse response) throws Exception {
		String error_code = request.getParameter("resultCode");
		log.info("错误码resultCode="+error_code);
		String error_info = "";
		if("1".equals(error_code)){
			error_info = "账号或口令错误";
		}else if("2".equals(error_code)){
			error_info = "账号或口令错误";
		}else if("3".equals(error_code)){
			error_info = "账号停止使用";
		}else if("4".equals(error_code)){
			error_info = "人员主数据不存在";
		}else if("5".equals(error_code)){
			error_info = "用户已经退出";
		}else if("6".equals(error_code)){
			error_info = "服务器内部错误";
		}else if("9".equals(error_code)){
			error_info = "没有权限访问";
		}else if("10".equals(error_code)){
			error_info = "应用不存在";
		}else if("13".equals(error_code)){
			error_info = "密码过期";
		}else if("15".equals(error_code)){
			error_info = "密码快过期";
		}else if("998".equals(error_code)){
			error_info = "SSO维护中";
		}else if("999".equals(error_code)){
			error_info = "SSO内部错误";
		}else if("21".equals(error_code)){
			error_info = "该用户已退出";
		}else if("22".equals(error_code)){
			error_info = "超时";
		}else if("1001".equals(error_code)){
			error_info = "验证码错误";
		}else{
			error_info = "合作方单点验证失败，请稍后再试。";
		}
		error_info = URLEncoder.encode(error_info, "utf-8");
		return new ModelAndView("redirect:"+ssoPartnerErrorAppUrl+"/?msg="+error_info);
	}

}