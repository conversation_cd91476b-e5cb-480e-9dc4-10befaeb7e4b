package cn.chinaunicom.sdsi.cloud.auth.config;

import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2020/10/21
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
//@Configuration
//@EnableResourceServer
public class AuthResourceServer extends ResourceServerConfigurerAdapter {

    private static final String RESOURCE_ID = "auth";

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) throws Exception {
        resources
                .resourceId(RESOURCE_ID).stateless(true)
//                .authenticationEntryPoint(authExceptionEntryPoint) // 外部定义的token错误进入的方法
//                .accessDeniedHandler(accessDeniedHandler); // 没有权限的进入方法
        ;
    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        // 配置order访问控制，必须携带令牌才可以访问
        super.configure(http);
    }


}