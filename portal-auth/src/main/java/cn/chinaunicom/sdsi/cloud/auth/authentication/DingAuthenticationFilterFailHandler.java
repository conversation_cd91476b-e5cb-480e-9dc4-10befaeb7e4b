package cn.chinaunicom.sdsi.cloud.auth.authentication;

import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.hutool.json.JSONUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

@Component
public class DingAuthenticationFilterFailHandler   extends SimpleUrlAuthenticationFailureHandler {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response,
                                        AuthenticationException exception) throws IOException, ServletException {

        logger.debug("登录失败");
        HttpSession session = request.getSession(false);
        if(session!=null){
            session.invalidate();
        }
        request.getSession(true);
        //设置状态码
        response.setStatus(200);
        response.setContentType("application/json;charset=UTF-8");
        BaseResponse<String> br = new BaseResponse<>();
        br.setResponseStatus(ResponseEnum.STATUS_CODE_401);
        br.setMessage(exception.getMessage());
        //将 登录失败 信息打包成json格式返回
        response.getWriter().write(JSONUtil.toJsonStr(br));
    }
}
