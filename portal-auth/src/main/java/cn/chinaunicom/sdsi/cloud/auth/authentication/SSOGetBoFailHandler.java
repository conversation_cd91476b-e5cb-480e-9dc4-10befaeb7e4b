package cn.chinaunicom.sdsi.cloud.auth.authentication;

import cn.hutool.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/12/1
 */
@Component
public class SSOGetBoFailHandler extends SimpleUrlAuthenticationFailureHandler {

    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Value("${auth.url.ssoCloudReadAppUrl}")
    private String ssoCloudReadAppUrl;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        System.err.println("SSOGetBoFailHandler====================11111");
        // 设置响应状态码为 403 Forbidden，表示没有权限
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        // 设置响应头的 content-type
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        // 获取响应输出流
        PrintWriter out = response.getWriter();
        // 创建 JSON 对象返回
        JSONObject obj = new JSONObject();
        obj.putOpt("success", "false");
        obj.putOpt("message", "没有权限");
        System.err.println("SSOGetBoFailHandler====================over");
        // 将 JSON 数据写入响应
        out.write(obj.toString());
    }
}
