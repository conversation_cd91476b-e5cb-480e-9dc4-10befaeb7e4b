package cn.chinaunicom.sdsi.cloud.auth.filter;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.fegin.SanquanOpportunityFeignClient;
import cn.chinaunicom.sdsi.cloud.auth.util.HttpclientUtils;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.roles.entity.SanquanRole;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于2.0系统跳入3.0系统的认证过滤器
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/2/25
 */
@Slf4j
@Component
public class SSOZhengqiDecideYKFilter extends AbstractAuthenticationProcessingFilter {


    @Value("${auth.url.ssoSessionSessionUrl}")
    private String sessionUrl;

    @Autowired
    private SanquanOpportunityFeignClient sanquanOpportunityFeignClient;

    public SSOZhengqiDecideYKFilter() {
        super(new AntPathRequestMatcher("/sso/decide/token"));
    }

    @Override
    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        super.setAuthenticationManager(authenticationManager);
    }

    private static String toUtf8(String str) {
        return new String(str.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("\\\"", "'").replace("\"", "");
    }

    private static String getRandomCode(int length) {
        SecureRandom random = new SecureRandom();
        return new BigInteger(length * 4, random).toString(32);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
        String todoCode = request.getParameter("todoCode");
        String authSessionId = request.getParameter("token");
        System.err.println("=======token==opportunityId======todoCode==after" + todoCode);
        System.err.println("=======token======authSessionId==after" + authSessionId);
        try {
            if (!StringUtils.isNotEmpty(authSessionId)) {
                System.err.println("token==================UsernameNotFoundException===" + authSessionId);
                throw new UsernameNotFoundException("无效的会话");
            }
            String url = String.format("%s%s", sessionUrl, authSessionId);
            System.err.println("sessionUrl=====================" + sessionUrl);
            System.err.println("获取用户url=====================" + url);
            String staffInfoStr = HttpclientUtils.get(url);
            System.err.println(staffInfoStr);
            JSONObject responseObject = new JSONObject(staffInfoStr);
            if ("success".equals(responseObject.getStr("rspInfo"))) {

                JSONObject staff = responseObject.getJSONObject("data").getJSONObject("userInfo");
                String jobNum = staff.getStr("login");
                UserVo userVo = sanquanOpportunityFeignClient.findStaff(jobNum);
                // 维护用户信息
                if (userVo == null) {
                    List<String> roleIds = new ArrayList<>();
                    userVo = new UserVo();
                    // 查找客户经理
                    TSanquanDZqztJihezxProvinceMappingVO vo = sanquanOpportunityFeignClient.findKhjlByLogin(jobNum);
                    if (vo != null) {
                        roleIds.add("100002");
                        userVo.setCity(vo.getCity().replace("市", ""));
                    }
                    userVo.setJobNum(jobNum);
                    userVo.setName(staff.getStr("name"));
                    userVo.setDeleted("normal");
                    userVo.setPhone(staff.getStr("phone"));
                    //权限信息
                    JSONArray jsonRoles = responseObject.getJSONObject("data").getJSONArray("userRoles");
                    for (int i = 0; i < jsonRoles.size(); i++) {
                        JSONObject role = jsonRoles.getJSONObject(i);
                        Long roleId = role.getLong("sysRoleId");
                        SanquanRole _role = sanquanOpportunityFeignClient.findRoleById(String.valueOf(roleId));
                        if (_role != null) {
                            roleIds.add(_role.getId());
                        }
                    }
                    //用户行业信息
                    JSONArray zqStaffInfoList = responseObject.getJSONObject("data").getJSONArray("zqStaffInfoList");
                    if (zqStaffInfoList != null && !zqStaffInfoList.isEmpty()) {
                        userVo.setIndustry(zqStaffInfoList.getJSONObject(0).getStr("industryNameSj"));
                    }
                    userVo.setRoles(permission(roleIds));
                    //保存用户信息
                    this.sanquanOpportunityFeignClient.saveUser(userVo);
                }
                //用户信息
                MallUser mallUser = new MallUser();
                mallUser.setTenantId("khjl");
                mallUser.setStaffId(staff.getStr("oaUserId"));
                mallUser.setStaffName(staff.getStr("name"));
                mallUser.setLoginName(staff.getStr("login"));
                mallUser.setUsername(staff.getStr("name") + getRandomCode(10));
                mallUser.setOrgCode(staff.getStr("orgId"));
                mallUser.setOrgName(staff.getStr("orgName"));
                mallUser.setTel(staff.getStr("phone"));
                mallUser.getCustomParam().put("oaUserInfo", staff.toString());
                TSanquanDZqztJihezxProvinceMappingVO vo = sanquanOpportunityFeignClient.findKhjlByLogin(staff.getStr("login"));
                mallUser.setCity(vo.getCity());
                mallUser.getCustomParam().put("authSessionId", authSessionId);
                mallUser.setCompanyId(authSessionId);
                System.err.println("token==================error5===" + authSessionId);
                //权限信息
                JSONArray jsonRoles = responseObject.getJSONObject("data").getJSONArray("userRoles");
                List<GrantedAuthority> authorities = new ArrayList<>();
                mallUser.setAuthorities(authorities);
                System.err.println("token==================error8===" + mallUser.getCompanyId());
                return new UnifastAuthToken(mallUser, authSessionId, mallUser.getAuthorities());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new UsernameNotFoundException("会话已失效");

    }

    public static List<String> permission(List<String> list) {
        List<String> newList = new ArrayList<>(list.size());
        list.forEach(i -> {
            if (!newList.contains(i)) { // 如果新集合中不存在则插入
                newList.add(i);
            }
        });
        return newList;
    }
}
