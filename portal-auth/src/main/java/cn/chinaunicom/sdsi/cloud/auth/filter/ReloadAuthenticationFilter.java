package cn.chinaunicom.sdsi.cloud.auth.filter;/**
 * Created by chentao on 2021/1/20.
 */

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.token.UniTokenExtractor;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @description:
 * @author: chentao
 * @time: 2021/1/20 13:22
 */
public class ReloadAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    private boolean postOnly = true;

    @Autowired
    private ResourceServerTokenServices resourceServerTokenServices;

    @Setter
    private UniTokenExtractor uniTokenExtractor;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    public ReloadAuthenticationFilter() {
        super(new AntPathRequestMatcher("/reloadToken", "POST"));
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if(this.postOnly && !request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        } else {
            try {
                //校验token的有效性
                String authOrization =  request.getHeader("Authorization");
                if (StringUtils.isEmpty(authOrization)){
                    throw new AuthenticationServiceException("token不存在！");
                }

                String token = uniTokenExtractor.readToken(request);
                OAuth2Authentication auth2Authentication = resourceServerTokenServices.loadAuthentication(token);
                //重新封装mallUser对象
                MallUser mallUser = null;
                if (auth2Authentication.getPrincipal() instanceof MallUser){
                    mallUser = (MallUser) auth2Authentication.getPrincipal();
                }else {
                    throw new AuthenticationServiceException("当前用户未登录！");
                }

                if (null == mallUser){
                    throw new AuthenticationServiceException("当前用户未登录！");
                }

                mallUser = (MallUser) userDetailsService.loadUserByUsername(mallUser.getStaffId());

                //刷新token
                UnifastAuthToken unifastAuthToken = new UnifastAuthToken(mallUser, mallUser.getPassword(), mallUser.getAuthorities());

                return unifastAuthToken;

            }catch (AuthenticationException ae){
                throw new AuthenticationServiceException("当前用户未登录！",ae);
            }catch(InvalidTokenException ie){
                throw new AuthenticationServiceException("当前token无效！",ie);
            }
        }
    }

    public void setUniTokenExtractor(UniTokenExtractor uniTokenExtractor) {
        this.uniTokenExtractor = uniTokenExtractor;
    }
}
