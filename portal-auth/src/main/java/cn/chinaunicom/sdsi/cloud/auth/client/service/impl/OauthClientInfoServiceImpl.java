package cn.chinaunicom.sdsi.cloud.auth.client.service.impl;

import cn.chinaunicom.sdsi.cloud.auth.client.dao.OauthClientInfoMapper;
import cn.chinaunicom.sdsi.cloud.auth.client.entity.OauthClientInfoPO;
import cn.chinaunicom.sdsi.cloud.auth.client.service.OauthClientInfoService;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoAO;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoQuery;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoVO;
import cn.chinaunicom.sdsi.cloud.auth.constant.Constants;
import cn.chinaunicom.sdsi.cloud.auth.util.AppKeyUtils;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.enums.UnifastEnum;
import cn.chinaunicom.sdsi.framework.exception.BusinessException;
import cn.chinaunicom.sdsi.framework.utils.QueryVoToPageUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统接入方管理服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
@Service
public class OauthClientInfoServiceImpl extends ServiceImpl<OauthClientInfoMapper, OauthClientInfoPO> implements OauthClientInfoService {
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private CacheManager cacheManager;

    /**
     * @see OauthClientInfoService#selectClientPage(OauthClientInfoQuery)
     */
    @Override
    public IPage<OauthClientInfoVO> selectClientPage(OauthClientInfoQuery query) {
        IPage page = QueryVoToPageUtil.toPage(query);
        return this.baseMapper.selectClientPage(page, query);
    }

    /**
     * @see OauthClientInfoService#fetchByAppKey(String)
     */
    @Override
    public OauthClientInfoVO fetchByAppKey(String appKey) {
        final OauthClientInfoPO po =
                this.baseMapper.selectOne(new QueryWrapper<OauthClientInfoPO>().lambda().eq(OauthClientInfoPO::getClientKey,
                        appKey));
        if (po == null) {
            throw new BusinessException("appKey不存在");
        }
        OauthClientInfoVO vo = new OauthClientInfoVO();
        BeanUtil.copyProperties(po, vo);
        return vo;
    }

    /**
     * @see OauthClientInfoService#registerClient(OauthClientInfoAO)
     */
    @Override
    public String registerClient(OauthClientInfoAO ao) {
        OauthClientInfoPO po = new OauthClientInfoPO();
        ao.setClientId(null);
        //生成全局唯一的key 和 secret
        final String appId = AppKeyUtils.getAppId();
        // 查询数据库确认appKey的唯一性
        final int count = this.count(new QueryWrapper<OauthClientInfoPO>().lambda().eq(OauthClientInfoPO::getClientKey,
                appId));
        if (count > 0) {
            throw new BusinessException("clientKey生成异常，请重试");
        }
        final String appSecret = AppKeyUtils.getAppSecret(appId);

        BeanUtil.copyProperties(ao, po);
        po.setClientKey(appId);
        po.setClientSecret(appSecret);
        po.setClientPass(passwordEncoder.encode(appSecret));

        po.setClientStatus(UnifastEnum.SysDataValid.valid.value());

        this.baseMapper.insert(po);
        return po.getClientId();
    }

    /**
     * @see OauthClientInfoServiceImpl#fetchClient(String)
     */
    @Override
    public OauthClientInfoVO fetchClient(String clientId) {
        OauthClientInfoPO po = this.baseMapper.selectById(clientId);
        if (po == null) {
            throw new BusinessException(ResponseEnum.STATUS_CODE_404, "认证中心-客户端管理");
        }
        OauthClientInfoVO vo = new OauthClientInfoVO();
        BeanUtil.copyProperties(po, vo);
        return vo;
    }

    /**
     * @see OauthClientInfoServiceImpl#deleteClient(String)
     */
    @Override
    public int deleteClient(String clientId) {
        OauthClientInfoPO po = this.baseMapper.selectById(clientId);
        if (po == null) {
            return 0;
        }
        // 删除客户端缓存
        final Cache cache = cacheManager.getCache(Constants.CLIENT_CACHE_KEY);
        if (cache != null) {
            cache.evict(po.getClientKey());
        }
        return this.baseMapper.deleteById(clientId);
    }

    /**
     * @see OauthClientInfoServiceImpl#editClient(OauthClientInfoAO)
     */
    @Override
    public int editClient(OauthClientInfoAO ao) {
        OauthClientInfoPO po = this.baseMapper.selectById(ao.getClientId());
        if (po == null) {
            throw new BusinessException("客户端系统不存在");
        }
        // 删除客户端缓存
        final Cache cache = cacheManager.getCache(Constants.CLIENT_CACHE_KEY);
        if (cache != null) {
            cache.evict(po.getClientKey());
        }
        BeanUtil.copyProperties(ao, po);
        final int i = this.baseMapper.updateById(po);
        if (i == 0) {
            throw new BusinessException("编辑失败");
        }
        return i;
    }

    /**
     * 未使用
     *
     * @see OauthClientInfoServiceImpl#enableClient(String)
     */
    @Override
    public int enableClient(String clientId) {
        OauthClientInfoPO po = new OauthClientInfoPO();
        po.setClientId(clientId);
        po.setClientStatus(UnifastEnum.SysDataValid.valid.value());
        return this.baseMapper.updateById(po);
    }

    /**
     * 未使用
     *
     * @see OauthClientInfoServiceImpl#disableClient(String)
     */
    @Override
    public int disableClient(String clientId) {
        OauthClientInfoPO po = new OauthClientInfoPO();
        po.setClientId(clientId);
        po.setClientStatus(UnifastEnum.SysDataValid.invalid.value());
        return this.baseMapper.updateById(po);
    }

    /**
     * 删除授权码
     *
     * @param code 授权码code
     * @return 影响条数
     */
    @Override
    public int deleteCode(String code) {
        return this.baseMapper.deleteCode(code);
    }
}
