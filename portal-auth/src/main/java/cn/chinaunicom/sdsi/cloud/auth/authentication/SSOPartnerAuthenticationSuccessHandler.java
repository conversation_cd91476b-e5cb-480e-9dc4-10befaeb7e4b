package cn.chinaunicom.sdsi.cloud.auth.authentication;

import com.google.common.collect.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 登录成功处理类，自动获取oauth2的token返回
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 * 参考：https://www.jianshu.com/p/19059060036b
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/11/30
 */
@Component
public class SSOPartnerAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Value("${auth.url.ssoPartnerSHreadAppUrl}")
    private String ssoPartnerSHreadAppUrl;

    @Value("${auth.url.ssoPartnerSHredirect}")
    private String ssoPartnerSHredirect;

    @Autowired
    private ClientDetailsService clientDetailsService;

    @Lazy
    @Autowired
    private AuthorizationCodeServices authorizationCodeServices;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response,
                                        Authentication authentication) throws IOException, ServletException {
        logger.debug("登录成功");

        // 单点登录的时候
        String clientId = "6qa8hRZz";

        HttpSession session = request.getSession(false);
        if(session!=null){
            session.invalidate();
        }
        request.getSession(true);

        //获取 ClientDetails
        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        //授权码模式, 组建 authentication
//        Date now = new Date();
//        Set<String> set = clientDetails.getScope();
//        set.add(now+"");
        OAuth2Request oAuth2Request = new OAuth2Request(Maps.newHashMap(),
                clientId,clientDetails.getAuthorities(),true,
                clientDetails.getScope(),clientDetails.getResourceIds(),
                ssoPartnerSHredirect,null,null);
        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, authentication);
        String code = authorizationCodeServices.createAuthorizationCode(oAuth2Authentication);

        response.sendRedirect(ssoPartnerSHreadAppUrl+code);
    }
}


