package cn.chinaunicom.sdsi.cloud.auth.controller;

import cn.chinaunicom.sdsi.cloud.aop.annotation.WhiteListToken;
import cn.chinaunicom.sdsi.cloud.auth.client.service.OauthClientInfoService;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoVO;
import cn.chinaunicom.sdsi.cloud.auth.service.SmsService;
import cn.chinaunicom.sdsi.cloud.system.constant.UserCenterConstant;
import cn.chinaunicom.sdsi.cloud.system.feign.UserCenterFeignClient;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffInfoVO;
import cn.chinaunicom.sdsi.framework.base.BaseController;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.DateUtils;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.security.util.Sm2Encryptor;
import cn.hutool.http.Header;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.RandomUtils;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

@Tag(name = "短信发送控制器类")
@Slf4j
@RestController
@RequestMapping("/sms")
public class SmsController extends BaseController {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private Sm2Encryptor sm2Encryptor;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SmsService smsService;

    @Autowired
    private OauthClientInfoService oauthClientInfoService;

    @Resource
    UserCenterFeignClient userCenterFeignClient;

    @Value("${unifast.security.private-key:}")
    private String privateKey;


    /**
     * TODO 待验证白名单token介入功能
     * @param request
     * @param response
     */
    @Operation(description = "模拟发送手机短信",summary="模拟发送手机短息")
    @PostMapping("/mocksendSms")
    @WhiteListToken
    public void testSendSms(HttpServletRequest request, HttpServletResponse response) {
        System.out.println(request.getHeader(Header.AUTHORIZATION.getValue()));
        String keyInRedis = smsService.getSmsTypeRedisKeyPrefixMap().get("0") + "18653176857";
        redisUtils.set(keyInRedis,"666666",600L);
    }

    @Operation(description = "获取redis前缀标识",summary="获取redis前缀标识")
    @GetMapping("/getSmsTypeRedisKeyPrefixMap")
    public String getSmsTypeRedisKeyPrefixMap(@RequestParam(value = "smsType") String smsType) {
        return smsService.getSmsTypeRedisKeyPrefixMap().get(smsType);
    }


    /**
     * authkey 加密字符串 用公钥加密字符串：smsTYpe@clientid@手机号 ，smsType:短信类型，0:登录验证码；1：重置密码验证码；2:待办催办，0或1两种类型不需传username和processTitle
     * username：  催办人姓名 如 张三
     * processTitle: 流程标题 如 关于第三次水质检查的通知
     */
    @Operation(description = "发送手机短信",summary="发送手机短息")
    @PostMapping("/sendSms")
    @WhiteListToken
    public BaseResponse<JSONObject> sendSms(@RequestParam(value = "authkey") String authkey, @RequestParam(value = "userName",required = false) String userName, @RequestParam(value = "processTitle",required = false) String processTitle) {

        /**验证skey**/
        String smsType=null;//短信类型,0:登录验证码；1：重置密码验证码；2:待办催办
        String clientid=null;
        String mobile=null;
        String keyInRedis=null;
        try {
            final String str = sm2Encryptor.decryptString(privateKey, authkey);
            String[] info = str.split("@");
            smsType=info[0];
            clientid=info[1];
            mobile=info[2];

            if(StringUtils.isEmpty(smsType) || (!"0".equals(smsType) && !"1".equals(smsType) && !"2".equals(smsType) && !"3".equals(smsType))) {
                JSONObject re=new JSONObject();
                re.put("resultcode",1);
                re.put("resultmsg","短信类型不支持");
                return ok(re);
            }
            if(("0".equals(smsType) || "1".equals(smsType) || "3".equals(smsType)) && (mobile.contains(",") || mobile.contains("，"))) {
                JSONObject re=new JSONObject();
                re.put("resultcode",2);
                re.put("resultmsg","短信验证码不支持发送多个手机号");
                return ok(re);
            }
            if("2".equals(smsType) && (StringUtils.isEmpty(userName) || StringUtils.isEmpty(processTitle))){
                JSONObject re=new JSONObject();
                re.put("resultcode",3);
                re.put("resultmsg","所需参数不全");
                return ok(re);
            }
            if("3".equals(smsType) && StringUtils.isEmpty(mobile)){
                JSONObject re=new JSONObject();
                re.put("resultcode",3);
                re.put("resultmsg","所需参数不全");
                return ok(re);
            }
            if("0".equals(smsType) || "1".equals(smsType) || "3".equals(smsType)){
                keyInRedis=smsService.getSmsTypeRedisKeyPrefixMap().get(smsType)+mobile;
                if(redisUtils.exists(keyInRedis)){
                    Long expire = redisUtils.getExpire(keyInRedis);
                    log.info("redis.exprie===="+expire);
                    if(expire>(9*60)){
                        JSONObject re=new JSONObject();
                        re.put("resultcode",4);
                        re.put("resultmsg","验证码发送过于频繁");
                        return ok(re);
                    }
                }
            }


            //检查clientid是否存在
            final OauthClientInfoVO oauthClientInfoVO = oauthClientInfoService.fetchByAppKey(clientid);
            if(oauthClientInfoVO==null){
                log.info("{}短信发送验证不通过，无效的客户端id:{}", smsService.getSmsTypeTitleMap().get(smsType),clientid);
                //clientid无效
                JSONObject re=new JSONObject();
                re.put("resultcode",5);
                re.put("resultmsg",smsService.getSmsTypeTitleMap().get(smsType)+"短信发送失败，没有权限");
                return ok(re);
            }

            //验证该手机号是否在系统中存在
            BaseResponse<SysStaffInfoVO> voData = userCenterFeignClient.getUserVoByMobile(mobile);
            SysStaffInfoVO loginUserDetailVO = voData.getData();
            log.info("loginUserDetailVO==================={}",loginUserDetailVO);
            if (loginUserDetailVO == null || StringUtils.isEmpty(loginUserDetailVO.getStaffId())
                || loginUserDetailVO.getStaffStatus()
                .equalsIgnoreCase(UserCenterConstant.USER_STAFF_STATUS_INVALID)) {
                log.info("{}短信发送验证不通过，无此手机号:{}", smsService.getSmsTypeTitleMap().get(smsType),mobile);
                //手机号码无效
                JSONObject re=new JSONObject();
                re.put("resultcode",6);
                re.put("resultmsg",smsService.getSmsTypeTitleMap().get(smsType)+"短信发送失败，用户未绑定此手机号");
                return ok(re);
            }


            String msg=null;
            if(smsType.equals("0") || smsType.equals("1") || smsType.equals("3")){
                msg = Integer.toString(RandomUtils.nextInt(100000,999999));//六位随机码
                msg ="666666";
            }else if(smsType.equals("2")){
                msg=userName+","+processTitle;
            }
            JSONObject responseEntity = smsService.sendSmsCode(smsType,mobile,msg);
            String resultcode = responseEntity.getString("resultcode");
            String resultmsg = responseEntity.getString("resultmsg");
            String taskid = responseEntity.getString("taskid");

            if(StringUtils.isNotEmpty(resultcode) && "0".equals(resultcode)) { //发送成功
                if("0".equals(smsType) || "1".equals(smsType) || "3".equals(smsType)) redisUtils.set(keyInRedis,msg,600L);
                log.info("{}短信发送成功，手机号码：{}，clientid：{},发送时间：{}",smsService.getSmsTypeTitleMap().get(smsType),mobile,clientid, DateUtils.formatDateTime(new Date()));
            }
            /**log.info("------responseEntity.resultcode------->"+resultcode);
            log.info("------responseEntity.resultmsg------->"+resultmsg);
            log.info("------responseEntity.taskid------->"+taskid);*/
            return ok(responseEntity);


        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
            log.info("{}短信发送失败，手机号码：{}，clientid：{},时间：{},原因：{}",smsService.getSmsTypeTitleMap().get(smsType),mobile,clientid, DateUtils.formatDateTime(new Date()),e.fillInStackTrace());
            JSONObject re=new JSONObject();
            re.put("resultcode",6);
            re.put("resultmsg",smsService.getSmsTypeTitleMap().get(smsType)+"短信发送失败，没有权限");
            return ok(re);
        }


    }








    public static void main(String[] args) {
        SmsController c = new SmsController();
        String mobile="18888889898,1788999828,3333333";
        boolean contains = mobile.contains("，");
        System.out.println(contains);

        /**String str="wang@zhen@qian";
        String[] info = StringUtils.split(str, "@");
        String clientid=info[0];
        String mobile=info[1];
        //String msg=info[2];
        System.out.println(mobile);
        System.out.println(clientid);
        //System.out.println(msg);
        */

        //c.sendSmsCode(null);
        Sm2Encryptor tool = new Sm2Encryptor();
        /** */
        String ss="2@bVS46ElU@18653176857";
        String publickey="0428D625CEEB71CE823BD7D78DFEE7B122F2DA5C4D21E32253AD684D0FE21810394A799639C0CDFBFEB535A1DFD6A366A637E582CE0B1466A5FE7858841135DE6B";
        try {
            final String s = tool.encryptString(ss,publickey);
            System.out.println("s=="+s);
        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
        }
        /**
        String privateKey="4F7144028D4DCF88FA50F0E2B3FFDDCF63BBE17D1700537DCE037687D3AA3DA7";
        String skey="0405c3a1f1ae83bb1c799c299e9decab337e31faf5a26b0444247692ec929924fe4b62aaf94940c7536c558d7087351b895c0044d8374acd67570524b563f10c04dd11264dc6ec5c4cd0a1f3fce3f1d554e96b9450d05e3a330103d870265b66231fdf384f30d423ab31e7d31802de237bce758bdb2b74017b48b9f52588be8806fd";
        try {
            final String str = tool.decryptString(privateKey, skey);
            String[] info = str.split("@");

            String smsType=info[0];
            String clientid=info[1];
            String mobile=info[2];

            System.out.println("s===="+str);

        } catch (InvalidCipherTextException e) {
            e.printStackTrace();
        }
         */

    }

}
