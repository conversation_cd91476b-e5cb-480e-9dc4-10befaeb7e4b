package cn.chinaunicom.sdsi.cloud.auth.provider;

import cn.chinaunicom.sdsi.security.browser.captcha.LoginRetryCounter;
import cn.chinaunicom.sdsi.security.browser.properties.UniFastSecurityProperties;
import cn.chinaunicom.sdsi.security.util.PasswordChecker;
import cn.chinaunicom.sdsi.security.util.Sm2Encryptor;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.rememberme.InvalidCookieException;
import org.springframework.util.StringUtils;

/**
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/3/4
 */
public class MallAuthenticationProvider extends DaoAuthenticationProvider {

    @Setter
    private Sm2Encryptor sm2Encryptor;

    @Setter
    private UniFastSecurityProperties securityProperties;

    @Autowired
    private LoginRetryCounter loginRetryCounter;


    @Override
    protected void additionalAuthenticationChecks(UserDetails userDetails,
                                                  UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
        if (authentication.getCredentials() == null) {
            logger.debug("Authentication failed: no credentials provided");
            throw new DisabledException("认证失败 ");
        }
        //校验弱口令
        String username = authentication.getName();
        this.checkLoginRetry(username);
        //提交过来的密码
        String presentedPassword = authentication.getCredentials().toString();
        final String decodePass;
        try {
            decodePass= sm2Encryptor.decryptString(securityProperties.getPrivateKey(), presentedPassword);
        } catch (Exception e) {
            logger.debug("密码解密出现异常:{}", e);
            throw new DisabledException("认证失败,密码解密错误");
        }

        if (!getPasswordEncoder().matches(decodePass, userDetails.getPassword().trim())) {
            logger.debug("Authentication failed: password does not match stored value");
            String msg = recordLoginRetry(username);
            throw new DisabledException(msg);
        } else {
            //密码正确，次数清零
            this.loginRetryCounter.setRetryCount(username, 0);
        }

        final PasswordChecker.LEVEL level = PasswordChecker.checkPassStrength(decodePass);
        if (level.getScore() < PasswordChecker.LEVEL.MEDIUM.getScore()) {
            throw new DisabledException("您录入的是弱口令，登录失败！");
        }

    }

    private String recordLoginRetry(String username) {
        String msg = "";
        if (!StringUtils.isEmpty(username)) {
            int retryLimitCount = this.securityProperties.getPasswordErrorLimit();
            int retryCount = this.loginRetryCounter.getRetryCount(username);
            if (retryLimitCount > 0) {
                if (retryLimitCount == retryCount) {
                    msg = "登录错误次数已超限，请" + this.securityProperties.getPasswordErrorFrozen() / 60 + "分钟后再试！";
                } else if (retryLimitCount > retryCount) {
                    msg = "登录错误，再错误" + (retryLimitCount - retryCount) + "次，您将无法登录！";
                } else {
                    msg = "登录错误次数已超限，请稍后再试！";
                }
                ++retryCount;
                this.loginRetryCounter.setRetryCount(username, retryCount);
            }
        }
        return msg;
    }
    private void checkLoginRetry(String username) {
        int retryLimitCount = this.securityProperties.getPasswordErrorLimit();
        System.err.println(this.loginRetryCounter.getRetryCount(username)+"========");
        if (retryLimitCount > 0 && this.loginRetryCounter.getRetryCount(username) >= retryLimitCount) {
            throw new DisabledException("登录错误次数已超限，请" + this.securityProperties.getPasswordErrorFrozen() / 60 + "分钟后再试！");
        }
    }
}
