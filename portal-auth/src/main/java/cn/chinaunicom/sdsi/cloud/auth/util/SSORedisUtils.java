package cn.chinaunicom.sdsi.cloud.auth.util;

import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPoolConfig;

import java.util.LinkedHashSet;
import java.util.Set;

public class SSORedisUtils {

    private JedisCluster getConnect() {
        JedisPoolConfig config = new JedisPoolConfig();
        //最大空闲连接数, 应用自己评估
        config.setMaxIdle(200);
        //最大连接数, 应用自己评估
        config.setMaxTotal(300);
        // 最大允许等待时间，如果超过这个时间还未获取到连接，则会报异常：
        config.setMaxWaitMillis(30000L);
        // 连接超时时间
        int connectionTimeout = 5000;
        // 读取数据超时时间
        int soTimeout = 3000;
        // 尝试次数
        int maxAttempts = 10;
        Set<HostAndPort> nodes = new LinkedHashSet<HostAndPort>();
        nodes.add(new HostAndPort("*************", 32516));
        nodes.add(new HostAndPort("*************", 32515));
        nodes.add(new HostAndPort("************", 32623));
        nodes.add(new HostAndPort("*************", 32595));
        nodes.add(new HostAndPort("*************", 32635));
        nodes.add(new HostAndPort("*************", 32638));
        return new JedisCluster(nodes, connectionTimeout, soTimeout, maxAttempts, "SdGzvisual20220720", config);
    }


    public String get(String key) {
        try {
            JedisCluster jedisCluster = getConnect();
            String s = jedisCluster.get(key);
            jedisCluster.close();
            return s;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

}
