package cn.chinaunicom.sdsi.cloud.auth.filter;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.fegin.SanquanBoFeignClient;
import cn.chinaunicom.sdsi.cloud.auth.util.AESUtil;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.cloud.roles.entity.SanquanRole;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuVo;
import cn.chinaunicom.sdsi.platform.role.entity.RoleVo;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * 用于2.0系统跳入3.0系统的认证过滤器
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2021/2/25
 */
@Slf4j
@Component
public class SSOGetBoOaAndPhoneDecideFilter extends AbstractAuthenticationProcessingFilter {

    @Value("${auth.url.ssoSessionSessionUrl}")
    private String sessionUrl;

    @Value("${app.aes.key}")
    private String AESKey;

    @Autowired
    private SanquanBoFeignClient sanquanBoFeignClient;
    public SSOGetBoOaAndPhoneDecideFilter() {
        super(new AntPathRequestMatcher("/sso/oauth/bosession/oaAndPhone", "GET"));
    }

    @Override
    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        super.setAuthenticationManager(authenticationManager);
    }

    private static String toUtf8(String str) {
        return new String(str.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("\\\"", "'").replace("\"", "");
    }

    private static String getRandomCode(int length) {
        SecureRandom random = new SecureRandom();
        return new BigInteger(length * 4, random).toString(32);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
        String info = request.getParameter("info");
        System.err.println("SSOGetBoDecideFilter==========authSessionId======"+info);
        if (!StringUtils.isNotEmpty(info)) {
            throw new UsernameNotFoundException("无效的会话");
        }
        long startTime = System.currentTimeMillis();
        JSONObject responseObject = null;
        try {
            long endTime1 = System.currentTimeMillis();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
            String nowTime = sdf.format(new Date());
            String yxloginMessage = nowTime + "请求亚信登录接口本次登录耗时" + (endTime1 - startTime) + "毫秒";
            System.err.println(yxloginMessage);
            // 解析加密信息
            String decrypt = AESUtil.decrypt(info, AESKey);
            System.err.println("解密后的信息：" + decrypt);
            responseObject = new JSONObject(decrypt);
            long time = responseObject.getLong("time");
            // 验证时间的有效性
            boolean isValidTime = isValidTime(time);
            if (isValidTime) {
                List<String> roleIds = new ArrayList<>();
                String jobNum = responseObject.getStr("oa");
                //查询登录信息
                String phone = responseObject.getStr("phone");
                long startTime2 = System.currentTimeMillis();
                UserVo userVo = sanquanBoFeignClient.findStaff(jobNum);
                long endTime2 = System.currentTimeMillis();
                String yxloginMessage2 = nowTime + "请求sanquanBoFeignClient.findStaff耗时" + (endTime2 - startTime2) + "毫秒--"+(userVo==null?"null":userVo.getName());
                System.err.println(yxloginMessage2);
                //用户信息
                MallUser mallUser = new MallUser();
                if (userVo == null) {
                    throw new UsernameNotFoundException("没有当前用户！");
                }
                List<GrantedAuthority> authorities = new ArrayList<>();
                // 查询用户拥有的角色
                long startTime3 = System.currentTimeMillis();
                List<RoleVo> userHasRoleList = sanquanBoFeignClient.findRoleByUserId2(userVo.getId());
                long endTime3 = System.currentTimeMillis();
                String yxloginMessage3 = nowTime + "请求sanquanBoFeignClient.findRoleByUserId2耗时" + (endTime3 - startTime3) + "毫秒";
                System.err.println(yxloginMessage3);
                for (RoleVo roleVo : userHasRoleList) {
                    authorities.add(new SimpleGrantedAuthority(roleVo.getRoleCode()));
                }
                String maxRoleLevel = getMaxRoleLevel(userHasRoleList);//获取该用户最高优先级的角色等级
                mallUser.getCustomParam().put("userRoleArray", new JSONArray(userHasRoleList));
                // 菜单权限
                List<MenuVo> menuPermission = sanquanBoFeignClient.findUserHasPermission(userVo.getId());

                for (MenuVo menuVo : menuPermission) {
                    if (StringUtils.isNotEmpty(menuVo.getMenuPermission())) {
                        authorities.add(new SimpleGrantedAuthority(menuVo.getMenuPermission()));
                    }
                }
                mallUser.setMaxRoleLevel(maxRoleLevel);
                mallUser.getCustomParam().put("userId", userVo.getId());
//                mallUser.setCompanyId(authSessionId);
                mallUser.setTenantId("system");
                mallUser.setStaffId(jobNum);
                mallUser.setStaffName(userVo.getName());
                mallUser.setLoginName(jobNum);
                mallUser.setUsername(jobNum + getRandomCode(12));
//                mallUser.setOrgCode(staff.getStr("orgId"));
//                mallUser.setOrgName(staff.getStr("orgName"));
                mallUser.setTel(userVo.getPhone());
                mallUser.setCity(userVo.getCity());
                mallUser.setDistrict(userVo.getCounty());
                mallUser.getCustomParam().put("city", userVo.getCity());
                mallUser.getCustomParam().put("district", userVo.getCounty());
                mallUser.setAuthorities(authorities);
                mallUser.getCustomParam().put("industryNameSj", userVo.getIndustry());
                long endTime = System.currentTimeMillis();
                String loginMessage = mallUser.getStaffName() + "===" + mallUser.getStaffId() + nowTime + "本次登录耗时" + (endTime - startTime) + "毫秒";
                System.err.println(loginMessage);

                UnifastAuthToken uniToken = new UnifastAuthToken(mallUser, jobNum + time, mallUser.getAuthorities());
                System.err.println("uniToken======================="+uniToken);
                log.info("登录日志:{}", loginMessage);
                log.error("登录日志:{}", loginMessage);
                return uniToken;
            }

        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
        throw new UsernameNotFoundException("会话已失效");

    }

    // 验证时间的有效性
    private static boolean isValidTime(long time) {
        // 验证 time 和当前时间差值是否超过五分钟
        long currentTime = Instant.now().toEpochMilli(); // 获取当前时间的时间戳（毫秒）
        long timeDifference = currentTime - time;  // 计算时间差（毫秒）
        // 五分钟的毫秒数
        long fiveMinutesInMillis = 5 * 60 * 1000;
        boolean isValidTime = timeDifference < fiveMinutesInMillis;
        return isValidTime;
    }

    private String getMaxRoleLevel(List<RoleVo> userHasRoleList) {
        String level = "";
        int maxLevel = 0;
        for (RoleVo roleVo : userHasRoleList) {
            int tempmaxLevel = roleVo.getPriorityLevel();
            if(tempmaxLevel > maxLevel){
                maxLevel = tempmaxLevel;
            }
        }
        level = String.valueOf(maxLevel);
        return level;
    }

    public static List<String> permission(List<String> list) {
        List<String> newList = new ArrayList<>(list.size());
        list.forEach(i -> {
            if (!newList.contains(i)) { // 如果新集合中不存在则插入
                newList.add(i);
            }
        });
        return newList;
    }

}
