package cn.chinaunicom.sdsi.cloud.auth.util;


import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.lang.reflect.Array;

public class AESUtil {

	private static final String ADD = "/add/";

	public static String encrypt(String str, String key) throws Exception {
		if (str == null || key == null) {
			return null;
		}
		Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
		cipher.init(Cipher.ENCRYPT_MODE,
				new SecretKeySpec(key.getBytes("utf-8"), "AES"));
		byte[] bytes = cipher.doFinal(str.getBytes("utf-8"));
		String result = new BASE64Encoder().encode(bytes);
		result = result.toString().replaceAll("\r\n", "");
		result = result.replaceAll("\\+", ADD);
		return result;
	}

	public static String decrypt(String str, String key) throws Exception {
		if (str == null || key == null) {
			return null;
		}
		str = str.replaceAll(ADD,"+");
		Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
		cipher.init(Cipher.DECRYPT_MODE,
				new SecretKeySpec(key.getBytes("utf-8"), "AES"));
		byte[] bytes = new BASE64Decoder().decodeBuffer(str);
		bytes = cipher.doFinal(bytes);
		return new String(bytes);
	}

	public static void main(String[] args) throws Exception {
		long timestamp = System.currentTimeMillis();
		System.out.println(timestamp);
		String b = "{\"phone\":\"18620028876\",\"time\":1612170192349,\"oa\":\"sd-duanzk\"}";
		b = b.replace("1612170192349",String.valueOf(timestamp));
		String a = encrypt( b, "1234567891234567");
		System.err.println(a);
	}


}