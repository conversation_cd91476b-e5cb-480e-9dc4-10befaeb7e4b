package cn.chinaunicom.sdsi.cloud.auth.config;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.authentication.*;
import cn.chinaunicom.sdsi.cloud.auth.filter.*;
import cn.chinaunicom.sdsi.cloud.auth.provider.MallAuthenticationProvider;
import cn.chinaunicom.sdsi.cloud.auth.service.SaaSUserDetailsService;
import cn.chinaunicom.sdsi.cloud.token.UniTokenExtractor;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.enums.UnifastEnum.LoginType;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.RedisUtils;
import cn.chinaunicom.sdsi.security.browser.captcha.CaptchaHandler;
import cn.chinaunicom.sdsi.security.browser.captcha.CaptchaProcessingFilter;
import cn.chinaunicom.sdsi.security.browser.captcha.LoginRetryCounter;
import cn.chinaunicom.sdsi.security.browser.details.CustomWebAuthenticationDetails;
import cn.chinaunicom.sdsi.security.browser.properties.UniFastSecurityProperties;
import cn.chinaunicom.sdsi.security.util.Sm2Encryptor;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy;
import org.springframework.security.web.session.SessionManagementFilter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * webSecurity配置类
 * [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/9/30
 */
@Primary
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
@ConfigurationProperties(prefix = "unifast.cloud.resource")
public class WebSecurityConfigurer extends WebSecurityConfigurerAdapter {
    @Getter
    @Setter
    private List<String> whiteList;
    // 验证码是否有效,true校验验证码有效性,fasle不校验验证码有效性
    @Value("${unifast.security.captcha-valid:true}")
    private boolean captchaValid;
    /**
     * 获取认证中心地址
     */
    @Value("${security.oauth2.resource.token-info-uri:}")
    public String AUTH_URL;
    /**
     * 获取clientid
     */
    @Value("${security.oauth2.client.client-id:}")
    public String CLIENT_ID;
    /**
     * 获取CLIENT_SECRET
     */
    @Value("${security.oauth2.client.client-secret:}")
    public String CLIENT_SECRET;
    @Resource
    private UserDetailsService userDetailsService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Autowired
    private MyAuthenticationSuccessHandler authenticationSuccessHandler;
    @Autowired
    private ExpertAuthenticationSuccessHandler expertAuthenticationSuccessHandler;
    @Autowired
    private PartnerAuthenticationSuccessHandler partnerAuthenticationSuccessHandler;
    @Autowired
    private MyAuthenticationFailHandler authenticationFailHandler;
    @Autowired
    private ExpertAuthenticationFailHandler expertAuthenticationFailHandler;
    @Autowired
    private PartnerAuthenticationFailHandler partnerAuthenticationFailHandler;
    @Autowired
    private CloudAuthenticationSuccessHandler cloudAuthenticationSuccessHandler;
    @Autowired
    private CloudAuthenticationFailHandler cloudAuthenticationFailHandler;
    @Autowired
    private SSOPartnerAuthenticationSuccessHandler ssoPartnerAuthenticationSuccessHandler;
    @Autowired
    private SSOPartnerAuthenticationFailHandler ssoPartnerAuthenticationFailHandler;
    @Autowired
    private PartnerClientAuthSuccessHandler partnerClientAuthSuccessHandler;
    @Autowired
    private ReloadAuthenticationHandler reloadAuthenticationHandler;
    @Autowired
    private PartnerClientAuthFailHandler partnerClientAuthFailHandler;
    @Autowired
    private OuCutAuthenticationSuccessHandler ouCutAuthenticationSuccessHandler;
    @Autowired
    private OuCutAuthenticationFailHandler ouCutAuthenticationFailHandler;
    @Autowired
    private DingCodeDecideFilterSuccessHandler dingCodeDecideFilterSuccessHandler;
    @Autowired
    private DingCodeDecideFilterFailHandler dingCodeDecideFilterFailHandler;
    @Autowired
    private SmsCodeAuthenticationFilterSuccessHandler smsCodeAuthenticationFilterSuccessHandler;
    @Autowired
    private SmsCodeAuthenticationFilterFailHandler smsCodeAuthenticationFilterFailHandler;
    @Autowired
    private SSORedisFilterSuccessHandler ssoRedisFilterSuccessHandler;
    @Autowired
    private SSORedisFilterBoSuccessHandler ssoRedisFilterBoSuccessHandler;
    @Autowired
    private SSORedisFilterFailHandler ssoRedisFilterFailHandler;
    @Autowired
    private SSORedisFilterBoFailHandler ssoRedisFilterBoFailHandler;
    @Autowired
    private SSOGetSuccessHandler ssoGetSuccessHandler;
    @Autowired
    private SSOGetFailHandler ssoGetFailHandler;
    // bo手机端
    @Autowired
    private SSOGetBoSuccessHandler ssoGetBoSuccessHandler;
    @Autowired
    private SSOGetBoFailHandler ssoGetBoFailHandler;
    @Autowired
    private SSORedisYKFilterFailHandler ssoRedisYKFilterFailHandler;
    @Autowired
    private SSORedisYKFilterSuccessHandler ssoRedisYKFilterSuccessHandler;
    @Autowired
    private SSOAppSuccessHandler ssoAppSuccessHandler;
    @Autowired
    private SSOAppFailHandler ssoAppFailHandler;

    @Autowired
    private SSOAppCitySuccessHandler ssoAppCitySuccessHandler;
    @Autowired
    private SSOAppCityFailHandler ssoAppCityFailHandler;

    @Autowired
    private SSOYKCityFilterFailHandler ssoykCityFilterFailHandler;
    @Autowired
    private SSOYKCityFilterSuccessHandler ssoykCityFilterSuccessHandler;

    // 售前支撑人单点
    @Autowired
    private SSOYKPresaleSupporterFilterFailHandler ssoykPresaleSupporterFilterFailHandler;
    @Autowired
    private SSOYKPresaleSupporterFilterSuccessHandler ssoykPresaleSupporterFilterSuccessHandler;

    // 售前支撑人单点(手机端)
    @Autowired
    private SSOAppPresaleSupporterFailHandler ssoAppPresaleSupporterFailHandler;
    @Autowired
    private SSOAppPresaleSupporterSuccessHandler ssoAppPresaleSupporterSuccessHandler;

    // 手机端单点（统一）(bo)
    @Autowired
    private SSOBoAppUnifyFailHandler ssoBoAppUnifyFailHandler;
    @Autowired
    private SSOBoAppUnifySuccessHandler ssoBoAppUnifySuccessHandler;

    // pc端单点（统一）
    @Autowired
    private SSOUnifyYKFilterFailHandler ssoUnifyYKFilterFailHandler;
    @Autowired
    private SSOUnifyYKFilterSuccessHandler ssoUnifyYKFilterSuccessHandler;

    // pc端单点（统一）Bo稽核
    @Autowired
    private SSOUnifyBoFilterFailHandler ssoUnifyBoFilterFailHandler;
    @Autowired
    private SSOUnifyBoFilterSuccessHandler ssoUnifyBoFilterSuccessHandler;

    @Autowired
    private WechatAuthenticationFilterSuccessHandler wechatAuthenticationFilterSuccessHandler;
    @Autowired
    private WechatAuthenticationFilterFailHandler wechatAuthenticationFilterFailHandler;
    @Autowired
    private DingLoginDecideFilterSuccessHandler dingLoginDecideFilterSuccessHandler;
    @Autowired
    private DingLoginDecideFilterFailHandler dingLoginDecideFilterFailHandler;
    @Autowired
    private DingLoginDecideFilterSuccessHandler DingAuthenticationFilterSuccessHandler;
    @Autowired
    private DingLoginDecideFilterFailHandler DingAuthenticationFilterFailHandler;
    @Resource
    private AuthenticationDetailsSource<HttpServletRequest, CustomWebAuthenticationDetails> authenticationDetailsSource;
    @Resource
    private LoginRetryCounter loginRetryCounter;
    @Resource
    private UniTokenExtractor uniTokenExtractor;
    @Resource
    private DefaultTokenServices tokenServices;
    @Resource
    private Gson gson;
    private final UniTokenExtractor extractor = new UniTokenExtractor();
    @Resource
    private UniFastSecurityProperties securityProperties;
    @Resource
    private Sm2Encryptor sM2Encryptor;
    @Resource
    private JumpInDecideFilter jumpInDecideFilter;
    @Resource
    SaaSUserDetailsService saaSUserDetailsService;
    @Resource
    private CaptchaHandler captchaHandler;
    //    @Autowired
    @Resource
    RedisTokenStore redisTokenStore;
    @Autowired
    RedisUtils redisUtils;

    // bo手机端单点（根据手机号或工号验证）
    @Autowired
    private SSOGetBoOaAndPhoneSuccessHandler ssoGetBoOaAndPhoneSuccessHandler;
    @Autowired
    private SSOGetBoOaAndPhoneFailHandler ssoGetBoOaAndPhoneFailHandler;

    /**
     * 设置用户加密信息
     *
     * @param auth
     * @throws Exception
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userDetailsService).passwordEncoder(passwordEncoder);
    }

    @Bean
    public AuthenticationProvider authenticationProvider() {
        MallAuthenticationProvider provider = new MallAuthenticationProvider();
        provider.setSecurityProperties(securityProperties);
        provider.setPasswordEncoder(passwordEncoder);
        provider.setSm2Encryptor(sM2Encryptor);
        provider.setUserDetailsService(userDetailsService);
        return provider;
    }

    /**
     * 忽略获取授权接口
     *
     * @param http
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.csrf().disable().formLogin()
                .successHandler(authenticationSuccessHandler).failureHandler(authenticationFailHandler);
        http.authenticationProvider(authenticationProvider());
        // 若干白名单
        if (null != whiteList) {
            for (String au : whiteList) {
                http.authorizeRequests().antMatchers(au).permitAll();
            }
        }
        SaaSAuthenticationFilter saaSAuthenticationFilter = new SaaSAuthenticationFilter(securityProperties,
                authenticationSuccessHandler, authenticationFailHandler, authenticationDetailsSource);
        saaSAuthenticationFilter.setCaptchaHandler(captchaHandler);
        saaSAuthenticationFilter.setAuthenticationManager(authenticationManagerBean());
        saaSAuthenticationFilter.setLoginRetryCounter(loginRetryCounter);
        saaSAuthenticationFilter.setSm2Encryptor(sM2Encryptor);
        saaSAuthenticationFilter.setPasswordEncoder(passwordEncoder);
        saaSAuthenticationFilter.setSaaSUserDetailsService(saaSUserDetailsService);
        saaSAuthenticationFilter.setAuthenticationDetailsSource(authenticationDetailsSource);
        saaSAuthenticationFilter.setSessionAuthenticationStrategy(new SessionFixationProtectionStrategy());
        saaSAuthenticationFilter.setCaptchaValid(this.captchaValid);
        saaSAuthenticationFilter.setAUTH_URL(AUTH_URL);
        saaSAuthenticationFilter.setCLIENT_ID(CLIENT_ID);
        saaSAuthenticationFilter.setCLIENT_SECRET(CLIENT_SECRET);
        http.addFilterBefore(ssoAppFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoAppCityFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(redisDecideFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(redisDecideBoFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(redisDecideYKFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(sooGetDecideFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoGetBoDecideFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoBoOaAndPhoneUnifyFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(decideYKCityFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(decideYKPresaleSupporterFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoAppPresaleSupporterFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoAppUnifyFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoPcUnifyFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ssoPcBoUnifyFilter(), UsernamePasswordAuthenticationFilter.class); // 统一登录 bo稽核
        http.addFilterAt(saaSAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(jumpInDecideFilter, UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(cloudAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(ouCutAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(smsCodeAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(wechatAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(reloadAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(authDecideFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(new MallRedirectAuthFilter(tokenServices), SessionManagementFilter.class);

        http.authorizeRequests()
                .requestMatchers(EndpointRequest.toAnyEndpoint()).permitAll()
                .anyRequest().authenticated();
        http.exceptionHandling(t -> t.authenticationEntryPoint((request, response, e) -> {
            BaseResponse<String> result = new BaseResponse<>(ResponseEnum.STATUS_CODE_401.getCode(), ResponseEnum.STATUS_CODE_401.getMsg(), "error");
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.getWriter().print(gson.toJson(result));
        }));
        http.logout().logoutSuccessHandler((request, response, authentication) -> {

            if (!request.getMethod().equals("POST")) {
                throw new AuthenticationServiceException("POST Authentication method not supported");
            } else {
                final String token = extractor.readToken(request);
                final boolean b = tokenServices.revokeToken(token);
                BaseResponse<String> result = new BaseResponse<>(ResponseEnum.SUCCESS.getCode(), "注销成功", "success");
                if (!b) {
                    result.setData("error");
                    result.setMessage("未获取到令牌或令牌已注销");
                }
                if (Objects.nonNull(authentication)) {
                    MallUser mallUser = (MallUser) authentication.getPrincipal();
                    //TODO 增加清理标记，可以根据clientid 清理
                    cleanMutilClientsAccessTokenCache(mallUser);
                    if (Objects.nonNull(mallUser)) {
                        // 保存登录日志
                        saaSUserDetailsService.saveLoginRecord(request, mallUser.getStaffId(), mallUser.getLoginName(),
                                LoginType.logout.getCode(), LoginType.logout.getMsg(),
                                mallUser.getTenantId(), "pc_logout");
                    }
                }
                response.setContentType("application/json;charset=utf-8");
                response.setStatus(HttpStatus.OK.value());
                response.getWriter().print(gson.toJson(result));
            }

        });
    }

    /**
     * 清除缓存的同一个账号的多个clientid对应的token信息
     *
     * @param user
     */
    public void cleanMutilClientsAccessTokenCache(MallUser user) {
        Set<String> tokens = redisUtils.getSet("UniCloud_Auth_Tokens:" + user.getStaffId());
        Iterator car = tokens.iterator();
        while (car.hasNext()) {
            //此方法会删掉各类accessToken、RefreshToken等各类token信息
            redisTokenStore.removeAccessToken(car.next().toString());
        }
        //删除计数器
        redisUtils.remove("UniCloud_Auth_Tokens:" + user.getStaffId());
    }

    /**
     * 实例化授权管理器
     *
     * @return
     * @throws Exception
     */
    @Override
    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Bean
    public CaptchaProcessingFilter captchaProcessingFilter() throws Exception {
        CaptchaProcessingFilter captchaProcessingFilter = new CaptchaProcessingFilter(
                securityProperties, authenticationSuccessHandler,
                authenticationFailHandler, authenticationDetailsSource);
        captchaProcessingFilter.setAuthenticationManager(authenticationManagerBean());
        captchaProcessingFilter.setLoginRetryCounter(loginRetryCounter);
        captchaProcessingFilter.setCaptchaHandler(captchaHandler);
        captchaProcessingFilter.setAuthenticationDetailsSource(authenticationDetailsSource);
        captchaProcessingFilter.setSessionAuthenticationStrategy(new SessionFixationProtectionStrategy());
        return captchaProcessingFilter;
    }

//    @Bean
//    public DingLoginDecideFilter dingLoginDecideFilter() throws Exception {
//        DingLoginDecideFilter dingLoginDecideFilter = new DingLoginDecideFilter();
//        dingLoginDecideFilter.setAuthenticationManager(authenticationManagerBean());
//        dingLoginDecideFilter.setAuthenticationSuccessHandler(dingLoginDecideFilterSuccessHandler);
//        dingLoginDecideFilter.setAuthenticationFailureHandler(dingLoginDecideFilterFailHandler);
//        return dingLoginDecideFilter;
//    }

    @Bean
    public ExpertAuthenticationFilter expertAuthenticationFilter() throws Exception {
        ExpertAuthenticationFilter expertFilter = new ExpertAuthenticationFilter(securityProperties,
                expertAuthenticationSuccessHandler, expertAuthenticationFailHandler, authenticationDetailsSource);

        expertFilter.setAuthenticationManager(authenticationManagerBean());
        expertFilter.setLoginRetryCounter(loginRetryCounter);
        expertFilter.setAuthenticationDetailsSource(authenticationDetailsSource);
        expertFilter.setSessionAuthenticationStrategy(new SessionFixationProtectionStrategy());
        return expertFilter;
    }

    @Bean
    public OuCutAuthenticationFilter ouCutAuthenticationFilter() throws Exception {
        OuCutAuthenticationFilter ouFilter = new OuCutAuthenticationFilter();
        ouFilter.setAuthenticationManager(authenticationManagerBean());
        ouFilter.setAuthenticationSuccessHandler(ouCutAuthenticationSuccessHandler);
        ouFilter.setAuthenticationFailureHandler(ouCutAuthenticationFailHandler);
        ouFilter.setUniTokenExtractor(uniTokenExtractor);
        return ouFilter;
    }

    @Bean
    public SSOZhengqiDecideFilter redisDecideFilter() throws Exception {
        SSOZhengqiDecideFilter ss = new SSOZhengqiDecideFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoRedisFilterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoRedisFilterFailHandler);
        return ss;
    }
    @Bean
    public SSOZhengqiBoDecideFilter redisDecideBoFilter() throws Exception {
        SSOZhengqiBoDecideFilter ss = new SSOZhengqiBoDecideFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoRedisFilterBoSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoRedisFilterBoFailHandler);
        return ss;
    }
    @Bean
    public SSOZhengqiDecideYKFilter redisDecideYKFilter() throws Exception {
        SSOZhengqiDecideYKFilter ss = new SSOZhengqiDecideYKFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoRedisYKFilterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoRedisYKFilterFailHandler);
        return ss;
    }

    @Bean
    public SSOZhengqiDecideYKCityFilter decideYKCityFilter() throws Exception {
        SSOZhengqiDecideYKCityFilter ss = new SSOZhengqiDecideYKCityFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoykCityFilterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoykCityFilterFailHandler);
        return ss;
    }

    @Bean
    public SmsCodeAuthenticationFilter smsCodeAuthenticationFilter() throws Exception {
        SmsCodeAuthenticationFilter smsCodeAuthenticationFilter = new SmsCodeAuthenticationFilter();
        smsCodeAuthenticationFilter.setAuthenticationManager(authenticationManagerBean());
        smsCodeAuthenticationFilter.setAuthenticationSuccessHandler(smsCodeAuthenticationFilterSuccessHandler);
        smsCodeAuthenticationFilter.setAuthenticationFailureHandler(smsCodeAuthenticationFilterFailHandler);
        smsCodeAuthenticationFilter.setUniTokenExtractor(uniTokenExtractor);
        return smsCodeAuthenticationFilter;
    }

    @Bean
    public SSOGetDecideFilter sooGetDecideFilter() throws Exception {
        SSOGetDecideFilter ss = new SSOGetDecideFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoGetSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoGetFailHandler);
        return ss;
    }
    @Bean
    public SSOGetBoDecideFilter ssoGetBoDecideFilter() throws Exception {
        SSOGetBoDecideFilter ss = new SSOGetBoDecideFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoGetBoSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoGetBoFailHandler);
        return ss;
    }

    @Bean
    public SSOAppDecideYKFilter ssoAppFilter() throws Exception {
        SSOAppDecideYKFilter ss = new SSOAppDecideYKFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoAppSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoAppFailHandler);
        return ss;
    }

    @Bean
    public SSOAppDecideYKCityFilter ssoAppCityFilter() throws Exception {
        SSOAppDecideYKCityFilter ss = new SSOAppDecideYKCityFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoAppCitySuccessHandler);
        ss.setAuthenticationFailureHandler(ssoAppCityFailHandler);
        return ss;
    }

    // 售前支撑人单点
    @Bean
    public SSOZhengqiDecideYKPresaleSupporterFilter decideYKPresaleSupporterFilter() throws Exception {
        SSOZhengqiDecideYKPresaleSupporterFilter ss = new SSOZhengqiDecideYKPresaleSupporterFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoykPresaleSupporterFilterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoykPresaleSupporterFilterFailHandler);
        return ss;
    }

    // 售前支撑人单点(手机端)
    @Bean
    public SSOAppDecideYKCityPresaleSupporterFilter ssoAppPresaleSupporterFilter() throws Exception {
        SSOAppDecideYKCityPresaleSupporterFilter ss = new SSOAppDecideYKCityPresaleSupporterFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoAppPresaleSupporterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoAppPresaleSupporterFailHandler);
        return ss;
    }

    // 手机端单点（统一）
    @Bean
    public SSOBoAppUnifyYKFilter ssoAppUnifyFilter() throws Exception {
        SSOBoAppUnifyYKFilter ss = new SSOBoAppUnifyYKFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoBoAppUnifySuccessHandler);
        ss.setAuthenticationFailureHandler(ssoBoAppUnifyFailHandler);
        return ss;
    }

    // pc端单点（统一）
    @Bean
    public SSOZhengqiDecideUnifyYKFilter ssoPcUnifyFilter() throws Exception {
        SSOZhengqiDecideUnifyYKFilter ss = new SSOZhengqiDecideUnifyYKFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoUnifyYKFilterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoUnifyYKFilterFailHandler);
        return ss;
    }

    // pc端单点（统一）BO稽核
    @Bean
    public SSOZhengqiDecideBoUnifyYKFilter ssoPcBoUnifyFilter() throws Exception {
        SSOZhengqiDecideBoUnifyYKFilter ss = new SSOZhengqiDecideBoUnifyYKFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoUnifyBoFilterSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoUnifyBoFilterFailHandler);
        return ss;
    }

    // bo手机端使用手机号或工号登录
    @Bean
    public SSOGetBoOaAndPhoneDecideFilter ssoBoOaAndPhoneUnifyFilter() throws Exception {
        SSOGetBoOaAndPhoneDecideFilter ss = new SSOGetBoOaAndPhoneDecideFilter();
        ss.setAuthenticationManager(authenticationManagerBean());
        ss.setAuthenticationSuccessHandler(ssoGetBoOaAndPhoneSuccessHandler);
        ss.setAuthenticationFailureHandler(ssoGetBoOaAndPhoneFailHandler);
        return ss;
    }


    @Bean
    public WechatAuthenticationFilter wechatAuthenticationFilter() throws Exception {
        WechatAuthenticationFilter wechatAuthenticationFilter = new WechatAuthenticationFilter();
        wechatAuthenticationFilter.setAuthenticationManager(authenticationManagerBean());
        wechatAuthenticationFilter.setAuthenticationSuccessHandler(wechatAuthenticationFilterSuccessHandler);
        wechatAuthenticationFilter.setAuthenticationFailureHandler(wechatAuthenticationFilterFailHandler);
        return wechatAuthenticationFilter;
    }


//    @Bean
//    public DingAuthenticationFilter dingAuthenticationFilter() throws Exception {
//        DingAuthenticationFilter dingAuthenticationFilter = new DingAuthenticationFilter();
//        dingAuthenticationFilter.setAuthenticationManager(authenticationManagerBean());
//        dingAuthenticationFilter.setAuthenticationSuccessHandler(DingAuthenticationFilterSuccessHandler);
//        dingAuthenticationFilter.setAuthenticationFailureHandler(DingAuthenticationFilterFailHandler);
//        return dingAuthenticationFilter;
//    }

    @Bean
    public CloudAuthenticationFilter cloudAuthenticationFilter() throws Exception {
        CloudAuthenticationFilter expertFilter = new CloudAuthenticationFilter();
        expertFilter.setAuthenticationManager(authenticationManagerBean());
        expertFilter.setAuthenticationSuccessHandler(cloudAuthenticationSuccessHandler);
        expertFilter.setAuthenticationFailureHandler(cloudAuthenticationFailHandler);
        return expertFilter;
    }

    @Bean
    public SSOPartnerAuthenticationFilter ssoPartnerAuthenticationFilter() throws Exception {
        SSOPartnerAuthenticationFilter ssoPartnerFilter = new SSOPartnerAuthenticationFilter();
        ssoPartnerFilter.setAuthenticationManager(authenticationManagerBean());
        ssoPartnerFilter.setAuthenticationSuccessHandler(ssoPartnerAuthenticationSuccessHandler);
        ssoPartnerFilter.setAuthenticationFailureHandler(ssoPartnerAuthenticationFailHandler);
        return ssoPartnerFilter;
    }


    @Bean
    public ReloadAuthenticationFilter reloadAuthenticationFilter() throws Exception {
        ReloadAuthenticationFilter reloadFilter = new ReloadAuthenticationFilter();
        reloadFilter.setAuthenticationManager(authenticationManagerBean());
        reloadFilter.setAuthenticationSuccessHandler(reloadAuthenticationHandler);
        reloadFilter.setUniTokenExtractor(uniTokenExtractor);
        return reloadFilter;
    }

    @Bean
    public AuthDecideFilter authDecideFilter() throws Exception {
        AuthDecideFilter authDecideFilter = new AuthDecideFilter();
        authDecideFilter.setAuthenticationManager(authenticationManagerBean());
        return authDecideFilter;
    }

    @Bean
    public LoginRetryCounter getLoginRetryCounter() {
        return new LoginRetryCounter();
    }

    @Bean
    public UniTokenExtractor getUniTokenExtractor() {
        return new UniTokenExtractor();
    }

}
