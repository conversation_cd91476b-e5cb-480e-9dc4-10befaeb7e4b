package cn.chinaunicom.sdsi.cloud.auth.util;

import lombok.extern.log4j.Log4j2;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 云享数据平台的加密工具类
 * @date 2020/6/1
 **/
@Log4j2
public class HmacSignatureUtil {

  public static final String ALG_TYPE = "HmacSHA256";

  public static final String UTF_8 = "UTF-8";

  public static final String EQUAL = "=";

  public static final String AND = "&";

  private static final String SIGNED_HEADER = "1";


  /**
   * 加签算法
   *
   * @param XClientId
   * @param XTimestamp
   * @param XNonce
   * @param secretKey
   * @return
   */
  public static String generateSign(String XClientId, String XTimestamp, String XNonce,
      String secretKey) {
    if (StringUtils.isEmpty(secretKey)) {
      return null;
    }

    try {
      //生成HmacSHA256
      //HmacSHA256  加密
      SecretKeySpec signingKey = new SecretKeySpec(secretKey.getBytes(UTF_8), ALG_TYPE);
      Mac mac = Mac.getInstance(ALG_TYPE);
      mac.init(signingKey);
      //按照key自然排序，生成key1=value1&key2=value2的格式
      StringJoiner joiner = new StringJoiner(AND);
      joiner.add(XClientId).add(XTimestamp).add(XNonce);
      //进行加密
      byte[] bytes = mac.doFinal(joiner.toString().getBytes(UTF_8));
      //清空
      String stmp = null;
      StringBuffer sb = new StringBuffer();
      //字节转换为16进制字符串
      for (int n = 0; bytes != null && n < bytes.length; n++) {
        stmp = Integer.toHexString(bytes[n] & 0XFF);
        if (stmp.length() == 1) {
          sb.append('0');
        }
        sb.append(stmp);
      }
      return sb.toString();
    } catch (Exception e) {
      log.warn("生成签名串失败");
      log.error("生成签名串异常:{}", e);
      return null;
    }
  }
}
