package cn.chinaunicom.sdsi.cloud.auth.filter;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.fegin.SanquanBoFeignClient;
import cn.chinaunicom.sdsi.cloud.auth.util.HttpclientUtils;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.cloud.roles.entity.SanquanRole;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.menu.entity.MenuVo;
import cn.chinaunicom.sdsi.platform.role.entity.RoleVo;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * 用于2.0系统跳入3.0系统的认证过滤器
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @since 2021/2/25
 */
@Slf4j
//@Component
public class SSOGetBoDecideFilter extends AbstractAuthenticationProcessingFilter {

    @Value("${auth.url.ssoSessionSessionUrl}")
    private String sessionUrl;

    @Autowired
    private SanquanBoFeignClient sanquanBoFeignClient;
    public SSOGetBoDecideFilter() {
        super(new AntPathRequestMatcher("/sso/oauth/bosession/token", "GET"));
    }

    @Override
    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        super.setAuthenticationManager(authenticationManager);
    }

    private static String toUtf8(String str) {
        return new String(str.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("\\\"", "'").replace("\"", "");
    }

    private static String getRandomCode(int length) {
        SecureRandom random = new SecureRandom();
        return new BigInteger(length * 4, random).toString(32);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
        String authSessionId = request.getParameter("authSessionId");
        //String authSessionId = "3673ba9d-8190-436e-a56f-43208c39df2c";
        System.err.println("SSOZhengqiDecideFilter打印返回的用户信息zhengqi==attemptAuthentication======"+authSessionId);
        JSONObject responseObject = null;
        try {
            if (!StringUtils.isNotEmpty(authSessionId) || !"3673ba9d-8190-436e-a56f-43208c39df2c".equals(authSessionId)) {
                throw new UsernameNotFoundException("无效的会话");
            }
//            String url = String.format("%s%s", sessionUrl, authSessionId);
//            String staffInfoStr = HttpclientUtils.get(url);
//            responseObject = new JSONObject(staffInfoStr);
//            if ("success".equals(responseObject.getStr("rspInfo"))) {
            List<String> roleIds = new ArrayList<>();
//                JSONObject staff = responseObject.getJSONObject("data").getJSONObject("userInfo");
//                String jobNum = "sd-liuyw5";
            String jobNum = "sd-duanzk";
            //查询登录信息
            UserVo userVo = sanquanBoFeignClient.findStaff(jobNum);
            //用户信息
            MallUser mallUser = new MallUser();
            if (userVo == null) {
                userVo = new UserVo();
                // 查找客户经理
                TSanquanDZqztJihezxProvinceMappingVO vo = sanquanBoFeignClient.findKhjlByLogin(jobNum);
                if (vo != null) {
                    roleIds.add("100002");
                    userVo.setCity(vo.getCity().replace("市", ""));
                }
                //地市接口人
                TSanquanPrefectureInterfacePersonVO cityInterfaceVo = sanquanBoFeignClient.findCityUserByJobNumber(jobNum);
                userVo.setJobNum(jobNum);
                userVo.setName("刘寅伟");
                userVo.setDeleted("normal");
                userVo.setPhone("18654943594");
                if (cityInterfaceVo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(cityInterfaceVo.getType())) {
                    roleIds.add("100008");
                    //区县审核
                    if ("1".equals(cityInterfaceVo.getType())) {
                        roleIds.add("100005");
                    }
                    //地市政企审核
                    if ("1".equals(cityInterfaceVo.getCityRole())) {
                        roleIds.add("100006");
                    }
                    if (!(("1".equals(cityInterfaceVo.getType())) || "1".equals(cityInterfaceVo.getCityRole()))) {
                        roleIds.add("100004");
                    }
                    userVo.setCity(cityInterfaceVo.getListCustomerCity().replace("市", ""));
                    userVo.setCounty(cityInterfaceVo.getListCustomerDistrict());
                }
                //权限信息
                JSONArray jsonRoles = responseObject.getJSONObject("data").getJSONArray("userRoles");
                for (int i = 0; i < jsonRoles.size(); i++) {
                    JSONObject role = jsonRoles.getJSONObject(i);
                    Long roleId = role.getLong("sysRoleId");
                    SanquanRole _role = sanquanBoFeignClient.findRoleById(String.valueOf(roleId));
                    if (_role != null) {
                        roleIds.add(_role.getId());
                    }
                }
                //用户行业信息
                JSONArray zqStaffInfoList = responseObject.getJSONObject("data").getJSONArray("zqStaffInfoList");
                if (zqStaffInfoList != null && !zqStaffInfoList.isEmpty()) {
                    userVo.setIndustry(zqStaffInfoList.getJSONObject(0).getStr("industryNameSj"));
                }
                userVo.setRoles(permission(roleIds));
                //保存用户信息
                userVo = this.sanquanBoFeignClient.saveUser(userVo);
            }
            List<GrantedAuthority> authorities = new ArrayList<>();
            // 查询用户拥有的角色
            List<RoleVo> userHasRoleList = sanquanBoFeignClient.findRoleByUserId2(userVo.getId());
            for (RoleVo roleVo : userHasRoleList) {
                authorities.add(new SimpleGrantedAuthority(roleVo.getRoleCode()));
            }
            mallUser.getCustomParam().put("userRoleArray", new JSONArray(userHasRoleList));
            // 菜单权限
            List<MenuVo> menuPermission = sanquanBoFeignClient.findUserHasPermission(userVo.getId());
            for (MenuVo menuVo : menuPermission) {
                if (StringUtils.isNotEmpty(menuVo.getMenuPermission())) {
                    authorities.add(new SimpleGrantedAuthority(menuVo.getMenuPermission()));
                }
            }
            mallUser.getCustomParam().put("userId", userVo.getId());
            mallUser.setCompanyId(authSessionId);
            mallUser.getCustomParam().put("authSessionId", authSessionId);
            mallUser.setTenantId("system");
            mallUser.setStaffId(jobNum);
            mallUser.setStaffName(userVo.getName());
            mallUser.setLoginName(jobNum);
            mallUser.setUsername(jobNum + getRandomCode(12));
            mallUser.setOrgCode("sd-duanzk");
            mallUser.setOrgName("test");
            mallUser.setTel(userVo.getPhone());
            mallUser.setCity(userVo.getCity());
            mallUser.setDistrict(userVo.getCounty());
            mallUser.getCustomParam().put("city", userVo.getCity());
            mallUser.getCustomParam().put("district", userVo.getCounty());
            mallUser.setAuthorities(authorities);
            mallUser.getCustomParam().put("industryNameSj", userVo.getIndustry());
            return new UnifastAuthToken(mallUser, authSessionId, mallUser.getAuthorities());
//            }
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
        throw new UsernameNotFoundException("会话已失效");

    }
    //    @Override
//    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
//    //        String authSessionId = request.getParameter("authSessionId");
//        String authSessionId = "3673ba9d-8190-436e-a56f-43208c39df2c";
//        JSONObject responseObject = null;
//        try {
//            if (!StringUtils.isNotEmpty(authSessionId)) {
//                throw new UsernameNotFoundException("无效的会话");
//            }
//    //            String url = String.format("%s%s", sessionUrl, authSessionId);
//    //            String staffInfoStr = HttpclientUtils.get(url);
//    //            responseObject = new JSONObject(staffInfoStr);
//    //            if ("success".equals(responseObject.getStr("rspInfo"))) {
//            List<String> roleIds = new ArrayList<>();
//    //                JSONObject staff = responseObject.getJSONObject("data").getJSONObject("userInfo");
//    //                String jobNum = "sd-liuyw5";
//            String jobNum = "111999";
//            jobNum = "sd-hanrx";
//            jobNum = "zhaoy590";
//            //查询登录信息
//            UserVo userVo = sanquanBoFeignClient.findStaff(jobNum);
//            System.err.println("sssssssssssss"+userVo.getName());
//            //用户信息
//            MallUser mallUser = new MallUser();
//            if (userVo == null) {
//                userVo = new UserVo();
//                // 查找客户经理
//                TSanquanDZqztJihezxProvinceMappingVO vo = sanquanBoFeignClient.findKhjlByLogin(jobNum);
//                if (vo != null) {
//                    roleIds.add("100002");
//                    userVo.setCity(vo.getCity().replace("市", ""));
//                }
//                //地市接口人
//                TSanquanPrefectureInterfacePersonVO cityInterfaceVo = sanquanBoFeignClient.findCityUserByJobNumber(jobNum);
//                userVo.setJobNum(jobNum);
//                userVo.setName("刘寅伟");
//                userVo.setDeleted("normal");
//                userVo.setPhone("18654943594");
//                if (cityInterfaceVo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(cityInterfaceVo.getType())) {
//                    roleIds.add("100008");
//                    //区县审核
//                    if ("1".equals(cityInterfaceVo.getType())) {
//                        roleIds.add("100005");
//                    }
//                    //地市政企审核
//                    if ("1".equals(cityInterfaceVo.getCityRole())) {
//                        roleIds.add("100006");
//                    }
//
//                    if (!(("1".equals(cityInterfaceVo.getType())) || "1".equals(cityInterfaceVo.getCityRole()))) {
//                        roleIds.add("100004");
//                    }
//                    userVo.setCity(cityInterfaceVo.getListCustomerCity().replace("市", ""));
//                    userVo.setCounty(cityInterfaceVo.getListCustomerDistrict());
//                }
//                //权限信息
//                JSONArray jsonRoles = responseObject.getJSONObject("data").getJSONArray("userRoles");
//                for (int i = 0; i < jsonRoles.size(); i++) {
//                    JSONObject role = jsonRoles.getJSONObject(i);
//                    Long roleId = role.getLong("sysRoleId");
//                    SanquanRole _role = sanquanBoFeignClient.findRoleById(String.valueOf(roleId));
//                    if (_role != null) {
//                        roleIds.add(_role.getId());
//                    }
//                }
//                //用户行业信息
//                JSONArray zqStaffInfoList = responseObject.getJSONObject("data").getJSONArray("zqStaffInfoList");
//                if (zqStaffInfoList != null && !zqStaffInfoList.isEmpty()) {
//                    userVo.setIndustry(zqStaffInfoList.getJSONObject(0).getStr("industryNameSj"));
//                }
//                userVo.setRoles(permission(roleIds));
//                //保存用户信息
//                userVo = this.sanquanBoFeignClient.saveUser(userVo);
//            }
//            List<GrantedAuthority> authorities = new ArrayList<>();
//            // 查询用户拥有的角色
//            List<RoleVo> userHasRoleList = sanquanBoFeignClient.findRoleByUserId2(userVo.getId());
//            for (RoleVo roleVo : userHasRoleList) {
//                authorities.add(new SimpleGrantedAuthority(roleVo.getRoleCode()));
//            }
//            mallUser.getCustomParam().put("userRoleArray", new JSONArray(userHasRoleList));
//            // 菜单权限
//            List<MenuVo> menuPermission = sanquanBoFeignClient.findUserHasPermission(userVo.getId());
//            for (MenuVo menuVo : menuPermission) {
//                if (StringUtils.isNotEmpty(menuVo.getMenuPermission())) {
//                    authorities.add(new SimpleGrantedAuthority(menuVo.getMenuPermission()));
//                }
//            }
//            mallUser.getCustomParam().put("userId", userVo.getId());
//            mallUser.setCompanyId(authSessionId);
//            mallUser.getCustomParam().put("authSessionId", authSessionId);
//            mallUser.setTenantId("system");
//            mallUser.setStaffId(jobNum);
//            mallUser.setStaffName(userVo.getName());
//            mallUser.setLoginName(jobNum);
//            mallUser.setUsername(jobNum + getRandomCode(12));
//            mallUser.setOrgCode("sd-liuyw5");
//            mallUser.setOrgCode("111999"); // jxt!!!
//            mallUser.setOrgCode("miaokai1"); // jxt!!!
//            mallUser.setOrgName("test");
//            mallUser.setTel(userVo.getPhone());
//            mallUser.setCity(userVo.getCity());
//            mallUser.setDistrict(userVo.getCounty());
//            mallUser.getCustomParam().put("city", userVo.getCity());
//            mallUser.getCustomParam().put("district", userVo.getCounty());
//            mallUser.setAuthorities(authorities);
//            mallUser.getCustomParam().put("industryNameSj", userVo.getIndustry());
//
//            return new UnifastAuthToken(mallUser, authSessionId, mallUser.getAuthorities());
//    //            }
//
//        } catch (Exception e) {
//            System.err.println(e.getMessage());
//        }
//        throw new UsernameNotFoundException("会话已失效");
//
//    }
    private String getMaxRoleLevel(List<RoleVo> userHasRoleList) {
        String level = "";
        int maxLevel = 0;
        for (RoleVo roleVo : userHasRoleList) {
            int tempmaxLevel = roleVo.getPriorityLevel();
            if(tempmaxLevel > maxLevel){
                maxLevel = tempmaxLevel;
            }
        }
        level = String.valueOf(maxLevel);
        return level;
    }

    public static List<String> permission(List<String> list) {
        List<String> newList = new ArrayList<>(list.size());
        list.forEach(i -> {
            if (!newList.contains(i)) { // 如果新集合中不存在则插入
                newList.add(i);
            }
        });
        return newList;
    }

}
