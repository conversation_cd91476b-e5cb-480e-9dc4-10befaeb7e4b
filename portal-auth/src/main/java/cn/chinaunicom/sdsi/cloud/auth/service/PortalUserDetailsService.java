package cn.chinaunicom.sdsi.cloud.auth.service;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.converter.SysRoleConverter;
import cn.chinaunicom.sdsi.cloud.converter.UserJobDetailConverter;
import cn.chinaunicom.sdsi.cloud.system.role.entity.SysRoleVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffInfoVO;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffOrgVO;
import cn.chinaunicom.sdsi.cloud.system.feign.UserCenterFeignClient;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;

/**
 * PortalUserDetailsService
 * @date 2021/08/17
 * @update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Slf4j
@Service
public class PortalUserDetailsService {
    public static final String INVALID = "invalid";

    @Resource
    UserCenterFeignClient userCenterFeignClient;

    /**
     * 根据手机号码查询UserDetails对象
     * <p>
     * @param mobile 手机号码，唯一键
     * @return UserDetails实现类
     */
    public UserDetails loadUserByMobile(String mobile)  {
        BaseResponse<SysStaffInfoVO> voData = userCenterFeignClient.getUserVoByMobile(mobile);
        return this.processData(voData);
    }

    /**
     * 根据微信全局唯一标识查询用户信息详情
     *
     * @param unionid 用户微信全局唯一标识
     * @param unionid 租户id
     * @return 用户详情对象
     * <AUTHOR>
     * -
     * @date 2022/2/18
     * -
     * @version 1.0
     * @description 内容描述
     */
    public UserDetails findByUnionId(String unionid)  {
        BaseResponse<SysStaffInfoVO> voData = userCenterFeignClient.findByUnionId(unionid);
        return this.processData(voData);
    }

    /**
     * 根据手机号码、租户查询UserDetails对象
     * <p>
     * @param mobile 手机号码，唯一键
     * @param tenantId 租户，唯一键
     * @return UserDetails实现类
     */
    public UserDetails loadUserByMobile(String mobile, String tenantId)  {
        BaseResponse<SysStaffInfoVO> voData = userCenterFeignClient.getUserVoByMobileAndTenant(mobile, tenantId);
        return this.processData(voData);
    }

    private MallUser processData(BaseResponse<SysStaffInfoVO> voData) {
        SysStaffInfoVO loginUserDetailVO = voData.getData();
        log.info("loginUserDetailVO==================={}",loginUserDetailVO);
        final MallUser entity = new MallUser();
        if (loginUserDetailVO == null){
            log.info("loginUserDetailVO = null 原因{}",voData.getMessage());
//            throw new InternalAuthenticationServiceException("认证失败,原因：【手机号或验证码错误】");
            return null;
        }
        if (INVALID.equalsIgnoreCase(loginUserDetailVO.getStaffStatus())){
            log.info("用户被冻结，无法登录：{}",loginUserDetailVO);
            throw new InternalAuthenticationServiceException("用户被冻结，无法登录，原因：" + loginUserDetailVO.getAttra());
        }
        entity.setOrgLevel(loginUserDetailVO.getOrgLevel());
        entity.setTenantId(loginUserDetailVO.getTenantId());
        entity.setStaffId(loginUserDetailVO.getStaffId());
        entity.setStaffName(loginUserDetailVO.getStaffName());
        entity.setStaffOrgId(loginUserDetailVO.getStaffOrgId());
        entity.setOrgId(loginUserDetailVO.getOrgId());
        entity.setOrgName(loginUserDetailVO.getOrgName());
        entity.setProvinceName(loginUserDetailVO.getOrgName());
        entity.setProvince(loginUserDetailVO.getOrgId());
        entity.setPassword(loginUserDetailVO.getPasswd());
        entity.setUsername(loginUserDetailVO.getStaffId());
        entity.setStaffStatus(loginUserDetailVO.getStaffStatus());
        entity.setStaffKind(loginUserDetailVO.getStaffKind());
        entity.setUserPwdFlag(loginUserDetailVO.getUserPwdFlag());
        entity.setLoginName(loginUserDetailVO.getLoginName());
        entity.setEnabled(true);
        List<String> authoritiesList =loginUserDetailVO.getPermissionsStr();
        if (!CollectionUtils.isEmpty(authoritiesList)){
            entity.setAuthorities(AuthorityUtils.createAuthorityList(authoritiesList.stream().filter(t->null!=t&&!t.isEmpty()).toArray(String[]::new)));
        }
        //角色对应权限关系
        List<SysRoleVO> authorityList =new ArrayList();
        authorityList = loginUserDetailVO.getRoles();
        List<SysRoleConverter> list = new ArrayList();
        authorityList.forEach(vo->{
            SysRoleConverter eshopRole = new SysRoleConverter();
            BeanUtil.copyProperties(vo,eshopRole);
            list.add(eshopRole);
        });
        entity.setAuthorityList(list);
        //用户岗位信息
        List<SysStaffOrgVO> userJobDetailVOList = loginUserDetailVO.getStaffOrgs();
        List<UserJobDetailConverter> jobList = new ArrayList();
        if (CollectionUtils.isNotEmpty(userJobDetailVOList)){
            userJobDetailVOList.forEach(vo->{
                UserJobDetailConverter jobDetailConverter = new UserJobDetailConverter();
                BeanUtil.copyProperties(vo,jobDetailConverter);
                jobList.add(jobDetailConverter);
            });
        }
        entity.setAuthorityList(list);
        entity.setUserJobDetailVOList(jobList);
        entity.setEmail(loginUserDetailVO.getEmail());
        entity.setCellphone(loginUserDetailVO.getCellphone());
        HashMap<String, Serializable> customerParam =Maps.newHashMap();
        customerParam.put(UnifastConstants.TENANT_ID,loginUserDetailVO.getTenantId());
        customerParam.put(UnifastConstants.TENANT_NAME,loginUserDetailVO.getTenantName());
        customerParam.put(UnifastConstants.TENANT_LOGIN_NAME,loginUserDetailVO.getTenantLoginName());
        customerParam.put(UnifastConstants.TENANT_ADMIN_ID,loginUserDetailVO.getTenantAdminId());
        customerParam.put(UnifastConstants.APP_JG_PUSH_TAGS,pushTagHandler(loginUserDetailVO));
        customerParam.put(UnifastConstants.APP_JG_PUSH_ALIAS,pushTagHandler(loginUserDetailVO)+"@"+loginUserDetailVO.getStaffId());
        /**
         * 增加用户身份标示userType，标示是否是系统管理员(tenant_id为system)admin，
         * 租户管理员(staff_id=tenant_admin_id)tennatAdmin,普通用户(tenant_id不为空)user
         */
        if (loginUserDetailVO.getTenantId().equalsIgnoreCase(UnifastConstants.DEFAULT_TENANT_ID)) {
            customerParam.put(UnifastConstants.USER_TYPE, UnifastConstants.USERT_TYPE_ADMIN);
        } else if (loginUserDetailVO.getStaffId().equalsIgnoreCase(loginUserDetailVO.getTenantAdminId())) {
            customerParam.put(UnifastConstants.USER_TYPE, UnifastConstants.USERT_TYPE_TENANTADMIN);
        } else {
            customerParam.put(UnifastConstants.USER_TYPE, UnifastConstants.USERT_TYPE_USER);
        }
        entity.setCustomParam(customerParam);
        return entity;
    }
    public String pushTagHandler(SysStaffInfoVO sysStaffInfoVO){
        return sysStaffInfoVO.getTenantId()+"@"+sysStaffInfoVO.getOrgId();
    }
}


