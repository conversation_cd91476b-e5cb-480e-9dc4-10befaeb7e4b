package cn.chinaunicom.sdsi.cloud.auth.filter;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.fegin.SanquanOpportunityFeignClient;
import cn.chinaunicom.sdsi.cloud.auth.util.HttpclientUtils;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于2.0系统跳入3.0系统的认证过滤器
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/2/25
 */
@Slf4j
@Component
public class SSOAppDecideYKCityPresaleSupporterFilter extends AbstractAuthenticationProcessingFilter {


    @Value("${auth.url.ssoSessionSessionUrl}")
    private String sessionUrl;

    @Autowired
    private SanquanOpportunityFeignClient sanquanOpportunityFeignClient;

    public SSOAppDecideYKCityPresaleSupporterFilter() {
        super(new AntPathRequestMatcher("/app/sso/decide/presaleSupporter/token"));
    }

    @Override
    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        super.setAuthenticationManager(authenticationManager);
    }

    private static String toUtf8(String str) {
        return new String(str.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("\\\"", "'").replace("\"", "");
    }

    private static String getRandomCode(int length) {
        SecureRandom random = new SecureRandom();
        return new BigInteger(length * 4, random).toString(32);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
        String todoCode = request.getParameter("todoCode");
        String authSessionId = request.getParameter("token");
        log.error("=======token==workFeedbackId======todoCode==after" + todoCode);
        log.error("=======token======authSessionId==after" + authSessionId);
        try {
            if (!StringUtils.isNotEmpty(authSessionId)) {
                log.error("token==================UsernameNotFoundException===" + authSessionId);
                throw new UsernameNotFoundException("无效的会话");
            }
            String url = String.format("%s%s", sessionUrl, authSessionId);
            log.error("sessionUrl=====================" + sessionUrl);
            log.error("获取用户url=====================" + url);
            String staffInfoStr = HttpclientUtils.get(url);
            log.error(staffInfoStr);
            JSONObject responseObject = new JSONObject(staffInfoStr);
            if ("success".equals(responseObject.getStr("rspInfo"))) {

                JSONObject staff = responseObject.getJSONObject("data").getJSONObject("userInfo");
                //用户信息
                MallUser mallUser = new MallUser();
                mallUser.setTenantId("sqzcr");
                mallUser.setStaffId(staff.getStr("oaUserId"));
                mallUser.setStaffName(staff.getStr("name"));
                mallUser.setLoginName(staff.getStr("login"));
                mallUser.setUsername(staff.getStr("name") + getRandomCode(10));
                mallUser.setOrgCode(staff.getStr("orgId"));
                mallUser.setOrgName(staff.getStr("orgName"));
                mallUser.setTel(staff.getStr("phone"));
                mallUser.getCustomParam().put("oaUserInfo", staff.toString());
                TSanquanDZqztJihezxProvinceMappingVO vo = sanquanOpportunityFeignClient.findKhjlByLogin(staff.getStr("login"));
                mallUser.setCity(vo.getCity());
                mallUser.getCustomParam().put("authSessionId", authSessionId);
                mallUser.setCompanyId(authSessionId);
                log.error("token==================error5===" + authSessionId);
                //权限信息
                List<GrantedAuthority> authorities = new ArrayList<>();
                mallUser.setAuthorities(authorities);
                log.error("token==================error8===" + mallUser.getCompanyId());
                return new UnifastAuthToken(mallUser, authSessionId, mallUser.getAuthorities());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new UsernameNotFoundException("会话已失效");

    }

}
