package cn.chinaunicom.sdsi.cloud.auth.util;

import cn.chinaunicom.sdsi.security.util.Sm2Encryptor;
import org.apache.commons.codec.digest.DigestUtils;
import org.bouncycastle.crypto.InvalidCipherTextException;

/**
 * @测试 赵芳城
 * @时间 2021/3/12
 * @描述
 */
public class TestCert {
    public static void main(String args[]) throws InvalidCipherTextException {
        String publicKey="0428D625CEEB71CE823BD7D78DFEE7B122F2DA5C4D21E32253AD684D0FE21810394A799639C0CDFBFEB535A1DFD6A366A637E582CE0B1466A5FE7858841135DE6B";
        String privateKey="4F7144028D4DCF88FA50F0E2B3FFDDCF63BBE17D1700537DCE037687D3AA3DA7";
        String jiami="04773d94bcb67f768d076c98c57eeff951051f95fe3a49e9dcd3366d8014d0e0bdc26f410809a17fe7facd2f870cca220c8bacf6d77ba8b7aa636517f86abce0f0d591872d13dd77e06619ac5fe779a8440db569ebef27061c5d1864f1cd480e9730f00ba2341cefac2969&password=04773d94bcb67f768d076c98c57eeff951051f95fe3a49e9dcd3366d8014d0e0bdc26f410809a17fe7facd2f870cca220c8bacf6d77ba8b7aa636517f86abce0f0d591872d13dd77e06619ac5fe779a8440db569ebef27061c5d1864f1cd480e9730f00ba2341cefac2969";
        String data="Eshop@2020";
        Sm2Encryptor sm2Encryptor=new Sm2Encryptor();
/*        System.out.println(sm2Encryptor.privateDecrypt(data,publicKey));;
        System.out.println(sm2Encryptor.publicEncrypt(data,publicKey));;*/
        System.out.println(sm2Encryptor.privateDecrypt(jiami,privateKey));;
       /* byte[] bytes=data.getBytes();
        String sha1 = DigestUtils.sha1Hex(bytes);
        System.out.println(sha1);*/
    }
}
