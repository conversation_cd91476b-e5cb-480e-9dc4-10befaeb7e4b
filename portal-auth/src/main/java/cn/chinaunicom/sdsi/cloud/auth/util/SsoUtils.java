package cn.chinaunicom.sdsi.cloud.auth.util;

import cn.chinaunicom.sdsi.cloud.auth.constant.SSOConstant;
import com.chinaunicom.usercenter.sso.util.HttpUtil;
import com.chinaunicom.usercenter.sso.util.UserEntry;
import com.chinaunicom.usercenter.sso.util.XmlHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.http.client.utils.URIBuilder;
import org.dom4j.Document;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.core.AuthenticationException;
import sun.misc.BASE64Encoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.net.URI;
import java.security.PublicKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class SsoUtils {

//	@Value("${auth.url.ssoUtilsUserCenterUrl}")
//	private static String ssoUtilsUserCenterUrl;
	//测试环境地址
//	public static final String userCenterUrl = "https://10.124.131.213:8101";
	//生产环境地址
	public static final String userCenterUrl = "https://uac.sso.chinaunicom.cn";

	/**
	 * 封装单点表单
	 *
	 * @param request
	 * @param response
	 * @param succUrl
	 * @param errUrl
	 * @param returnurl
	 * @throws Exception
	 */
	public static void setSsologinPage(HttpServletRequest request, HttpServletResponse response, String succUrl, String errUrl, String returnurl) throws AuthenticationException {
		try {
			log.info("云门户单点....");
			long startTime = System.currentTimeMillis();
			// 若尚未登陆，构造一个登录界面，并提交到云门户相应的认证地址，并传递过去一个成功后处理的url地址，
			// rediect_login
			response.setContentType("text/html;charset=utf-8");
			PrintWriter out = response.getWriter();
			returnurl = returnurl == null ? "" : returnurl;

			HttpUtil getHttpUtil = HttpUtil.getIstance(SSOConstant.APP_ID_USERCENTER, userCenterUrl + "/uac-sso-config/get_url_config",
					userCenterUrl + "/uac-sso-config/get_url_config");
            log.info("getHttpUtil===================={}",getHttpUtil);
			String checkLoginUrl = getHttpUtil.getUserCenterAddr(HttpUtil.CHECK_LOGIN, userCenterUrl+"/uac-sso/check_login");
            log.info("checkLoginUrl===================={}",checkLoginUrl);
			log.info("succUrl===================={}",succUrl);
			log.info("errUrl===================={}",errUrl);
			log.info("appid===================={}",SSOConstant.APP_ID_USERCENTER);
			//普通单点
			response.setContentType("text/html;charset=utf-8");

			out.println("<html>");
			out.println("<meta http-equiv=\"content-type\" content=\"text/html; charset=UTF-8\">");
			out.println("<body>");
			// out.println("<body\">");
			out.println("<form id=\"checkForm\" name=\"checkForm\" action=\""
					+ checkLoginUrl
					+ "\" method=\"GET\">");
			out.println("		<input type=\"hidden\" name=\"success\" value=\""
					+ succUrl+"?t="+System.currentTimeMillis() + "\">");
			out.println("		<input type=\"hidden\" name=\"error\" value=\""
					+ errUrl + "\">");
			out.println("		<input type=\"hidden\" name=\"appid\" value=\""
					+ SSOConstant.APP_ID_USERCENTER + "\">");
			out.println("		<input type=\"hidden\" name=\"return\" value=\""
					+ returnurl + "\">");
			// out.println("<input type=\"submit\" name=\"tijiao\">");
			out.println("</form>");
			out.println("<script>");
			out.println("  document.checkForm.submit()");
			out.println("</script>");
			out.println("</body>");
			out.println("</html>");

			out.flush();

			URI uri = new URIBuilder(checkLoginUrl)
			.setParameter("success", succUrl)
			.setParameter("error",errUrl)
			.setParameter("appid",SSOConstant.APP_ID_USERCENTER)
			.setParameter("return",returnurl).build();
			log.info("uri======================={}",uri);

			// 创建Httpclient对象
//			CloseableHttpClient httpclient = HttpClients.createDefault();
//			CloseableHttpResponse closeableHttpResponse = null;
//			try {
//
//				// 定义请求的参数
//				URI uri = new URIBuilder(checkLoginUrl)
//						//.setParameter("t",System.currentTimeMillis()+"")
//						.setParameter("success", succUrl)
//						.setParameter("error",errUrl)
//						.setParameter("appid",SSOConstant.APP_ID_USERCENTER)
//						.setParameter("return",returnurl).build();
////				URI uri = new URIBuilder("http://localhost:8804/v1.0/role/view")
////						.setParameter("roleId","000004")
////						.build();
//
//				System.out.println(uri);
//
//				// 创建http GET请求
//				HttpGet httpGet = new HttpGet(uri);
//
//				// 执行请求
//				closeableHttpResponse = httpclient.execute(httpGet);
//				// 判断返回状态是否为200
//				if (closeableHttpResponse.getStatusLine().getStatusCode() == 200) {
//					String content = EntityUtils.toString(closeableHttpResponse.getEntity(), "UTF-8");
//					System.out.println(content);
//				}
//			}catch(Exception e){
//				e.printStackTrace();
//
//			}finally {
//				if (closeableHttpResponse != null) {
//					try {
//						closeableHttpResponse.close();
//					} catch (IOException e) {
//						e.printStackTrace();
//					}
//				}
//				try {
//					httpclient.close();
//				} catch (IOException e) {
//					e.printStackTrace();
//				}
//			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 单点验证
	 *
	 * @param request
	 * @param response
	 * @param
	 * @param
	 * @return
	 * @throws Exception
	 */
	public Map<String, Serializable> checkAuthentication(HttpServletRequest request, HttpServletResponse response)  {
		Map<String, Serializable> map = new HashMap<String, Serializable>();

		String soap = request.getParameter("soap");
        log.info("soap===================={}",soap);
		String token = XmlHelper.GetToken_v1(soap);
        log.info("token===================={}",token);

		boolean status = false;
		String error_info = "123";
		UserEntry userentry = null;

		try {
			if (token != null && token.length() > 0) {

                log.info("token is valid===================={}",token);
				HttpUtil getHttpUtil = HttpUtil.getIstance(SSOConstant.APP_ID_USERCENTER, userCenterUrl + "/uac-sso-config/get_url_config",
						userCenterUrl + "/uac-sso-config/get_url_config");
                log.info("token is valid getHttpUtil===================={}",getHttpUtil);
				String checkAuthenticationUrl = getHttpUtil.getUserCenterAddr(HttpUtil.CHECK_AUTHENTICATION, userCenterUrl + "/uac-sso/check_authentication");
                log.info("token is valid checkAuthenticationUrl===================={}",checkAuthenticationUrl);
				/**
				 * 单点验证
				 */
				String url = checkAuthenticationUrl
						+ "?token="
						+ token + "&appid=" + SSOConstant.APP_ID_USERCENTER; // "&appid=APP-e835fa18b1e72c0954a92066d292e619";

				Document doc = XmlHelper.XmlForSendRequest(url); // 获取XML返回信息

				ClassPathResource resource = new ClassPathResource("uac_sso_rsa.cer",this.getClass().getClassLoader());
                log.info("resource================={}",resource);
				log.info("resource.getInputStream()================={}",resource.getInputStream());
				InputStream inputStream = resource.getInputStream();
				File file = new File("uac_sso_rsa.cer");
				FileUtils.copyInputStreamToFile(inputStream, file);
				FileInputStream fis = new FileInputStream(file);

				if (XmlHelper.CheckSignForFileInputStream(doc, fis)) {// 校验签名
                    log.info("token is valid uac_sso_rsa.cer====================");
					String str = XmlHelper.GetStatus(doc);// 获取返回SAML中的状态信息
                    log.info("String str = XmlHelper.GetStatus(doc)===================={}",str);
					if(str!=null && str.equals("urn:oasis:names:tc:SAML:2.0:status:Success")){
						userentry = XmlHelper.get_userinfo(doc);//获取SAML中的用户信息
						status = true;
					}
					else if(str!=null && str.equals("urn:oasis:names:tc:SAML:2.0:status:Responder")){
						error_info="IDP处理出错";
					}
					else if(str!=null && str.equals("urn:oasis:names:tc:SAML:2.0:status:VersionMismatch")){
						error_info="请求版本错误";
					}
					else if(str!=null && str.equals("urn:oasis:names:tc:SAML:2.0:status:PartialLogout")){
						error_info="其他站点已注销";
					}
					else if(str!=null && str.equals("urn:oasis:names:tc:SAML:2.0:status:RequestDenied")){
						error_info="不支持的请求";
					}
					else if(str!=null && str.equals("urn:oasis:names:tc:SAML:2.0:status:Forbid")){
						error_info="该账号不允许访问该应用系统";
					}
				}
			}

            log.info("UserEntry map1===================={}",map);
			//status = true;
			map.put("status", status);
			map.put("error_info", error_info);
			map.put("userentry", userentry);
			log.info("UserEntry map2===================={}",map);

			return map;
		}catch (Exception e){
			e.printStackTrace();
		}
		return map;
	}

	/**
	 * 打印认证文件
	 *
	 * @throws CertificateException
	 * @throws FileNotFoundException
	 */
	@SuppressWarnings("unused")
	private void printFile(String filePathOnAppserver) throws CertificateException, FileNotFoundException {
		File file = new File(filePathOnAppserver+ "/lib/uac_sso_rsa.cer");
		System.out.println("uac_sso_rsa.cer.length ==" + file.length());

	    CertificateFactory cf = CertificateFactory.getInstance("X.509");
	    X509Certificate cert = (X509Certificate)cf.generateCertificate(new FileInputStream(filePathOnAppserver+ "/lib/uac_sso_rsa.cer"));
	    PublicKey publicKey = cert.getPublicKey();
	    BASE64Encoder base64Encoder=new BASE64Encoder();
	    String publicKeyString = base64Encoder.encode(publicKey.getEncoded());

	}


}
