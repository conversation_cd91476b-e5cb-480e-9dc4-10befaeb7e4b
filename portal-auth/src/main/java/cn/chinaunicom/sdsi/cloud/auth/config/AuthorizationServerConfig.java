package cn.chinaunicom.sdsi.cloud.auth.config;

import cn.chinaunicom.sdsi.cloud.auth.client.provider.OauthClientServiceImpl;
import cn.chinaunicom.sdsi.cloud.auth.client.service.OauthClientInfoService;
import cn.chinaunicom.sdsi.cloud.converter.UserEntityConverter;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.CompositeTokenGranter;
import org.springframework.security.oauth2.provider.TokenGranter;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.oauth2.provider.code.JdbcAuthorizationCodeServices;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 认证服务器配置
 * 参考：https://www.jianshu.com/p/1e974fc91f74
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2020/9/30
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 */
@Configuration
@RequiredArgsConstructor
@EnableAuthorizationServer
public class AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

    @Resource
    private AuthenticationManager authenticationManager;
    @Resource
    private UserDetailsService userDetailsService;
    @Resource
    private TokenStore tokenStore;
    @Resource
    private OauthClientInfoService clientInfoService;
    @Resource
    private DataSource dataSource;
    //    @Autowired
//    private AuthorizationCodeServices authorizationCodeServices;
    @Resource
    private DefaultTokenServices defaultTokenServices;

    /**
     * 声明 ClientDetails实现
     */
    @Bean
    public ClientDetailsService clientDetails() {
        final OauthClientServiceImpl clientService = new OauthClientServiceImpl();
        clientService.setClientInfoService(clientInfoService);
        return clientService;
    }

    @Bean
    public AuthorizationCodeServices authorizationCodeServices() {
        return new JdbcAuthorizationCodeServices(dataSource);
    }

    @Override
    @SneakyThrows
    public void configure(ClientDetailsServiceConfigurer clients) {
        clients.withClientDetails(clientDetails());
    }


    private TokenGranter tokenGranter(AuthorizationServerEndpointsConfigurer endpoints) {
        // 默认tokenGranter集合
        List<TokenGranter> granters = new ArrayList<>(Collections.singletonList(endpoints.getTokenGranter()));
        // 添加 自定义单点登录验证模式
//        granters.add(new CaptchaTokenGranter(authenticationManager, endpoints.getTokenServices(),
//                endpoints.getClientDetailsService(), endpoints.getOAuth2RequestFactory(), cloudRedisTemplate,
//                userDetailsService, cloudPasswordEncoder(), cloudSecurity));

        // 组合tokenGranter集合
        return new CompositeTokenGranter(granters);
    }

    /**
     * 令牌访问端点
     *
     * @param endpoints AuthorizationServerEndpointsConfigurer
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) {
        /*
         * 设置用户认证的管理器和生成的令牌的存储方式
         */
        endpoints.authenticationManager(this.authenticationManager)
                .tokenGranter(tokenGranter(endpoints))
                .allowedTokenEndpointRequestMethods(HttpMethod.POST, HttpMethod.GET);
        /*
         * 必须设置UserDetailsService才能使用refresh_token：指定使用refresh_token换取access_token时，从哪里获取认证用户信息
         */
//        endpoints.userDetailsService(userDetailsService);
        // 设置自定义的UserAuthenticationConverter
        DefaultAccessTokenConverter accessTokenConverter = new DefaultAccessTokenConverter();
        accessTokenConverter.setUserTokenConverter(new UserEntityConverter());
        endpoints.accessTokenConverter(accessTokenConverter);
        endpoints.authenticationManager(authenticationManager)
                .tokenStore(tokenStore);
        endpoints.authorizationCodeServices(authorizationCodeServices());
        endpoints.tokenServices(defaultTokenServices);
    }

    /**
     * 配置令牌端点(Token Endpoint)的安全约束
     *
     * @param oauthServer AuthorizationServerSecurityConfigurer
     */
    @Override
    public void configure(AuthorizationServerSecurityConfigurer oauthServer) {
        /*
         * 获取令牌不需要认证，校验令牌需要认证，允许表单认证
         */
        oauthServer
                // 允许客户表单认证,不加的话/oauth/token无法访问
                .allowFormAuthenticationForClients()
                // 对于CheckEndpoint控制器[框架自带的校验]的/oauth/token端点允许所有客户端发送器请求而不会被Spring-security拦截
                // 开启/oauth/token_key验证端口无权限访问
                .tokenKeyAccess("permitAll()")
                // 要访问/oauth/check_token必须设置为permitAll()，但这样所有人都可以访问了，设为isAuthenticated()又导致访问不了，这个问题暂时没找到解决方案
                // 开启/oauth/check_token验证端口认证权限访问
                .checkTokenAccess("permitAll()");
    }


}
