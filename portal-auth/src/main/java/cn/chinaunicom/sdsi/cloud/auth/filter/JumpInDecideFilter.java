package cn.chinaunicom.sdsi.cloud.auth.filter;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.service.SaaSUserDetailsService;
import cn.chinaunicom.sdsi.cloud.system.staff.vo.SysStaffVO;
import cn.chinaunicom.sdsi.framework.enums.ResponseEnum;
import cn.chinaunicom.sdsi.framework.response.BaseResponse;
import cn.chinaunicom.sdsi.framework.utils.UnifastConstants;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import cn.chinaunicom.sdsi.security.util.Sm2Encryptor;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.code.AuthorizationCodeServices;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * 用于2.0系统跳入3.0系统的认证过滤器
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/2/25
 */
@Slf4j
@Component
public class JumpInDecideFilter extends AbstractAuthenticationProcessingFilter {

    private final Sm2Encryptor encryptor = new Sm2Encryptor();
    @Value("${unifast.security.private-key:}")
    private String privateKey;
    @Value("${unifast.security.portal-ppk}")
    private String portalPrk;
    @Autowired
    private ClientDetailsService clientDetailsService;
    @Autowired
    private AuthorizationCodeServices authorizationCodeServices;
    @Autowired
    private Gson gson;
    @Autowired
    private SaaSUserDetailsService saaSUserDetailsService;


    public JumpInDecideFilter() {
        super(new AntPathRequestMatcher("/decide"));
    }

    @Override
    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        super.setAuthenticationManager(authenticationManager);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
        String originStr = request.getQueryString();
        try {
            String decodeStr = encryptor.decryptString(privateKey, originStr);
            final String[] split = decodeStr.split("@@");
            String clientId = split[0];
            String mmySource = split[1];
            String uidTenantId=encryptor.decryptString(portalPrk,mmySource);
            String redirectUri = split[2];
            log.info("传递过来的重定向地址是--->>>>>>>{}",redirectUri);
            log.info("参数传递的tenantid{}",uidTenantId);
            if(!uidTenantId.contains(":")){
                throw new AuthenticationServiceException("参数未传递tenantid");
            }
            String[] tempUserIds=uidTenantId.split(":");
            //访问用户中心
            SysStaffVO staffVO=new SysStaffVO();
            staffVO.setLoginName(tempUserIds[0]);
            staffVO.setTenantId(tempUserIds[1]);
            final MallUser mallUser = (MallUser) saaSUserDetailsService.loadUserByParams(staffVO);
            if (mallUser == null){
                throw new AuthenticationServiceException("当前用户不存在！");
            }
            UnifastAuthToken unifastAuthToken = new UnifastAuthToken(mallUser, mallUser.getPassword(),
                    mallUser.getAuthorities());
            unifastAuthToken.getUserEntity().getCustomParam().put(UnifastConstants.TENANT_LOGIN_NAME,mallUser.getCustomParam().get(UnifastConstants.TENANT_LOGIN_NAME));
            log.info(">>>>>>>>>>>>>>user中存在这个值吗？{}",mallUser.getCustomParam().get(UnifastConstants.TENANT_LOGIN_NAME));
            log.info(">>>>>>>>>>>>>>toString中存在这个值吗？{}",unifastAuthToken.toString());
            //获取 ClientDetails
            ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
            //授权码模式, 组建 authentication
//            Set<String> set = clientDetails.getScope();
//            set.add(now+"");
            OAuth2Request oAuth2Request = new OAuth2Request(Maps.newHashMap(), clientId,
                    clientDetails.getAuthorities(), true, clientDetails.getScope(),
                    clientDetails.getResourceIds(), redirectUri, null,
                    null);

            OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, unifastAuthToken);

            String code = authorizationCodeServices.createAuthorizationCode(oAuth2Authentication);

            final String question = "?";
            if (redirectUri.contains(question)) {
                redirectUri += "&code=" + code;
            } else {
                redirectUri += "?code=" + code;
            }
            //补充自定义参数
            if (split.length > 4) {
                String queryStr = split[4];
                redirectUri += "&" + queryStr;
            }
            log.info("最终重定向地址是--->>>>>>>{}",redirectUri);
            response.sendRedirect(redirectUri);
        } catch (Exception e) {
            log.error("处理跳转认证出现异常:{}", e.getLocalizedMessage());
            response.setContentType("application/json;charset=utf-8");
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.getWriter().println(gson.toJson(makeErrorResult("认证出现异常")));
            return null;
        }
        return null;
    }

    private BaseResponse<String> makeErrorResult(String message) {
        BaseResponse<String> baseResponse = new BaseResponse<>();
        baseResponse.setSuccess(false);
        baseResponse.setCode(ResponseEnum.STATUS_CODE_401.getCode());
        baseResponse.setResponseStatus(ResponseEnum.STATUS_CODE_401);
        baseResponse.setMessage(message);
        return baseResponse;
    }

}
