package cn.chinaunicom.sdsi.cloud.auth.client.service;

import cn.chinaunicom.sdsi.cloud.auth.client.entity.OauthClientInfoPO;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoAO;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoVO;
import cn.chinaunicom.sdsi.cloud.auth.client.vo.OauthClientInfoQuery;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 系统接入方管理服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-11
 */
public interface OauthClientInfoService extends IService<OauthClientInfoPO> {

    /**
     * 条件分页查询接入系统信息
     *
     * @param query OauthClientInfoQuery
     * @return IPage<OauthClientInfoVO>
     */
    IPage<OauthClientInfoVO> selectClientPage(OauthClientInfoQuery query);

    /**
     * 根据appKey获取客户端信息
     *
     * @param appKey String
     * @return OauthClientInfoVO
     */
    OauthClientInfoVO fetchByAppKey(String appKey);

    /**
     * 新增接入系统信息
     *
     * @param ao OauthClientInfoAO
     * @return String
     */
    String registerClient(OauthClientInfoAO ao);

    /**
     * 查询接入方详情
     *
     * @param clientId 系统ID
     * @return OauthClientInfoVO
     */
    OauthClientInfoVO fetchClient(String clientId);

    /**
     * 删除客户端系统
     *
     * @param clientId 系统ID
     * @return 影响条数
     */
    int deleteClient(String clientId);

    /**
     * 编辑客户端系统信息
     *
     * @param ao OauthClientInfoAO
     * @return 影响条数
     */
    int editClient(OauthClientInfoAO ao);

    /**
     * 启用客户端
     * @param clientId Long
     * @return 条数
     */
    int enableClient(String clientId);

    /**
     * 禁用客户端
     * @param clientId Long
     * @return 条数
     */
    int disableClient(String clientId);

    /**
     * 删除授权码
     *
     * @param code 授权码code
     * @return 影响条数
     */
    int deleteCode(String code);
}
