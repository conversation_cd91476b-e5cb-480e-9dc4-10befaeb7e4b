package cn.chinaunicom.sdsi.cloud.auth.filter;

import cn.chinaunicom.sdsi.cloud.auth.MallUser;
import cn.chinaunicom.sdsi.cloud.auth.fegin.SanquanBoFeignClient;
import cn.chinaunicom.sdsi.cloud.auth.fegin.SanquanOpportunityFeignClient;
import cn.chinaunicom.sdsi.cloud.auth.util.HttpclientUtils;
import cn.chinaunicom.sdsi.cloud.customer.vo.TSanquanDZqztJihezxProvinceMappingVO;
import cn.chinaunicom.sdsi.cloud.prefectureInterfacePerson.vo.TSanquanPrefectureInterfacePersonVO;
import cn.chinaunicom.sdsi.cloud.roles.entity.SanquanRole;
import cn.chinaunicom.sdsi.cloud.ssoPath.entity.TSanquanSsoPath;
import cn.chinaunicom.sdsi.core.exception.ServiceErrorException;
import cn.chinaunicom.sdsi.framework.utils.StringUtils;
import cn.chinaunicom.sdsi.platform.user.entity.UserVo;
import cn.chinaunicom.sdsi.security.browser.user.UnifastAuthToken;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * 用于2.0系统跳入3.0系统的认证过滤器
 * update [序号][日期YYYY-MM-DD] [更改人姓名][变更描述]
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/2/25
 */
@Slf4j
@Component
public class SSOZhengqiDecideBoUnifyYKFilter extends AbstractAuthenticationProcessingFilter {


    @Value("${auth.url.ssoSessionSessionUrl}")
    private String sessionUrl;

    @Autowired
    private SanquanBoFeignClient sanquanBoFeignClient;

    public SSOZhengqiDecideBoUnifyYKFilter() {
        super(new AntPathRequestMatcher("/sso/decide/bo/unify/token","GET"));
    }

    @Override
    @Autowired
    public void setAuthenticationManager(AuthenticationManager authenticationManager) {
        super.setAuthenticationManager(authenticationManager);
    }

    private static String toUtf8(String str) {
        return new String(str.getBytes(StandardCharsets.UTF_8), StandardCharsets.UTF_8).replace("\\\"", "'").replace("\"", "");
    }

    private static String getRandomCode(int length) {
        SecureRandom random = new SecureRandom();
        return new BigInteger(length * 4, random).toString(32);
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException, IOException {
        String authSessionId = request.getParameter("authSessionId");
        String authSessionId2 = request.getHeader("authSessionId");
        // 获取所有参数的名称
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            // 根据参数名称获取参数值，getParameterValues用于获取同名的多个值（比如复选框）
            String[] paramValues = request.getParameterValues(paramName);

            for (String value : paramValues) {
                System.err.println("请求头中的参数PC: " + paramName + ", Value: " + value);
            }
        }
        System.err.println("=======token======authSessionId==after" + authSessionId+" HEADauthSessionId="+authSessionId2);
        try {
            if (!StringUtils.isNotEmpty(authSessionId)) {
                System.err.println("token==================UsernameNotFoundException===" + authSessionId);
                throw new UsernameNotFoundException("无效的会话");
            }
            // 单点类型
            String ssoType = request.getParameter("ssoType");
            if(!StringUtils.isNotEmpty(ssoType)){
                throw new ServiceErrorException("缺失重定向类型！");
            }
            TSanquanSsoPath ssoByType = sanquanBoFeignClient.findByType(ssoType);
            if(ssoByType == null){
                throw new ServiceErrorException("没有当前登录配置信息！");
            }
            String url = String.format("%s%s", sessionUrl, authSessionId);
            System.err.println("sessionUrl=====================" + sessionUrl);
            System.err.println("获取用户url=====================" + url);
            String staffInfoStr = HttpclientUtils.get(url);
            System.err.println(staffInfoStr);
            JSONObject responseObject = new JSONObject(staffInfoStr);
            if ("success".equals(responseObject.getStr("rspInfo"))) {
                JSONObject staff = responseObject.getJSONObject("data").getJSONObject("userInfo");
                //用户信息
                MallUser mallUser = new MallUser();
                mallUser.setTenantId("khjl");
                mallUser.setStaffId(staff.getStr("oaUserId"));
                mallUser.setStaffName(staff.getStr("name"));
                mallUser.setLoginName(staff.getStr("login"));
                mallUser.setUsername(staff.getStr("name") + getRandomCode(10));
                mallUser.setOrgCode(staff.getStr("orgId"));
                mallUser.setOrgName(staff.getStr("orgName"));
                mallUser.setTel(staff.getStr("phone"));
                mallUser.getCustomParam().put("oaUserInfo", staff.toString());
                TSanquanPrefectureInterfacePersonVO cityInterfaceVo = null;
                // 权限
                List<GrantedAuthority> authorities = new ArrayList<>();
                // 地市接口人
                if("2".equals(ssoByType.getCustomerType())){
                    //地市接口人
                    cityInterfaceVo = sanquanBoFeignClient.findCityUserByJobNumber(staff.getStr("login"));
                    mallUser.setTenantId("dszq");
                    System.err.println("地市接口人信息:\n" + new JSONObject(cityInterfaceVo).toString());
                }

                String jobNum = staff.getStr("login");
                UserVo userVo = sanquanBoFeignClient.findStaff(jobNum);
                // 维护用户信息
                if (userVo == null) {
                    List<String> roleIds = new ArrayList<>();
                    userVo = new UserVo();
                    // 查找客户经理
                    TSanquanDZqztJihezxProvinceMappingVO vo = sanquanBoFeignClient.findKhjlByLogin(jobNum);
                    if (vo != null) {
                        roleIds.add("100002");
                        userVo.setCity(vo.getCity().replace("市", ""));
                    }
                    userVo.setJobNum(jobNum);
                    userVo.setName(staff.getStr("name"));
                    userVo.setDeleted("normal");
                    userVo.setPhone(staff.getStr("phone"));
                    // 地市接口人信息处理
                    if (cityInterfaceVo != null && org.apache.commons.lang3.StringUtils.isNotEmpty(cityInterfaceVo.getType())) {
                        roleIds.add("100008");
                        //区县审核
                        if ("1".equals(cityInterfaceVo.getType())) {
                            roleIds.add("100005");
                        }
                        //地市政企审核
                        if ("1".equals(cityInterfaceVo.getCityRole())) {
                            roleIds.add("100006");
                        }

                        if (!(("1".equals(cityInterfaceVo.getType())) || "1".equals(cityInterfaceVo.getCityRole()))) {
                            roleIds.add("100004");
                        }
                        userVo.setCity(cityInterfaceVo.getListCustomerCity().replace("市", ""));
                        userVo.setCounty(cityInterfaceVo.getListCustomerDistrict());
                    }
                    //权限信息
                    JSONArray jsonRoles = responseObject.getJSONObject("data").getJSONArray("userRoles");
                    for (int i = 0; i < jsonRoles.size(); i++) {
                        JSONObject role = jsonRoles.getJSONObject(i);
                        Long roleId = role.getLong("sysRoleId");
                        SanquanRole _role = sanquanBoFeignClient.findRoleById(String.valueOf(roleId));
                        if (_role != null) {
                            roleIds.add(_role.getId());
                        }
                    }
                    //用户行业信息
                    JSONArray zqStaffInfoList = responseObject.getJSONObject("data").getJSONArray("zqStaffInfoList");
                    if (zqStaffInfoList != null && !zqStaffInfoList.isEmpty()) {
                        userVo.setIndustry(zqStaffInfoList.getJSONObject(0).getStr("industryNameSj"));
                    }
                    userVo.setRoles(permission(roleIds));
                    //保存用户信息
                    this.sanquanBoFeignClient.saveUser(userVo);
                }

                // 地市信息处理
                if("1".equals(ssoByType.getCustomerType())){
                    TSanquanDZqztJihezxProvinceMappingVO vo = sanquanBoFeignClient.findKhjlByLogin(staff.getStr("login"));
                    mallUser.setCity(vo.getCity());
                }else if("2".equals(ssoByType.getCustomerType())){
                    if (cityInterfaceVo != null) {
                        mallUser.setCity(cityInterfaceVo.getListCustomerCity());
                        mallUser.getCustomParam().put("city", cityInterfaceVo.getListCustomerCity());
                        if ("1".equals(cityInterfaceVo.getCityRole())) {
                            authorities.add(new SimpleGrantedAuthority("ROLE_CITY_INTERFACE"));
                        }
                    }
                }

                mallUser.getCustomParam().put("authSessionId", authSessionId);
                mallUser.setCompanyId(authSessionId);
                System.err.println("token==================error5===" + authSessionId);
                //权限信息
                JSONArray jsonRoles = responseObject.getJSONObject("data").getJSONArray("userRoles");
                mallUser.setAuthorities(authorities);
                System.err.println("token==================error8===" + mallUser.getCompanyId());
                return new UnifastAuthToken(mallUser, authSessionId, mallUser.getAuthorities());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        throw new UsernameNotFoundException("会话已失效");

    }

    public static List<String> permission(List<String> list) {
        List<String> newList = new ArrayList<>(list.size());
        list.forEach(i -> {
            if (!newList.contains(i)) { // 如果新集合中不存在则插入
                newList.add(i);
            }
        });
        return newList;
    }
}
