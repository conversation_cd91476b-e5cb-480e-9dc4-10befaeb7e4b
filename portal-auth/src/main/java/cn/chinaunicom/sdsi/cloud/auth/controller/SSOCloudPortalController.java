package cn.chinaunicom.sdsi.cloud.auth.controller;

import cn.chinaunicom.sdsi.cloud.auth.util.SsoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;

/**
 * 云门户单点登录
 */
@Slf4j
@RestController
@RequestMapping("/sso")
public class SSOCloudPortalController {

	@Value("${auth.url.ssoCloudReadAppUrl}")
	private String ssoCloudReadAppUrl;

	@Value("${auth.url.ssoCloudErrorAppUrl}")
	private String ssoCloudErrorAppUrl;

	@GetMapping(value = "/cloudPortal")
	public void cloudPortal(HttpServletRequest request,
								HttpServletResponse response) throws Exception {
        log.error("============cloudPortal==================");
		String returnurl = request.getParameter("return");
		returnurl = returnurl == null ? "" : returnurl;

		String param = request.getQueryString();
		if (!StringUtils.isEmpty(param)){
			final String[] split = param.split("&");
			if (!StringUtils.isEmpty(returnurl) && split.length > 2) {
				String messageTrackId = split[1];
				String cloudPartyid = split[2];
				returnurl += "&" + messageTrackId + "&"+cloudPartyid;
			}
		}

		String succUrl = ssoCloudReadAppUrl + "/cloudPortalLogin";
		String errUrl = ssoCloudReadAppUrl + "/sso/cloudPortalLoginError";
		log.info("sso -------------- first request start");
		SsoUtils.setSsologinPage(request, response, succUrl, errUrl, returnurl);
		log.info("sso ============== first request end");

	}

	/**
	 * 单点失败后的提示地址
	 *
	 * <AUTHOR> 2012-9-21
	 * @param request
	 * @param response
	 * @throws Exception
	 */
	@GetMapping(value = "/cloudPortalLoginError")
	public ModelAndView cloudPortalLoginError(HttpServletRequest request,
											   HttpServletResponse response) throws Exception {
		log.error("============cloudPortalLoginError==================");
		String path = request.getContextPath();
		String basePath = request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+path+"/";
		String errorcode = request.getParameter("errorCode");
//		ParameterMap<String, String[]> error= (ParameterMap<String, String[]>) request.getParameterMap();

		String errorMsg = "";
		log.error("============cloudPortalLoginError=========errorcode========="+errorcode);
//		log.error("============cloudPortalLoginError=========error========="+ JSON.toJSONString(error));
		if(errorcode!=null && errorcode.length()>0){
			if(errorcode.equals("2")) errorMsg = "验证码出错";
			else if(errorcode.equals("1")) errorMsg = "账号或口令错误";
			else if(errorcode.equals("999")) errorMsg = "4A服务器内部错误";
			else if(errorcode.equals("3")) errorMsg = "账号停止使用";
			else if(errorcode.equals("4")) errorMsg = "没有访问此站点授权";
			else if(errorcode.equals("5")) errorMsg = "该站点未注册";
			else if(errorcode.equals("998")) errorMsg = "SSO维护中";
			else if(errorcode.equals("21")) errorMsg = "该用户已退出";
			else if(errorcode.equals("22")) errorMsg = "超时";
		}
//		errorMsg = "账号或口令错误";
		log.error(errorMsg);
//		return new ModelAndView("redirect:"+ ssoCloudErrorAppUrl+ "/login"+errorMsg);
//		return new ModelAndView("redirect:"+ ssoCloudErrorAppUrl);
		errorMsg = URLEncoder.encode(errorMsg, "utf-8");
				return new ModelAndView("redirect:"+ ssoCloudErrorAppUrl+"?errorMsg="+errorMsg);


	}

}
