/*******************************************************************************
 *     Cloud Foundry
 *     Copyright (c) [2009-2014] Pivotal Software, Inc. All Rights Reserved.
 *
 *     This product is licensed to you under the Apache License, Version 2.0 (the "License").
 *     You may not use this product except in compliance with the License.
 *
 *     This product includes a number of subcomponents with
 *     separate copyright notices and license terms. Your use of these
 *     subcomponents is subject to the terms and conditions of the
 *     subcomponent's license, as noted in the LICENSE file.
 *******************************************************************************/
package org.springframework.security.oauth2.provider.endpoint;

import cn.chinaunicom.sdsi.cloud.token.UniTokenExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.codec.Base64;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.error.DefaultWebResponseExceptionTranslator;
import org.springframework.security.oauth2.provider.error.WebResponseExceptionTranslator;
import org.springframework.security.oauth2.provider.token.AccessTokenConverter;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.util.Map;

/**
 * Controller which decodes access tokens for clients who are not able to do so (or where opaque token values are used).
 *
 * <AUTHOR> Taylor
 * <AUTHOR> D'sa
 */
@Slf4j
@FrameworkEndpoint
public class CheckTokenEndpoint {

    private ResourceServerTokenServices resourceServerTokenServices;
    @Autowired
    private ClientDetailsService clientDetailsService;


    public UniTokenExtractor getUniTokenExtractor() {
        return  new UniTokenExtractor();
    }

    private AccessTokenConverter accessTokenConverter = new DefaultAccessTokenConverter();

    protected final Log logger = LogFactory.getLog(getClass());

    private WebResponseExceptionTranslator<OAuth2Exception> exceptionTranslator = new DefaultWebResponseExceptionTranslator();

    public CheckTokenEndpoint(ResourceServerTokenServices resourceServerTokenServices) {
        this.resourceServerTokenServices = resourceServerTokenServices;
    }

    /**
     * @param exceptionTranslator the exception translator to set
     */
    public void setExceptionTranslator(WebResponseExceptionTranslator<OAuth2Exception> exceptionTranslator) {
        this.exceptionTranslator = exceptionTranslator;
    }

    /**
     * @param accessTokenConverter the accessTokenConverter to set
     */
    public void setAccessTokenConverter(AccessTokenConverter accessTokenConverter) {
        this.accessTokenConverter = accessTokenConverter;
    }

    @PostMapping(value = "/oauth/check_token")
    @ResponseBody
    public Map<String, ?> checkToken(@RequestParam(name="token",defaultValue = "") String value, HttpServletRequest req, HttpServletResponse resp) {
        String clientId = req.getHeader("clientId");
        String str = req.getHeader("Authorization");
        String authorization = null;
        /**
         * 增加checkToken从cookie中取值的方式，当参数不传递时，从cookie中取
         * 当cookie和参数value中不一致时以参数中的为准----->modify by zhaofc 2022-4-20 15:56:59弃用此规则当A、B Tab同时登录状态存在，B退出重新登录，刷新A页面会将老token携带过来导致认证失败。 （&&!value.equalsIgnoreCase(getUniTokenExtractor().readTokenFromCookiesOrParameter(req))）
         */
        if(!StringUtils.hasText(value)){
            value=getUniTokenExtractor().readTokenFromCookiesOrParameter(req);
            log.debug("checktoken参数未获取到，从cookie取值开始==================={}",value);
        }
        if (StringUtils.hasLength(str) && str.contains("Basic")){
            final String[] auth = str.split("Basic");
            log.info("auth==================={}",auth);
            String strObj = auth[1].trim();
            try {
                byte[] decode = Base64.decode(strObj.getBytes("UTF-8"));

                String res = new String(decode,"UTF-8");
                if (StringUtils.hasLength(res)){
                    authorization = res.substring(0, res.indexOf(":")).trim();
                }
            }
            catch (UnsupportedEncodingException e) {
                throw new IllegalStateException("Could not convert String");
            }
        }

        if (!StringUtils.hasLength(clientId) && !StringUtils.hasLength(authorization)) {
            Map<String, Object> result = new HashedMap();
            result.put("code", "401");
            result.put("message", "客户端信息不正确");
            return result;
        }

        if (!StringUtils.hasLength(clientId)){
            clientId = authorization;
        }

        this.clientDetailsService.loadClientByClientId(clientId);

        OAuth2AccessToken token = resourceServerTokenServices.readAccessToken(value);
        if (token == null) {
            throw new InvalidTokenException("Token was not recognised");
        }

        if (token.isExpired()) {
            throw new InvalidTokenException("Token has expired");
        }

        OAuth2Authentication authentication = resourceServerTokenServices.loadAuthentication(token.getValue());

        Map<String, Object> response = (Map<String, Object>) accessTokenConverter.convertAccessToken(token, authentication);

        // gh-1070
        response.put("active", true);    // Always true if token exists and not expired

        return response;
    }

    @ExceptionHandler(InvalidTokenException.class)
    public ResponseEntity<OAuth2Exception> handleException(Exception e) throws Exception {
        logger.info("Handling error: " + e.getClass().getSimpleName() + ", " + e.getMessage());
        // This isn't an oauth resource, so we don't want to send an
        // unauthorized code here. The client has already authenticated
        // successfully with basic auth and should just
        // get back the invalid token error.
        @SuppressWarnings("serial")
        InvalidTokenException e400 = new InvalidTokenException(e.getMessage()) {
            @Override
            public int getHttpErrorCode() {
                return 400;
            }
        };
        return exceptionTranslator.translate(e400);
    }

}
