# 认证中心 cloud-auth

```
测试账号：sysman 
密码明文 pd@2021 密码修改方法在usercenter中
密文 042ea85f9a137ca1158537b9b08595b6411fd2a413096290e02f408e73e452459d45ff9070f186db64d64783803b270b5771d340cbda88ebf33eb2bb462ee9819abd80056b5f4fe8a60f78d0f137b487a2a8cdc296ced381bd0b22251e371c784bfcc10466f7340d

公钥：
0428D625CEEB71CE823BD7D78DFEE7B122F2DA5C4D21E32253AD684D0FE21810394A799639C0CDFBFEB535A1DFD6A366A637E582CE0B1466A5FE7858841135DE6B
```

提供认证，可接入集团用户、合作方用户、专家用户

_角色管理、权限管理、角色权限分配等功能于用户中心实现；鉴权动作于各个服务（或网关）执行_


### 登录方式

目前测试账号： 

1. web认证（表单提交，在response中可直接返回令牌信息）
    ```http
    POST /auth/login HTTP/1.1
    Host: localhost:8100
    Content-Type: application/x-www-form-urlencoded
    Content-Length: 30
    
    username=admin&password=123456
    ```

1. 隐藏式获取令牌（需要先经过web认证，client_id、redirect_uri需要在客户端表中维护）

    请求：

    ```shell
    curl --location --request GET 'http://localhost:8100/auth/oauth/authorize?response_type=token&redirect_uri=http://localhost&scope=all&client_id=bVS46ElU' \
    --header 'Cookie: JSESSIONID=00A549A12C856FCCD51A5EEB475F8549'
    ```

    响应（页面重定向，其中access_token的值即令牌）：

    `http://localhost#access_token=492c17c4-7213-4be4-b9fb-df1618a8d1e2&token_type=bearer&expires_in=34998`

    ​    

1. 授权码方式获取令牌（需要先经过web认证）

    请求（需要先经过web认证，client_id、redirect_uri需要在客户端表中维护）：

    ```shell
    curl --location --request GET 'http://localhost:8100/auth/oauth/authorize?response_type=code&client_id=bVS46ElU&redirect_uri=http://localhost&scope=all' \
    --header 'Cookie: JSESSIONID=454D3B09CE1B892D54CB9A96BC779656'
    ```

    响应（页面重定向）：

    `http://localhost?code=4hapqM`

    请求（client_id、client_secret、redirect_uri需要和客户端表中信息匹配，code是上一步刚获取的授权码）：

    ```shell
    curl --location --request POST 'http://localhost:8100/auth/oauth/token?grant_type=authorization_code&client_id=bVS46ElU&client_secret=58ea04ba02475c8da2321cc99849d2a10f15b749&redirect_uri=http://localhost&scope=all&code=MtfUD2' \
    --header 'Cookie: JSESSIONID=77B95074D75E80E7C8CA39DCC30D2EDE'
    ```

    响应（其中，value即是令牌，refreshToken.value为刷新令牌）：

    ```json
    {
        "value": "492c17c4-7213-4be4-b9fb-df1618a8d1e2",
        "expiration": "2020-11-27 23:56:48",
        "tokenType": "bearer",
        "refreshToken": {
            "expiration": "2020-12-27 11:56:48",
            "value": "3a8adddc-cab9-48b4-886b-30fcdb2ccb67"
        },
        "scope": [
            "all"
        ],
        "additionalInformation": {}
    }
    ```

    ​    

1. 客户端认证

    请求：

    ```shell
    curl --location --request POST 'http://localhost:8100/auth/oauth/token?grant_type=client_credentials&client_id=bVS46ElU&client_secret=58ea04ba02475c8da2321cc99849d2a10f15b749'
    ```

    响应（value为令牌）：

    ```json
    {
        "value": "06e1d52f-d5ed-415a-9623-3ff919666d53",
        "expiration": "2020-11-28 02:25:07",
        "tokenType": "bearer",
        "refreshToken": null,
        "scope": [
            "all"
        ],
        "additionalInformation": {}
    }
    ```

    

### 用户对象 MallUser

| 属性                  | 注释       | 类型                          | 值                                                         |
| --------------------- | ---------- | ----------------------------- | ---------------------------------------------------------- |
| tenantId               | 租户id     | String                        | 租户表主键，本项目暂时恒为1 |
| ou               | 用户中心组织编码     | String                        | 所属组织的编码 |
| ouName               | 组织名称     | String                        | 所属组织的名称 |
| province               | 用户所在省分     | String                   | 所属组织的省分编码 |
| city               | 用户所在市分    | String                        | 所属组织的市分编码 |
| staffId               | 人员id     | String                        | 门户全国目录中的唯一编码，uid |
| staffOrgId            | 岗位id（框架遗留属性，不采用） | —— | —— |
| username              | 登录名     | String | 同staffId，即邮箱前缀 |
| password              | 密码       | String | 恒为 N/A |
| orgId                 | 部门id | String | 同ou |
| staffName             | 姓名       | String                        | 中文姓名                                                   |
| staffType             | 用户类型   | String                        | own-内部用户；partner-合作方；expert-专家；              |
| enabled               | 是否可用   | Boolean                       | true/false                                                 |
| customParam           | 自定义属性 | HashMap<String, Serializable> | 待定                                                       |
| authorities           | 权限字     | List<GrantedAuthority>        | 权限字集合                                                 |
| accountNonExpired     | 账号未过期 | Boolean                       | 目前恒为true，表示未过期                                   |
| accountNonLocked      | 账号未锁定 | Boolean                       | 目前恒为true，表示未锁定                                   |
| credentialsNonExpired | 密码未过期 | Boolean                       | 目前恒为true，表示密码未过期                               |
|                       |            |                               |                                                            |
|                       |            |                               |                                                            |







### 知识点

密码模式登陆

grant_type: 

- password 密码模式(将用户名,密码传过去,直接获取token)
- authorization_code 授权码模式(即先登录获取code,再获取token)
- client_credentials 客户端模式(无用户,用户向客户端注册,然后客户端以自己的名义向’服务端’获取资源)
- implicit 简化模式(在redirect_uri 的Hash传递token; Auth客户端运行在浏览器中,如JS,Flash)
- refresh_token 刷新access_token

端点：

/oauth/authorize 授权端点
/oauth/token 令牌端点
/oauth/confirm_access 用户确认授权提交端点
/oauth/error 授权服务错误信息端点
/oauth/check_token 用于资源服务访问的令牌解析端点
/oauth/token_key 提供公有密钥的端点，如果使用JWT令牌的话